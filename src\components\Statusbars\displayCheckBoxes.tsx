import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { EventName } from '@/Apps/EventSystem';
import { CadDrawingLayerType } from '@/Apps/LayoutAI/Drawing/TDrawingLayer';
import { TLayoutEntityContainer } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { ITopMenuItem } from '@svg/antd-cloud-design/lib/TopMenu';
import { useEffect, useState } from 'react';
import useStyles from './style/index'; // 导入样式
import { Checkbox } from '@svg/antd';
import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
import { useStore } from '@/models';
interface ICommandTopMenuItem extends ITopMenuItem {
  command_name?: string;
  titleCn?: string;
  subList?: ICommandTopMenuItem[];
}

const DisplayCheckBoxes: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  const { styles } = useStyles();
  const t = LayoutAI_App.t;
  const store = useStore();

  const getCurrentLayerState = (): { [key: string]: boolean } => {
    let layer_state_data: { [key: string]: boolean } = {};
    let appManager = (LayoutAI_App.instance as TAppManagerBase);
    if (!appManager?.drawing_layers) {
      layer_state_data[CadDrawingLayerType.CadEzdxfDrawing] = false;
      layer_state_data[CadDrawingLayerType.CadRoomStrucure] = true;
      layer_state_data[CadDrawingLayerType.CadFurniture] = true;
      layer_state_data[CadDrawingLayerType.CadCabinet] = true;
      layer_state_data[CadDrawingLayerType.CadOutLine] = true;
      layer_state_data[CadDrawingLayerType.CadLighting] = false;
      layer_state_data[CadDrawingLayerType.CadCeiling] = false;
      layer_state_data[CadDrawingLayerType.CadDecorates] = false;
      layer_state_data[CadDrawingLayerType.CadSubRoomAreaDrawing] = LayoutAI_App.IsDebug ? true : false;
      layer_state_data[CadDrawingLayerType.CadDimensionWallElement] = false;
      layer_state_data[CadDrawingLayerType.CadDimensionOutterWallElement] = false;
      layer_state_data[CadDrawingLayerType.RulerDrawing] = true;
      layer_state_data[CadDrawingLayerType.CadRoomName] = true;
      layer_state_data['LockEzdxf'] = false;
      return layer_state_data;
    } else {
      layer_state_data[CadDrawingLayerType.CadEzdxfDrawing] = appManager.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing]?.visible;
      layer_state_data[CadDrawingLayerType.CadRoomStrucure] = appManager.drawing_layers[CadDrawingLayerType.CadRoomStrucure]?.visible;
      layer_state_data[CadDrawingLayerType.CadFurniture] = appManager.drawing_layers[CadDrawingLayerType.CadFurniture]?.visible;
      layer_state_data[CadDrawingLayerType.CadCabinet] = appManager.drawing_layers[CadDrawingLayerType.CadCabinet]?.visible;
      layer_state_data[CadDrawingLayerType.CadOutLine] = appManager.drawing_layers[CadDrawingLayerType.CadOutLine]?.visible;
      layer_state_data[CadDrawingLayerType.CadLighting] = appManager.drawing_layers[CadDrawingLayerType.CadLighting]?.visible;
      layer_state_data[CadDrawingLayerType.CadCeiling] = appManager.drawing_layers[CadDrawingLayerType.CadCeiling]?.visible;
      layer_state_data[CadDrawingLayerType.CadDecorates] = appManager.drawing_layers[CadDrawingLayerType.CadDecorates]?.visible;
      layer_state_data[CadDrawingLayerType.CadSubRoomAreaDrawing] = appManager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing]?.visible;
      layer_state_data[CadDrawingLayerType.CadDimensionWallElement] = appManager.drawing_layers[CadDrawingLayerType.CadDimensionWallElement]?.visible;
      layer_state_data[CadDrawingLayerType.CadDimensionOutterWallElement] = appManager.drawing_layers[CadDrawingLayerType.CadDimensionOutterWallElement]?.visible;
      layer_state_data[CadDrawingLayerType.RulerDrawing] = appManager.drawing_layers[CadDrawingLayerType.RulerDrawing]?.visible;
      layer_state_data[CadDrawingLayerType.CadRoomName] = appManager.drawing_layers[CadDrawingLayerType.CadRoomName]?.visible;
      layer_state_data['LockEzdxf'] = appManager.drawing_layers['LockEzdxf']?.visible || false;
      return layer_state_data;
    }
  };

  const [layerBtnState, setLayerBtnState] = useState<{ [key: string]: boolean }>(getCurrentLayerState());
  

  let display_btnList: ICommandTopMenuItem[] = [];
  
  if (store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode) {
    display_btnList = [
      {
        id: CadDrawingLayerType.CadRoomName,
        title: t('空间名称'),
        titleCn: '空间名称', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadRoomName]
      },
      {
        id: CadDrawingLayerType.CadDimensionWallElement,
        title: t('内墙标注'),
        titleCn: '内墙标注', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadDimensionWallElement]
      },
      {
        id: CadDrawingLayerType.CadDimensionOutterWallElement,
        title: t('外墙标注'),
        titleCn: '外墙标注', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadDimensionOutterWallElement]
      },
      {
        id: CadDrawingLayerType.CadEzdxfDrawing,
        title: t('显示临摹图'),
        titleCn: '显示临摹图', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadEzdxfDrawing]
      },
      {
        id: 'LockEzdxf',
        title: t('锁定临摹图'),
        titleCn: '锁定临摹图', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState['LockEzdxf']
      }
    ];
  } else {
    display_btnList = [
      {
        id: CadDrawingLayerType.CadRoomStrucure,
        title: t('墙体结构'),
        titleCn: '墙体结构', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadRoomStrucure]
      },
      {
        id: CadDrawingLayerType.CadFurniture,
        title: t('家具'),
        titleCn: '家具', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadFurniture]
      },
      {
        id: CadDrawingLayerType.CadCabinet,
        title: t('定制柜'),
        titleCn: '定制柜', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadCabinet]
      },
      {
        id: CadDrawingLayerType.CadSubRoomAreaDrawing,
        title: t('区域'),
        titleCn: '区域', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadSubRoomAreaDrawing]
      },
      {
        id: CadDrawingLayerType.CadCeiling,
        title: t('吊顶'),
        titleCn: '吊顶', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadCeiling]
      },
      {
        id: CadDrawingLayerType.CadRoomName,
        title: t('空间名称'),
        titleCn: '空间名称', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.CadRoomName]
      },
      {
        id: CadDrawingLayerType.RulerDrawing,
        title: t('量尺'),
        titleCn: '量尺', //[i18n:ignore]
        type: 'checkbox',
        checked: layerBtnState[CadDrawingLayerType.RulerDrawing]
      }
    ];
  }

  display_btnList = display_btnList.filter(ele => ele);
  const onClickItem = (item: ICommandTopMenuItem) => {
    if (layerBtnState[item.id] !== undefined) {
      let state = { ...layerBtnState };
      state[item.id] = !state[item.id];
      LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleSwitchDrawingLayer, state);
    }
  };

  useEffect(() => {
    LayoutAI_App.on_M(EventName.SwitchDrawingLayer, 'display-check-box', (state_data: { [key: string]: boolean }) => {
      let state = { ...layerBtnState, ...state_data };
      setLayerBtnState(state);
    });

    return () => {
      LayoutAI_App.off_M(EventName.SwitchDrawingLayer, 'display-check-box');
    };
  }, []);
  return (
    <div className={styles.checkBoxes} style={{ display: isVisible ? 'block' : 'none' }}>
      {display_btnList.map((item, index) => (
        <div key={'display_check_' + index}>
          <Checkbox
            checked={layerBtnState[item.id]}
            onChange={ev => {
              onClickItem(item);
            }}
          >
            {item.title}
          </Checkbox>
        </div>
      ))}
    </div>
  );
};

export default DisplayCheckBoxes;
