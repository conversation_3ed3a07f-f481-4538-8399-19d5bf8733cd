"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1418],{37883:function(e,n,t){t.r(n),t.d(n,{default:function(){return Ie}});var r=t(13274),o=t(9003),a=t(23825);function i(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function s(){var e=i(["\n      width:100%;\n      height:100vh;\n    "]);return s=function(){return e},e}function c(){var e=i(["\n      position: absolute;\n      top: 0;\n      left: 0; \n      right:0;\n      bottom:0;\n      overflow: hidden;\n    "]);return c=function(){return e},e}function u(){var e=i(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      width: 100px;\n      height: 60px;\n      overflow: hidden;\n    "]);return u=function(){return e},e}function l(){var e=i(["\n      position:absolute;\n      top:0;\n      left:0;\n      right:0;\n      bottom:0;\n      background-color: #EAEAEB;\n      z-index:-1;\n    "]);return l=function(){return e},e}function f(){var e=i(["\n        position: absolute;\n        left:0;\n        top: 0;\n        background-color: #EAEAEB;\n        width : 100%;\n        height : 100%;\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(./static/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(./static/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(./static/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(./static/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(./static/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(./static/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(./static/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return f=function(){return e},e}function p(){var e=i(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return p=function(){return e},e}function d(){var e=i(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return d=function(){return e},e}function h(){var e=i(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return h=function(){return e},e}function y(){var e=i(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return y=function(){return e},e}var g=(0,t(8268).rU)(function(e){var n=e.css;return{root:n(s()),content:n(c()),topBtns:n(u()),canvas3d:n(l()),canvas_pannel:n(f(),(0,a.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:n(p()),backBtn:n(d()),forwardBtn:n(h()),schemeNameSpan:n(y())}}),b=t(27347),v=t(98612),m=t(41594),_=t(88934),w=t(65640);function x(e,n,t,r,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,o)}function D(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function S(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function C(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var I=function(){function e(){!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e),S(this,"targetWindow",void 0),S(this,"onMessage",void 0)}var n,t,r;return n=e,t=[{key:"init",value:function(){var e=this;this.onMessage||(this.onMessage=function(n){e.handleMessage(n.data)},window.addEventListener("message",this.onMessage,!1)),window.parent&&(this.targetWindow=window.parent)}},{key:"dispose",value:function(){this.onMessage&&window.removeEventListener("message",this.onMessage,!1)}},{key:"bindTargetWindow",value:function(e){e&&(this.targetWindow=e)}},{key:"handleMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"Command"===e.msgType?this.processCommand(e):"Event"===e.msgType?this.handleEvent(e):"Async"===e.msgType?this.processAsyncMsg(e):"Connected"===e.msgType&&this._onConnected(e)}},{key:"processCommand",value:function(e){}},{key:"handleEvent",value:function(e){}},{key:"processAsyncMsg",value:function(e){return(n=function(){return C(this,function(e){return[2]})},function(){var e=this,t=arguments;return new Promise(function(r,o){var a=n.apply(e,t);function i(e){x(a,r,o,i,s,"next",e)}function s(e){x(a,r,o,i,s,"throw",e)}i(void 0)})})();var n}},{key:"_onConnected",value:function(e){w.log("Connected!@@@",e)}},{key:"postMessage",value:function(e){this.targetWindow&&this.targetWindow.postMessage(e,"*")}},{key:"postEventMessage",value:function(e,n){var t={msgType:"Event",eventName:e,eventData:n};this.postMessage(t)}}],r=[{key:"makeInstance",value:function(){e._instance&&e._instance.dispose(),e._instance=new e}},{key:"instance",get:function(){return e._instance||e.makeInstance(),e._instance}}],t&&D(n.prototype,t),r&&D(n,r),e}();S(I,"_instance",void 0);var M=t(79489),k=t(65640);function P(e,n,t,r,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,o)}function O(e){return function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){P(a,r,o,i,s,"next",e)}function s(e){P(a,r,o,i,s,"throw",e)}i(void 0)})}}function j(e,n,t){return n=R(n),function(e,n){if(n&&("object"==(t=n,t&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)||"function"==typeof n))return n;var t;return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,N()?Reflect.construct(n,t||[],R(e).constructor):n.apply(e,t))}function A(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function T(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function E(e,n,t){return E="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,n,t){var r=function(e,n){for(;!Object.prototype.hasOwnProperty.call(e,n)&&null!==(e=R(e)););return e}(e,n);if(r){var o=Object.getOwnPropertyDescriptor(r,n);return o.get?o.get.call(t||e):o.value}},E(e,n,t||e)}function R(e){return R=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},R(e)}function z(e,n){return z=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},z(e,n)}function N(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(N=function(){return!!e})()}function L(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var B=function(e){function n(){var e;return function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,n),T(e=j(this,n),"_async_processors",void 0),T(e,"_commands_dict",void 0),T(e,"_async_queue",void 0),T(e,"_active_data",null),e._async_processors={},e._commands_dict={},e._async_queue=[],e.initDefaultProcessors(),e}var t,r,o;return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&z(e,n)}(n,e),t=n,o=[{key:"makeInstance",value:function(){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])&&I._instance||(I._instance=new n,e&&e())}}],(r=[{key:"init",value:function(){E(R(n.prototype),"init",this).call(this),window.parent&&window.parent.postMessage({msgType:"Connected"},"*")}},{key:"addAsyncProcessor",value:function(e,n){this._async_processors[e]=n}},{key:"addCommand",value:function(e,n){this._commands_dict[e]=n}},{key:"initDefaultProcessors",value:function(){this.addAsyncProcessor("TestA",function(e){return O(function(){return L(this,function(e){switch(e.label){case 0:return[4,(0,M.IP)(2e3)];case 1:return e.sent(),[2,"testDone!"]}})})()})}},{key:"processCommand",value:function(e){this._commands_dict[e.command]?this._commands_dict[e.command]():b.nb.RunCommand(e.command||"")}},{key:"handleEvent",value:function(e){b.nb.DispatchEvent(e.eventName||"",e.eventData||null)}},{key:"processAsyncMsg",value:function(e){return O(function(){return L(this,function(n){return this._async_queue.push({msgData:e}),this.startProcessAsyncMsg(),[2]})}).call(this)}},{key:"startProcessAsyncMsg",value:function(){this._active_data||this._async_queue[0]&&this._exactProcessAsyncMsg(this._async_queue[0].msgData)}},{key:"onDoneMsgData",value:function(e){this._active_data&&(this._active_data=null),this._async_queue.length>0&&(this._async_queue.splice(0,1),this.startProcessAsyncMsg())}},{key:"_exactProcessAsyncMsg",value:function(e){return O(function(){var n,t,r;return L(this,function(o){switch(o.label){case 0:if(n={msgType:"Async",asyncId:e.asyncId,asyncEventName:e.asyncEventName,asyncResult:null},this._active_data=e,!this._async_processors[e.asyncEventName])return[3,5];o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this._async_processors[e.asyncEventName](e)];case 2:return t=o.sent(),n.asyncResult=t,n.asyncState=1,[3,4];case 3:return r=o.sent(),k.log(r),n.aysncError="Error: In Process!",n.asyncState=-1,[3,4];case 4:return this.onDoneMsgData(n),this.postMessage(n),[3,6];case 5:n.aysncError="Error: No Processor!---"+e.asyncEventName,n.asyncState=-1,this.onDoneMsgData(n),this.postMessage(n),o.label=6;case 6:return[2]}})}).call(this)}}])&&A(t.prototype,r),o&&A(t,o),n}(I),U=t(7784),F=t(80324),W={};W[F.t.Update]=function(){b.nb.instance.update()};var G=t(78644),q=t(99030),H=t(67869),Y=t(32184),V=t(15696),X=t(44936),K=t(65640);function $(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function Z(e,n,t,r,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,o)}function J(e){return function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){Z(a,r,o,i,s,"next",e)}function s(e){Z(a,r,o,i,s,"throw",e)}i(void 0)})}}function Q(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ee(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function ne(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.forEach(function(n){ee(e,n,t[n])})}return e}function te(e,n){return n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):function(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,r)}return t}(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}),e}function re(e){return function(e){if(Array.isArray(e))return $(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"==typeof e)return $(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return $(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oe(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function ae(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{x:1,y:0,z:0};return X.r[e]?te(ne({},X.r[e]),{pos:{x:n.x,y:n.y,z:n.z},nor:{x:t.x,y:t.y,z:t.z}}):null}var ie=function(){function e(){!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,e)}var n,t,r;return n=e,r=[{key:"scripts0",value:function(){return J(function(){var n,t,r,o,a,i;return oe(this,function(s){switch(s.label){case 0:return[4,(n=e.ClientFuncs).setDrawing2DMode({mode:Y.qB.Texture})];case 1:return s.sent(),b.nb.IsDebug?[3,3]:[4,n.loadByBuidlingId({building_id:"1ILGHCIGIJLGKDFKHC"})];case 2:s.sent(),s.label=3;case 3:return K.log("loading BuildingId Done"),[4,n.getAllRoomInfos()];case 4:return t=s.sent(),K.log("rooms",t),[4,n.applyAiLayoutInRooms({filteredRoomNames:["客餐厅"],append_furniture_entites:!0})];case 5:return s.sent(),[4,n.showDrawingLayer({layers:[{layerName:Y.U0.CadCeiling,visible:!0}]})];case 6:return s.sent(),[4,n.getCeilingAreasInRoom({filteredRoomNames:["客餐厅"]})];case 7:return s.sent(),r=K.log,o=["styleList"],[4,n.getStyleSeriesList({ruleType:1})];case 8:return r.apply(K,o.concat([s.sent()])),a=K.log,i=["ApplyStyle"],[4,n.applyStyleSeriesInRooms({seriesKgId:"335564",filteredRoomNames:["客餐厅"]})];case 9:return a.apply(K,i.concat([s.sent()])),[2]}})})()}},{key:"scripts1",value:function(){return J(function(){var n,t,r,o,a,i,s,c,u,l,f,p,d,h,y,g;return oe(this,function(b){switch(b.label){case 0:return K.log("测试设置不同吊顶参数"),[4,(n=e.ClientFuncs).showDrawingLayer({layers:[{layerName:Y.U0.CadDecorates,visible:!0},{layerName:Y.U0.CadCeiling,visible:!0},{layerName:Y.U0.CadSubRoomAreaDrawing,visible:!0}]})];case 1:return b.sent(),[4,n.getCeilingAreasInRoom({})];case 2:return t=b.sent(),K.log("吊顶参数信息",t),(r=t.find(function(e){return e.room_name.includes("客餐厅")}))?(K.log("客餐厅吊顶参数",r),[3,4]):(K.log("没找到客餐厅"),[2]);case 3:b.sent(),b.label=4;case 4:return o=K.log,a=["init3D"],[4,n.initScene3D({visible:!0})];case 5:return o.apply(K,a.concat([b.sent()])),i=K.log,s=["show3D"],[4,n.changeSceneMode({sceneMode:"3D_FirstPerson",isHidden3D:!1})];case 6:return i.apply(K,s.concat([b.sent()])),[4,n.setUsePanelType({panelType:1})];case 7:b.sent(),b.label=8;case 8:return K.log("尝试修改餐厅区, 吊顶样式"),(c=r.sub_areas[1])?[3,9]:(K.log("没找到餐厅区"),[3,12]);case 9:return[4,(0,M.IP)(1e3)];case 10:return b.sent(),u={uuid:c.uuid,ceiling_type:Y.Xj.Suspended,is_auto_sub_area:!1},l=K.log,[4,n.editCeilingAreaInRoom(u)];case 11:l.apply(K,[b.sent()]),b.label=12;case 12:return K.log("尝试在客餐厅内添加灯具"),(f=r.sub_areas[0])?(p=f.area_rect.rect_center,d=[{pos:{x:p.x,y:p.y,z:0},length:100,depth:100,height:85,materialId:"351229910"},{pos:{x:p.x-300,y:p.y,z:0},length:100,depth:100,height:85,materialId:"351229910"},{pos:{x:p.x+300,y:p.y,z:0},length:100,depth:100,height:85,materialId:"351229910"},{pos:{x:p.x,y:p.y-300,z:0},length:100,depth:100,height:85,materialId:"351229910"},{pos:{x:p.x,y:p.y+300,z:0},length:100,depth:100,height:85,materialId:"351229910"}],[4,n.addDownLights({down_lights:d})]):(K.log("没找到客厅区"),[2]);case 13:b.sent(),K.log("添加结束"),b.label=14;case 14:return K.log("再次在客厅内添加灯具, 测试是否清空重新添加"),(h=r.sub_areas[0])?(y=h.area_rect.rect_center,[4,(0,M.IP)(1e3)]):(K.log("没找到客厅区"),[2]);case 15:return b.sent(),g=[{category:"筒灯",pos:{x:y.x,y:y.y,z:0},length:100,depth:100,height:85,materialId:"351229910"},{category:"筒灯",pos:{x:y.x-300,y:y.y,z:0},length:100,depth:100,height:85,materialId:"351229910"},{category:"筒灯",pos:{x:y.x+300,y:y.y,z:0},length:100,depth:100,height:85,installType:1,materialId:"351229910"},{category:"格栅灯",materialId:"165728936",_rect_shape:M.UB.Rect,pos:{x:y.x,y:y.y+100,z:0},length:500,depth:50,height:50,installType:1},{category:"轨道格栅灯",materialId:"152977463",_rect_shape:M.UB.Rect,pos:{x:y.x,y:y.y-100,z:0},length:500,depth:50,height:50,installType:0},{category:"轨道",materialId:"293909058",_rect_shape:M.UB.Rect,pos:{x:y.x,y:y.y-200,z:0},length:500,depth:50,height:50}],[4,n.addDownLights({down_lights:g})];case 16:b.sent(),K.log("添加结束"),b.label=17;case 17:return[2]}})})()}},{key:"scripts2",value:function(){return J(function(){var n,t,r,o,a,i;return oe(this,function(s){switch(s.label){case 0:return[4,(n=e.ClientFuncs).showDrawingLayer({layers:[{layerName:Y.U0.CadDecorates,visible:!1},{layerName:Y.U0.CadCeiling,visible:!0},{layerName:Y.U0.CadSubRoomAreaDrawing,visible:!0},{layerName:Y.U0.CadRoomName,visible:!1}]})];case 1:return s.sent(),[4,n.getCeilingAreasInRoom({})];case 2:return t=s.sent(),K.log("吊顶参数信息",t),(r=t.find(function(e){return e.room_name.includes("客餐厅")}))?(K.log("再次在客厅内添加灯具, 测试是否清空重新添加"),(o=r.sub_areas[0])?(a=o.area_rect.rect_center,[4,(0,M.IP)(1e3)]):(K.log("没找到客厅区"),[2])):(K.log("没找到客餐厅"),[2]);case 3:return s.sent(),i=[ae("筒灯",{x:a.x,y:a.y,z:0}),ae("筒灯",{x:a.x-300,y:a.y,z:0}),ae("筒灯",{x:a.x+300,y:a.y,z:0}),ae("格栅灯",{x:a.x,y:a.y+100,z:0}),ae("轨道格栅灯",{x:a.x,y:a.y-100,z:0}),ae("轨道",{x:a.x,y:a.y-200,z:0})],[4,n.addDownLights({down_lights:i})];case 4:s.sent(),K.log("添加结束"),s.label=5;case 5:return[2]}})})()}},{key:"scripts_addlights",value:function(){return J(function(){var n,t,r,o,a,i,s;return oe(this,function(c){switch(c.label){case 0:return[4,(n=e.ClientFuncs).showDrawingLayer({layers:[{layerName:Y.U0.CadDecorates,visible:!1},{layerName:Y.U0.CadCeiling,visible:!0},{layerName:Y.U0.CadSubRoomAreaDrawing,visible:!1},{layerName:Y.U0.CadRoomName,visible:!1}]})];case 1:return c.sent(),[4,n.getCeilingAreasInRoom({})];case 2:return t=c.sent(),K.log("吊顶参数信息",t),(r=t.find(function(e){return e.room_name.includes("客餐厅")}))?(o=e.addDownLightsSubArea(r.sub_areas[0],0,{lightCategory:"筒灯",lightNum:3,lightGap:300,offsetY:200}),[4,n.addDownLights({down_lights:o})]):(K.log("没找到客餐厅"),[2]);case 3:case 5:case 7:return c.sent(),[4,(0,M.IP)(1e3)];case 4:return c.sent(),a=e.addDownLightsSubArea(r.sub_areas[0],1,{lightCategory:"筒灯",lightGap:600,offsetY:200}),[4,n.addDownLights({down_lights:a})];case 6:return c.sent(),i=e.addDownLightsSubArea(r.sub_areas[0],1,{lightCategory:"筒灯",lightGap:600}),void 0!==r.sub_areas[1]&&(s=i).push.apply(s,re(e.addDownLightsSubArea(r.sub_areas[1],0,{lightCategory:"格栅灯",lightGap:600}))),[4,n.addDownLights({down_lights:i})];case 8:return c.sent(),[2]}})})()}},{key:"scripts_test_sub_space",value:function(){return J(function(){var n,t;return oe(this,function(r){switch(r.label){case 0:return[4,(n=e.ClientFuncs).applyAiLayoutInRooms({filteredRoomNames:["客餐厅"],append_furniture_entites:!0})];case 1:return r.sent(),[4,n.showDrawingLayer({layers:[{layerName:Y.U0.CadCeiling,visible:!0}]})];case 2:return r.sent(),[4,n.applyAiLayoutInSubspaces({filterSubSpaceNames:["客厅区"]})];case 3:return t=r.sent(),K.log(t),[2]}})})()}},{key:"addDownLightsSubArea",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(0==t)return e.makeDownLightsInRowWithRectData(n.area_rect,ne({},r));if(1==t){var o,a,i,s=e.makeDownLightsInRowWithRectData(n.area_rect,te(ne({},r),{nor:{x:1,y:0,z:0},offsetFrom:"Bottom",offsetY:100}));return(o=s).push.apply(o,re(e.makeDownLightsInRowWithRectData(n.area_rect,te(ne({},r),{nor:{x:-1,y:0,z:0},offsetFrom:"Bottom",offsetY:100})))),(a=s).push.apply(a,re(e.makeDownLightsInRowWithRectData(n.area_rect,te(ne({},r),{nor:{x:0,y:1,z:0},offsetFrom:"Bottom",offsetY:100})))),(i=s).push.apply(i,re(e.makeDownLightsInRowWithRectData(n.area_rect,te(ne({},r),{nor:{x:0,y:-1,z:0},offsetFrom:"Bottom",offsetY:100})))),s}}},{key:"makeDownLightsInRowWithRectData",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=(null==n?void 0:n.lightCategory)||"筒灯",r=X.r[t]||X.r["筒灯"],o=(null==n?void 0:n.lightGap)||0,a=o+r.length,i=1,s=(null==n?void 0:n.padding)||400,c=new M.BB;c.importRectData(e),(null==n?void 0:n.nor)&&(c=M.BB.fromPoints(c.positions,n.nor)),o&&(a=(o=n.lightGap)+r.length,i=Math.floor((c.w-s)/a+.001)),n.lightNum&&(i=Math.min(Math.floor((c.w-s)/a+.001),n.lightNum));var u=0,l=null==n?void 0:n.offsetFrom,f=(null==n?void 0:n.offsetY)||0;"Bottom"==l&&(f<r.depth/2&&(f=r.depth/2),u=-c._h/2+f);for(var p=-a*i/2,d=[],h=0;h<i;h++){var y=p+a*h+a/2,g=c.unproject({x:y,y:u});d.push(ae(t,g,c.nor))}return d}}],(t=null)&&Q(n.prototype,t),r&&Q(n,r),e}();ee(ie,"ClientFuncs",U.l);var se=t(53704),ce=t(65810),ue=t(10371),le=t(54282),fe=t(96119);function pe(e,n,t,r,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,o)}function de(e,n,t){return n=ge(n),function(e,n){if(n&&("object"==(t=n,t&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)||"function"==typeof n))return n;var t;return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,ve()?Reflect.construct(n,t||[],ge(e).constructor):n.apply(e,t))}function he(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ye(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function ge(e){return ge=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},ge(e)}function be(e,n){return be=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},be(e,n)}function ve(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ve=function(){return!!e})()}function me(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=n.call(e,a)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var _e=function(e){function n(e){var t;return function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,n),ye(t=de(this,n,[le.uW.Scene3DViewImageLayer,e]),"_img",void 0),ye(t,"_rect",void 0),ye(t,"_renderTarget",void 0),t._img=new Image,t._rect=new M.BB(1,1),t._renderTarget=null,t}var t,r,o;return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&be(e,n)}(n,e),t=n,r=[{key:"scene3D",get:function(){return this._manager.scene3D}},{key:"updateImage",value:function(){var e;return(e=function(){var e,n,t,r,o,a,i,s;return me(this,function(c){return e=this,this._img.src="",this._img=null,(n=this.scene3D)&&n.isValid()?(this._renderTarget||(this._renderTarget=new H.nWS(n.viewWidth,n.viewHeight,{format:H.GWd,depthBuffer:!0,stencilBuffer:!0,colorSpace:H.er$})),(t=n.controls[ue.I5.Perspective])?(r=t.focusCenterWithFixedScale(1),t.update(),o=n.outlinePostProcessing,a=n.outlineMaterialMode,i=o.outline_style_mode,n.outlineMaterialMode=0,s=o.camera,o.camera=t.camera,n.makeDirty(),o.renderToTarget(this._renderTarget),o.camera=s,this._img=new Image,this._img.src=fe.c.getRenderTargetImg(n.renderer,this._renderTarget),n.outlineMaterialMode=a,o.outline_style_mode=i,n.active_controls._updateMeshVisibleByCameraPosition(),n.makeDirty(),n.update(),r.mainFlag?(this._rect._w=r.boxWidth,this._rect._h=r.boxWidth/r.width*r.height):(this._rect._w=r.boxHeight/r.height*r.width,this._rect._h=r.boxHeight),this._rect.nor.set(0,-1,0),this._rect.rect_center={x:r.transform._p_center.x,y:r.transform._p_center.y,z:0},this._img?[2,new Promise(function(n,t){e._img.onload=function(){n(!0)},e._img.complete&&n(!0)})]:[2,!1]):[2]):[2]})},function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){pe(a,r,o,i,s,"next",e)}function s(e){pe(a,r,o,i,s,"throw",e)}i(void 0)})}).call(this)}},{key:"onDraw",value:function(){this._img&&this._img.width>1&&(this.painter._context.globalAlpha=1,this.painter.fillImageInRect(this._img,this._rect))}}],r&&he(t.prototype,r),o&&he(t,o),n}(le.c1);function we(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function xe(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,s=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){s=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(s)throw o}}return a}}(e,n)||Se(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function De(e){return function(e){if(Array.isArray(e))return we(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Se(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(e,n){if(e){if("string"==typeof e)return we(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?we(e,n):void 0}}var Ce={popupType:"Layout",sceneMode:"2D",prev3DSceneMode:"3D",usingPanelType:0,prevSceneUpdateTime:0},Ie=(0,V.observer)(function(){var e=(0,o.P)(),n=g().styles,t=xe((0,m.useState)(-2),2),i=t[0],s=t[1],c=xe((0,m.useState)(Ce.usingPanelType),2),u=c[0],l=c[1],f=xe((0,m.useState)(!1),2),p=f[0],d=f[1],h=xe((0,m.useState)(Y.qB.Figure2D),2),y=(h[0],h[1],"SdkFrame"),w=function(){b.nb.instance&&(b.nb.instance.bindCanvas(document.getElementById("cad_canvas")),b.nb.instance.update()),x()},x=function(){b.nb.instance&&(b.nb.instance._is_landscape=window.innerWidth>window.innerHeight);e.homeStore.IsLandscape;e.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))};b.nb.NewApp(v.e.AppName),b.nb.UseApp(v.e.AppName);return(0,m.useEffect)(function(){if(b.nb.instance&&(b.nb.instance._is_website_debug=a.iG),window.addEventListener("resize",w),w(),B.makeInstance(!1,function(){for(var e in U.l)B.instance.addAsyncProcessor(e,U.l[e]);for(var n in W)B.instance.addCommand(n,W[n]);window.IFrameMsgServerExFuncs=U.l,window.IFrameMsgTestScripts=ie}),B.instance.init(),b.nb.instance){if(!b.nb.instance.initialized){if(b.nb.instance.init(),b.nb.instance.Configs.needs_adjust_ceiling_after_matching=!0,b.nb.instance.Configs.default_is_auto_sub_area=!0,b.nb.instance.Configs.prepare_auto_layout=!1,b.nb.instance.Configs.drawing_down_lights_in_ceiling=!0,b.nb.instance.Configs.update3d_when_xmlscheme_onloaded=!0,b.nb.instance.Configs.is_auto_predict_roomname=!0,b.nb.instance.Configs.is_drawing_ceiling_lines_in_2d=!0,b.nb.instance.Configs.is_post_add_main_lights=!1,b.nb.instance.scene3D){var n=b.nb.instance.scene3D;n.sky&&(n.sky.visible=!1),n.gridHelper&&(n.gridHelper.visible=!1);var t=b.nb.instance.layer_DefaultBatchLayer;if(t){var r,o=b.nb.instance,c=new _e(b.nb.instance);o.drawing_layers[c.cad_drawing_type]=c;var u=[c].concat(De(t._drawing_layers));t._drawing_layers.length=0,(r=t._drawing_layers).push.apply(r,De(u))}}b.nb.RunCommand(v.f.AiCadMode),b.nb.instance.prepare().then(function(){b.nb.instance.scene3D}),b.nb.instance.bindCanvas(document.getElementById("cad_canvas"))}b.nb.instance.update()}return b.nb.on_M(F.A.Init3DScene,y,function(e){e!==p&&d(e)}),b.nb.on_M(F.A.Set3DSceneZIndex,y,function(e){s(e)}),b.nb.on_M(F.A.ChangeSceneMode,y,function(n){0==Ce.usingPanelType&&function(n){var t=b.nb.instance.scene3D,r=b.nb.instance.layout_container;if("2D"===n);else if("3D"===n)b.nb.DispatchEvent(b.n0.Match3dPreviewMaterials,null),t.setCenter(r.painter.p_center),t.setCemeraMode(ue.I5.Perspective),b.nb.DispatchEvent(b.n0.cleanSelect,null),t&&b.nb.emit_M(_.U.Scene3DUpdated,!1);else if("3D_FirstPerson"===n){if(b.nb.DispatchEvent(b.n0.Match3dPreviewMaterials,null),t.setCemeraMode(ue.I5.FirstPerson),"2D"==e.homeStore.viewMode){var o,a=r._room_entities.reduce(function(e,n){return e?n._area>e._area?n:e:n},null);a?(t.setCenter((null==a||null===(o=a._main_rect)||void 0===o?void 0:o.rect_center)||new H.Pq0(0,0,0)),t.update()):t.setCenter(r.painter.p_center)}t&&b.nb.emit_M(_.U.Scene3DUpdated,!1)}n&&"2D"!==n&&(Ce.prev3DSceneMode=n),e.homeStore.setViewMode(n)}((null==n?void 0:n.sceneMode)||"2D"),"2D"===n.sceneMode||n.isHidden3D?(s(-2),0!=Ce.usingPanelType&&b.nb.emit_M(_.U.SceneModeChanged,n.sceneMode)):(s(2),0!=Ce.usingPanelType&&b.nb.emit_M(_.U.SceneModeChanged,n.sceneMode))}),b.nb.on_M(ce.z.showLight3DViewer,y,function(e){e?2!==i&&(s(2),b.nb.emit(q.r.UpdateScene3D,!0)):s(-2)}),b.nb.on_M(_.U.SceneContentStateChanged,y,function(e){if(e){var n=(new Date).getTime();n>Ce.prevSceneUpdateTime+100&&(Ce.prevSceneUpdateTime=n,setTimeout(function(){B.instance.postEventMessage(F.A.SceneContentStateChanged,e)},100))}}),b.nb.on_M(F.A.SetUsePanelType,y,function(e){Ce.usingPanelType=e.panelType||0,l(e.panelType||0)}),function(){b.nb.off_M_All({object_id:y})}},[]),(0,m.useEffect)(function(){var e=b.nb.instance.scene3D;p&&i>0?e.startRender():e.stopRender()},[i]),(0,r.jsxs)("div",{className:n.root,children:[1==u&&(0,r.jsx)(se.A,{updateKey:1}),(0,r.jsx)("div",{className:"3d_container "+n.canvas3d,style:{zIndex:i},children:(0,r.jsx)(G.A,{defaultViewMode:5})}),(0,r.jsx)("div",{id:"Canvascontent",className:n.content,children:(0,r.jsx)("div",{id:"body_container",className:n.canvas_pannel,children:(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){e.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){e.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var t=n.touches[0].clientX-n.touches[1].clientX,r=n.touches[0].clientY-n.touches[1].clientY,o=Math.sqrt(t*t+r*r);e.homeStore.setInitialDistance(o/e.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var t=n.touches[0].clientX-n.touches[1].clientX,r=n.touches[0].clientY-n.touches[1].clientY,o=Math.sqrt(t*t+r*r)/e.homeStore.initialDistance;o>5?o=5:o<.05&&(o=.05),e.homeStore.setScale(o),b.nb.DispatchEvent(b.n0.scale,o)}},onTouchEnd:function(){e.homeStore.setInitialDistance(null)}})})})]})})}}]);