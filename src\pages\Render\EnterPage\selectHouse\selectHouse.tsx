import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from "react-i18next";
import useStyles from './style';
import { openPluginUrl } from '@/config';
import { message } from '@svg/antd';
import { axios } from '@svg/request';
import { useStore } from '@/models';

const SelectHouse: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [webviewUrl, setWebviewUrl] = useState('');
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const store = useStore();

    const init = async () => {
        const appId = '20345075692' // 切换成自己的appId
        const appSecret = 'b8e9032f984a45dba5fe85b9168543f4' // 切换成自己的密钥
        try {
            console.log('sendRequest');
            const tokenResponse = await axios({
                url: 'https://graph.3vjia.com/oauth/token',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: new URLSearchParams({
                    grant_type: 'client_credentials',
                    client_id: appId,
                    client_secret: appSecret
                }).toString()
            });
            // 获取 access_token
            const accessToken = tokenResponse.data.access_token;
            if (accessToken) {
                const newResponse = await axios({
                    url: 'https://open-gateway.3vjia.com/common/api/joinAuth/authorize', // 替换为实际的 API 端点
                    method: 'POST',
                    headers: {
                        'Access-Token': accessToken,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        "svjUserId": "",
                        "enterpriseUserId": "xxx",
                        "tokenExpiresIn": 3600,
                        "bizExtParams": "{\"enterpriseUserName\":\"张三\",\"mobile\":\"18666628697\",\"limitCount\":\"5\"}"
                    }
                });
                console.log('新请求响应：', newResponse.data);
                const code = newResponse.data.data.code
                const url = `${openPluginUrl}/hx-select-ui/?appId=${appId}&code=${code}`
                setWebviewUrl(url); // 设置 webview 的 URL
                store.trialStore.setHouseData({});
            } else {
                message.error(t('无法获取 access_token'));
            }
        } catch (error) {
            console.error(t('请求错误')+":", error);
            message.error(t('请求错误'));
        }
    }

    useEffect(() => {
        init();
        let num = 0
        window.addEventListener('message', (event) => {
            if(event.data.type === "layoutSelected"){
                
                store.trialStore.setHouseData({
                    ...event.data.data,
                    num: num++
                });
                if(event.data.data.id){
                    store.homeStore.setIsShowHouseDetail({show: true, source: 'selectHouse'});
                }
            }
        });
    }, []);


    return (
        <div className={styles.container} ref={iframeRef}>
            <div className={styles.iframe}>
                <iframe src={webviewUrl} title={t("户型选择")} />
            </div>
            {/* {store.homeStore.isShowHouseDetail.show && <div className={styles.dialog}
                onClick={() => store.homeStore.setIsShowHouseDetail({show: false, source: ''})}
            >
                <HouseDetail
                    closeHouseDetail={() => store.homeStore.setIsShowHouseDetail({show: false, source: ''})}
                    toLayout={toLayout}
                />
            </div>} */}
        </div>
    );
};

export default SelectHouse; 