import { MaterialService } from "./MaterialService";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { compareNames } from "@layoutai/z_polygon";
import { I_DesignMaterialInfo } from "../../Layout/IMaterialInterface";
import { TFigureElement } from "../../Layout/TFigureElements/TFigureElement";
import { TRoom } from "../../Layout/TRoom";
import { TWindowDoorEntity } from "../../Layout/TLayoutEntities/TWinDoorEntity";
import { UserDataKey } from "../../Scene3D/NodeName";
import { Model3dApi } from "../../Api/Model3dApi";
import { Group } from "three";
import { GltfManager } from "@layoutai/model3d_api";


export class TDesignMaterialUpdater {
    private static _instance: TDesignMaterialUpdater = null;

    private _designMaterialDict: { [key: string]: I_DesignMaterialInfo };

    constructor() {
        this._designMaterialDict = {};
    }

    get scene3D() {
        if (LayoutAI_App.instance) {
            return LayoutAI_App.instance.scene3D;
        }
        return null;
    }
    static get instance() {
        if (!TDesignMaterialUpdater._instance) {
            TDesignMaterialUpdater._instance = new TDesignMaterialUpdater();
        }
        return TDesignMaterialUpdater._instance;
    }

    async updateRoomFurnitureDesignMaterials(room: TRoom, figure_elements: TFigureElement[] = null, force: boolean = true) {

        if (room._room_entity) {
            room._room_entity.updateMesh3D();

        }

        figure_elements = figure_elements || room._furniture_list;

        let t_figure_elements: TFigureElement[] = [];

        let pushFigure = (ele: TFigureElement) => {
            if (t_figure_elements.includes(ele)) return;
            if (!ele._matched_material) return;
            if (compareNames([ele.category], ["开关", "应急", "给水", "强电"])) return;
            if (force || !ele._solid_mesh3D) {
                t_figure_elements.push(ele);
            }
        }
        figure_elements.forEach(ele => {
            if (ele.furnitureEntity) {
                ele.furnitureEntity.displayed_figure_elements.forEach((inner_ele) => {
                    pushFigure(inner_ele);
                    if (inner_ele.decorationElements != null) {
                        inner_ele.decorationElements.forEach((df) => {
                            pushFigure(df);
                        });
                    }
                });
            }
            else {
                pushFigure(ele);
            }

            if (ele.decorationElements != null) {
                ele.decorationElements.forEach((df) => {
                    pushFigure(df);
                });
            }
        });
        // 空间装饰品---例如：挂画，墙饰
        if(room.decoration_elements)
        {
            room.decoration_elements.forEach((ele)=>pushFigure(ele));
        }
        await this.updateFurnituresDesignMaterials(t_figure_elements);
    }

    async updateWindowsFurnitureDesignMaterials(windows: TWindowDoorEntity[]) {
        let figure_elements = windows.map((win) => win._win_figure_element).filter((ele) => ele);
        await this.updateFurnituresDesignMaterials(figure_elements);

    }

    async updateFurnituresDesignMaterials(figure_elements: TFigureElement[], force: boolean = false) {
        if (this.scene3D) {
            if (!force && !this.scene3D.isValid())  // 只有3D场景显示的时候, 才更新
            {
                return;
            }
        }
        let material_ids: string[] = [];
        figure_elements.forEach(ele => {
            let material_id = ele.getMaterialID();
            if (material_id) {
                if (!this._designMaterialDict[material_id]) {
                    material_ids.push(material_id);
                }
            }
        });

        if (material_ids.length > 0) {
            let results = await MaterialService.getDesignMaterialInfoByIds(material_ids);
            if (results) {
                results.forEach((info) => {
                    this._designMaterialDict[info.MaterialId] = info;
                });
            }
        }

        let promise_list : any = [];
        figure_elements.forEach((ele) => {
            let material_id = ele.getMaterialID();
            if(ele._solid_mesh3D && ele._solid_mesh3D.userData[UserDataKey.MaterialId] === material_id)
            {                
                if(!ele._solid_mesh3D.parent)
                {
                    ele.resetMesh3DParent();
                }
                Model3dApi.UpdatePoseOfModel(ele._solid_mesh3D as Group, TFigureElement.makeMesh3DMaterialInfoOptions(ele));
                if(ele.cabinetStyleId)
                {
                    promise_list.push(GltfManager.updateCabinet3DModelWithStyleBrush(ele._solid_mesh3D as Group,ele.cabinetStyleId,
                        {category:ele.category}));
                }

                return;
            }
            else{
                ele.setDesignMaterialInfo(this._designMaterialDict[material_id]);
                promise_list.push(new Promise((resolve,reject)=>{
     
                     ele.updateMesh3DWithDesignMaterialInfo().then(()=>resolve(true));
                }))
            }


        })
        await Promise.allSettled(promise_list);
        return;

    }

    async getDesginMaterialsByMaterialIds(query_ids: { [key: string]: I_DesignMaterialInfo }) {
        let material_ids: string[] = [];
        for (let key in query_ids) {
            if (this._designMaterialDict[key]) {
                query_ids[key] = this._designMaterialDict[key];
            }
            else {
                if (!material_ids.includes(key)) {
                    material_ids.push(key);

                }
            }
        }

        if (material_ids.length > 0) {
            let results = await MaterialService.getDesignMaterialInfoByIds(material_ids);
            results.forEach((info) => {
                this._designMaterialDict[info.MaterialId] = info;
            })
        };
        for (let key in query_ids) {
            if (this._designMaterialDict[key]) {
                query_ids[key] = this._designMaterialDict[key];
            }
        }

        return query_ids;

    }
}