import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        houseMatch: css`
            width: 100%;
            height: 100%;
            background-color: #fff;
            border-radius: 12px;
            padding: 12px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            align-self: stretch;
            flex: 1 0 0;
        `,
        tagListBox: css`
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            .choose_tag_row {
                display: grid;
                grid-template-columns: 28px 1fr;
                gap: 24px;
                .taglabel {
                    width: 28px;
                    height: 24px;
                    color: #2E3238;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 171.429% */
                }
                .tagbtns {
                    display: flex;
                    align-items: center;
                    gap: 4px 16px;
                    flex-wrap: wrap;
                    .tag {
                        display: flex;
                        height: 24px;
                        box-sizing: border-box;
                        padding: 2px 4px;
                        justify-content: center;
                        align-items: center;
                        border-radius: 4px;
                        border: 1px solid transparent;
                        background: #F2F3F4;

                        color: #282828;
                        text-align: center;
                        font-family: "PingFang SC";
                        font-size: 13px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 169.231% */
                        &.checked {
                            border-radius: 4px;
                            border: 1px solid #BA63F0;
                            background: linear-gradient(90deg, #EDE5FF 0%, #EAF0FF 100%);
                            color: #5C42FB;
                        }
                    }
                }
            }
        `,
        result_container: css`
            width: 100%;
            height: 100%;
            overflow: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            &::-webkit-scrollbar {
                display: none; /* Chrome, Safari and Opera */
            }
        `,
        resultprogress: css`
            position:relative;
            width: 100%;
            height: 100%;
            overflow: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            &::-webkit-scrollbar {
                display: none; /* Chrome, Safari and Opera */
            }
        `,
        houseMatch_skeleton: css`
            position:absolute;
            top:0;
            left:0;
            width:100%;
            height:100%;
            background: #fff;
            overflow:hidden;
            z-index: 9;

            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            justify-items: start;
            align-content: start;
            .Skeleton_content {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                position: relative;
                
                .ant-skeleton {
                    width: 100% !important;
                    height: auto !important;
                    
                    .ant-skeleton-button {
                        width: 100% !important;
                        height: 100% !important;
                    }
                    
                    &:nth-child(1) {
                        height: 134px !important;
                    }
                    &:nth-child(2) {
                        height: 22px !important;
                        margin-top: 4px !important;
                    }
                    &:nth-child(3) {
                        height: 16px !important;
                        margin-top: 4px !important;
                        margin-bottom: 4px !important;
                    }
                }
            }
        `,
        result_list: css`
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            justify-items: start;
            align-content: start;
            .result_list_item {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                position: relative;
                cursor: pointer;
                min-width: 0;
                width: 100%;
                
                .hximg {
                    position: absolute;
                    top: 4px;
                    left: 4px;
                    width: 80px;
                    height: 60px;
                    z-index: 2;
                    .img_element {
                        position: relative;
                        width: 80px;
                        height: 60px;
                        border-radius: 4px;
                        box-sizing: border-box;
                        .layout2d_img {
                            position: absolute;
                            top: 0;
                            left: 0;
                            height: 52px;
                            object-fit: contain;
                            object-position: left;
                            border-radius: 2px;
                        }
                    }
                }
                .result_img {
                    width: 100%;
                    height: 133.875px;
                    flex-shrink: 0;
                    align-self: stretch;
                    border-radius: 4px;
                    object-fit: cover;
                }
                .result_info {
                    width: 100%;
                    padding: 8px 0;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    .result_title {
                        color: #2E3238;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 20px;
                        margin-bottom: 4px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 100%;
                    }
                    .result_tags {
                        height: 22px;
                        width: auto;
                        padding: 0 4px;
                        border-radius: 4px;
                        border: 1px solid #EAEAEB;
                        background: linear-gradient(90deg, #FAFAFA 0%, #EAEAEB 100%);
                        display: flex;
                        justify-content: flex-start;
                        align-self: flex-start;
                        margin-right: auto;
                        &.goodFit {
                            border: 1px solid #D9F7BE;
                            background: linear-gradient(90deg, #F6FFED 0%, #D9F7BE 100%);
                        }
                        .bestFit, .defaultFit {
                            padding: 0 4px;
                            border-radius: 4px;
                            font-family: "PingFang SC";
                            font-size: 12px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 20px;
                            white-space: nowrap;
                        }
                        .bestFit {
                            color: #52C41A;
                        }
                        .defaultFit {
                            color: #5B5E60;
                        }
                    }
                }
            }
        `,
        emptymatch: css`
            height: 100%;
            .emptybox {
                display: flex;
                flex-direction: column;
                gap: 10px;
                align-items: center;
                justify-content: center;
                height: 100%;
                img {
                    width: 70px;
                    height: 70px;
                }
                .name {
                    color: #282828;
                    font-family: "PingFang SC";
                    font-size: 13px;
                }
            }
        `,
    }
});