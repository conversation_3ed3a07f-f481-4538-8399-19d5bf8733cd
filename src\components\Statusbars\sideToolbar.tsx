import React, { useEffect, useState } from 'react';
import IconFont from '@/components/IconFont/iconFont'; // 确保导入 IconFont 组件
import useStyles from './style/index'; // 导入样式
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/models';
import { AI2DesignBasicModes, AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { Vector3 } from "three";
import { QuoteService } from '@/Apps/AI2Design/Services/QuoteService';
import { TSeriesSample } from '@/Apps/LayoutAI/Layout/TSeriesSample';
import { hotelQuote } from '@/services/design/index';
import { aiDrawingTukuBtnId } from '../ImageGallery/aiDrawingGallery';
import { Badge, message, Modal, Slider, Tooltip } from '@svg/antd';
import DisplayCheckBoxes from './displayCheckBoxes';
import { LayoutContainerUtils } from '@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils';
import { useNavigate } from 'react-router-dom';
import { TSeriesFurnisher } from '@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher';
import { DrawingFigureMode } from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { OutlineMode, SceneLightMode } from '@/Apps/LayoutAI/Scene3D/SceneMode';
// import { GuideMapInterationData } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TGuideMapLayout";
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { TViewCameraEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity';
import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
import { Scene3D } from '@/Apps/LayoutAI/Scene3D/Scene3D';
import Sharebar from '../Sharebar/sharebar';
import { is_dreamer_mini_App } from '@/config';
import { If } from 'react-if';
import { LightRuleService } from "@/Apps/LayoutAI/Scene3D/light/rule/LightRuleService";
import { AutoLightingService } from '@/Apps/LayoutAI/Services/AutoLighting/AutoLightService';

interface IconButtonProps {
    iconType: string; // 图标类型
    onClick: () => void; // 点击事件
    className?: string; // 可选的自定义类名
    name: string; // 名称
    isHidden?: boolean,
    isChecked?: boolean,
    id?: string,
    label?: string,
    divider?: boolean,
}

const RadioButton:{lable:string, radioMode:number}[] = [
    {
        lable: "4:3",
        radioMode: 1,
    },
    {
        lable: "16:9",
        radioMode: 2,
    },
    {
        lable: "3:4",
        radioMode: 3,
    },
    {
        lable: "9:16",
        radioMode: 4,
    }
]
const cameraStateList = [
    {
        id: 'texie',
        label: '特写',
    },
    {
        label: '人眼',
        id: 'renyan',
    },
    {
        label: '标准',
        id: 'biaozhun',
    },
    {
        label: '广角',
        id: 'guangjiao',
    }
]

const SideToolbar: React.FC<any> = ({setSceneMode}) => {
    const { styles } = useStyles();
    const store = useStore();
    const { 
        setShowDreamerPopup, 
        setIsdrawPicture,
    } = store.homeStore;
    let scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
    const container = (LayoutAI_App.instance as AI2DesignManager).layout_container;
    const [messageApi, contextHolder] = message.useMessage()
    const t = LayoutAI_App.t;
    // 当前选中的图标类型
    const [activeIconType, setActiveIconType] = useState<string | null>(null);
    const navigate = useNavigate();
    const [isShowShareBar, setIsShowShareBar] = useState(false);
    let initList: IconButtonProps[] = [
        {
            iconType: 'icon-chexiao',
            onClick: () => {
                LayoutAI_App.RunCommand(LayoutAI_Commands.Undo)
            },
            name: t('撤销'),
            isHidden: store.homeStore.viewMode !== "2D"
        },
        {
            iconType: 'icon-huifu',
            onClick: () => {
                LayoutAI_App.RunCommand(LayoutAI_Commands.Redo)
            },
            name: t('恢复'),
            isHidden: store.homeStore.viewMode !== "2D",
            divider: true
        },
        {
            iconType: 'icon-change_logo',
            onClick: async () => {
                if(store.homeStore.viewMode == "3D") {
                    setSceneMode("3D_FirstPerson");
                }
                else {
                    setSceneMode("3D");
                }
            },
            name: store.homeStore.viewMode == "3D" ? t('漫游') : t('鸟瞰'),
            isHidden: store.homeStore.viewMode == "2D" || (store.homeStore.isdrawPicture && !store.userStore.isHaiEr), 
        },
        {
            iconType: 'icon-save',
            onClick: () => {
                if (container._room_entities.length == 0) {
                    message.error(t('当前方案为空，无法保存！'));
                } else {
                    if (container._layout_scheme_id == null) {
                        store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'topMenu'});
                    } else {
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
                    }
                }
            },
            name: t('保存'),
            isHidden: store.homeStore.isdrawPicture && !store.userStore.isHaiEr,
        },
        // {
        //     iconType: 'iconfile',
        //     onClick: () => {
        //         // setIsShowMySchemeList(true);
        //         LayoutAI_App.emit(EventName.OpenHouseSearching, true);
        //     },
        //     name: t('新建'),
        //     isHidden: store.homeStore.isdrawPicture,
        // },
        // {
        //     iconType: 'icon-tuku',
        //     onClick: () => {
        //         if (container._room_entities.length == 0) {
        //             message.warning(t('请先创建方案'));
        //         } else {
        //             // LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
        //             store.homeStore.setShowAtlas(true);
        //             store.homeStore.setAtlasMode('aidraw');
        //         }
        //         return;
        //     },
        //     name: t('图册1'),
        //     id: aiDrawingTukuBtnId,
        //     isHidden: !store.homeStore.isdrawPicture || store.homeStore.drawPictureMode === "render", /* [i18n:ignore] */
        // },
        {
            iconType: 'icon-tuku',
            onClick: async () => {
                await store.homeStore.query_genCount()
                if (container._room_entities.length == 0) {
                    message.warning(t('请先创建方案'));
                } else {
                    // LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                    store.homeStore.setShowAtlas(true);
                    // store.homeStore.setAtlasMode('aidraw');
                }
                return;
            },
            name: t('图册'),
            id: 'renderingTukuBtnId',
            isHidden: store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode,
        },
        {
            iconType: 'icon-xiangjishezhi',
            onClick: () => {
                store.homeStore.setShowSubmitInfo(true);
            },
            name: t('相机'),
            // 2D场景置为true，短路后面的判断；出图模式和针对海尔的情况【不隐藏】
            isHidden: store.homeStore.viewMode === "2D" || !(store.homeStore.isdrawPicture || store.userStore.isHaiEr),
            isChecked: true
        },
        {
            iconType: 'icon-lishibanben',
            onClick: () => {
                LayoutAI_App.emit(EventName.setMultiSchemeListVisible, true);
            },
            name: t('多方案'),
            isHidden: true || store.homeStore.viewMode !== "2D"
        },
        // {
        //     iconType: 'iconempty',
        //     onClick: () => {
        //         let text = t('布局');
        //         if (container.drawing_figure_mode === DrawingFigureMode.Texture) {
        //             text = t("风格");
        //         }
        //         confirm({
        //             title: t('清空') + text,
        //             content: t(`您将清空【全屋】${text}，此操作不可恢复。是否确认清空？`),
        //             okText: t('取消'),
        //             cancelText: t('确认清空'),
        //             onOk() { },
        //             onCancel() {
        //                 if (container.drawing_figure_mode === DrawingFigureMode.Figure2D) {
        //                     LayoutAI_App.RunCommand(LayoutAI_Commands.Empty);
        //                 }
        //                 else {
        //                     TSeriesFurnisher.instance.deleteSeriesSample();
        //                     LayoutAI_App.instance.update();
        //                 }
        //             },
        //         });
        //     },
        //     name: t('清空布局'),
        //     isHidden: store.homeStore.viewMode !== "2D"
        // },
        {
            iconType: 'icondisplay',
            onClick: () => {
                // 不再直接设置isShowMoreBtns和isShowDisplayCheckBoxes
                // 而是通过activeIconType来控制
            },
            name: t('显隐'),
            isHidden: store.homeStore.viewMode !== "2D"
        },
        {
            iconType: 'iconhuizhong',
            onClick: () => {

                if (store.homeStore.viewMode === '2D') {
                    if (window.innerWidth < window.innerHeight * 0.8) {
                        LayoutContainerUtils.focusCenterByWholeBox(container, 0.7);
                    }
                    else {
                        LayoutContainerUtils.focusCenterByWholeBox(container, 0.5);
                    }
                    LayoutAI_App.instance.update();
                    // LayoutAI_App.DispatchEvent(LayoutAI_Events.setFocus, { focus: 100 });
                }
                else {
                    container.updateWholeBox();
                    let center = container._whole_bbox.getCenter(new Vector3());
                    scene3D.setCenter(center);
                }
            },
            name: t('居中'),
            isHidden: store.homeStore.viewMode !== '2D'
        },
        {
            iconType: 'icon-baojia',
            onClick: async () => {
                // LayoutAI_App.emit(EventName.setMultiSchemeListVisible, true);
                let params = {};
                let manager = (LayoutAI_App.instance) as AI2DesignManager;
                let quoteService = QuoteService.instance;
                let series: any = {
                    seriesKgId: null,
                    ruleId: null,
                    seedSchemeId: null,

                    ruleName: null,
                    seedSchemeName: null,
                    roomName: null,
                    seriesName: null,

                    status: null,
                    thumbnail: null,
                    roomList: null,
                    ruleImageList: null,

                    layoutTemplates: null,
                }
                let quoteData = quoteService.makeQuoteData(manager.layout_container, new TSeriesSample(series));
                if (quoteData) {
                    params = {
                        data: JSON.stringify(quoteData),
                    }
                    const res = await hotelQuote(params);
                    if (res.success) {
                        message.success(t('报价成功'));
                        const link = document.createElement('a');
                        link.href = res.data;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }

                }
            },
            name: t('装修'),
            isHidden: true
        },
        {
            iconType: 'icon-baojia',
            onClick: async () => {
                store.homeStore.setShowCabinetCompute(true);
            },
            name: t('算量'),
            isHidden: store.homeStore.viewMode !== "2D" || store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode, 
        },
        {
            iconType: 'icon-search',
            onClick: async () => {
                setShowDreamerPopup(true);
            },
            name: t('找相似'),
            isHidden: store.homeStore.viewMode !== "2D" || !is_dreamer_mini_App, 
        },
        // {
        //     iconType: scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline ? 'iconShowoutline_Nor' : 'iconShowmaterial_Nor',
        //     onClick: () => {
        //         let t = scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline;
        //         if (t) {
        //             scene3D.outlineMaterialMode = OutlineMode.MaterialOnly;
        //         }
        //         else {
        //             scene3D.outlineMaterialMode = OutlineMode.WhiteModelOutline;
        //         }
        //     },
        //     name: t('轮廓'),
        //     isHidden: store.homeStore.viewMode === "2D",
        //     isChecked: true
        // },
        {
            iconType: scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline ?  'iconShowmaterial_Nor' :'iconShowoutline_Nor' ,
            name: scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline ? t('材质') : t('轮廓'),
            onClick: () => {
                let a = scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline;
                if (a) {
                    scene3D.outlineMaterialMode = OutlineMode.MaterialOnly;
                    setList(prevList =>
                        prevList.map(item =>
                            item.name === LayoutAI_App.t('材质')
                                ? { ...item, name: LayoutAI_App.t('轮廓'), iconType: 'iconShowoutline_Nor' }
                                : item
                        )
                    );
                } else {
                    scene3D.outlineMaterialMode = OutlineMode.WhiteModelOutline;
                    setList(prevList =>
                        prevList.map(item =>
                            item.name === LayoutAI_App.t('轮廓')
                                ? { ...item, name: LayoutAI_App.t('材质'), iconType: 'iconShowmaterial_Nor' }
                                : item
                        )
                    );
                }
                messageApi.open({
                    type: null,
                    content: scene3D?.outlineMaterialMode == OutlineMode.WhiteModelOutline ? t('显示轮廓') : t('显示材质'),
                    className: 'custom-class',
                })
            },
            isHidden: store.homeStore.viewMode === "2D",
            isChecked: true
        },
        {
            iconType: LayoutAI_App.instance.Configs.isClickDrawPic ? 'icon-chanpinzhiru' : 'icon-maikefeng1',
            onClick: () => {
                LayoutAI_App.instance.Configs.isClickDrawPic = !LayoutAI_App.instance.Configs.isClickDrawPic;
                LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
                LayoutAI_App.instance.scene3D.setSelectionBox(null);              
                messageApi.open({
                    type: null,
                    content: LayoutAI_App.instance.Configs.isClickDrawPic ? t('进入演讲模式') : t('进入换搭模式'),
                    className: 'custom-class',
                })
            },
            name: LayoutAI_App.instance.Configs.isClickDrawPic ? t('换搭') : t('演讲'),
            isHidden: store.homeStore.viewMode !== "3D_FirstPerson" || (store.homeStore.isdrawPicture && !store.userStore.isHaiEr),
        },
        {
            iconType: 'icon-Frame',
            onClick: () => {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
                setIsdrawPicture(true);
                LayoutAI_App.instance.Configs.isClickDrawPic = true;
                scene3D.raycasteControls.onSelectedFigure(null);
            },
            name: t('出图'),
            isHidden: store.userStore.isHaiEr || store.homeStore.viewMode !== "3D_FirstPerson" || store.homeStore.isdrawPicture,
            isChecked: true
        },
        {
            iconType: 'icon-fenxiang',
            onClick: async () => {
                setIsShowShareBar(!isShowShareBar);
            },
            name: t('分享'),
            isHidden: (store.homeStore.isdrawPicture && !store.userStore.isHaiEr) || store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode, 
        },
        {
            iconType: 'iconmore',
            onClick: () => {
                // 不再直接设置isShowMoreBtns和isShowDisplayCheckBoxes
                // 而是通过activeIconType来控制
            },
            name: null,
            isHidden: store.homeStore.viewMode !== '2D',
            isChecked: true
        },
        {
            iconType: 'icon-icon',
            onClick: () => {
                setIsdrawPicture(false);
                LayoutAI_App.instance.Configs.isClickDrawPic = false;
                LayoutAI_App.instance.renderSubmitObject = {
                    drawPictureMode: null,
                    radioMode: 0,
                    resolution: 0
                };
                scene3D.setLightMode(SceneLightMode.Day);
                AutoLightingService.instance.cleanLighting();
                LayoutAI_App.instance.scene3D.setLightGroupVisible(false, false, false);
                LightRuleService.cleanLight();  //清除灯光效果
                // store.homeStore.setDrawPictureMode("aiDrawing");
            },
            name: t('取消'),
            isHidden: !store.homeStore.isdrawPicture || store.userStore.isHaiEr,
            isChecked: true
        },
    ];


    const [list, setList] = useState<IconButtonProps[]>();
    useEffect(() => {
        LayoutAI_App.on_M(EventName.Scene3DUpdated, 'Statusbars', () => {
            let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            scene3D = LayoutAI_App.instance.scene3D as Scene3D;
        });
    }, [])

    // 点击外部关闭菜单
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;

            const isClickInsideToolbar = target.closest('#ipad-sideToolbar') !== null;

            if (!isClickInsideToolbar) {
                setActiveIconType(null);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleIconClick = (iconType: string, action: () => void) => {
        if (activeIconType === iconType) {
            setActiveIconType(null);
        } else {
            setActiveIconType(iconType);
        }
        action();
    };

    let moreList: IconButtonProps[] = [
        {
            iconType: 'iconfile',
            onClick: () => {
                // setIsShowMySchemeList(true);
                store.homeStore.setShowEnterPage({show: true, source: 'sideToolbar'});
            },
            name: t('新建'),
            isHidden: store.userStore.isHaiEr
        },
        {
            iconType: 'iconbuzhisucai',
            onClick: () => {
                store.homeStore.setShowMySchemeList(true);
            },
            name: t('方案')
        },
        {
            iconType: 'icona-zaoxingbianjishapeediting',
            onClick: () => {
                LayoutAI_App.instance._current_handler_mode = AI2DesignBasicModes.HouseDesignMode;
                LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
                store.homeStore.setDesignMode(AI2DesignBasicModes.HouseDesignMode);
            },
            name: t('户型编辑'),
            isHidden:  store.userStore.isHaiEr ||store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode 
            && store.homeStore.isSingleRoom
        }
    ];
    moreList = moreList.filter(item => item && !item.isHidden);

    useEffect(() => {
        setList(initList.filter(item => item !== null && !item.isHidden));
        console.log('store.homeStore.viewMode', store.homeStore.viewMode);
        
    },[store.homeStore.viewMode, store.homeStore.drawPictureMode, store.homeStore.isdrawPicture,LayoutAI_App.instance.Configs.isClickDrawPic])


    const is2DMode = store.homeStore.viewMode === "2D";

    const [genCount, setGenCount] = useState(0)
    useEffect(() => {
        setGenCount(store.homeStore.genCount)
    }, [store.homeStore.genCount])

    return (
        <div id="ipad-sideToolbar">
            <div className={`${styles.container} ${store.homeStore.viewMode !== "2D" ? styles.blackColor : ''}`}>

                {list && list.map((item, index) => (
                    <div
                        key={index}
                        className={`${styles.iconButton} ${(activeIconType === item.iconType) ? (item.isChecked ? "checked" : "notChecked") : ""}`}
                        onClick={() => handleIconClick(item.iconType, item.onClick)}
                        id={item.id}
                    >
                        {item.name === "图册" ? (
                            <Badge count={genCount === null? "?" : genCount} size={'small'}>
                                <svg 
                                    className="icon" 
                                    aria-hidden="true" 
                                    style={{width: '20px', height: '20px'}}
                                    fill={store.homeStore.viewMode !== "2D" ? '#fff' : '#595959'}>
                                    <use xlinkHref={`#${item.iconType}`}></use>
                                </svg>
                            </Badge>
                        ) : (
                            <svg 
                                className="icon" 
                                aria-hidden="true" 
                                style={{width: '20px', height: '20px'}}
                                fill={store.homeStore.viewMode !== "2D" ? '#fff' : '#595959'}>
                                <use xlinkHref={`#${item.iconType}`}></use>
                            </svg>
                        )}
                        <If condition={item.name}>
                            <div className={'iconButtonText'}>{item.name}</div>
                        </If>
                        <If condition={item.divider}>
                            <span className={'divider'}></span>
                        </If>
                    </div>
                ))}
                {activeIconType === 'iconmore' && <div className={styles.morebtns_container}>
                {moreList.map((item, index) => (
                    <div
                        key={index}
                        className={styles.iconButton}
                        onClick={() => {
                            setActiveIconType(null);
                            item.onClick();
                        }}
                        id={item.id}
                    >
                        <svg 
                            className="icon" 
                            aria-hidden="true" 
                            style={{width: '20px', height: '20px'}}
                            fill={store.homeStore.viewMode !== "2D" ? '#fff' : '#595959'}>
                            <use xlinkHref={`#${item.iconType}`}></use>
                        </svg>
                        <div className={'iconButtonText'}>{item.name}</div>
                        <If condition={item.divider}>
                            <span className={'divider'}></span>
                        </If>
                    </div>
                ))}
            </div>}
            </div>
            {<DisplayCheckBoxes isVisible={activeIconType === 'icondisplay' && is2DMode}></DisplayCheckBoxes>}

            {isShowShareBar && <div className="" style={{ 
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: "999", 
                background: "#fff", 
                padding: "10px",
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                borderRadius: '4px'
            }}>
                <Sharebar onClose={() => {
                    setIsShowShareBar(false);
                }}></Sharebar>
            </div>}

            {contextHolder}
        </div>
    );
};

export default observer(SideToolbar);