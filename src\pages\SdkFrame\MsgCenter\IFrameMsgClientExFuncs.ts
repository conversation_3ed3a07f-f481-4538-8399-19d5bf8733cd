import { IFrameMsgClient } from "./IFrameMsgClient";
import * as IRoomInterface from "../Data/IRoomInterface";
import * as SwjLayoutData from "../Data/SwjLayoutData";
import * as IMsgType from "./IMsgType";


// 脚本自动生成，请勿手动修改
export var IFrameMsgClientExFuncs = {
  /**
   * @description 纯纯测试用
   * @param input.word
   * @returns
   */
  testA: async (input: { word: string }): Promise<string> => {
    return await IFrameMsgClient.Client.asyncCall("testA", input);
  },//[End:Async]

  /**
   * 重绘画布---一般其他函数都会自动重绘
   * --- 如果没有重绘就画以下
   * --- 一般都在100ms
   * @param input
   */
  updateCanvas: async (input: {} = {}) => {
    return await IFrameMsgClient.Client.asyncCall("updateCanvas", input);
  },//[End:Async]
  /**
   * @description 通过layoutschemeId载入布局方案
   * @param input.scheme_id
   * @returns
   */
  loadByLayoutSchemeId: async (input: { scheme_id: string }) => {
    return await IFrameMsgClient.Client.asyncCall("loadByLayoutSchemeId", input);
  },//[End:Async]

  /**
   * @description 通过三维家3D方案Id载入布局方案
   * @param input.scheme_id
   * @returns
   */
  loadBySwjSchemeId: async (input: { scheme_id: string }): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("loadBySwjSchemeId", input);
  },//[End:Async]

  /**
   * 根据户型ID,打开户型方案
   * @param input.building_id
   * @returns
   */
  loadByBuidlingId: async (input: { building_id: string }): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("loadByBuidlingId", input);
  },//[End:Async]

  /**
   *
   * @param input.building_id 户型Id
   * @param input.access_token 开发者访问的access_token
   * @returns
   */
  loadByOpenBuildingId: async (input: {
    building_id: string;
    access_token: string;
  }): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("loadByOpenBuildingId", input);
  },//[End:Async]
  /**
   * 根据SchemeXmlUrl 打开方案
   * @param input.xml_url
   * @returns
   */
  loadSchemeXmlUrl: async (input: { xml_url: string }): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("loadSchemeXmlUrl", input);
  },//[End:Async]

  /**
   * 根据Base64字符串 打开方案
   * @param input
   * @returns
   */
  loadSchemeXmlBase64: async (input: { base64text: string }) => {
    return await IFrameMsgClient.Client.asyncCall("loadSchemeXmlBase64", input);
  },//[End:Async]

  /**
   * 直接读方案json
   * @param input
   * @returns
   */
  loadSchemeXmlJson: async (input: { scheme: SwjLayoutData.I_SwjXmlScheme }) => {
    return await IFrameMsgClient.Client.asyncCall("loadSchemeXmlJson", input);
  },//[End:Async]
  /**
   * 获得所有房间信息
   * @param input
   */
  getAllRoomInfos: async (input: {} = {}): Promise<IRoomInterface.I_Room[]> => {
    return await IFrameMsgClient.Client.asyncCall("getAllRoomInfos", input);
  },//[End:Async]

  /**
   * 对不同的房间应用Ai布局
   * @param input.filteredRoomUids  基于uid过滤房间
   * @param input.filterRoomUuids   基于uuid过滤房间
   * @param input.filteredRoomNames   基于房间名称过滤房间
   * @param input.append_furniture_entites 是否自动应用最高分的布局
   * @param input.solver_methods 不同的自动推荐的算法
   * @param input.output_details 是否输出布局细节信息
   * @returns
   */
  applyAiLayoutInRooms: async (
    input: {
      filteredRoomUids?: string[];
      filterRoomUuids?: string[];
      filteredRoomNames?: string[];
      solver_methods?: IRoomInterface.SolverMethods[];
      append_furniture_entites?: boolean;
      output_details?: boolean;
    } = {}
  ): Promise<IRoomInterface.I_RoomLayoutSchemeResult[]> => {
    return await IFrameMsgClient.Client.asyncCall("applyAiLayoutInRooms", input);
  },//[End:Async]

  /**
   * 对不同的子空间应用Ai布局
   */
  applyAiLayoutInSubspaces: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
    filterSubSpaceNames?: string[];
    filterSubSpaceUuids?: string[];
    queryTemplates?: boolean;
    solver_methods?: IRoomInterface.SolverMethods[];
    append_furniture_entites?: boolean;
    output_details?: boolean;
  }) => {
    return await IFrameMsgClient.Client.asyncCall("applyAiLayoutInSubspaces", input);
  },//[End:Async]

  /**
   * 获得房间的分区---直接是吊顶分区
   * @param input
   */
  getCeilingAreasInRoom: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
  }): Promise<IRoomInterface.I_RoomSubAreasResult[]> => {
    return await IFrameMsgClient.Client.asyncCall("getCeilingAreasInRoom", input);
  },//[End:Async]

  /**
   * 修改分区, 要用uuid来指定分区来修改
   * @param input
   * @returns 0: 没找到对应的吊顶
   */
  editCeilingAreaInRoom: async (input: IRoomInterface.I_RoomSubAreaSimpleData): Promise<IRoomInterface.I_RoomSubAreaSimpleData> => {
    return await IFrameMsgClient.Client.asyncCall("editCeilingAreaInRoom", input);
  },//[End:Async]

  /**
   *  清空每个空间的筒灯
   */
  clearDownLightsInRoom: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
  }) => {
    return await IFrameMsgClient.Client.asyncCall("clearDownLightsInRoom", input);
  },//[End:Async]

  /**
   *  @description 添加筒灯列表: 会按位置自动添加到对应的空间
   *  @param input.down_lights 要添加灯具的列表
   *  @param input.not_clean_down_lights  默认为false,即默认会清空现有筒灯;如果true, 则不会
   *  @param input.add_lights_to_room  默认为false, 即默认不会添加筒灯到房间中
   *  @returns 返回添加的筒灯的uuid列表
   */
  addDownLights: async (input: {
    down_lights: IRoomInterface.I_SimpleFigureElement[];
    not_clean_down_lights?: boolean;
    add_lights_to_room?: boolean;
  }): Promise<string[]> => {
    return await IFrameMsgClient.Client.asyncCall("addDownLights", input);
  },//[End:Async]

  /**
   *  @description 更新筒灯的颜色和亮度
   *  @param input.uuid 筒灯的uuid
   *  @param input.color 颜色
   *  @param input.brightness 亮度
   */
  updateDownLight: async (input: {
    uuid: string;
    color?: number;
    brightness?: number;
  }) => {
    return await IFrameMsgClient.Client.asyncCall("updateDownLight", input);
  },//[End:Async]

  /**
   *  @description 自动灯光
   *  @param input.isClean 是否清空现有灯光
   *  @param input.brightness 亮度
   *  @param input.color 颜色
   *  @returns 返回自动灯光实体的uuid列表
   * */
  autoLighting: async (input: {
    isClean?: boolean;
    config?: any;
  }): Promise<string[]> => {
    return await IFrameMsgClient.Client.asyncCall("autoLighting", input);
  },//[End:Async]


  /**
   *  @param input.ruleType 1：平台套系,  2:企业套系
   *  获得风格套系列表
   */
  getStyleSeriesList: async (
    input: {
      ruleType?: number;
      pageIndex?: number;
      pageSize?: number;
      orderBy?: string;
      schemeKeyWord?: string;
      ruleKeyWord?: string;
      spaceName?: string[];
      schemeStyleId?: string;
      ruleStyleId?: string;
      queryType?: number;
    } = {}
  ): Promise<IRoomInterface.I_StyleSeriesItem[]> => {
    return await IFrameMsgClient.Client.asyncCall("getStyleSeriesList", input);
  },//[End:Async]

  /**
   * 对不同的房间应用风格套系
   * @param input.seriesKgId  套系KgId
   * @param input.filteredRoomUids  基于uid过滤房间
   * @param input.filterRoomUuids   基于uuid过滤房间
   * @param input.filteredRoomNames   基于房间名称过滤房间
   * @param input.needsDiversityMatched  是否每次匹配的结果随机
   * @returns
   */
  applyStyleSeriesInRooms: async (
    input: {
      seriesKgId?: string;
      filteredRoomUids?: string[];
      filterRoomUuids?: string[];
      filteredRoomNames?: string[];
      needsDiversityMatched?: boolean;
      needsUpdateScene3d?: boolean;
    } = {}
  ): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("applyStyleSeriesInRooms", input);
  },//[End:Async]

  /**
   *  设置2D绘制样式
   */
  setDrawing2DMode: async (input: { mode: IRoomInterface.DrawingFigureMode }): Promise<void> => {
    return await IFrameMsgClient.Client.asyncCall("setDrawing2DMode", input);
  },//[End:Async]

  /**
   * 显示不同的图层
   * @param input
   */
  showDrawingLayer: async (input: {
    layers: { layerName: IRoomInterface.DrawingLayerNames; visible: boolean }[];
  }): Promise<void> => {
    return await IFrameMsgClient.Client.asyncCall("showDrawingLayer", input);
  },//[End:Async]

  /**
   * @description 基于整体包围盒，2D画布场景居中
   * @param input.p_scale 直接设置scale
   * @param input.fixed_scale 相对于屏幕长宽的放缩比 如果是1.0， 那么整体包围盒长边或宽边会贴边。
   */
  focusCenterByWholeBox: async (
    input: { fixed_scale: number; p_scale?: number } = { fixed_scale: 0.8 }
  ) => {
    return await IFrameMsgClient.Client.asyncCall("focusCenterByWholeBox", input);
  },//[End:Async]

  /**
   * 设置画布的放缩比
   * @param input
   */
  setCanvasTransform: async (input: { p_center: { x: number; y: number }; scale: number }) => {
    return await IFrameMsgClient.Client.asyncCall("setCanvasTransform", input);
  },//[End:Async]

  /**
   * @description 获得当前画布的图像base64---canvas.toDataUrl()
   * @param input.width  如果没有设置，则默认是当前显示画布的宽
   * @param input.height  如果没有设置height，默认跟width相同
   * @param input.p_scale 直接设置画布的p_scale --- 如果设置了p_scale, 则fixed_scale无效
   * @param input.fixed_scale, 新设置了长宽才会启用，默认值是0.8
   * @param input.posNum 对应buildingInfo的信息, 但好像没有用
   * @param input.scaleNum 对应buildingInfo的信息, 实际上用的是input.scaleNum.x/2的值, 即设置p_scale = scaleNum.x/2
   * @return
   */
  getCanvas2dImage: async (
    input: {
      width?: number;
      height?: number;
      fixed_scale?: number;
      p_scale?: number;
      posNum?: { x: number; y: number };
      scaleNum?: { x: number; y: number };
    } = {}
  ): Promise<IRoomInterface.I_CanvasImageResult> => {
    return await IFrameMsgClient.Client.asyncCall("getCanvas2dImage", input);
  },//[End:Async]

  /**
   * 初始化3D场景, 默认是没有3D的，需要初始化
   * @param input
   */
  initScene3D: async (input: {} = {}): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("initScene3D", input);
  },//[End:Async]
  /**
   * 去掉3D场景
   * @param input
   */
  removeScene3D: async (input: {} = {}): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("removeScene3D", input);
  },//[End:Async]

  /**
   *
   * @param input.sceneMode
   * @param input.isHidden3D --- 是否显示3D-如果是true 则在后台执行(实际上是zIndex=-2)
   * @returns  1: 正常改变;  0: 如果3D没有初始化就设置3D模式
   *   *
   */
  changeSceneMode: async (input: {
    sceneMode: IRoomInterface.SceneViewMode;
    isHidden3D?: boolean;
  }): Promise<number> => {
    return await IFrameMsgClient.Client.asyncCall("changeSceneMode", input);
  },//[End:Async]

  /**
   *
   * @param input.mode - 0: 白模模式 1：线框模式 2：材质模式 3：材质-线框模式
   */
  changeMaterialMode: async (input: { mode: number }) => {
    return await IFrameMsgClient.Client.asyncCall("changeMaterialMode", input);
  },//[End:Async]

  /**
   * 鸟瞰模式的居中
   * @param input 
   */
  focusCenter3d: async (input: { fixed_scale?: number; }) => {
    return await IFrameMsgClient.Client.asyncCall("focusCenter3d", input);
  },//[End:Async]
  /**
   * 获得当前3D画布的图像
   * @param input  
   * @returns 
   */
  getCanvas3dImage: async (input: {}) => {
    return await IFrameMsgClient.Client.asyncCall("getCanvas3dImage", input);
  },//[End:Async]

  /**
   * @description 获得白模3D图像base64---canvas.toDataUrl()
   * @param input.width  如果没有设置，则默认是当前显示画布的宽
   * @param input.height  如果没有设置height，默认跟width相同
   */
  getWhiteModelCanvas3dImage: async (input: {
    fixed_scale?: number;
    width?: number;
    height?: number;
  }): Promise<IRoomInterface.I_CanvasImageResult> => {
    return await IFrameMsgClient.Client.asyncCall("getWhiteModelCanvas3dImage", input);
  },//[End:Async]
  /**
   * 对不同的房间, 生成相机视角
   * @param input
   * @param input.method 0:默认方法-数量更多 1:调整后更精简的方法。 默认值为1
   * @param input.camera_zval : 默认不用设置(为1400), 为了看吊顶可以设置成1600
   */
  generateViewCameraList: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
    method?: number;
    camera_zval?: number;
  }): Promise<SwjLayoutData.I_SwjViewCameraData[]> => {
    return await IFrameMsgClient.Client.asyncCall("generateViewCameraList", input);
  },//[End:Async]
  /**
   * 获得视角的预览图像
   * @param input.uuid 指定的uuid
   * @param input.preview_type  "Plane2D"|"View3D"  <=> 平面2D  | 3D预览截图
   * @param input.img_width  图像的尺寸width - 暂时只影响Plane2D模式, 默认为300
   * @param input.img_height 图像的尺寸height
   * @param input.force 是否强制重新生成 --- 若不强制，会返回上次生成的结果
   * @returns  图像的Base64字符串 - 如果没有会返回 ""(空字符串)
   */
  getViewCameraImage: async (input: {
    uuid: string;
    preview_type: SwjLayoutData.ViewCameraImgPreviewType;
    img_width?: number;
    img_height?: number;
    force?: boolean;
  }): Promise<string> => {
    return await IFrameMsgClient.Client.asyncCall("getViewCameraImage", input);
  },//[End:Async]

  /**
   * 应用当前视角
   * @param input.uuid 视角的uuid
   * @param input.fov 视场角，单位角度，默认一般是 65
   * @param input.pitch 俯仰角,单位角度，默认一般是90度
   * 
   */
  applyCurrentViewCamera: async (input: { uuid: string, fov?: number, pitch?: number }) => {
    return await IFrameMsgClient.Client.asyncCall("applyCurrentViewCamera", input);
  },//[End:Async]


  /**
   * 获取当前视角参数
   * 
   */
  getCurrentViewCameraParams: async (input: {}) => {
    return await IFrameMsgClient.Client.asyncCall("getCurrentViewCameraParams", input);
  },//[End:Async]

  /**
   * 是否打开Panel
   * @param input.panelType : 0 无;  1: 有对应标准移动端的Panel
   */
  setUsePanelType: async (input: { panelType: number }) => {
    return await IFrameMsgClient.Client.asyncCall("setUsePanelType", input);
  },//[End:Async]

  /**
   * 离线渲染当前视角场景
   * @returns  {
   * success: boolean,  // 是否提交成功
   * msg: string,  // 提示信息，如提交离线渲染任务成功
   * schemeId: string  // 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * queueId: string,  // 离线渲染任务的队列ID，如 QO202504180000000000000842980652
   * }
   */
  offlineRenderScene: async (input: {}): Promise<{
    success: boolean;
    msg: string;
    queueId: string;
    schemeId: string;
  }> => {
    return await IFrameMsgClient.Client.asyncCall("offlineRenderScene", input);
  },//[End:Async]

  /**
   * 获取离线渲染任务的图册url，需要轮询，可能在排队中（success为true，url为空）
   * @param input.schemeId 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * @param input.queueId 离线渲染任务的队列ID，如 QO202504180000000000000842980652
   * @returns  {
   * success: boolean,  // 是否获取成功
   * msg: string,  // 提示信息，如 获取图片url成功
   * url: string  // 离线渲染任务的图册url，如 https://img3.admin.3vjia.com///UpFile_Render/C00000022/DesignSchemeRenderFile/68d1762afd5b4940b9dfe75e68c2e1e2/QO202504180000000000000842980652.jpg
   * }
   */
  getOfflineRenderUrl: async (input: {
    schemeId: string;
    queueId: string;
  }): Promise<{ success: boolean; msg: string; url: string }> => {
    return await IFrameMsgClient.Client.asyncCall("getOfflineRenderUrl", input);
  },//[End:Async]

  /**
   * 获取离线渲染任务的图册列表
   * @param input.schemeId 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * @param input.pageIndex 页码，默认1
   * @param input.pageSize 每页数量，默认10
   * @returns  {
   * success: boolean,  // 是否获取成功
   * msg: string,  // 提示信息，如 获取图册列表成功
   * data: {}[];
   * }
   */
  getOfflineRenderAtlasList: async (input: {
    schemeId: string;
    pageIndex?: number;
    pageSize?: number;
  }): Promise<{ success: boolean; msg: string; data: {}[] }> => {
    return await IFrameMsgClient.Client.asyncCall("getOfflineRenderAtlasList", input);
  },//[End:Async]

  /**
   * 获取AI绘图风格类型
   * @returns 风格类型列表
   * {
   *  text: string; // 风格类型名称
   *  value: string; // 风格类型值
   *  cover: string; // 风格类型封面
   * }[]
   */
  getAigcDesignStyles: async (input: {}): Promise<
    {
      text: string;
      value: string;
      cover: string;
    }[]
  > => {
    return await IFrameMsgClient.Client.asyncCall("getAigcDesignStyles", input);
  },//[End:Async]

  /**
   * 自动保存方案
   * @param input
   */
  autoSaveScheme: async (input: {}) => {
    return await IFrameMsgClient.Client.asyncCall("autoSaveScheme", input);
  },//[End:Async]

  /**
   * 提交绘图请求
   * @param input.roomUid 当前选中房间uid
   * @param input.imgUrl 视角的预览图像 url
   * @param input.imageNum 图片数量（1,2,4）
   * @param input.style 风格类型（现代,中式,北欧,奶油风,法式,混搭,轻奢,诧寂）
   * @returns 绘图结果 {
   *  success: boolean;
   *  msg: string;
   *  schemeId: string;
   *  atlasId: string;
   * }
   */
  submitAigcDraw: async (input: {
    roomUid: string;
    imgUrl: string;
    imageNum: number;
    style: string;
  }): Promise<{ success: boolean; msg: string; schemeId: string; atlasId: string }> => {
    return await IFrameMsgClient.Client.asyncCall("submitAigcDraw", input);
  },//[End:Async]

  /**
     * 查询绘图结果
     * @param input.atlasId 图册ID
     * @returns 绘图结果 {
        success: boolean;
        msg: string;
        data: AigcAtlasInfo
     */
  getAigcAtlasInfo: async (input: {
    atlasId: string;
  }): Promise<{ success: boolean; msg: string; data: IMsgType.AigcAtlasInfo }> => {
    return await IFrameMsgClient.Client.asyncCall("getAigcAtlasInfo", input);
  },//[End:Async]

  /**
   * 删除图册
   * @param input.atlasId 图册ID
   * @returns 删除结果 {
   *  success: boolean;
   *  msg: string;
   * }
   */
  deleteAigcAtlasInfo: async (input: {
    atlasId: string;
  }): Promise<{ success: boolean; msg: string }> => {
    return await IFrameMsgClient.Client.asyncCall("deleteAigcAtlasInfo", input);
  },//[End:Async]

  /**
   * 删除图册中的图片，只有一张图片时不允许删除，可以选择删除图册
   * @param input.imgId 图片ID
   * @returns 删除结果 {
   *  success: boolean;
   *  msg: string;
   * }
   */
  deleteAigcAtlasImage: async (input: {
    imgId: string;
  }): Promise<{ success: boolean; msg: string }> => {
    return await IFrameMsgClient.Client.asyncCall("deleteAigcAtlasImage", input);
  },//[End:Async]

  /**
   * 获取图册列表
   * @param input.schemeId 方案ID
   * @param input.pageIndex 页码，从1开始
   * @param input.pageSize 每页数量，默认10
   * @returns 图册列表 {
   *  success: boolean;
   *  msg: string;
   *  data: AigcAtlasListItem[];
   * }
   */
  getAigcAtlasList: async (input: {
    schemeId: string;
    pageIndex: number;
    pageSize: number;
  }): Promise<{
    success: boolean;
    msg: string;
    data: IMsgType.AigcAtlasListItem[];
  }> => {
    return await IFrameMsgClient.Client.asyncCall("getAigcAtlasList", input);
  },//[End:Async]


  /**
  * 设置场景灯光模式
  * @param input.lightMode 灯光模式
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setSceneLightMode: async (input: {
    lightMode: IMsgType.LightMode;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    return await IFrameMsgClient.Client.asyncCall("setSceneLightMode", input);
  },//[End:Async]

  /**
  * 设置灯光调试是否显示
  * @param input.showOfflineLight 是否显示光
  * @param input.showTestLight 是否显示补光板
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setDebugLightVisible: async (input: {
    showOfflineLight: boolean;
    showTestLight: boolean;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    return await IFrameMsgClient.Client.asyncCall("setDebugLightVisible", input);
  },//[End:Async]


  /**
  * 设置分辨率
  * @param input.resolutionTag 分辨率类型（SD,HD,FHD,4K,8K） 默认HD
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setResolution: async (input: {
    resolutionTag: string;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    return await IFrameMsgClient.Client.asyncCall("setResolution", input);
  },//[End:Async]

  /**
  * 更新吊顶类型
  * @param input.tp 吊顶类型
  * @param input.config 吊顶类型配置
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  updateCeilingLayerTemplate: async (input: {
    tp: string;
    config: IRoomInterface.I_CeilingLayerData;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    return await IFrameMsgClient.Client.asyncCall("updateCeilingLayerTemplate", input);
  },//[End:Async]

  /**
  * 获取高度超过指定高度的家具列表
  * @param input.roomUUID 房间UID，不传默认所有房间
  * @param input.height 高度，不传默认2500
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  *  data: IRoomInterface.I_FurnitureData[];
  * }
  */
  getFurnitureList: async (input: {
    roomUUID: string;
    height: number;
  }): Promise<IRoomInterface.I_SimpleFigureElement[]> => {
    return await IFrameMsgClient.Client.asyncCall("getFurnitureList", input);
  },//[End:Async]

  /**
  * 创建房间子区域
  * @param input.roomUUID 房间UID
  * @param input.spaceType 空间类型
  * @param input.center 中心点
  * @param input.w 宽度
  * @param input.h 高度
  * @param input.nor 法向量
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  createRoomSubArea: async (input: {
    roomUUID: string;
    spaceAreaType: IRoomInterface.IRoomSpaceAreaType;
    center: { x: number, y: number, z: number };
    w: number;
    h: number;
    nor: { x: number, y: number, z: number };
  }): Promise<{ success: boolean, msg: string }> => {
    return await IFrameMsgClient.Client.asyncCall("createRoomSubArea", input);
  },//[End:Async]

  /**
  * 更新房间子区域
  * @param input.roomUUID 房间UID
  * @param input.subAreaIndex 子区域索引
  * @param input.center 中心点
  * @param input.w 宽度
  * @param input.h 高度
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  updateRoomSubArea: async (input: {
    roomUUID: string;
    subAreaIndex: number;
    center?: { x: number, y: number, z: number };
    w?: number;
    h?: number;
  }): Promise<{ success: boolean, msg: string }> => {
    return await IFrameMsgClient.Client.asyncCall("updateRoomSubArea", input);
  },//[End:Async]

  /**
  * 更新房间子区域
  * @param input.roomUUID 房间UID
  * @param input.subAreaIndex 子区域索引
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  deleteRoomSubArea: async (input: {
    roomUUID: string;
    subAreaIndex: number;
  }): Promise<{ success: boolean, msg: string }> => {
    return await IFrameMsgClient.Client.asyncCall("deleteRoomSubArea", input);
  },//[End:Async]
};

(globalThis as any).IFrameMsgClientExFuncs = IFrameMsgClientExFuncs;