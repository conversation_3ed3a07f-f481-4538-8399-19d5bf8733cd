import { magiccubeRequest, openApiRequest } from '@/utils';

/**
 * @description 临摹图识别比例尺接口
 */
export async function getScalePoint(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `/sd-biz/api/building/roommodel/v1/scale/point`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}


/**
 * @description 获取AI个人工作台户型详情
 */
export async function getHouseScheme(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `api/njvr/layoutSchemeHouseType/get`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
;
  return res;
}

/**
 * @description 获取我的方案数据
 */
export async function getMyCase(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `api/njvr/LayoutScheme/listByPage`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
  return res;
}

/**
 * @description 根据id获取我的方案信息
 */
export async function getMyCaseById(params: any) {
  const res = await magiccubeRequest({
    method: 'post',
    url: `api/njvr/LayoutScheme/get`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any): null => {
    return null;
  });
  return res;
}