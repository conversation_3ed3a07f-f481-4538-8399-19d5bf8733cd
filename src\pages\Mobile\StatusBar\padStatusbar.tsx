import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Button, message, Modal, Select } from "@svg/antd";
import Icon from '@/components/Icon/icon'
import { LayoutPopEvents } from "../layoutPopup/layoutPopup";
import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { useStore } from '@/models';
import { AI_PolyTargetType, IRoomEntityType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import IconFont from "@/components/IconFont/iconFont";
import { useNavigate } from 'react-router-dom';
import { _from, checkIsMobile, mini_APP } from "@/config";
import { uploadImageToOss } from "@/Apps/LayoutAI/Utils/file_utils";
import { AIGCService } from "@/Apps/LayoutAI/Services/AIGC/AIGCService";
import { AILightService } from "@/Apps/LayoutAI/Scene3D/light/AILightService";
import AddGroupModal from "@/components/AddGroupModal/addGroupModal";
import { combinationSizeStorage, combinationStorage } from "@/services/addGroup";
import { TBaseGroupEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity";
import { LayoutSchemeJsonSaver } from "@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeJsonSaver";
import { StringUtil } from "@/components/NewReplaceProduct/services/material/StringUtil";
import { AI2DesignBasicModes } from "@/Apps/AI2Design/AI2DesignManager";
/**
 * @description 状态栏
 */
interface I_Btn {
    id: string;
    icon: string;
    label: string;
    eventName?: keyof typeof EventName;
    onClick?: () => void;
}
const { confirm } = Modal;

interface ConfigType {
    designStyle: Array<{
        text: string;
        value: string;
        cover: string;
    }>;
    rooms: Array<{
        text: string;
        value: string;
        inspire: string;
    }>;
}

const PadStatusBar: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const store = useStore();
    const object_id = "PadStatusBar";
    const [btnList, setBtnList] = useState<I_Btn[]>([]);
    const addGroupModalRef = useRef<any>(null);
    const [isIconOnly, setIsIconOnly] = useState<boolean>(false);
    const [activeComponent, setActiveComponent] = useState<string | null>('null'); // 新增状态
    const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const [commandBar, setCommandBar] = useState<any[]>([]);
    const [_key, setKey] = useState<number>(0);
    const [combinatName, setCombinatName] = useState<string>('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isCircle, setIsCircle] = useState(false);
    const [isMove, setIsMove] = useState(false);
    const [autoRuler, setAutoRuler] = useState(false);
    const [config, setConfig] = useState<ConfigType>({
        designStyle: [],
        rooms: []
    });
    const navigate = useNavigate();
    const barHeight = isIconOnly ? 36 : 45;
    const whole_bar_list = [
        {
            id: "Layout",
            label: t("布局"),
            icon: "icon-a-TypebujuStateDefault"
        },
        {
            id: "Matching",
            label: t("风格"),
            icon: "icon-a-TypefenggeStateDefault"
        },
        store.homeStore.viewMode === '2D' ? {
            id: "material",
            label: t("素材"),
            icon: "icon-a-TypesucaiStateDefault"
        } : null,
        {
            id: "attribute",
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault"
        },
        store.homeStore.viewMode === '3D_FirstPerson' ?   /*[i18n:ignore]*/
            {
                id: "view",
                label: t("视角"),
                icon: "icon-smarttemplate"
            } : null,
        store.homeStore.viewMode === '2D'
            ? {
                id: "aidraw",
                label: t("AI绘图"),
                icon: "icon-AIchutu"
            } : null,
        // {
        //     id: 'ailight',
        //     label: t('AI灯光'),
        //     icon: 'icon-AIchutu',
        // },
        mini_APP ? {
            id: "similar",
            label: t("相似匹配"),
            icon: "icona-Typexuanzebujian"
        } : null,
        mini_APP ? {
            id: "create",
            label: t("新建"),
            icon: "iconfile"
        } : null,

    ].filter(Boolean);


    let manyou_bar_list = [
        {
            id: "Layout",
            label: t("布局"),
            icon: "icon-a-TypebujuStateDefault"
        },
        {
            id: "Matching",
            label: t("风格"),
            icon: "icon-a-TypefenggeStateDefault"
        },
        {
            id: "submitDrawing",
            label: t("提交绘图"),
            icon: "icon-xuanranRender"
        },
        {
            id: "atlas",
            label: t("图册"),
            icon: "icon-tuku"
        }
    ].filter(Boolean);


    let commandBarData = [
        {
            id: 'size',
            icon: 'icon-chicun',
            label: t('尺寸'),
            onClick: () => {
                store.homeStore.setSizeInfo({
                    type: 'size',
                    visible: true,
                });
                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'sizeEditor');

            },
            disabled: false,
            divider: false,
        },
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'flip',
            icon: 'icon-horizontalflip_line',
            label: t('左右镜像'),
            disabled: false,
            divider: false,
        },
        {
            id: 'copy',
            icon: 'icon-niantie',
            label: t('复制'),
            disabled: false,
            divider: false,
        },
        {
            id: 'autoRuler',
            icon: 'icon-chicun',
            label: t('开启标尺'),
            disabled: false,
            divider: false,
        },

        // store.homeStore.selectEntity?.type === 'Furniture' && !store.homeStore.selectEntity?.figure_element.haveMatchedMaterial() && 
        // {
        //     id: 'pos_z',
        //     icon: 'icon-lidi',
        //     label: t('离地'),
        //     disabled: false,
        //     divider: false,
        // },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        }

    ];
    commandBarData = commandBarData.filter(Boolean);

    const doorData = [
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    const baywindowData = [
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    const structureData = [
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
        }
    ]

    let wallData = [
        {
            id: 'attribute',
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault",
            onClick: () => {
                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'attribute');
            }
        },
        {
            id: 'splitWall',
            icon: "iconsplit",
            label: t('拆分墙'),
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]

    if(store.homeStore.designMode === AI2DesignBasicModes.HouseDesignMode)
    {
        let propertyItem = 
        {
            id: "attribute",
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault",
            onClick: () => {
                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'attribute');
            }
        }
        doorData.unshift(propertyItem as any);
        baywindowData.unshift(propertyItem as any);
        structureData.unshift(propertyItem as any);
        if(checkIsMobile)
        {
            wallData = wallData.filter(a => a.id !== 'splitWall')
        }
    }
    const BaseGroup = [
        {
            id: 'size',
            icon: 'icon-chicun',
            label: t('尺寸'),
            onClick: () => {
                store.homeStore.setSizeInfo({
                    type: 'size',
                    visible: true,
                });
                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'sizeEditor');

            },
            disabled: false,
            divider: false,
        },
        {
            id: 'rotate',
            icon: 'icon-rotate',
            label: t('旋转'),
            disabled: false,
            divider: false,
        },
        {
            id: 'ungroupTemplate',
            icon: 'icon-jiezu-2',
            label: t('解组'),
            disabled: false,
            divider: false,
        },
        {
            id: 'combinationStorage',
            icon: 'icon-anzhuangInstall',
            label: t('组合入库'),
            disabled: false,
            divider: false,
        },
        {
            id: 'delete',
            icon: 'icon-delete',
            label: t('删除'),
            disabled: false,
            divider: true,
        },
    ]
    // const defaultData = [
    // {
    //     id: 'delete',
    //     icon: 'icon-delete',
    //     label: t('删除'),
    //     disabled: false,
    //     divider: true,
    // },
    // ]

    let roomAreaData = [
        {
            id: "attribute",
            label: t("属性"),
            icon: "icon-a-TypeshuxingStateDefault",
            onClick: () => {
                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'attribute');
            }
        },
        {
            id: "material",
            label: t("清除布局"),
            icon: "icon-shanchubuju",
            EventName: 'ClearLayout',
        },
        // {
        //     id: "clearSeries",
        //     label: t("清除风格"),
        //     icon: "icon-qingchufengge",
        //     onClick: () => {
        //         if (layoutContainer && layoutContainer._selected_room) {
        //             TSeriesFurnisher.instance.clearRoom2SeriesSample(layoutContainer._selected_room, false);
        //             LayoutAI_App.instance.update();
        //         }
        //         else {
        //             confirm({
        //                 title: t('清空风格'),
        //                 content: t(`您将清空【全屋】风格，此操作不可恢复。是否确认清空？`),
        //                 okText: t('取消'),
        //                 cancelText: t('确认清空'),
        //                 onOk() { },
        //                 onCancel() {
        //                     TSeriesFurnisher.instance.deleteSeriesSample();
        //                     LayoutAI_App.instance.update();
        //                 },
        //             });
        //         }
        //     }
        // },
        // {
        //     id: "searchMaterial",
        //     label: t("查看素材"),
        //     icon: "icon-sucai",
        //     onClick: () => {
        //         LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'searchMaterial');
        //     }
        // },
        !store.homeStore.isSingleRoom && store.homeStore.viewMode === '2D'
            ? {
                id: "focusSpace",
                label: t("专注空间"),
                icon: "icon-zhuanzhukongjian",
                EventName: "SingleRoomLayout",
                onClick: () => {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.SingleRoomLayout, store.homeStore.selectEntity);
                    store.homeStore.setIsSingleRoom(true);
                }
            }
            : null,
        // store.homeStore.viewMode === '2D' 
        // ? {
        //     id : "addRoomSubArea",
        //     label :t("添加分区"),
        //     icon :"icona-zaoxingbianjishapeediting",
        //     onClick: ()=>{
        //         confirm({
        //             title: t('清空内容'),
        //             content: t('是否要清空当前空间的布局和风格内容'),
        //             okText: t('清空'),
        //             cancelText: t('不清空'),
        //             onOk() { 
        //                 LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearLayout, this);
        //                 LayoutAI_App.DispatchEvent(LayoutAI_Events.AddSubRoomArea, store.homeStore.selectEntity);
        //                 store.homeStore.setIsSingleRoom(true);
        //             },
        //             onCancel() {
        //                 LayoutAI_App.DispatchEvent(LayoutAI_Events.AddSubRoomArea, store.homeStore.selectEntity);
        //                 store.homeStore.setIsSingleRoom(true);
        //             },
        //         });
                
        //     }
        // }:null,
        // store.homeStore.isSingleRoom && store.homeStore.viewMode === '2D'
        //     ? {
        //         id: "exit",
        //         label: "",
        //         icon: "icon-a-tianchongFace-1",
        //         onClick: () => {
        //             LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
        //             store.homeStore.setIsSingleRoom(false);
        //         }
        //     }
        //     : null
    ]
    roomAreaData = roomAreaData.filter(item => item !== null);




    useEffect(() => {
        LayoutAI_App.on_M(EventName.SelectingTarget, object_id, (params) => {
            let Entity: TBaseEntity = params || null;
            store.homeStore.setSelectEntity(params);
            if (!params) {
                store.homeStore.setShowReplace(false);
            }
            let entity_type: IRoomEntityType = Entity?.type || null;
            let entity_label = params?.ex_prop?.label || null;
            if (entity_type === "Furniture") {
                let groupData: any = [];
                groupData = [...commandBarData];

                // if (Entity?.matched_rect) {
                    // groupData.splice(groupData.length - 1, 0, {
                    //     id: 'replace',
                    //     icon: 'icon-change_logo',
                    //     label: t('替换'),
                    //     disabled: false,
                    //     divider: false,
                    // });
                // }
                setCommandBar(groupData);
            }
            else if (entity_type === "Group") {
                let groupData: any = [
                    {
                        id: 'size',
                        icon: 'icon-chicun',
                        label: t('尺寸'),
                        onClick: () => {
                            store.homeStore.setSizeInfo({
                                type: 'size',
                                visible: true,
                            });
                            LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'sizeEditor');
            
                        },
                        disabled: false,
                        divider: false,
                    },
                    {
                        id: 'combination',
                        label: t('组合'),
                        icon: 'iconcombination',
                        tips: t('组合'),
                        disabled: false,
                        divider: false,
                    },
                ];
                setCommandBar(groupData);
            }
            else if (entity_type === "BaseGroup") {
                // let groupData: any = [];
                // groupData = [...BaseGroup];
                // if (Entity?.matched_rect) {
                //     groupData.splice(groupData.length - 1, 0, {
                //         id: 'replace',
                //         icon: 'icon-change_logo',
                //         label: t('替换'),
                //         disabled: false,
                //         divider: false,
                //     });
                // }

                const tmp = window.location.pathname.includes('addGroup') ? BaseGroup : BaseGroup.filter(a => a.id !== "combinationStorage")
                setCommandBar(tmp)
            }
            else if (entity_type === "Door" || entity_type === "Window") {
                if (entity_label === "baywindow") {
                    setCommandBar(baywindowData);
                    return;
                }
                setCommandBar(doorData);

            }
            else if(entity_type === "Wall"){
                setCommandBar(wallData);
            } else if(entity_type === "StructureEntity")
            {
                setCommandBar(structureData);
            } else if (entity_type === AI_PolyTargetType.RoomSubArea) {
                setCommandBar([
                {
                    id:'roomSubAreaAttribute',
                    icon:'icon-a-TypeshuxingStateDefault',
                    label:t('属性'),
                    disabled:false,
                    divider:false,
                    onClick:()=>{
                        LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'SpaceAreaAttribute');
                    }
                },
                {
                    id: 'copyRoomSubArea',
                    icon: 'iconcopy',
                    label: t('复制'),
                    disabled: false,
                    divider: true,
                },  
                {
                    id: 'deleteRoomSubArea',
                    icon: 'icon-delete',
                    label: t('删除'),
                    disabled: false,
                    divider: true,
                }
            ]);
            }
        });
        fetchConfig();
    }, []);

    useEffect(() => {
        if (store.homeStore.viewMode === '3D_FirstPerson') /*[i18n:ignore]*/ {
            setBtnList(manyou_bar_list);
        }
        else {
            setBtnList(whole_bar_list);
        }
    }, [store.homeStore.viewMode]);

    useEffect(() => {

        // 漫游模式下，点击非家具，弹出替换弹窗
        if (store.homeStore.viewMode === '3D_FirstPerson' && store.homeStore.selectEntity?.type !== 'Furniture') /*[i18n:ignore]*/ {
            // LayoutAI_App.emit_M(LayoutPopEvents.showPopup, '');
            // setIsVisible(true);
            handleComponentChange(null);
            (LayoutAI_App.instance as TAppManagerBase).layout_container.cleanDimension();
            return;
        }

        // 漫游模式下，点击家具，弹出替换弹窗
        if (store.homeStore.viewMode === '3D_FirstPerson' && store.homeStore.selectEntity?.type === 'Furniture') /*[i18n:ignore]*/ {
            LayoutAI_App.emit_M(LayoutPopEvents.showPopup, 'replace');
            (LayoutAI_App.instance as TAppManagerBase).layout_container.cleanDimension();
            setIsVisible(false);
            handleComponentChange(null);
            return;
        }

        // 正常模式下，点击对应模块，弹出对应弹窗
        let entity_type: IRoomEntityType = store.homeStore.selectEntity?.type || null;
        handleComponentChange(entity_type);

    }, [store.homeStore.selectEntity?.type, store.homeStore.viewMode])

    const [isVisible, setIsVisible] = useState<boolean>(true); // 控制组件可见性
    const handleComponentChange = (id: string) => {
        setIsVisible(false);
        setTimeout(() => {
            setActiveComponent(id || 'null');
            setIsVisible(true);
            if(store.homeStore.viewMode === '3D')
            {
                setIsVisible(false);
            }
        }, 30);
    };


    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const [position, setPosition] = useState({ top: 80, left: screenWidth / 2 });
    const [isDragging, setIsDragging] = useState(false);
    const statusBarRef = useRef<HTMLDivElement>(null);
    const initialPosition = useRef({ top: 0, left: 0 });
    const initialTouch = useRef({ x: 0, y: 0 });
    const [columflex, setColumflex] = useState(false);
    const handleTouchStart = (e: React.TouchEvent) => {
        setIsDragging(true);
        initialPosition.current = { top: position.top, left: position.left }; // 记录初始位置
        initialTouch.current = { x: e.touches[0].clientX, y: e.touches[0].clientY }; // 记录初始触摸点
    };

    const updatePosition = (top: number, left: number, isCircle: boolean, columflexState: boolean = false) => {
        setPosition({ top, left });
        setIsCircle(isCircle);
        setColumflex(columflexState);
    };
    const handleTouchMove = (e: React.TouchEvent) => {
        e.preventDefault();
        return;

    };
    function isPointInTriangle(pt: any, v1: any, v2: any, v3: any) {
        const area = (v1: any, v2: any, v3: any) => Math.abs((v1.x * (v2.y - v3.y) + v2.x * (v3.y - v1.y) + v3.x * (v1.y - v2.y)) / 2);

        const A = area(v1, v2, v3); // 原三角形面积
        const A1 = area(pt, v2, v3); // 点与两个顶点构成的小三角形面积
        const A2 = area(v1, pt, v3);
        const A3 = area(v1, v2, pt);

        return A === A1 + A2 + A3; // 判断面积之和是否等于原三角形面积
    }

    const handleTouchEnd = (e: TouchEvent) => {
        return;
    };

    const commonStyle = () => {
        return {
            position: 'fixed',
            top: position.top,
            left: "50%",
            maxWidth: isCircle ? barHeight + 'px' : (columflex ? barHeight + 'px' : '550px'),
            maxHeight: isCircle ? barHeight + 'px' : (columflex ? '550px' : barHeight + 'px'),
            minWidth: barHeight + 'px',
            minHeight: barHeight + 'px',
            flexDirection: columflex ? 'column' : 'row',
        };
    }

    const leftRightRadius = () => {
        return <div onClick={() => setIsIconOnly(!isIconOnly)} style={{ borderTopLeftRadius: '50%', width: !columflex ? 24 : 64, height: !columflex ? 64 : 24, backgroundColor: '#fff' }}></div>
    }

    const circleIcon = () => {
        return isCircle && <IconFont type="icon-a-TypegongjuStateDefault" style={{ fontSize: '31px', color: '#282828', margin: '-3px 14px 0px 17px' }} onClick={showToolBar}></IconFont>
    }

    const showToolBar = () => {
        updatePosition(60, screenWidth / 2, false, false);
    }



    useEffect(() => {
        const statusBarElement = statusBarRef.current;
        if (statusBarElement) {
            statusBarElement.addEventListener('touchend', handleTouchEnd);
        }

        return () => {
            if (statusBarElement) {
                statusBarElement.removeEventListener('touchend', handleTouchEnd);
            }
        };
    }, [isDragging, isMove]);

    useEffect(() => {
        showToolBar();
    }, [store.homeStore.IsLandscape]);

    useEffect(() => {
        if (store.homeStore.zIndexOf3DViewer === 4) {
            renderComponent();
        }
    }, [store.homeStore.zIndexOf3DViewer]);

    useEffect(() => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.AutoRuler, { AutoRuler: autoRuler });
        setCommandBar(commandBar.map(item => {
            if (item.id === 'autoRuler') {
                item.label = !autoRuler ? t('开启标尺') : t('关闭标尺');
            }
            return item;
        }));
      }, [autoRuler]);
    
    const fetchConfig = async () => {
        try {
            const response = await fetch('https://3vj-render.3vjia.com/config/3d/aidraw.json');
            const data = await response.json();
            setConfig(data);
            // console.log('获取到配置数据:', config);
        } catch (error) {
            console.error('获取配置失败:', error);
        }
    };

    const addCombinationStorage = async() => {
        let addGroupData = store.addGroupStore.addGroupData;

        let entity = store.homeStore.selectEntity as TBaseGroupEntity;
        let imgUrl = await uploadImageToOss(
            LayoutSchemeJsonSaver.saveEntityImage(entity),
            'snapShot' + Math.floor(Math.random() * 10000) + '.png'
          );
        let params = {} as any;
        if(addGroupData.type === 'addsizeGroup')
        { 
            params = {
                metaData: entity.exportData(),
                metaHeight: entity.rect.rect_center_3d.z,
                metaLength: entity.rect.w,
                metaWidth: entity.rect.h,
                metaImage: imgUrl,
                metaName: entity.name,
                metaImageId: addGroupData.sizeObj.id,
            }
            let res = await combinationSizeStorage(params);
            if(res.success)
            {
                message.success(t('尺寸链组合入库成功'));
                window.parent.postMessage({
                    origin: 'layoutai.api',
                    type: 'GroupMessage',
                    data: {
                        success: true,
                    },
                }, '*');
            }
        }
        


    }

    const renderComponent = () => {
        switch (activeComponent) {
            case "null":
                return <></>;
            case "Furniture":
            case "BaseGroup":
            case "Door":
            case "Window":
            case "Group":
            case "StructureEntity":
            case "Wall":
                return <div ref={statusBarRef} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} style={commonStyle() as any} className={`${styles.root} ${isVisible ? styles.show : styles.hide}`}>
                    {leftRightRadius()}
                    {circleIcon()}                    {
                        commandBar.map((btn, index) => {
                            return (
                                <div className={styles.btnInfo} key={index}
                                    onClick={() => {
                                        if (btn.onClick) {
                                            btn.onClick();
                                            return;
                                        }
                                        switch (btn.id) {
                                            case 'rotate':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.RotateFurniture);
                                                break;
                                            case 'flip':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.FlipFurniture);
                                                break;
                                            case 'flip_vertical':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.FlipFurnitureVertical);
                                                break;
                                            case 'delete':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
                                                break;
                                            case 'deleteRuler':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteRuler);
                                                break;
                                            case 'copy':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.CopyFurniture);
                                                break;
                                            case 'combination':
                                                addGroupModalRef.current.onModal();
                                                break;
                                            case 'ungroupTemplate':
                                                LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleUnGroupTemplate, {});
                                                store.homeStore.setKey(Date.now());
                                                break;
                                            case 'replace':
                                                LayoutAI_App.emit_M(LayoutPopEvents.showPopup, btn.id);
                                                setIsVisible(false);
                                                break;
                                            case 'size':
                                                store.homeStore.setSizeInfo({
                                                    type: 'size',
                                                    visible: true,
                                                });
                                                break;
                                            case 'pos_z':
                                                store.homeStore.setSizeInfo({
                                                    type: 'pos_z',
                                                    visible: true,
                                                });
                                                break;
                                            case 'copyRoomSubArea':
                                                LayoutAI_App.DispatchEvent(LayoutAI_Events.CopyRoomSubArea,null);
                                                break;
                                            case 'deleteRoomSubArea':
                                                LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
                                                break;
                                            case 'combinationStorage':
                                                addCombinationStorage();
                                                break;
                                            case 'autoRuler':
                                                setAutoRuler(!autoRuler);
                                                break;
                                            default:
                                                break;
                                        }
                                    }}
                                >
                                    {
                                        btn.divider && <div className='divider'></div>
                                    }
                                    <>
                                        <div>
                                            <IconFont
                                                type={btn.icon}
                                                style={{
                                                    fontSize: '20px',
                                                    color: '#282828'
                                                }}></IconFont>
                                        </div>
                                        {!isIconOnly && <div className="label">{btn.label}</div>}
                                    </>

                                </div>
                            )
                        })
                    }
                    <>
                        <div>
                            <IconFont
                                type={'icon-a-tianchongFace-1'}
                                onClick={() => {
                                    LayoutAI_App.DispatchEvent(LayoutAI_Events.cleanSelect, null);
                                }}
                                style={{
                                    fontSize: '20px',
                                    color: '#BCBEC2'
                                }}></IconFont>
                        </div>
                    </>
                    {leftRightRadius()}
                </div>; // 替换为实际组件
            case "RoomArea":
                return (
                    <div ref={statusBarRef} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} className={`${styles.root} ${isVisible ? styles.show : styles.hide}`} style={commonStyle() as any}>
                        {circleIcon()}
                        {leftRightRadius()}
                        <>
                            {
                                roomAreaData.map((btn, index) => {
                                    return (
                                        <div className={styles.btnInfo} key={index} style={{ margin: columflex ? '4px 0' : '0 8px' }}
                                            onClick={() => {
                                                if (btn.onClick) {
                                                    btn.onClick();
                                                    setKey(Math.floor(Math.random() * 10000));
                                                } else {
                                                    if (btn?.EventName) {
                                                        setKey(Math.floor(Math.random() * 10000));
                                                        LayoutAI_App.DispatchEvent(btn?.EventName, store.homeStore.selectEntity);
                                                    }
                                                }
                                            }}
                                        >
                                            <div>
                                                <IconFont
                                                    type={btn.icon}
                                                    style={{
                                                        fontSize: '20px',
                                                        color: '#282828'
                                                    }}></IconFont>
                                            </div>
                                            {!isIconOnly && <div className="label">{btn.label}</div>}
                                        </div>
                                    )
                                })
                            }
                        </>
                        {leftRightRadius()}
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <>
        <div key={_key}>
            {renderComponent()}
        </div>
        <AddGroupModal ref={addGroupModalRef}></AddGroupModal>
        </>
    );

};

export default observer(PadStatusBar);
