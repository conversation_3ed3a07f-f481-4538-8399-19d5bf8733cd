import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      background: #fff;
      height: 500px;
      padding: 0 16px;
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100);
        padding: 0 12px;
      }
      .ant-segmented
      {
        background-color: #EAEBEA;
        color: #282828 !important;
      }
    `,
    topInfo: css`
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 0 24px;
        @media screen and (orientation: landscape) {
          margin: 12px 0px;
          padding: 0 0px;
        }
        .info
        {
          display: flex;
          img{
            width: 72px;
            height: 72px;
            border-radius: 4px;
            margin-right: 16px;
            @media screen and (orientation: landscape) {
              width: 80px;
              height: 80px;
              margin-right: 12px;
            }
          }
        }
         .sizeInfo
         {
          padding: 8px 0px;
          @media screen and (orientation: landscape) {
            padding: 0px 0px;
          }
            .size
            {
              color: #5B5E60;
              margin-top: 4px;
              @media screen and (orientation: landscape) {
                margin-top: 8px;
                font-size: 10px;
              }
            }
         } 
      `,
    divider: css`
      margin: 20px 0 14px 0px;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media screen and (orientation: landscape) {
        margin: 0px 0 8px 0px;
      }
    `,
    goodsInfo: css`
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      max-height: calc(var(--vh, 1vh) * 100 - 240px);
      margin-top: 10px;
      align-items: flex-start;
       /* 隐藏滚动条 */
      &::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
      }
      
      /* 对于 Firefox */
      scrollbar-width: none; /* 隐藏滚动条 */
      -ms-overflow-style: none; /* IE 和 Edge */

      
    `,
    loading: css`
      text-align: center;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    `,
    goodsItem: css`
      overflow: hidden;
      width: 104px;
      margin: 6px 12px 0 12px;
      text-align: center;
      @media screen and (max-width: 800px){
         width: 122px;
      }
      @media screen and (max-width: 450px){
         width: 106px;
      }
      @media screen and (max-width: 400px){
         width: 94px;
      }
      @media screen and (orientation: landscape) {
        margin: 6px 6px 0 6px;
        width: 88px;
        font-size: 10px;
        text-align: left;
      }
      img{
        width: 100%;
        aspect-ratio: 1 / 1;
        border-radius: 4px;
        background-color: #eaeaea;
        border-radius: 8px;
      }
    `,
    selectIcon: css`
    
    `,
    sizeInfo: css`
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 4px;
    `,
    noData: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      margin: 0 auto;
      .emptyImg{
        width: 120px;
        height: 120px;
        margin-bottom: 12px;
      }
      span{
        color: #5B5E60;
      }
    `
  }
});
