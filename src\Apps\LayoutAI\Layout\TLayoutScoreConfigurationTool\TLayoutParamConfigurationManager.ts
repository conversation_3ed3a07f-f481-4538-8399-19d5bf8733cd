import { DefaultLayoutScoreParamConfig } from "./DefaultLayoutScoreParamConfig";
import { TLayoutBaseRuleParamConfiguration, RoomLayoutScoreType } from "./TLayoutBaseRuleParamConfiguration";

export class TLayoutParamConfigurationManager
{
    private static _instance: TLayoutParamConfigurationManager;
    private static readonly _jsonFilePath: string = './layoutScoreParamConfig.json';
    private static readonly _localStorageKey: string = 'layoutScoreParamConfig';
    public static readonly IndexedDB_Prop_Key : string = "layoutScoreParamConfig";

    private _roomParamConfigs: TLayoutBaseRuleParamConfiguration[];
    constructor()
    {
        this._roomParamConfigs = [];
    }

    public static get instance(): TLayoutParamConfigurationManager
    {
        if(!TLayoutParamConfigurationManager._instance)
        {
            TLayoutParamConfigurationManager._instance = new TLayoutParamConfigurationManager();
        }
        return TLayoutParamConfigurationManager._instance;
    }

    public static get localStorageKey(): string
    {
        return TLayoutParamConfigurationManager._localStorageKey;
    }

    public loadJson(): any
    {
        // 优先从localStorage读取配置
        const localConfig = localStorage.getItem(TLayoutParamConfigurationManager._localStorageKey);
        if (localConfig) {
            try {
                return JSON.parse(localConfig);
            } catch (e) {
                console.warn('Failed to parse local config, falling back to default config');
            }
        }

        // 如果localStorage中没有配置或解析失败，则加载默认配置
        let json: any = DefaultLayoutScoreParamConfig;
        return json;
    }

    public saveJson(jsonData: any): void {
        try {
            const jsonStr = JSON.stringify(jsonData);
            localStorage.setItem(TLayoutParamConfigurationManager._localStorageKey, jsonStr);
        } catch (e) {
            console.error('Failed to save config to localStorage:', e);
        }
    }


    // 清除本地存储的配置
    public clearLocalConfig(): void {
        localStorage.removeItem(TLayoutParamConfigurationManager._localStorageKey);
    }

    // 重置为默认配置
    public resetToDefaultConfig(): void {
        this.clearLocalConfig();
        const defaultConfig = require(`${TLayoutParamConfigurationManager._jsonFilePath}`);
        this.saveJson(defaultConfig);
        this.update();
    }

    public addParamConfig(paramConfig: TLayoutBaseRuleParamConfiguration)
    {
        if(paramConfig && !this._roomParamConfigs.includes(paramConfig))
        {
            paramConfig.setFilePath(TLayoutParamConfigurationManager._jsonFilePath);
            this._roomParamConfigs.push(paramConfig);
        }
    }

    public update()
    {
        for(let roomParamConfig of this._roomParamConfigs)
        {
            roomParamConfig.updateParam();
        }
    }

    public getRoomParamConfigs(): TLayoutBaseRuleParamConfiguration[] {
        return this._roomParamConfigs;
    }

    public toJson(): any {
        // 定义固定的输出顺序
        const orderedKeys = [
            'bathRoomParamConfig',
            'livingRoomParamConfig',
            'kitchenRoomParamConfig',
            'bedRoomParamConfig'
        ];

        const tempResult: any = {};
        const paramNameMap: { [key in RoomLayoutScoreType]: string } = {
            [RoomLayoutScoreType.k_none]: '',
            [RoomLayoutScoreType.k_bathRoom]: 'bathRoomParamConfig',
            [RoomLayoutScoreType.k_livingRoom]: 'livingRoomParamConfig',
            [RoomLayoutScoreType.k_kitchenRoom]: 'kitchenRoomParamConfig',
            [RoomLayoutScoreType.k_bedRoom]: 'bedRoomParamConfig',
            [RoomLayoutScoreType.k_entranceRoom] : ''
        };

        const originalJson = this.loadJson();

        for (const config of this._roomParamConfigs) {
            const roomType = config.getRoomType();
            const paramName = paramNameMap[roomType];
            if (!paramName) continue;

            const groupConfigs = config.getRuleParamGroupConfigs();
            tempResult[paramName] = {
                name: originalJson[paramName].name,
                data: {}
            };

            // 处理每个组的数据
            for (const [groupName, groupData] of groupConfigs) {
                tempResult[paramName].data[groupName] = {
                    name: originalJson[paramName].data[groupName].name,
                    data: {}
                };

                // 遍历组内的每个规则
                for (const [ruleName, ruleData] of Object.entries(groupData)) {
                    if (ruleName === 'name') continue;

                    const originalRuleData = originalJson[paramName].data[groupName].data[ruleName];
                    tempResult[paramName].data[groupName].data[ruleName] = {
                        name: originalRuleData?.name,
                        data: {}
                    };

                    // 处理规则数据
                    if ('paramItems' in ruleData) {
                        tempResult[paramName].data[groupName].data[ruleName].data = {
                            paramItems: ruleData.paramItems
                        };
                    } else if ('value' in ruleData) {
                        tempResult[paramName].data[groupName].data[ruleName].data = {
                            value: ruleData.value
                        };
                    } else if ('child' in ruleData) {
                        const childData: any = {};
                        for (const [childKey, childValue] of Object.entries(ruleData.child)) {
                            childData[childKey] = {
                                name: originalRuleData?.data[childKey].name,
                                data: {}
                            };
                            if ('value' in childValue) {
                                childData[childKey].data = {
                                    value: childValue.value
                                };
                            } else if ('paramItems' in childValue) {
                                childData[childKey].data = {
                                    paramItems: childValue.paramItems
                                };
                            }
                        }
                        tempResult[paramName].data[groupName].data[ruleName].data = childData;
                    }
                }
            }
        }

        // 按照固定顺序重新组织结果
        const result: any = {};
        for (const key of orderedKeys) {
            if (tempResult[key]) {
                result[key] = tempResult[key];
            }
        }

        return result;
    }

    public toFormattedJson(): string {
        const jsonObj = this.toJson();
        return JSON.stringify(jsonObj, (key, value) => {
            // 处理特殊的数值
            if (typeof value === 'number') {
                if (value === Number.POSITIVE_INFINITY) {
                    return "Infinity";
                }
                if (value === Number.NEGATIVE_INFINITY) {
                    return "-Infinity";
                }
            }
            return value;
        }, 4);
    }
}