import { MaterialService, toStringForMaterialMatchingItem } from "./MaterialService";
import { Logger } from "../../Utils/logger";
import { TFigureElement } from "../../Layout/TFigureElements/TFigureElement";
import { TRoom } from "../../Layout/TRoom";
import { TSeriesSample } from "../../Layout/TSeriesSample";
import { TPostFigureElementAdjust } from "../../Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostFigureElementAdjust";
import { addBlackAndBoldLine, compareNames } from "@layoutai/z_polygon";
import { debug } from "@/config"
import { TPostLayoutCeiling } from "../../Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutCeiling";
import { Vector3 } from "three";
import { TPostDecoratesLayout } from "../../Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TGraphBasicConfigs } from "../../Layout/TLayoutGraph/TGraphBasicConfigs";
import { TMatchingOrdering } from "./TMatchingOrdering";
import { TMaterialMatchingConfigs } from "./TMaterialMatchingConfigs";
import { FigureCategoryManager } from "../../Layout/TFigureElements/FigureCategoryManager";
import { TDesignMaterialUpdater } from "./TDesignMaterialUpdater";
import { TWindowDoorEntity } from "../../Layout/TLayoutEntities/TWinDoorEntity";
import { TBaseGroupEntity } from "../../Layout/TLayoutEntities/TBaseGroupEntity";
import { I_MaterialMatchingItem } from "../../Layout/IMaterialInterface";
import { MatchingPostProcesser } from "./MatchingPostProcesser";
import { getMaterialBlockData } from "@/services/material";
import { FigureTopViewer } from "../../Scene3D/process/FigureTopViewer";
import { TTableTopEntity } from "../../Layout/TLayoutEntities/TTableTopEntity";
import { TextureManager } from "../../Scene3D/TextureManager";
import { CabinetStyleService } from "./CabinetStyleService";
import { Permissions } from "../../setting/Permissions";
import { PERMISSIONS } from "@/config/permissions";

/**
 *   素材匹配工具
 */
export class TMaterialMatcher {
    private static _instance: TMaterialMatcher = null;

    private useDefaultMaterial: boolean = true;
    public matching_ordeing_method: TMatchingOrdering;
    public autoResizeProductMaterial: boolean;
    public enableAddDecorations: boolean = true;

    private _debug = true;

    // 固定装置---备选数据列表
    //    模型位
    //      --- 套系
    //Fixture 在建筑与室内设计领域中，常用于指：
    // 固定安装的设备或装置，通常包括照明、电气、水暖等系统组件。
    // 例如：
    // Light fixtures（照明设备，如筒灯、吊灯）
    // Plumbing fixtures（水暖装置，如水龙头、坐便器）
    // Electrical fixtures（电气装置，如插座、开关）
    private _fixture_candidates: { [key: string]: { seriesKgId: string, seriesKgName?: string, figure_element: TFigureElement }[] } = {};

    constructor() {
        this.matching_ordeing_method = new TMatchingOrdering();

        this._fixture_candidates = {
            "筒灯": [],
            "强电插座": [],
            "开关": [],
            "应急": [],
            "给水": []
        }

        this.autoResizeProductMaterial = Permissions.instance.hasPermission(PERMISSIONS.SERIES.ALLOW_STRETCH_FINISHED_PRODUCT);
        this.enableAddDecorations = Permissions.instance.hasPermission(PERMISSIONS.SERIES.GEN_SMART_DECORATION);
    }
    static get instance() {
        if (!TMaterialMatcher._instance) {
            TMaterialMatcher._instance = new TMaterialMatcher();
        }
        return TMaterialMatcher._instance;
    }


    /**
     * 为房间匹配套系素材
     * @param roomItem 需要匹配套系素材的房间
     * @param seriesSampleItem 套系
     * @param logger 日志记录器
     * @param matchedMaterials 所有匹配到的素材列表
     */
    async furnishRoom(roomItem: TRoom, seriesSampleItem: TSeriesSample, logger: Logger,
        matchedMaterials: I_MaterialMatchingItem[]) {
        //如果房间被锁定，则直接返回，不匹配套系素材
        if (roomItem.locked) return;
        //更新房间当前应用的套系
        roomItem._series_sample_info = seriesSampleItem;
        //记录房间的应用范围所对应的套系
        roomItem.updateCurrentScopeSeries(seriesSampleItem);
        //从_furniture_list中删除房间中的电器装饰图元
        TPostDecoratesLayout.post_clean_electricity_decorations(roomItem, roomItem._furniture_list);

        if(this.enableAddDecorations)
        {
            // 风格套系匹配时, 自动添加饰品
            TPostDecoratesLayout.post_add_decorations(roomItem,roomItem._furniture_list);
        } else {
            TPostDecoratesLayout.post_clean_decorations(roomItem,roomItem._furniture_list);
        }


        //获取需要匹配套系素材的图元列表
        let toBeMatchedFigureElements: TFigureElement[] = [];
        //判断当前房间是否正在走补全的套系应用流程（应用套系到剩余的未匹配到素材的图元）
        if (!roomItem.isCurrentApplyRemainingCategory()) {
            //如果不是，则获取房间中需要匹配套系素材的图元列表
            toBeMatchedFigureElements = roomItem._furniture_list.filter((fe) => {
                if (compareNames([fe.modelLoc], TMaterialMatchingConfigs._ignoreCategories)) return false;
                return true;
            });
            //当前是常规套系应用流程，所以要清空此图元列表
            roomItem._remaining_figure_list = [];
        } else {
            //当前是补全套系应用流程，所以先获取房间中未匹配套系素材的图元列表
            toBeMatchedFigureElements.push(...roomItem._furniture_list.filter((fe) => !fe.haveMatchedMaterial()));
        }

        //用于保存等待着要匹配套系素材的硬装图元列表
        let waitingHardElements: TFigureElement[] = [];
        //判断当前房间的硬装类别图元是否需要应用套系（匹配套系里的素材）
        if (roomItem.isCurrentApplyHardCategory()) {
            //判断当前房间没有生成过硬装图元
            if (!roomItem.haveGeneratedHardElements) {
                    //生成当前房间的硬装图元
                    waitingHardElements = this.makeHardFurnishingElements(roomItem);
                } else {
                    //获取当前房间的硬装图元
                    waitingHardElements = roomItem.getHardFurnishingElements();
                }
            //将硬装图元添加到需要匹配套系素材的图元列表中
            toBeMatchedFigureElements.push(...waitingHardElements);
        }

        //过滤掉当前房间应用范围外的图元
        toBeMatchedFigureElements = toBeMatchedFigureElements.filter((fe) => {
            //判断当前图元是否在当前房间的应用范围内，并且没有被锁定
            let shouldFilter: boolean = roomItem.isFigureElementInCurrentScope(fe) && !fe.locked;
            if (shouldFilter) {
                //如果当前是补全套系应用流程，则清空图元的匹配素材
                if (roomItem.isCurrentApplyRemainingCategory()) {
                    fe.clearMatchedMaterials();
                } else {
                    //如果当前是常规套系应用流程，则彻底清空图元的所有匹配素材，包括子图元和饰品图元的匹配素材
                    fe.clearAllMatchedMaterials();
                }
            } else if (!roomItem.isCurrentApplyRemainingCategory() && !fe.haveMatchedMaterial()) {
                //如果当前图元被排除在当前常规套系应用流程中，并且没有匹配到素材，则将此图元添加到_remaining_figure_list
                roomItem._remaining_figure_list.push(fe);
            }
            return shouldFilter;
        });

        //用于记录所有台面家具图元（即放置在台面家具图元上、依附在台面家具图元上的装饰图元，也包括组合类图元中的台面家具图元）
        let allTableDecorationElements: TFigureElement[] = [];
        if(this.enableAddDecorations) {
            for (let waitingFigure of toBeMatchedFigureElements) {
                //判断当前图元是否是组合类图元（disassembledElements保存了组合类图元的子图元）
                if (waitingFigure.disassembledElements) {
                    //如果是组合类图元，则遍历其子图元中寻找所有嵌套的饰品图元
                    for (let disassembled of waitingFigure.disassembledElements) {
                        //获取当前子图元的装饰图元列表
                        let decorationElements = disassembled.decorationElements;
                        //如果当前子图元有装饰图元，则将装饰图元添加到allTableDecorationElements
                        if (decorationElements && decorationElements.length > 0) {
                            allTableDecorationElements.push(...decorationElements);
                        }
                    }
                } else {
                    //如果当前图元不是组合类图元，则获取直接跟当前图元关联的装饰图元列表
                    let decorationElements = waitingFigure.decorationElements;
                    //如果当前图元有装饰图元，则将装饰图元添加到allTableDecorationElements
                    if (decorationElements && decorationElements.length > 0) {
                        allTableDecorationElements.push(...decorationElements);
                    }
                }
            }

            if(roomItem.decoration_elements)
            {
                allTableDecorationElements.push(...roomItem.decoration_elements);
            }
        }

        //用于记录所有组合类图元的Entity对象（Entity和FigureElement是一对一的关系）
        let groupEntities: TBaseGroupEntity[] = [];
        let groupElements: TFigureElement[] = toBeMatchedFigureElements.filter((fe) => {
            //判断当前图元是否是组合类图元（只有组合类图元的furnitureEntity才是TBaseGroupEntity）
            if (fe.furnitureEntity instanceof TBaseGroupEntity) {
                //如果是组合类图元，则将组合类图元的Entity对象添加到groupEntities
                groupEntities.push(fe.furnitureEntity as TBaseGroupEntity);
                return true;
            }
        });

        //遍历所有组合类图元的Entity对象，并设置其matchedVisible为false
        //这是组合图元在匹配素材前的状态初始化流程之一
        groupEntities.forEach((entity) => {
            entity.setMatchedVisible(false);
        });

        //用于记录所有组合类图元的成员图元（子图元）
        let allMemberElements: TFigureElement[] = [];
        //用于记录所有组合类图元的成员图元的匹配素材
        let allMemberMaterials: I_MaterialMatchingItem[] = [];
        //用于记录所有组合类图元的装饰图元的匹配素材
        let allDecorationMaterials: I_MaterialMatchingItem[] = [];
        groupElements.forEach((ge) => {
            //这是组合图元在匹配素材前的状态初始化流程之一
            ge.matched_rect && ge.matched_rect.copy(ge.rect);
            //遍历组合类图元的所有子图元
            ge.disassembledElements.forEach((de) => {
                //如果当前子图元没有匹配到素材，则将当前子图元添加到allMemberElements
                if (!de.haveMatchedMaterial()) {
                    allMemberElements.push(de);
                }
            });
        });

        // 寻找有收口方向的柜体图元
        let toBeMatchedCabinets = toBeMatchedFigureElements.filter((val) => TGraphBasicConfigs.CloseDirectionCategories.findIndex((e) => e == val.category) >= 0);
        if (toBeMatchedCabinets && toBeMatchedCabinets.length > 0) {
            let allRoomCabinetElements = roomItem._furniture_list.filter((val) => TGraphBasicConfigs.CloseDirectionCategories.findIndex((e) => e == val.category) >= 0);
            TPostFigureElementAdjust.post_process_cabinet_close_direction(roomItem, allRoomCabinetElements);
        }

        //判断当前房间硬装类型图元是否在套系应用流程中的应用范围内
        if (roomItem.isCurrentApplyHardCategory()) {
            //生成一系列固定装置图元，并添加到需要匹配套系素材的图元列表（toBeMatchedFigureElements）中
            let fixtureElements: TFigureElement[] = this.makeFixtureElements(seriesSampleItem.seriesKgId, seriesSampleItem.seriesName);
            toBeMatchedFigureElements.push(...fixtureElements);
        }

        // if (!roomItem.isCurrentApplyRemainingCategory()) {
        //将所有组合类的成员图元添加到需要匹配套系素材的图元列表（toBeMatchedFigureElements）中
        toBeMatchedFigureElements.push(...allMemberElements);
        if(this.enableAddDecorations) {
            //将所有组合类的装饰图元添加到需要匹配套系素材的图元列表（toBeMatchedFigureElements）中
            toBeMatchedFigureElements.push(...allTableDecorationElements);
        }
        // }

        //  最后匹配一下台面上的星盆和灶具
        if(roomItem._room_entity?.tabletop_entities && roomItem._room_entity.tabletop_entities.length > 0)
        {            
           let cooker_mat_list = await MaterialService.getSeriesMaterialByModelLoc(''+seriesSampleItem.ruleId,"炉灶");
           let sinker_mat_items = await MaterialService.getSeriesMaterialByModelLoc(''+seriesSampleItem.ruleId,"星盆");
         
           roomItem._room_entity.tabletop_entities.forEach((tabletop_entity)=>{
                tabletop_entity._cooker_elements.forEach(ele=>MaterialService.matchKgMaterailItemsOnFigureEle(ele,cooker_mat_list));
                tabletop_entity._sink_elements.forEach((ele)=>MaterialService.matchKgMaterailItemsOnFigureEle(ele,sinker_mat_items));
           })
        }
        //将需要匹配套系素材的图元列表打印出来
        debug && this._log_debug_to_be_matched_elements(roomItem, toBeMatchedFigureElements, logger);

        if (toBeMatchedFigureElements.length == 0) {
            if (debug) {
                logger.log("Nothing need to match.");
            }
            return;
        }

        let organizationId: string = null;
        // 调用素材服务的素材匹配接口，对toBeMatchedFigureElements的所有图元进行素材匹配
        // 素材匹配接口的参数说明：
        //     toBeMatchedFigureElements：需要匹配套系素材的图元列表
        //     seriesSampleItem.seriesKgId：套系ID
        //     seriesSampleItem.seriesName：套系名称
        //     seriesSampleItem.seedSchemeId：种子方案ID
        //     organizationId：组织ID
        //     roomItem.room_type：房间类型
        //     roomItem.area：房间面积
        //     logger.traceId：日志ID
        await MaterialService.materialMatching(toBeMatchedFigureElements,
            seriesSampleItem.seriesKgId as number,
            seriesSampleItem.seriesName,
            seriesSampleItem.seedSchemeId,
            organizationId,
            roomItem.room_type,
            roomItem.area,
            logger.traceId);

        //用于记录所有未匹配到素材的组合类图元
        let unMatchedGroupElement: TFigureElement[] = groupElements.filter((fe) => {
            if (!fe.haveMatchedMaterial()) {
                return true;
            }
        });

        //判断当前是否属于补全套系应用流程
        if (roomItem.isCurrentApplyRemainingCategory()) {
            //如果是，则清空房间中未匹配到素材的图元列表
            roomItem._unmatched_remaining_figure_list = [];
        }

        if (this.autoResizeProductMaterial) {
            //遍历toBeMatchedFigureElements的所有图元, 将成品素材尺寸拉伸到跟模型位的一致
            toBeMatchedFigureElements.forEach((fe) => {
                fe.autoResizeMatchedMaterialToModelloc();
            });
        }

        //遍历toBeMatchedFigureElements的所有图元
        toBeMatchedFigureElements.forEach((fe) => {
            let haveMatched: boolean = false;
            //判断当前图元是否属于未匹配到素材的组合类图元
            if (unMatchedGroupElement.indexOf(fe) > -1) {
                //如果是，则判断组合图元是否匹配到素材
                if (!fe.haveMatchedMaterial()) {
                    //如果组合图元没有匹配到素材，则遍历当前图元的所有成员图元（保存在disassembled_figure_elements中），确定是否有（一个或多个）成员图元匹配到素材了
                    const memberElements: TFigureElement[] = (fe.furnitureEntity as TBaseGroupEntity).disassembled_figure_elements;
                    for (let mi = 0; mi < memberElements.length; mi++) {
                        if (memberElements[mi].haveMatchedMaterial()) {
                            haveMatched = true;
                            break;
                        }
                    }
                } else {
                    haveMatched = true;
                }
            } else if (fe.haveMatchedMaterial()) {
                haveMatched = true;
            }
            if (!haveMatched) {
                //如果当前组合图元没有匹配到素材，则根据当前是否属于补全套系应用流程，将当前图元添加到房间中未匹配到素材的图元列表中（_remaining_figure_list或者_unmatched_remaining_figure_list）
                if (!roomItem.isCurrentApplyRemainingCategory()) {
                    roomItem._remaining_figure_list.push(fe);
                } else {
                    roomItem._unmatched_remaining_figure_list.push(fe);
                }
            }
        });

        //如果图元没有匹配到素材，则使用图元默认素材设置为图元的匹配素材，并返回和保存图元默认素材对象
        this.checkAndUpdateDefaultMaterialIds(toBeMatchedFigureElements, matchedMaterials);
        this.checkAndUpdateDefaultMaterialIds(allMemberElements, allMemberMaterials);
        this.checkAndUpdateDefaultMaterialIds(allTableDecorationElements, allDecorationMaterials);

        let allMatchedMaterials = matchedMaterials.concat(allMemberMaterials);
        // 将素材和房间关联
        // 遍历所有匹配到的素材，如果素材没有roomUid，则设置为当前房间的uid
        allMatchedMaterials.forEach((material) => {
            if (!material.roomUid) {
                material.roomUid = roomItem.uid;
            }
        });


        // 遍历所有组合类图元，如果组合类图元没有匹配到素材，则跳过
        for (let fe of groupElements) {
            if (!fe.haveMatchedMaterial()) {
                continue;
            }
            // 根据组合图元所匹配到的组合素材的子素材组成情况，逐一生成家具实体对象
            allMemberElements.push(...(await this.generateGroupMemberMatchedEntities(fe)));
        }

        /*************************************************************************************/
        /***************************     进入匹配素材的后处理流程    ***************************/
        /*************************************************************************************/

        //进入匹配素材的后处理流程，调整内容包括但不限于：素材尺寸、素材位置、图元位置
        MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements(roomItem, allMatchedMaterials);



        //如果满足以下任一条件，则根据定制柜调整吊顶图元：
        // 1. 当前房间正在对硬装图元进行常规套系应用
        // 2. 当前房间正在对定制柜图元进行常规套系应用，并且在之前硬装图元已经应用了套系素材
        if (roomItem.isCurrentApplyHardCategory() || (roomItem.isCurrentApplyCabinetCategory && roomItem.isApplyHardCategory())) {
            if (roomItem._ceilling_list) {
                //调整吊顶图元，使其与定制柜对齐
                TPostLayoutCeiling.instance.adjustCeilingRectByCabinets(roomItem, TMaterialMatchingConfigs._storey_height);
                //根据房间的吊顶高度属性，调整吊顶素材的下吊高度
                TPostLayoutCeiling.instance.adjustCeilingTopOffsetByRoom(roomItem);
            }
            if (roomItem._door_figure_list) {
                //更新门图元的材质
                await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials([...roomItem._door_figure_list], true);
            }
        }

        // 调整饰品的高度和位置，或删除饰品素材
        TPostDecoratesLayout.post_adjust_decorations(roomItem, null, { storey_height: TMaterialMatchingConfigs._storey_height, floor_z: roomItem._room_entity.floor_thickness });
        //调整背景墙相关的图元：
        // 1. 限制背景墙素材的深度最大为120mm
        // 2. 调整背景墙相关的家具图元，使其与背景墙对齐
        MatchingPostProcesser.adjust_backgroundwall_related(roomItem);
        
        if (roomItem.isCurrentApplyCabinetCategory()) {
            // TPostProcessLayout.post_adjust_patching_boards(room);
        }
        if (roomItem.isCurrentApplySoftCategory()) {
            // 调整窗帘的高度和位置
            TPostLayoutCeiling.instance.adjustCurtainSize(roomItem, TMaterialMatchingConfigs._storey_height, roomItem._room_entity.floor_thickness);
            // 调整吊顶素材的位置，使其与窗帘对齐
            TPostLayoutCeiling.instance.adjustMaterialPositionByCeiling(roomItem, TMaterialMatchingConfigs._storey_height);
        }

        // 隐藏同属于一种模型位的重复素材（采用设置素材可见性为false的方式）
        MatchingPostProcesser.hideDuplicatedMaterialOfSameModelloc(toBeMatchedFigureElements, allMemberElements);

        // 移除多余的装饰图元匹配素材（采用清空图元素材的方式）
        MatchingPostProcesser.removeRedundantDecorations(toBeMatchedFigureElements);

        // 判断当前房间是否正在对软装图元进行常规套系应用，或者正在进行补全套系应用
        if (roomItem.isCurrentApplySoftCategory() || roomItem.isCurrentApplyRemainingCategory()) {
            // 对软装图元生成顶视图材质
            MatchingPostProcesser.createTopViewTexturesForProductMaterial(roomItem, toBeMatchedFigureElements);
            // 对软装图元生成线框图材质
            // MatchingPostProcesser.createWireframeTexturesForProductMaterial(roomItem, toBeMatchedFigureElements);
        }
        // 判断当前房间是否正在对定制柜图元进行常规套系应用，或者正在进行补全套系应用
        if (roomItem.isCurrentApplyCabinetCategory() || roomItem.isCurrentApplyRemainingCategory()) {
            // 过滤出定制柜图元
            let cabinetElements = toBeMatchedFigureElements.filter((fe) => fe.haveMatchedMaterial() && FigureCategoryManager.isCustomCabinet(fe));
            // 对定制柜图元生成顶视图材质
            MatchingPostProcesser.createTopViewTexturesForCabinetMatatrial(cabinetElements, seriesSampleItem, logger);            
            let cabinetStyleId = MaterialService.cabinetStyleIdMap.get(seriesSampleItem.seriesKgId.toString());
            cabinetElements.forEach((ele)=>{
                ele.cabinetStyleId = cabinetStyleId;
            });
            roomItem._cabinetStyleId = cabinetStyleId;
        }
        if (waitingHardElements && waitingHardElements.length > 0) {
            // 对硬装图元生成顶视图材质
            MatchingPostProcesser.createTopViewTexuturesForHardMaterial(roomItem, seriesSampleItem, waitingHardElements);
        }
        // groupEntities.forEach((group) => group.rotateAllMatchedMemberMaterialsAlignToGroup());

        // 对未匹配到素材的图元添加高亮标记(用于在绘制时标红边框)
        MatchingPostProcesser.spotlightingUnmatchedFigureElements(toBeMatchedFigureElements, matchedMaterials);

        if (debug) {
            // 打印所有匹配到的素材
            this._log_debug_matched_materials(roomItem, matchedMaterials, toBeMatchedFigureElements, logger);

            // 打印所有未匹配到的组合类图元
            if (unMatchedGroupElement.length >= 1) {
                this.log_unmatch_group_elements(roomItem, unMatchedGroupElement, logger);
            }
        }

        // 如果台面家具匹配到组合素材，则隐藏附加在台面家具的装饰图元
        MatchingPostProcesser.hideDecorationMaterialIfNeeded(toBeMatchedFigureElements);


    }

    async furnishRoomFor3dPreview(roomItem: TRoom, seriesSampleItem: TSeriesSample, logger: Logger) {
        let toBeMatchedFigureElements: TFigureElement[] = roomItem._furniture_list.filter((fe) => !fe.haveMatchedMaterial() && (!fe._3dpreview_matched_material || !fe._3dpreview_matched_material.modelId));

        if (toBeMatchedFigureElements.length == 0) {
            if (debug) {
                logger.log("Nothing need to match.");
            }
            return null;
        }

        let groupElements: TFigureElement[] = toBeMatchedFigureElements.filter((fe) => {
            if (fe.furnitureEntity instanceof TBaseGroupEntity) {
                return true;
            }
        });

        let allMemberElements: TFigureElement[] = [];
        groupElements.forEach((ge) => {
            ge.matched_rect && ge.matched_rect.copy(ge.rect);
            ge.disassembledElements.forEach((de) => {
                if (!de.haveMatchedMaterial()) {
                    allMemberElements.push(de);
                }
            });
        });

        toBeMatchedFigureElements.push(...allMemberElements);

        if (debug) this._log_debug_to_be_matched_elements(roomItem, toBeMatchedFigureElements, logger);

        let organizationId: string = null;
        await MaterialService.match3dPreviewMaterials(toBeMatchedFigureElements,
            seriesSampleItem.seriesKgId as number,
            seriesSampleItem.seriesName,
            seriesSampleItem.seedSchemeId,
            organizationId,
            roomItem.room_type,
            roomItem.area,
            logger.traceId);

        let allMatchedMaterials: I_MaterialMatchingItem[] = [];
        toBeMatchedFigureElements.forEach((fe) => {
            if (fe.modelLoc == "Default") {
                return;
            }
            if (fe._3dpreview_matched_material && fe._3dpreview_matched_material.modelId) {
                allMatchedMaterials.push(fe._3dpreview_matched_material);
            }

        });

        MatchingPostProcesser.adjust3dPreviewMaterialsAndFigureElements(roomItem, allMatchedMaterials);

        if (debug) this._log_debug_matched_materials(roomItem, allMatchedMaterials, toBeMatchedFigureElements, logger);

        await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials(toBeMatchedFigureElements, true);
        return toBeMatchedFigureElements;
    }

    checkAndUpdateDefaultMaterialIds(toBeMatchedFigureElements: TFigureElement[], allSoftMaterials: I_MaterialMatchingItem[]) {
        toBeMatchedFigureElements.forEach((fe) => {
            if (fe.modelLoc == "Default") {
                // fe.highlight = true;
                return;
            }
            if (!fe._matched_material?.modelId) {
                fe.clearMatchedMaterials();
            }
            if (!fe._matched_material) {
                if (this.useDefaultMaterial) {
                    if (!fe._default_matched_material) {
                        fe.updateDefaultMaterialMatchingItemByDefaultModelId();
                    }
                    if (fe._default_matched_material) {
                        fe._matched_material = { ...fe._default_matched_material };
                        fe._matched_material.figureElement = fe;
                    }
                }
            }

            if (fe._matched_material && fe._matched_material.modelId) {
                allSoftMaterials.push(fe._matched_material);
            }
            else {
                fe.clearMatchedMaterials();
            }

        });
    }

    makeHardFurnishingElements(roomItem: TRoom) {
        let toBeMatchedFigureElements: TFigureElement[] = [];
        if (TMaterialMatchingConfigs.roomTypesSupportCeilling.has(roomItem.room_type)) {
            if (!roomItem._ceilling_list) {
                TPostLayoutCeiling.instance.addCurtainBoxCeiling(roomItem);
                TPostLayoutCeiling.instance.postAddCeilingFigures(roomItem);
            }
            toBeMatchedFigureElements.push(...roomItem._ceilling_list);
        }

        roomItem._door_figure_list = [];
        if (roomItem._room_entity) {
            let win_rects = roomItem._room_entity._win_rects;

            for (let win_rect of win_rects) {
                let win_entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win_rect) as TWindowDoorEntity;
                if (win_entity) {
                    let room_types = win_entity.roomtypes;
                    room_types.sort((a, b) => TMaterialMatchingConfigs.roomTypesOrders.indexOf(b) - TMaterialMatchingConfigs.roomTypesOrders.indexOf(a));
                    if (room_types.length >= 2 && room_types[0] !== roomItem._room_entity.room_type) {
                        continue;
                    }
                    win_entity.initWinFigureElement();
                    toBeMatchedFigureElements.push(win_entity._win_figure_element);
                    if (win_entity._win_figure_element.modelLoc.indexOf("门") >= 0) {
                        win_entity._win_figure_element.params.entityId = win_entity.uidN;
                        roomItem._door_figure_list.push(win_entity._win_figure_element);
                    }
                }
            }
        }

        toBeMatchedFigureElements.push(roomItem.wallTexture);
        toBeMatchedFigureElements.push(roomItem.tile);

        // console.log(roomItem._wallTexture,roomItem._tile,toBeMatchedFigureElements);
        let doorModelLocName = TMaterialMatchingConfigs.roomType2DoorModellocMap.get(roomItem.roomname);
        if (doorModelLocName == null) {
            doorModelLocName = '其它门';
        }
        if (roomItem.doors) {
            for (let door of roomItem.doors) {
                let door_figure = roomItem._door_figure_list.find(fe => {
                    return fe.rect.rect_center.x == door.pos_x && fe.rect.rect_center.y == door.pos_y;
                });
                if (door_figure != null) {
                    door_figure.params.entityId = door.id;
                    continue;
                }
                let fe = TFigureElement.createSimple(doorModelLocName);
                fe._room = roomItem;
                fe.length = door.length;
                fe.width = door.width;
                fe.height = door.height;
                fe.params.entityId = door.id;
                fe.rect.rect_center = new Vector3(door.pos_x, door.pos_y, door.pos_z);
                roomItem._door_figure_list.push(fe);
                toBeMatchedFigureElements.push(fe);
            }
        }

        roomItem.haveGeneratedHardElements = true;

        return toBeMatchedFigureElements;
    }

    makeFixtureElements(seriesKgId: string | number, seriesKgName: string = "") {
        let elements: TFigureElement[] = [];
        for (let model_loc in this._fixture_candidates) {
            let _d_candidate = this._fixture_candidates[model_loc].find((val) => val.seriesKgId == seriesKgId);
            if (_d_candidate) {
                elements.push(_d_candidate.figure_element);
            }
            else {
                let figure_ele = TFigureElement.createSimple(model_loc);
                figure_ele._is_decoration = true;
                figure_ele.params.length = 100;
                figure_ele.params.depth = 100;
                figure_ele.params.height = 100;
                figure_ele.rect._w = 100;
                figure_ele.rect._h = 100;
                figure_ele.rect.updateRect();
                this._fixture_candidates[model_loc].push({
                    seriesKgId: '' + seriesKgId,
                    seriesKgName: seriesKgName,
                    figure_element: figure_ele,
                });
                elements.push(figure_ele);
            }
        }
        return elements;
    }

    getFixtureElement(model_loc:string, seriesKgId:string="")
    {
        let data = this._fixture_candidates[model_loc];
        if(!data) return null;
        
        let default_ele = data[0];
        if(!default_ele) return null;
        
        let target_ele = data.find((ele)=>ele.seriesKgId==seriesKgId);
        let result_ele = target_ele || default_ele;
        return result_ele.figure_element;
    }

    /**
     * 对匹配到的素材进行位置调整、尺寸调整的后处理
     * @param allMatchedMaterial 
     * @param room 
     */
    postProcessMatchedMaterials(allMatchedMaterial: I_MaterialMatchingItem[], room: TRoom) {

        MatchingPostProcesser.adjustMatchedMaterialsAndFigureElements(room, allMatchedMaterial);

        MatchingPostProcesser.adjust_backgroundwall_related(room);

        if (room.isCurrentApplyHardCategory() || (room.isCurrentApplyCabinetCategory && room.isApplyHardCategory())) {
            if (room._ceilling_list) {

                TPostLayoutCeiling.instance.adjustCeilingRectByCabinets(room, TMaterialMatchingConfigs._storey_height);
                TPostLayoutCeiling.instance.adjustCeilingTopOffsetByRoom(room);
            }
        }

        

        TPostDecoratesLayout.post_adjust_decorations(room, null, { storey_height: TMaterialMatchingConfigs._storey_height, floor_z: room._room_entity.floor_thickness });

        if (room.isCurrentApplyCabinetCategory()) {
            // TPostProcessLayout.post_adjust_patching_boards(room);
        }
        if (room.isCurrentApplySoftCategory()) {
            TPostLayoutCeiling.instance.adjustCurtainSize(room, TMaterialMatchingConfigs._storey_height, room._room_entity.floor_thickness);
            TPostLayoutCeiling.instance.adjustMaterialPositionByCeiling(room, TMaterialMatchingConfigs._storey_height);
        }

        let allMemberElements: TFigureElement[] = [];
        room._furniture_list.forEach((fe) => {
            if (fe.furnitureEntity instanceof TBaseGroupEntity) {
                return true;
            }
            if (!fe.haveMatchedMaterial() && fe.disassembledElements && fe.disassembledElements.length > 0) {
                allMemberElements.push(...fe.disassembledElements);
            }
        });

        MatchingPostProcesser.hideDuplicatedMaterialOfSameModelloc(room._furniture_list, allMemberElements);
    }


    private log_figure_elements(roomItem: TRoom, figureElements: TFigureElement[], logger: Logger, prefix: string = "") {
        if (!debug || !this._debug) return;
        let logContent = prefix + " Figure Elements " + (roomItem ? ("in " + roomItem.name + roomItem.uid + ": " + roomItem.room_type + "," + roomItem.room_size + "m²") : "");
        figureElements.forEach((fe) => {
            logContent += "\n   " + fe.toString();
        });
        logger.log(logContent);

    }

    private log_unmatch_group_elements(roomItem: TRoom, groupFigureElements: TFigureElement[], logger: Logger) {
        if (!debug || !this._debug) return;
        let logContent = "#############################";
        logContent += "\nUnmatched Group Figure Element in " + roomItem.name + roomItem.uid + ": " + roomItem.room_type + "," + roomItem.room_size + "m²";
        groupFigureElements.forEach((fe) => {
            logContent += "\n   " + fe.toString();
        });
        logger.warn(logContent);

    }

    private _log_debug_to_be_matched_elements(roomItem: TRoom, toBeMatchedFigureElements: TFigureElement[], logger: Logger) {
        if (!debug || !this._debug) return;
        let logContent = "#############################";
        logContent += "\nRoom Layout for " + roomItem.name + roomItem.uid + ": " + roomItem.room_type + "," + roomItem.room_size + "m²";
        toBeMatchedFigureElements.forEach((fe) => {
            logContent += "\n   " + fe.toString();
        });
        logger.log(logContent);

    }

    private _log_debug_matched_materials(roomItem: TRoom, matchedMaterials: I_MaterialMatchingItem[], toBeMatchedFigureElements: TFigureElement[], logger: Logger) {
        if (debug && this._debug) {
            let logContent = "Matched Material List:  (" + roomItem.name + roomItem.uid + ": " + roomItem.room_type + "," + roomItem.room_size + "m²)";
            if (matchedMaterials.length == 0) {
                logContent += "\n    Warning: No matches found!";
            }
            matchedMaterials.forEach((material) => {
                logContent += "\n    " + toStringForMaterialMatchingItem(material);
            });
            logger.log(logContent);
            let failMatchFigureElements: TFigureElement[] = [];
            for (let fi = 0; fi < toBeMatchedFigureElements.length; fi++) {
                let found = false;
                for (let i = 0; i < matchedMaterials.length; i++) {
                    if (matchedMaterials[i].index == fi) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    failMatchFigureElements.push(toBeMatchedFigureElements[fi]);
                }
            };
            if (failMatchFigureElements.length > 0) {
                logger.warn("[MaterialMatchFail] Fail to match material");
                failMatchFigureElements.forEach((fe) => {
                    logger.warn("    " + fe.toString());
                });
            }
            logger.log("Trace ID: " + logger.traceId);
        }
    }

    async generateGroupMemberMatchedEntities(group: TFigureElement) : Promise<TFigureElement[]> {
        let groupMaterialMemberElements: TFigureElement[] = [];
        let groupEntity: TBaseGroupEntity = group.furnitureEntity as TBaseGroupEntity;
        if (groupEntity._matched_combination_entitys.length > 0) return groupMaterialMemberElements;

        let memberMaterials: I_MaterialMatchingItem[] = await MaterialService.getGroupMaterialDetail(groupEntity.figure_element.matchedMaterialId);
        if (memberMaterials == null || memberMaterials.length == 0) return groupMaterialMemberElements;
        groupEntity.setMatchedVisible(true);
        groupMaterialMemberElements = groupEntity.createMemberMatchedEntitiesFromMatchedMaterials(memberMaterials, () => this.update());

        let shouldPrintLog: boolean = false;
        shouldPrintLog = debug && this._debug;

        return groupMaterialMemberElements;
    }

    async replaceMaterial(target_figure: TFigureElement, matchedMaterial: I_MaterialMatchingItem, rooms: TRoom[], options: { post_process_in_room: boolean, needs_update_3d: boolean } = { post_process_in_room: true, needs_update_3d: true }) {
        if (target_figure && !target_figure?.matched_rect) {
            target_figure.matched_rect = target_figure.rect.clone()
        }
        let t_back_center = target_figure.matched_rect.back_center.clone();
        // 定制柜替换后，尺寸还是用原来的尺寸
        if(!FigureCategoryManager.isCustomCabinet(target_figure))
        {
            target_figure.matched_rect.length = matchedMaterial.length;
            target_figure.matched_rect.depth = matchedMaterial.width;
        }
        target_figure.matched_rect.nor = target_figure.rect.nor;
        // 有些模型是背靠墙的去定位的，所以需要特殊处理
        if (compareNames([target_figure.category, target_figure.sub_category, target_figure.modelLoc], TMaterialMatchingConfigs._backto_wall_modelLoc_list) == 1) {
            target_figure.matched_rect.back_center = t_back_center;
        } else {
            target_figure.matched_rect.rect_center = target_figure.rect.rect_center.clone();
        }
        target_figure.matched_rect.updateRect();

        if (target_figure) {

            const oldMatchedMaterial = target_figure._matched_material;
            if (matchedMaterial.isAiCabinet === true) {
                if (target_figure._matched_material) {
                    for (let key in target_figure._matched_material) {
                        if (!(matchedMaterial as any)[key]) (matchedMaterial as any)[key] = (target_figure._matched_material as any)[key];
                    }
                }
                if (!matchedMaterial.modelLoc) {
                    matchedMaterial.modelLoc = target_figure.modelLoc;
                }
                if (!target_figure._candidate_materials.find((material) => {
                    return material.modelId == matchedMaterial.modelId
                })) {
                    let count = 0;
                    let same_name_materials = target_figure._candidate_materials.filter((material) => {
                        if (material.name && material.name.includes(matchedMaterial.name)) {
                            return true;
                        }
                        return false;
                    });
                    matchedMaterial.name = matchedMaterial.name + (same_name_materials.length + 1);
                    target_figure._candidate_materials.push(matchedMaterial);
                }

                matchedMaterial.length = target_figure.length;
                // matchedMaterial.width = figure_element_selected.width;
                // matchedMaterial.height = figure_element_selected.height;
            }

            target_figure._matched_material = matchedMaterial;
            target_figure._matched_material.targetPosition = { x: target_figure.rect.rect_center.x, y: target_figure.rect.rect_center.y, z: target_figure.rect.rect_center.z };
            target_figure._matched_material.targetRotation = { ...oldMatchedMaterial ? oldMatchedMaterial.targetRotation : { x: 0, y: 0, z: target_figure.rect.rotation_z } };
            if (["地毯", "窗帘", "背景墙", "沙发背景墙", " 电视背景墙"].indexOf(target_figure.modelLoc) > -1) {
                target_figure._matched_material.targetSize = { ...oldMatchedMaterial ? oldMatchedMaterial.targetSize : { length: target_figure.rect.w, width: target_figure.params.depth, height: target_figure.params.height } };
            } else {
                target_figure._matched_material.targetSize = { length: matchedMaterial.length, width: matchedMaterial.width, height: matchedMaterial.height };
                if (this.autoResizeProductMaterial) {
                    target_figure.autoResizeMatchedMaterialToModelloc();
                }
            }
            if (oldMatchedMaterial.targetPosition && oldMatchedMaterial.targetPosition.z + oldMatchedMaterial.targetSize.height > 2400) {
                target_figure._matched_material.targetPosition.z = oldMatchedMaterial.targetPosition.z + oldMatchedMaterial.targetSize.height - target_figure._matched_material.targetSize.height;
            }

            matchedMaterial.figureElement = target_figure;
            if (target_figure.furnitureEntity instanceof TBaseGroupEntity) {
                (target_figure.furnitureEntity as TBaseGroupEntity)._matched_combination_entitys = [];
                await this.generateGroupMemberMatchedEntities(target_figure);
                // 线框图加了之后，需要更新组合矩形
                target_figure.furnitureEntity.updateSize();
            }


            if (matchedMaterial.topViewImage) {
                let img = new Image();
                img.src = matchedMaterial.topViewImage + '?x-oss-process=image/resize,m_fixed,w_250,h_250';
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    if (target_figure) {
                        target_figure.pictureViewImg = img;
                        target_figure.pictureViewImgUrl = matchedMaterial.topViewImage;
                        this.update();
                    }
                }
            }
            if (compareNames([target_figure.sub_category], ["地面", "墙面"])) {
                let img = new Image();
                img.src = matchedMaterial.imageUrl;
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    if (target_figure) {
                        target_figure.pictureViewImg = img;
                        if(LayoutAI_App.instance.scene3DManager)
                        {
                            LayoutAI_App.instance.scene3DManager.onElementUpdate(target_figure,{updateTexture:true});
                        }
                        this.update();
                    }
                }
            }
            else {
                delete target_figure.pictureViewImg;
            }


            if (options.post_process_in_room) {
                let theRoom = target_figure._room || rooms.find((room) => room._furniture_list.includes(target_figure));

                if (theRoom) {
                    TMaterialMatcher.instance.postProcessMatchedMaterials([target_figure._matched_material], theRoom);
                }

            }

            if (LayoutAI_App.instance.scene3D && LayoutAI_App.instance.scene3D.isValid() && options.needs_update_3d) {
                let figures_need_update: TFigureElement[] = [];
                if (target_figure.furnitureEntity instanceof TBaseGroupEntity) {
                    let groupEntity = target_figure.furnitureEntity as TBaseGroupEntity;
                    figures_need_update.push(...groupEntity.combination_entitys.filter((entity => entity.figure_element.pictureViewImg == null && entity.figure_element?.haveMatchedMaterial()))
                        .map((entity => entity.figure_element)));
                } else {
                    figures_need_update.push(target_figure);
                }
                LayoutAI_App.instance.scene3DManager.onElementUpdate(target_figure,{updateTexture:true});
                await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials(figures_need_update, true);
            }
        }
        // 处理线框图
        if (target_figure) {
            await FigureTopViewer.instance.updateFigureWireFrameImage(target_figure);
        }
        this.update();
    }

    async getOutLineImage(item:any) {
        // const res = await getMaterialBlockData([
        //     {
        //       materialId: "213775788",
        //       directionType: [
        //           "Top"
        //       ]
        //     }
        // ]);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedMaterial, item);
        // if(res.success) {
        //     // const img_url = 'https://img3.admin.3vjia.com/UpFile/*********/DesignMaterial/202504/22/311039394/Wireframe_Top_2960175354.png?x-oss-process=image/resize,m_fixed,h_400,w_400';
        //     const img_url = await addBlackAndBoldLine('https://img3.admin.3vjia.com/UpFile/*********/DesignMaterial/202504/22/311039394/Wireframe_Top_2960175354.png?x-oss-process=image/resize,m_fixed,h_400,w_400');
        //     item.length = 1600;
        //     item.height = 2000;
        //     let img = new Image();
        //     img.src = img_url;
        //     img.crossOrigin = "Anonymous";
        //     img.onload = () => {
        //         item.wireframeImage = img;
        //         LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedMaterial, item);
        //     }
        // }
    }

    update() {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.update();
        }
    }
}