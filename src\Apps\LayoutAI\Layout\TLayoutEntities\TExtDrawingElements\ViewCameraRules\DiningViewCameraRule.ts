// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { ZEdge } from "@layoutai/z_polygon";
import { BedRoomViewCameraRule } from "./BedRoomViewCameraRule";
export class DiningViewCameraRule extends BaseViewCameraRule {

    static generateViewCamera(dining_element: TFigureElement, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}): TViewCameraEntity[] {
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);
        let view_cameras: TViewCameraEntity[] = [];
        let dining_rect = dining_element?.matched_rect || dining_element?.rect;
        let dining_entity = room_entity.furniture_entities.find(f => compareNames([...f.category], ["餐边柜"]));
        if(dining_element.furnitureEntity instanceof TBaseGroupEntity)
        {
            dining_element.furnitureEntity.combination_entitys.forEach((ele)=>{ 
                if(compareNames([ ele.category], ["餐桌"]))
                {
                    dining_rect = ele.matched_rect || ele.rect;
                }
            })
        }
        else
        {
            dining_rect = dining_element?.matched_rect || dining_element.rect;
        }

        const min_hallway_length = 1500;
        for(let edge of dining_rect.edges){
            if (!edge) return;
            let int_data = room_entity._room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone().negate());
            if (!int_data || !int_data.point) return;

            let target_center = edge.center;
            let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
            
            // 这个是计算离餐桌的距离，小于700mm的，不考虑
            let add_dist = Math.abs(dining_rect._w - edge.length) < 10 ? dining_rect._h : dining_rect._w; 
            let dist_to_back_wall = int_data.point.clone().sub(edge.center).length() - add_dist;
            if(dist_to_back_wall < 700) continue;
            let max_dist = dist_to_front_wall + min_hallway_length;
            // 聚焦模式在墙内
            // if (options.no_focus_mode) {
            //     max_dist = dist_to_front_wall + 300;   // 聚焦模式下, 相机离墙300mm
            // }
            max_dist = 2000 + add_dist; 
            let t_dist = max_dist;
            let t_rect = new ZRect(500, 500);
            t_rect.nor = edge.nor;
            t_rect.zval = 1150; // 默认高度还是高一些1400mm更合理
            let dist_step = 200;

            let iter = 20;
            while (iter--) {
            let pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -t_dist });

            t_rect.rect_center = pos;

                TViewCameraEntity.updateCameraByRect(camera, t_rect);

                let mvp_matrix = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);


                let s_p0 = TViewCameraEntity.cameraProjectPos(mvp_matrix, edge.v0.pos);

                let xx = Math.abs(s_p0.x);
                let yy = Math.abs(s_p0.y);

                let ml = Math.max(xx, yy);

                if (ml < 0.40 || t_dist > max_dist - 10) {
                    break;
                }

                t_dist += dist_step;
                if (t_dist > max_dist) t_dist = max_dist;
            }
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(t_rect);

            view_camera._room_entity = room_entity;
            let view_camera_name = prefix;
            if (!options.back_edge_only) {
                view_camera_name = prefix + ((edge._edge_id + 1) % dining_rect.length || dining_rect.length);
            }
            view_camera.name = '餐厅-餐桌视角';
            if(dining_entity && dining_entity.rect.nor.dot(edge.nor) > 0.9)
            {
                view_camera.name = '餐厅-朝向餐边柜';
                view_camera._target = ['餐边柜'];
            } else if(dining_entity && dining_entity.rect.nor.dot(edge.nor) < -0.9)
            {
                view_camera.name = '餐厅-朝向餐桌正面';
                view_camera._target = ['餐桌'];
            } else
            {
                view_camera.name = '餐厅-侧方';
                view_camera._target = ['餐桌','餐边柜'];
            }

            view_camera._is_focus_mode = true;
            view_camera._main_rect = dining_rect;
            view_camera.hideFurnitures = this.hideFurnitures;
            if (!view_camera.is_focus_mode) {
                view_camera.near = 600;
            }

            // 默认65
            view_camera.fov = 70;
            // 如果离餐桌的距离大于700mm，并且离墙的距离小于1200mm, fov设置为75
            // if(dist_to_back_wall > 700 && dist_to_back_wall < 1200){
            //     view_camera.fov = 75;
            // }
        
            // if (options.enableHideFurnitures) {
            //     let view_rect = view_camera.rect.clone();
            //     view_rect.back_center = view_rect.rect_center;
            //     view_rect._h = Math.max(view_rect._h / 2, view_camera.near);
            //     view_rect.updateRect();

            //     let furniture_list = room_entity?._room?.furnitureList || [];

            //     furniture_list.forEach((ele) => {
            //         let furnitureEntity = ele.furnitureEntity;
            //         if(dining_rect.rect_center.distanceTo(furnitureEntity.rect.rect_center) < 10)
            //         {
            //             return;
            //         }
            //         if (furnitureEntity && furnitureEntity.height > 2000 && compareNames([ele.sub_category, ele.category], ["窗帘"]) == 0) {
            //             let has_int = furnitureEntity.rect.intersect_rect(view_rect);
            //             if (has_int) {
            //                 view_camera._hide_furniture_entities.push(furnitureEntity);
            //             }
            //         } else 
            //         {
            //             let new_edge = new ZEdge({pos: view_rect.rect_center}, {pos: dining_rect.rect_center});
            //             let rect = new ZRect(new_edge.length, 10);
            //             rect.nor = edge.nor.clone().cross(new Vector3(0, 0, 1));

            //             rect.rect_center = new_edge.center;
            //             rect.updateRect();
            //             if(furnitureEntity instanceof TBaseGroupEntity)
            //             {
            //                 furnitureEntity.combination_entitys.forEach((sub_entity)=>{
            //                     if(sub_entity.rect.intersect_rect(rect))
            //                     {
            //                         view_camera._hide_furniture_entities.push(sub_entity);
            //                     }
            //                 })
            //             }
            //             else
            //             {
            //                 let has_int = rect.intersect_rect(ele.rect);
            //                 // TViewCameraEntity._test_rect = rect;
            //                 if (has_int) {
            //                     view_camera._hide_furniture_entities.push(furnitureEntity);
            //                 } 
            //             }
            //         }
            //     })
            // }
            view_camera._view_center = dining_rect.rect_center;
            view_camera._room_entity = room_entity;
            view_cameras.push(view_camera);
        }
        // view_cameras.push(view_camera);
        return view_cameras;
    }
}