import { IRoomEntityRealType } from "../../IRoomInterface"
import { GroupSpaceCategory } from "../TGraphConfigureInterface"


export var DefaultOcclusionRules: {
    [key: string]: {
        structure_types: IRoomEntityRealType[],
        allow_group_space_categories?: GroupSpaceCategory[],
        allow_figure_categories?: string[], occlusion_dir_extend?: string, occlusion_front_extend?: string
    }[]
} = {
        "Default": [
            {
                structure_types: ["OneWindow", "BayWindow"],
                allow_group_space_categories: ["窗帘区", "钻石形淋浴房区", 
                "水槽地柜", "地柜收口板", "吊柜收口板", "转角地柜", "地毯区"],
                allow_figure_categories: ["窗帘", "钻石形淋浴房区", "水槽地柜",
                    "地柜收口板", "吊柜收口板", "转角地柜", "地毯", "单人沙发",
                    "休闲椅", "背景墙", "餐边柜", "餐桌", "餐椅", "多人沙发","直排沙发", "转角沙发", "筒灯", "吊灯", "马桶", "毛巾架","玄关柜"],
                occlusion_dir_extend: '-Math.max(t_l / 4, 100)',
                occlusion_front_extend: ''+50
            },
            {
                structure_types: ["DoubleDoor", "SingleDoor"],
                allow_group_space_categories: ["地毯区", "窗帘区", "餐桌区"],
                allow_figure_categories: [ "地毯", "窗帘", "餐桌", "餐椅", "筒灯", "吊灯","床"],
                occlusion_dir_extend: ''+ -100,
                occlusion_front_extend: ''+750
            },
            {
                structure_types: ["SlidingDoor"],
                allow_group_space_categories: [],
                allow_figure_categories: ["休闲椅","直排沙发","脚踏","餐椅","书椅","餐桌","书桌","窗帘","双开帘","地毯","落地灯"],
                occlusion_dir_extend: '-t_l * 0.35',
                occlusion_front_extend: ''+100
            },

        ],
        "厨房": [

        ],
        "阳台": [

        ],
        "卫生间": [
            {
                structure_types: ["OneWindow", "BayWindow"],
                allow_group_space_categories: ["窗帘区", "钻石形淋浴房区","一字形淋浴房区",  "水槽地柜", "地毯区","马桶区"],
                allow_figure_categories: ["窗帘", "钻石形淋浴房", "一字形淋浴房","矩形淋浴房","浴室柜", "马桶","花洒","吸顶灯"],
                occlusion_dir_extend: ''+-100,
                occlusion_front_extend: ''+10
            },
            {
                structure_types: ["DoubleDoor", "SingleDoor"],
                allow_group_space_categories: [],
                allow_figure_categories: ["吸顶灯","多头吊灯","毛巾架"],
                occlusion_dir_extend: ''+-100,
                occlusion_front_extend: ''+750
            },
            {
                structure_types: ["Flue", "Envelope_Pipe", "Pillar"],
                allow_group_space_categories: [],
                allow_figure_categories: ["马桶", "毛巾架", "浴室柜"],
                occlusion_dir_extend: ''+10,
                occlusion_front_extend: ''+10
            },
        ],
        "卧室":[
            {
                structure_types: ["OneWindow", "BayWindow"],
                allow_group_space_categories: ["窗帘区", "钻石形淋浴房区", 
                "水槽地柜", "地柜收口板", "吊柜收口板", "转角地柜", "地毯区","卧床区","书桌区"],
                allow_figure_categories: ["窗帘", "钻石形淋浴房区", "水槽地柜",
                    "地柜收口板", "吊柜收口板", "转角地柜", "地毯", "单人沙发",
                    "休闲椅", "背景墙", "餐边柜", "餐桌", "餐椅", "多人沙发","直排沙发", 
                    "转角沙发", "筒灯", "吊灯", "马桶", "毛巾架","玄关柜","床","书桌","书椅","梳妆台","书桌","书柜"],
                occlusion_dir_extend: '-Math.max(t_l / 4, 100)',
                occlusion_front_extend: ''+50
            }
        ]

    }

