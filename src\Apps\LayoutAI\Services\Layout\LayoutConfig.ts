
/**
* @description 布局配置
* <AUTHOR>
* @date 2025-07-29 15:07:29
* @lastEditTime 2025-07-29 15:07:29
* @lastEditors xuld
*/
export class LayoutCategoryConfig {
    static readonly CATEGORY_WARDROBE: string = "衣柜";
    static readonly CATEGORY_BED: string = "床";
}

/**
* @description 布局尺寸配置
* <AUTHOR>
* @date 2025-07-29 15:53:21
* @lastEditTime 2025-07-29 15:53:21
* @lastEditors xuld
*/
export class LayoutSizeConfig {
    // 门禁区大小
    public static readonly DOOR_SIZE: number = 900;
    // 过道区宽度
    public static readonly CLEARANCE_WIDTH: number = 700;
    // 床默认长度
    public static readonly DEFAULT_BED_LENGTH: number = 1800;
    // 床默认宽度
    public static readonly DEFAULT_BED_DEPTH: number = 1500;
    // 衣柜默认深度
    public static readonly DEFAULT_WARDROBE_DEPTH: number = 600;
}

/**
* @description 房间类型配置
* <AUTHOR>
* @date 2025-07-29 15:53:21
* @lastEditTime 2025-07-29 15:53:21
* @lastEditors xuld
*/
export class RoomTypeConfig {
    static readonly CATEGORY_BEDROOM: string = "卧室";
}
