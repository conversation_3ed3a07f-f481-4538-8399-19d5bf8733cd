import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LayoutAiCadDataParser } from "./LayoutAiCadDataParser";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TRoom } from "../../TRoom";
import { TSeriesSample } from "../../TSeriesSample";
import SunvegaAPI from "@api/clouddesign";
import { ApplyTo3DService } from "../../../Services/Sunvega3D/ApplyTo3DService";
import { formatCurrentTime } from "@layoutai/z_polygon";
import { CustomerInfo, HouseTypeParam, LayoutSchemeService } from "../../../Services/Basic/LayoutSchemeService";
import { deflate_to_base64_str } from "@/Apps/LayoutAI/Utils/xml_utils";
import { designer_id, designer_open_id } from "@/config";
import { Vector3 } from "three";
import { LayoutSchemeAppletData } from "./LayoutSchemeAppletData";
import { LayoutSchemeXmlJsonParser } from "./LayoutSchemeXmlJsonParser";
import { TBaseEntity } from "../TBaseEntity";
import { ZRect } from "@layoutai/z_polygon";
import { TBaseGroupEntity } from "../TBaseGroupEntity";



export class LayoutSchemeJsonSaver {

    static saveLayoutSchemeImage(width: number = 1200, height: number = 1200, fixed_scale: number = 0, save_room_name: boolean = false, selected_room: TRoom = null) {
        if (!LayoutAiCadDataParser.Container) {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutAiCadDataParser.Container;
        let canvas = document.createElement("canvas") as HTMLCanvasElement;

        canvas.setAttribute("crossorigin", "anonymous");


        canvas.width = width;
        canvas.height = height;

        let origin_canvas = container.painter._canvas;
        let ts = container.painter.exportTransformData();
        container.painter.bindCanvas(canvas);

        let s_pc = container.painter._p_sc;
        container.painter._p_sc = 0.1;

        container.painter._p_sc = height / (origin_canvas.height) * s_pc;
        container.focusCenter();

        if (fixed_scale > 0) {
            
            container.updateWholeBox();
            let size = container._whole_bbox.getSize(new Vector3());
            let center = container._whole_bbox.getCenter(new Vector3());
            let sc_0 = width / size.x;
            let sc_1 = height / size.y;
            container.painter.p_center = center;
            container.painter._p_sc = Math.min(sc_0, sc_1) * fixed_scale;
        }
        else {
            container.painter._p_sc = height / (origin_canvas.height) * s_pc;

        }

        container.painter.enter_drawpoly();

        let manager = container.manager;

        if (manager.layer_DefaultBatchLayer) {
            manager.layer_DefaultBatchLayer.onDraw();
        }
        // if (manager.layer_CadFurnitureLayer) {
        //     manager.layer_CadFurnitureLayer.onDraw();
        // }
        // if (manager.layer_CadCabinetLayer) {
        //     manager.layer_CadCabinetLayer.onDraw();
        // }
        // if (manager.layer_CadRoomFrameLayer) {
        //     manager.layer_CadRoomFrameLayer.onDraw();
        // }
        // if (manager.layer_OutLineLayer) {
        //     manager.layer_OutLineLayer.onDraw();
        // }
        // if (save_room_name) {
        //     manager.layer_CadRoomNameLayer.onDraw();
        // }

        if (selected_room) {
            container.painter.strokeStyle = "#147ffA";
            container.painter.fillStyle = "#ff3";

            container.painter.fillPolygons([selected_room.room_shape._poly], 0.1);
            container.painter._context.lineWidth = 2;
            container.painter.strokePolygons([selected_room.room_shape._poly]);
        }

        container.painter.leave_drawpoly();


        container.painter.bindCanvas(origin_canvas);
        container.painter.importTransformData(ts, false);

        let img_url = canvas.toDataURL();
        // document.body.removeChild(canvas);
        return img_url;

    }

    static async saveSchemeLayout2Json(): Promise<string> {
        if (!LayoutAiCadDataParser.Container) {
            LayoutAiCadDataParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutAiCadDataParser.Container;
        return await LayoutSchemeJsonSaver.saveSchemeLayout2JsonAs({
            schemename: container._layout_scheme_name,
            schemeId: container._layout_scheme_id,
            username: container._customer_info?.cntactMan,
            telephone: container._customer_info?.mobile,
            address: container._customer_info?.address,
        });
    }


    static saveEntityImage(entity: TBaseEntity | TBaseGroupEntity) {
        let canvas: HTMLCanvasElement = document.createElement('canvas');
        let painter = (LayoutAI_App.instance as TAppManagerBase).layout_container.painter;
        let ts = painter.exportTransformData();
        let main_canvas = painter._canvas;
        canvas = canvas || document.createElement("canvas") as HTMLCanvasElement;
        let origin_sc = painter._p_sc;
        // 修改这里：使用 entity.rect 的实际宽高
        canvas.width = entity.rect.w / 4 || 200;
        canvas.height = entity.rect.h / 4 || 200;

        painter.bindCanvas(canvas);
        let t_rect = new ZRect(entity.length || 10000, entity.depth || 10000);

        t_rect._nor.copy({ x: 0, y: 1, z: 0 });

        t_rect.updateRect();
        if (entity instanceof TBaseGroupEntity) {
            // 由于 canvas 尺寸已经与 entity.rect 匹配，这里可以简化缩放计算
            let w_sc = canvas.width / entity.rect.w;
            let h_sc = canvas.height / entity.rect.h;

            painter._p_sc = Math.min(w_sc, h_sc);
            painter.p_center = entity.rect.rect_center;
            painter.clean();
            painter.enter_drawpoly();

            painter._context.lineWidth = 1;
            for (let ele of entity.combination_entitys) {
                ele.figure_element.drawFigure(painter,false,null,12,"#000000");
            }
            painter.leave_drawpoly();
        }
        painter.bindCanvas(main_canvas);
        painter.importTransformData(ts);
        painter._p_sc = origin_sc;
        return canvas.toDataURL();
    }

    /**
     * 方案另存为
     * @param schemeParams  
     * @param enableDownload 
     * @param enableUpload 
     * @returns 
     */
    static async saveSchemeLayout2JsonAs(
        schemeParams:
            {
                schemename: string,
                username: string,
                telephone: string,
                address: string,
                schemeId: string,
            },
        enableDownload: boolean = false, enableUpload: boolean = true): Promise<string> {
        LayoutAI_App.emit(EventName.SaveProgress, {
            progress: "ongoing"
        });
        if (!LayoutSchemeXmlJsonParser.Container) {
            LayoutSchemeXmlJsonParser.Container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        let container = LayoutSchemeXmlJsonParser.Container;

        let wireFrameImageJsonUrl = await container.exportWireFrameImageJson();
        let houseSchemeJsonObj = container.toXmlSchemeData();
        houseSchemeJsonObj.wireFrameImageJsonUrl = wireFrameImageJsonUrl;


        // 这一段是会保留从3D来的xml。
        //scope.updateSwjLayoutDataXml();
        //houseSchemeJsonObj.xml_str = scope.current_swj_layout_data.xml_str;

        // 这个是重新生成墙体，但从3D来的，可能有问题。
        if (container.needs_making_wall_xml) {
            houseSchemeJsonObj.xml_str = container.makeXmlStr();
        } else {
            houseSchemeJsonObj.xml_str = container.current_swj_layout_data.xml_str;
        }

        let room2SeriesSampleMap: Map<TRoom, TSeriesSample> = new Map();
        for (let room of container._rooms) {
            if (room._series_sample_info) {
                room2SeriesSampleMap.set(room, room._series_sample_info);
            }
        }

        let cameraArray: Array<SunvegaAPI.BasicBiz.CameraOption> = [];
        (houseSchemeJsonObj as any)["matchedLayout"] = ApplyTo3DService.instance.generateMatchedMaterialJson(room2SeriesSampleMap, cameraArray);
        (houseSchemeJsonObj as any)["room2Series"] = {};
        for (let [room, seriesSample] of room2SeriesSampleMap.entries()) {
            (houseSchemeJsonObj as any)["room2Series"][room.uid] = seriesSample.exportData();
        }
        if (cameraArray.length > 0) {
            (houseSchemeJsonObj as any)["cameras"] = cameraArray;
        }


        if (enableDownload) {
            const dataStr = JSON.stringify(houseSchemeJsonObj, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            // console.log(scope.manager.layout_container._src_swj_layout_data);
            // 弹出输入框让用户输入文件名
            let fileName = prompt("当前布局搭配即将保存到本地，请输入文件名：", 'scheme_' + formatCurrentTime() + '.json');

            // 如果用户取消输入，则不进行下载
            if (fileName === null) return;

            // 创建临时的<a>标签用于下载
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName; // 使用用户输入的文件名

            document.body.appendChild(a);
            a.click();

            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        if (enableUpload) {
            let svjSchemeName = schemeParams?.schemename || container._layout_scheme_name || container.current_swj_layout_data.name || '未命名';
            let svjSchemeId = container.manager.layout_container.current_swj_layout_data.scheme_id;
            let layoutSchemeImage = container.coverImageUrl || (container.saveLayoutSchemeImage(1200, 1200, 0.9));
            let svjSchemeArea: number = 0;

            let funcRequire = container.manager.layout_container._funcRequire;


            container._rooms.forEach((room: TRoom) => {
                svjSchemeArea += room.area;
            });
            let ruleId = null;
            if ((houseSchemeJsonObj as any)["matchedLayout"].length > 0) {
                ruleId = (houseSchemeJsonObj as any)["matchedLayout"][0].ruleId;
            }

            container._layout_scheme_name = svjSchemeName;

            let roomLayoutInfoList = [];

            for (let [roomItem, seriesSampleItem] of room2SeriesSampleMap.entries()) {
                roomLayoutInfoList.push({
                    area: roomItem.area,
                    roomName: roomItem.roomname,
                    ruleId: seriesSampleItem.ruleId,
                    seriesKgId: seriesSampleItem.seriesKgId,
                    uid: roomItem.uid
                });
            }

            const houseSchemeBase64 = deflate_to_base64_str(JSON.stringify(houseSchemeJsonObj));
            // 客户信息
            let customerInfo: CustomerInfo = {
                cntactMan: schemeParams.username,
                mobile: schemeParams.telephone,
                address: schemeParams.address
            } as CustomerInfo;

            let houseTypeParam: HouseTypeParam = container._houseTypeParam;
            // houseTypeParam.imageUrl = layoutSchemeImage;  //还要上传组件

            // 组装小程序需要的数据
            let appletUrl = await LayoutSchemeAppletData._makeAppletData();

            // 小程序分享设计师记录相关Id
            let designerId = designer_id;
            let designerOpenId = designer_open_id;

            return LayoutSchemeService.saveLayoutScheme(
                schemeParams.schemeId,
                svjSchemeName,
                houseSchemeBase64,
                layoutSchemeImage,
                roomLayoutInfoList,
                svjSchemeId,
                ruleId,
                svjSchemeArea,
                customerInfo,
                houseTypeParam,
                appletUrl,
                designerId,
                designerOpenId,
                funcRequire
            )
                .then((layoutSchemeId) => {
                    if (layoutSchemeId != null) {
                        container._layout_scheme_id = layoutSchemeId;
                        if (container._manager && LayoutAI_App.IsDebug) {
                            if (localStorage) {
                                houseSchemeJsonObj.layout_scheme_id = layoutSchemeId;
                                // console.log("saving local... ");
                                localStorage.setItem("layout_ai_scheme_xml_json", JSON.stringify(houseSchemeJsonObj));
                            }

                        }
                        LayoutAI_App.emit(EventName.SaveProgress, {
                            progress: "success",
                            id: layoutSchemeId,
                            name: svjSchemeName
                        });
                        container._customer_info = customerInfo;
                        LayoutAI_App.emit(EventName.SaveSchemeSuccess, {});
                        return layoutSchemeId;
                    } else {
                        LayoutAI_App.emit(EventName.SaveProgress, { progress: "fail" });
                        return null;
                    }
                }).catch((e: any): string => {
                    console.error(e);
                    LayoutAI_App.emit(EventName.SaveProgress, { progress: "fail" });
                    return null;
                });
        } else {
            return null;
        }
    }

}