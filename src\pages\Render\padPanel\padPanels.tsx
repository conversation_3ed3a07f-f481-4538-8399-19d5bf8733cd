import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import SchemeList from "../SchemeList/suggestLayout";
import FigureLabelList from '../materialList/Menu/figureLabelList';
import { useStore } from "@/models";
import { Segmented } from "@svg/antd";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { AI_PolyTargetType, DrawingFigureMode, IRoomEntityType, SceneViewMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { Vector3 } from "three";
import { LightMainEvents } from "../lightMain/lightMain";
import SideToolbar from "./components/sideToolbar/sideToolbar";
import { useNavigate } from "react-router-dom";
import AiDrawingGallery from "@/components/ImageGallery/aiDrawingGallery";
import PadStatusbar from "../StatusBar/padStatusbar";
import { LayoutPopEvents } from "../layoutPopup/layoutPopup";
import Sharebar from "@/components/Sharebar/sharebar";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { CameraViewMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import DrawPicture from "./components/drawPicture/drawPicture";
import { LightRuleService } from "@/Apps/LayoutAI/Scene3D/light/rule/LightRuleService";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import ExitBar from "./components/ExitBar";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { CSSTransition } from 'react-transition-group';
import MaterialReplace from "../MaterialReplace/materialReplace";
import Icon from "@/components/Icon/icon";
import { AI_CadData } from "@/Apps/LayoutAI/AICadData/AI_CadData";
import { TWindowDoorEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity";
import { AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import SelectRoom from "./components/selectRoom/selectRoom";
import { OutlineMode } from '@/Apps/LayoutAI/Scene3D/SceneMode';

const stateData = {
    popupType: "Layout",
    sceneMode: "2D",
    prev3DSceneMode: "3D_FirstPerson"
}
type SceneModes = SceneViewMode;
/**
 * 平板左侧的Panel
 * @returns 
 */
const PadPanels: React.FC<{ 
  updateKey: number, 
  isPanelOpen: boolean, 
  layoutType: string,
  setChosedModeFun: (mode: string) => void,
  updateCanvasForViewMode?: (targetViewMode: string, delay?: number) => void,
}> = ({ updateKey, isPanelOpen, layoutType, setChosedModeFun, updateCanvasForViewMode }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const navigate = useNavigate();
    const store = useStore();
    const { 
        viewMode,
        roomEntities,
        isdrawPicture,
        setIsdrawPicture,
        setViewMode,
        setSelectEntity,
        setShowReplace,
        setShowSaveLayoutSchemeDialog
    } = store.homeStore;
    const [isCollapse, setIsCollapse] = useState<boolean>(false);
    const [isShowShareBar, setIsShowShareBar] = useState<boolean>(false);
    const [sceneMode, setSceneMode] = useState<SceneModes>(viewMode as SceneModes);
    const [popupType, setShowPopupType] = useState<"Layout" | "Matching" | "view" | "material" | "attribute" | "replace" | "searchMaterial" | string>(stateData.popupType);
    const [selectedFigureElement, setSelectedFigureElement] = useState<TFigureElement>(null);
    const [bottomBtnItems, setBottomBtnItems] = useState<{ icon?: string, label?: string, onClick?: () => void, loading?: boolean }[]>([]);

    const [leftTabItems,setLeftTabItems] = useState<{label:string,value:string}[]>( [
        {
            value: "Layout",
            label: t('推荐布局'),
        },
        {
            value: "material",
            label: t('编辑布局'),
        }
    ]);
    const object_id = "PadLeftPanel";

    const manager = (LayoutAI_App.instance as TAppManagerBase);
    const layoutContainer = manager.layout_container;

    const [isNeedCleanLight, setIsNeedCleanLight] = useState<boolean>(false); // 是否需要清除灯光

    const [chooseMode, setChooseMode] = useState<string>('AI生成');
    const chooseModeList = ['AI生成', '户型匹配']
    /**
     *   由小黑条促发的属性
     */
    const StatusPopUpItems: { [key: string]: { value: string, label: string }[] } = {
        'attribute': [
            {
                value: 'attribute',
                label: t("属性")
            }
        ],
        'sizeEditor': [
            {
                value: 'sizeEditor',
                label: t("尺寸")
            }
        ],
        'SpaceAreaAttribute': [
            {
                value: 'SpaceAreaAttribute',
                label: t("属性")
            }
        ]   
    }

    const updateLeftTabItems = (needs_check_popup = false) => {
        if (manager.layout_container) {
            if (StatusPopUpItems[popupType]) {
                leftTabItems.length = 0;
                leftTabItems.push(...StatusPopUpItems[popupType]);
            }
            else if (sceneMode === "2D") {
                console.log(manager.layout_container.drawing_figure_mode);
                if (manager.layout_container.drawing_figure_mode == DrawingFigureMode.Figure2D) {
                    leftTabItems.length = 0;
                    leftTabItems.push(...[
                        {
                            value: "Layout",
                            label: t('推荐布局'),
                        },
                        {
                            value: "material",
                            label: t('编辑布局'),
                        }
                    ]
                    )
                }
                else {
                    leftTabItems.length = 0;
                    leftTabItems.push(...[
                        {
                            value: "Matching",
                            label: t('推荐风格'),
                        },
                        // {
                        //     value: "replace",
                        //     label: t('换搭素材'),
                        // }
                    ]
                    )
                }
            }
            else {
                leftTabItems.length = 0;
                leftTabItems.push(...[
                    {
                        value: "replace",
                        label: t('换搭素材'),
                    }
                ]
                )
            }
        }
        if (!selectedFigureElement) // 如果没有选中图元
        {
            const id = leftTabItems.findIndex(data => data.value === "replace");
            if (id >= 0) leftTabItems.splice(id, 1);
        }
        if (needs_check_popup) {
            const hasData = leftTabItems.find(data => data.value === popupType);
            if (!hasData) {
                onChangePopUpType(leftTabItems[0]?.value || "");
            }
        }
        setLeftTabItems([...leftTabItems]);
    }

    const saveLayoutSchemeAndNext = async () => {
        await TSeriesFurnisher.instance.autoApplySeries(); // 自动应用系列
        setSceneMode(stateData.prev3DSceneMode as any);
        // store.homeStore.setViewMode('3D_FirstPerson');
        const scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
        if(scene3D)
        {
          scene3D.outlineMaterialMode = OutlineMode.WhiteModelOutline;
        }
    }

    const updateBottomBtnsItems = () => {
        if (sceneMode === "2D") {
            const items = [
                {
                    label: t("下一步"),
                    onClick: async () => {
                        // console.log(stateData, stateData.prev3DSceneMode);
                        // await TSeriesFurnisher.instance.autoApplySeries();
                        // setSceneMode(stateData.prev3DSceneMode as any);
                        if (!layoutContainer._layout_scheme_id && layoutContainer._room_entities.length > 0) {
                            setShowSaveLayoutSchemeDialog({show: true, source: 'nextBtn'}); // 先要保存
                        } else if(layoutContainer._layout_scheme_id) {
                            saveLayoutSchemeAndNext();
                        }
                    }
                }
            ].filter((item) => item);
            setBottomBtnItems(items);
        }
        else if (sceneMode === "3D") {
            const items = [
                {
                    label: t("返回布局"),
                    onClick: () => {
                        setSceneMode("2D");
                    }
                }
            ]
            setBottomBtnItems(items);
        }
        else if (sceneMode === "3D_FirstPerson") /*[i18n:ignore]*/ {
            const items = [
                {
                    label: t("上一步"),
                    onClick: () => {
                        setSceneMode("2D");
                    }
                },
            ]
            setBottomBtnItems(items);
        }
        if(isdrawPicture){
            handleDrawPicBtnClick();
        }
    }

    const handleDrawPicBtnClick = () => {
        // 更新按钮
        setBottomBtnItems([]);
    }

    useEffect(() => {
        updateBottomBtnsItems();
    }, [sceneMode, isdrawPicture]);

    const onChangePopUpType = (type: string) => {
        stateData.popupType = type;
        setShowPopupType(type);
    }

    useEffect(() => {
        if(isNeedCleanLight){
            // console.log("清除灯光效果");
            LightRuleService.cleanLight();  //清除灯光效果
            setIsNeedCleanLight(false);
        }
    }, [isNeedCleanLight])

    const onFigureElementChanged = (figureElement: TFigureElement, type: IRoomEntityType) => {

        if (figureElement && figureElement === selectedFigureElement) return;
        if (layoutContainer.drawing_figure_mode === DrawingFigureMode.Figure2D && sceneMode === "2D") {
            if (type === "Furniture") {
                if (stateData.popupType === "Layout") // 自动跳转到编辑布局
                {
                    onChangePopUpType("material");
                }
            }
            else {
                onChangePopUpType("Layout");
            }

        }
        else {
            if (type === "Furniture" || type === "Door") {
                if (figureElement) {
                    onChangePopUpType("replace");
                }

            }
            else {
                if (figureElement && stateData.popupType == "replace") {
                    // 啥都别干
                    onChangePopUpType("replace");

                }
                else {
                    onChangePopUpType("Matching");
                }
            }
        }


        setSelectedFigureElement(figureElement);
    }
    const onSceneModeChanged = (sceneMode: SceneModes) => {
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let scene3D =  (LayoutAI_App.instance).scene3D as Scene3D;
        if (sceneMode === "2D") {
            if (scene3D) {
                scene3D.stopRender();
            }
            if (window.innerWidth < window.innerHeight * 0.8) {
                LayoutContainerUtils.focusCenterByWholeBox(container, 0.7);
                LayoutAI_App.instance.update();
            }
            else {
                LayoutContainerUtils.focusCenterByWholeBox(container, 0.6);
                LayoutAI_App.instance.update();
            }
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, false);
            setViewMode('2D');
        }
        else if (sceneMode === "3D") {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.Match3dPreviewMaterials, null);
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, true);

            scene3D.setCemeraMode(CameraViewMode.Perspective);
            setViewMode('3D');
            // 进入3D预览的时候, 需要清空选中的图元
            LayoutAI_App.DispatchEvent(LayoutAI_Events.cleanSelect, null);
            // scene3D.setCenter(container.painter.p_center);
            // scene3D.update();
            if (scene3D) {
                scene3D.startRender();
                LayoutAI_App.emit_M(EventName.Scene3DUpdated, false);

            }
        } else if (sceneMode === "3D_FirstPerson") /*[i18n:ignore]*/ {

            LayoutAI_App.DispatchEvent(LayoutAI_Events.Match3dPreviewMaterials, null);
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, true);
            scene3D.setCemeraMode(CameraViewMode.FirstPerson);

            if (viewMode == "2D") {
                let room = container._room_entities.reduce((maxRoom: TRoomEntity | null, currentRoom: TRoomEntity) => {
                    if (!maxRoom) return currentRoom;
                    return currentRoom._area > maxRoom._area ? currentRoom : maxRoom;
                }, null);
                if (room) {
                    if(container){
                        // 暂时先这么判断吧
                        if(room._view_cameras.length == 0)
                        {
                            TViewCameraEntity.updateViewCameraEntities(container,null,{methods: 2});
                            console.log('room._view_cameras', room._view_cameras);
                        }
                        scene3D.active_controls.bindViewEntity(room._view_cameras[0]);
                        store.homeStore.setCurrentViewCameras(room._view_cameras);
                    } else 
                    {
                        scene3D.setCenter(room?._main_rect?.rect_center || new Vector3(0, 0, 0));
                    }
                    scene3D.update();
                } else {
                    scene3D.setCenter(roomEntities[0]?._main_rect?.rect_center || new Vector3(0, 0, 0));
                }
            }

            setViewMode('3D_FirstPerson');  /*[i18n:ignore]*/
            if (scene3D) {
                scene3D.startRender();
                LayoutAI_App.emit_M(EventName.Scene3DUpdated, false);

            }
        }
        if (sceneMode && sceneMode !== "2D") {
            stateData.prev3DSceneMode = sceneMode;
        }
        stateData.sceneMode = sceneMode;
        updateLeftTabItems(true);
    }

    const onBackBtnClick = () => {
        // 根据当前viewMode状态决定返回行为
        if ((layoutType === 'view' && viewMode === '3D_FirstPerson') || viewMode === '2D') {
            // 2D全屏时，返回EnterPage组件
            store.homeStore.setShowEnterPage({show: true, source: 'backBtn'});
        } else {
            // 2D和3D左右分屏时，切换到2D全屏模式
            setSceneMode('2D');
            store.homeStore.setViewMode('2D');
            store.homeStore.setZIndexOf3DViewer(-2);
            
            // 使用统一的画布更新函数
            if (updateCanvasForViewMode) {
                updateCanvasForViewMode('2D', 400);
            }
        }
        // 将chooseMode修改成默认的AI生成
        setChooseMode('AI生成');
    }

    // 关闭整个页面
    const handleClose = () => {
        if (!layoutContainer._layout_scheme_id && layoutContainer._room_entities.length > 0) {
            setShowSaveLayoutSchemeDialog({show: true, source: 'exitBtn'}); // 先要保存
        } else {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
            // 发送消息，请求关闭iframe
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'closeIframe',
                data: {
                    canClose: true
                }
            }, '*');
        }
    }

    useEffect(()=>{
        // 点击下一步时没有保存方案弹出保存方案弹窗后，收到消息后保存方案并进入下一步
        const handleMessage = (event: MessageEvent) => {
            if (event.data.type === 'saveLayoutSchemeAndNext') {
                saveLayoutSchemeAndNext();
            }
        };
        window.addEventListener('message', handleMessage);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, [])

    useEffect(() => {
        updateLeftTabItems();
        updateBottomBtnsItems();
        LayoutAI_App.on_M(EventName.FigureElementSelected, object_id, (figure) => {

            let type = AI_CadData.get_polygon_type(figure?.rect);
            if(type == AI_PolyTargetType.Door){
                onFigureElementChanged(figure, AI_PolyTargetType.Door);
            }else{
                onFigureElementChanged(figure, AI_PolyTargetType.Furniture);
            }
        })
        LayoutAI_App.on_M(EventName.SelectingTarget, object_id, (params, event, pp) => {

            if (sceneMode != "2D") return;
            let Entity: TBaseEntity = params || null;

            if ((Entity as TFurnitureEntity)?.figure_element) {
                onFigureElementChanged((Entity as TFurnitureEntity).figure_element, "Furniture");
            }
            else if (Entity?.type == AI_PolyTargetType.RoomArea) {
                let room = (Entity as TRoomEntity)._room;
                if (room?.tile) {
                    onFigureElementChanged(room.tile, "RoomArea");
                }
                else {
                    onFigureElementChanged(null, "RoomArea");
                }
            }
            else if (Entity?.type == AI_PolyTargetType.Door)  {
                let door = (Entity as TWindowDoorEntity)?._win_figure_element;
                onFigureElementChanged(door, AI_PolyTargetType.Door);
            }else{
                onFigureElementChanged(null, "RoomArea");
            }

            setSelectEntity(Entity);
            if (!Entity) {
                setShowReplace(false);
            }


            let roomEntity = Entity as TRoomEntity;
            if (roomEntity && roomEntity?._room) {

                TSeriesFurnisher.instance.current_rooms = [roomEntity._room];
                TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: true });

            }
            else {
                if (layoutContainer._rooms) {
                    let rooms = layoutContainer._rooms.filter((room) => room && room.furnitureList.length > 0);
                    TSeriesFurnisher.instance.current_rooms = rooms;
                    TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering({ clickOnRoom: null });
                }
            }
        });

        LayoutAI_App.on_M(LayoutPopEvents.showPopup, object_id, (popupType) => {
            onChangePopUpType(popupType);
        });
        LayoutAI_App.on_M(EventName.SceneModeChanged, object_id, (i_sceneMode: SceneModes) => {
            setSceneMode(i_sceneMode);
        });
        // fetchConfig();
        return () => {
            LayoutAI_App.off_M_All({ object_id: object_id });


        }
        
    }, [sceneMode]);

    useEffect(() => {
        if (viewMode === "2D") {
            setSceneMode("2D");
        } 
        else if (viewMode === "3D_FirstPerson") {
            setSceneMode("3D_FirstPerson");

            
        }
    }, [viewMode]);

    useEffect(() => {
        onSceneModeChanged(sceneMode);
    }, [sceneMode]);

    useEffect(() => {
        // 每当viewMode变化时，更新点击渲染出图按钮的状态为false
        setIsdrawPicture(false);
        setIsNeedCleanLight(true); // 每次切换到2D模式时，需要清除灯光
    },[viewMode]);

    useEffect(() => {
        setChosedModeFun(chooseMode);
    }, [chooseMode]); // AI生成和户型出图的转换，并传递消息给父组件

    const isLeftPanelOpen = !isCollapse;
    const IsLandscape = window.innerWidth > window.innerHeight;
    return (
        <div>
            <div className={styles.navigation + " topNavigation"}>
                {/* {is_dreamer_mini_App ? 'true' : 'false'} */}
                {!store.homeStore.isdrawPicture && <div className={styles.header}>
                    <div className='icon' onClick={() => {
                        onBackBtnClick();
                    }}>
                        <Icon style={{fontSize: '20px'}} iconClass="icon-a-fangxiangzuo" />
                    </div>
                    <div className='center'>
                        {!isPanelOpen && <Segmented
                            options={chooseModeList}
                            shape='round'
                            value={chooseMode}
                            defaultValue={'AI生成'}
                            className='segmented'
                            onChange={(value) => {
                                setChooseMode(value)
                            }}
                        />}
                    </div>
                    <div className='icon' onClick={() => {
                        console.log('点击关闭')
                        handleClose();
                    }}>
                        <Icon style={{fontSize: '20px'}} iconClass="icon-close1" />
                    </div>
                </div>}
                {isShowShareBar && <div className="" style={{ top: 50, right: 12, position: 'fixed', zIndex: "999", background: "#fff", padding: "10px" }}>
                    <Sharebar onClose={() => {
                        setIsShowShareBar(false);
                    }}></Sharebar>
                </div>}
            </div>
            {isdrawPicture && 
                <ExitBar></ExitBar>
            }

            {isPanelOpen && <div className={styles.sideToolbarContainer + " sideToolbar " + (sceneMode != "2D" ? "is_3d_mode" : "")}>
                <SideToolbar setSceneMode={setSceneMode}></SideToolbar>
            </div>}
            {/* 渲染出图左侧面板不显示 */}
            {sceneMode === '2D' && isPanelOpen && 
                <div id="pad_left_panel" className={styles.leftPanelRoot + " leftPanelRoot " + (!isLeftPanelOpen ? "panel_hide" : "")} >
                    {isLeftPanelOpen && <div className="closeBtn iconfont iconclose1" onClick={() => setIsCollapse(true)}></div>

                    }
                    {isLeftPanelOpen && leftTabItems.length > 1 &&
                        <div className={styles.tabBar}>
                            {/* <Segmented value={popupType} onChange={(val) => {
                                onChangePopUpType(val);
                            }} block options={leftTabItems} /> */}
                            <div className='tabItem'>
                                {leftTabItems.map((item, index) => (
                                    <div key={index}
                                        className={'item' + (popupType === item.value ? ' active' : '')}
                                        onClick={() => {
                                            onChangePopUpType(item.value);
                                        }}
                                    >
                                        <div className='label'>{item.label}</div>
                                        <div className='line'></div>
                                    </div>
                                ))}
                            </div>
                        </div>}
                    <div className={styles.popupContainer + " side_pannel"}>
                        <div className={styles.listContainer} style={{ display: (popupType === "Layout" ? 'block' : 'none') }}> 
                            <div className='dropdown'>
                                <SelectRoom></SelectRoom>
                            </div>
                            <SchemeList width={400} showSchemeName={false} isLightMobile={true}></SchemeList> 
                        </div>
                        <div className={styles.listContainer} style={{ display: (popupType === "material" ? 'block' : 'none') }}> <FigureLabelList ></FigureLabelList> </div>
                    </div>
                </div>
            }
            {(leftTabItems.length > 0) && isPanelOpen && sceneMode=='2D' && <div className={styles.collapseBtn + (!isLeftPanelOpen ? " panel_hide iconfont iconfill_right" : " iconfont iconfill_left")} onClick={() => {
                setIsCollapse(!isCollapse);
            }}>

            </div>}
            <CSSTransition 
                in={viewMode != '2D' && !isdrawPicture && selectedFigureElement && isPanelOpen} 
                timeout={300} 
                classNames={{
                    enter: 'fadeEnter',
                    enterActive: 'fadeEnterActive',
                    exit: 'fadeExit',
                    exitActive: 'fadeExitActive'
                }}
                unmountOnExit
                >
                <div 
                    id="pad_left_panel" 
                    className={`${styles.leftPanelRoot} ${viewMode != '2D' ? styles.materialReplace : ''}`}
                >
                    <MaterialReplace selectedFigureElement={selectedFigureElement} />
                </div>
            </CSSTransition>
            <PadStatusbar></PadStatusbar>

            {isPanelOpen && <div className={styles.bottomButtons + " bottomBtns" + (isLeftPanelOpen ? " showLeftPanel" : "")}>
                
                {
                    bottomBtnItems && bottomBtnItems.length > 0 && bottomBtnItems.map((item, index) => (
                        <div
                            className={'btn' + (sceneMode !== "2D" ? ' blackColor' : '')}
                            key={"bottomBtn_" + index}
                            onClick={item?.onClick}
                        >
                        {item?.label}
                        </div>
                    ))
                }
            </div>}
            {isPanelOpen && 
                <div className={styles.layoutPlusButton}
                    onClick={() => {
                        console.log('跳转AI布局plus');
                    }}
                >
                    <Icon iconClass="icongallery1" style={{fontSize: '16px', color: '#5C42FB'}}/>
                    <div className='text'>跳转AI布局plus</div>
                </div>}
            <AiDrawingGallery></AiDrawingGallery>
            {isdrawPicture && <DrawPicture />}
        </div>
    )

};


export default observer(PadPanels);