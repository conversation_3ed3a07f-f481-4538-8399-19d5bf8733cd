import React, { useCallback, useEffect, useState } from 'react';
import useStyles from './style';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { Schema } from 'form-render';
import { useTranslation } from 'react-i18next'
import { useStore } from '@/models';
// import EzdxfRightPanel from '../EzdxfRightPanel/ezdxfRightPanel';
import RoomAreaBtns from '../roomAreaBtns/roomAreaBtns';
import { Input, InputNumber, Select, Slider } from '@svg/antd';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { LayoutContainerUtils } from '@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils';

interface Properties {
  name: { defaultValue: number; onChange: (value: number) => void };
  ceiling_height: { defaultValue: number; onChange: (value: number) => void };
  height: { defaultValue: number; onChange: (value: number) => void };
  floor_thickness: { defaultValue: number; onChange: (value: number) => void };
  storey_height: { defaultValue: number; onChange: (value: number) => void };
}


const MyComponent: React.FC = () => {
  const store = useStore();
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [update, setUpdate] = useState(0);
  const [locked, setLocked] = useState<number>(-1);
  const [title, setTile] = useState<string>('');
  const [schema, setSchema] = useState<Schema>({});
  const [panelType, setPanelType] = useState<number>(0);
  const [visible, setVisible] = useState<boolean>(false);
  const [properties, setProperties] = useState<any>({});
  const [name, setName] = useState<any>();
  const [ceiling_height, setCeiling_height] = useState<any>();
  const [storey_height, setStorey_height] = useState<any>();
  const [floor_thickness, setFloor_thickness] = useState<any>();
  const [max_cabinet_height, setMax_cabinet_height] = useState<any>();

  const [bedroomNum, setBedroomNum] = useState(0);
  const [livingRoomNum, setRivingRoomNum] = useState(0);
  const [toiletNum, setToiletNum] = useState(0);
  const [area, setArea] = useState(0);
  const [kitchen, setKitchen] = useState(0);

  let update_timestamp = 0;

  const [inputValue, setInputValue] = useState(1)

  const onChange = (newValue: number) => {
    setInputValue(newValue)
  }

  useEffect(() => {

    if (store.homeStore.attribute?.title == '空间信息' || store.homeStore.attribute?.tile == t('空间信息'))   /*[i18n:ignore]*/
    {
      setProperties(store.homeStore.attribute.properties);
      setName(store.homeStore.attribute?.properties?.name?.defaultValue);
      setStorey_height((store.homeStore.attribute?.properties?.storey_height?.defaultValue));
      setCeiling_height(store.homeStore.attribute?.properties?.ceiling_height?.defaultValue);
      setFloor_thickness(store.homeStore.attribute?.properties?.floor_thickness?.defaultValue);
      setMax_cabinet_height(store.homeStore.attribute?.properties?.max_cabinet_height?.defaultValue);
    }
  }, [store.homeStore.attribute])

  useEffect(() => {
    if (!store.homeStore.roomInfos) return;
    let area = 0;
    let bedroomCount = 0;
    let livingRoomCount = 0;
    let toiletCount = 0;
    let kitchen = 0;
    store.homeStore.roomInfos.forEach((room: any) => {
      area += room.area;
      if (room.name.includes('室') || room.name.includes('卧')) {
        bedroomCount++;
      }
      if (room.name.includes('厅')) {
        livingRoomCount++;
      }
      if (room.name.includes('厕所') || room.name.includes('卫生间')) {
        toiletCount++;
      }
      if (room.name.includes('厨房')) {
        kitchen++;
      }
    });
    setArea(parseFloat(area.toFixed(2)));
    setBedroomNum(bedroomCount);
    setRivingRoomNum(livingRoomCount);
    setToiletNum(toiletCount);
    setKitchen(kitchen);
    setStorey_height((LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height);
  }, [store.homeStore.roomInfos])



  const handleChange = useCallback((setter: React.Dispatch<React.SetStateAction<number>>, property: keyof Properties) => {
    return (value: any) => {
      setter(value);
      properties?.[property]?.onChange(value);

      // 更新空间，更新别名
      if(property === 'name'){
        LayoutContainerUtils.updateAliasName((LayoutAI_App.instance as TAppManagerBase).layout_container);
        store.homeStore.setRoomEntites((LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities);
      }
    };
    
  }, [properties]);


  const wholeVertical = () => {
    return (
      <>
        <div className='left'>
          <div>
            <div className='title'>{t('房屋使用面积')}</div>
            <div>
              <Input disabled={true} style={{ width: '100%' }} value={area} suffix={'m²'}></Input>
            </div>
          </div>
          <div className='title'>{t('户型')}</div>
          <div className='houseInfo'>
            <Input className='input' disabled={true} value={bedroomNum}></Input><span>{t('室')}</span>
            <Input className='input' disabled={true} value={livingRoomNum}></Input><span>{t('厅')}</span>
            <Input className='input' disabled={true} value={toiletNum}></Input><span>{t('卫')}</span>
            <Input className='input' disabled={true} value={kitchen}></Input><span>{t('厨')}</span>
          </div>
        </div>

        <div className='right'>
          <div className='title'>{t('当前层高')}</div>
          <div className='rightInfo'>
            <Slider
              min={2000}
              max={3500}
              onChange={(valueStr: any) => {
                const storeyHeight: number = Number(valueStr);
                if (storeyHeight >= 2200 && storeyHeight <= 6000) {
                  (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
                }
                setStorey_height(valueStr);
              }}
              style={{ width: '50%' }}
              value={storey_height}
            />
            <InputNumber
              min={2000}
              max={3500}
              style={{ width: '100px' }}
              value={storey_height}
              onChange={(valueStr) => {
                const storeyHeight: number = Number(valueStr);
                if (storeyHeight >= 2200 && storeyHeight <= 6000) {
                  (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
                }
                setStorey_height(valueStr);
              }}
              suffix={'mm'}
            />
          </div>
        </div>
      </>
    )
  }

  const wholeHorizontal = () => {
    return (
      <>
        <div>
          <div className='title'>{t('房屋使用面积')}</div>
          <div>
            <Input disabled={true} style={{ width: '100%' }} value={area} suffix={'m²'}></Input>
          </div>
        </div>
        <div className='title'>{t('户型')}</div>
        <div className='houseInfo'>
          <div>
            <Input className='input' disabled={true} value={bedroomNum}></Input><span>{t('室')}</span>
          </div>
          <div>
            <Input className='input' disabled={true} value={livingRoomNum}></Input><span>{t('厅')}</span>
          </div>
          <div>
            <Input className='input' disabled={true} value={toiletNum}></Input> <span>{t('卫')}</span>
          </div>
          <div>
            <Input className='input' disabled={true} value={kitchen}></Input> <span>{t('厨')}</span>
          </div>
        </div>
        <div className='title'>{t('当前层高')}

          <InputNumber
            min={2000}
            max={3500}
            style={{ width: '100px' }}
            value={storey_height}
            onChange={(valueStr) => {
              const storeyHeight: number = Number(valueStr);
              if (storeyHeight >= 2200 && storeyHeight <= 6000) {
                (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
              }
              setStorey_height(valueStr);
            }}
            suffix={'mm'}
          />
        </div>
        <div className='rightInfo'>
          <Slider
            min={2000}
            max={3500}
            onChange={(valueStr: any) => {
              const storeyHeight: number = Number(valueStr);
              if (storeyHeight >= 2200 && storeyHeight <= 6000) {
                (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
              }
              setStorey_height(valueStr);
            }}
            style={{ width: '100%' }}
            value={storey_height}
          />

        </div>

      </>
    )
  }


  const singleVertical = () => {
    return (
      <>
        <div className='left'>
          <div className='title'>{t('空间类型')}</div>
          <div>
            <Select
              value={name}
              style={{ width: '100%' }}
              onChange={handleChange(setName, 'name')}
              options={TRoomEntity.getRoomNameOptions()}
            />
          </div>

          <div className='title'>{t('当前层高')}</div>
          <div className='leftInfo'>
            <Slider
              min={0}
              max={2800}
              onChange={handleChange(setStorey_height, 'storey_height')}
              style={{ width: '100%' }}
              value={storey_height}
              disabled={true}
            />
            <InputNumber
              min={0}
              max={2800}
              disabled={true}
              style={{ width: '100px' }}
              value={storey_height}
              onChange={(e) => handleChange(setStorey_height, 'storey_height')(Number(e))}
              suffix={'mm'}
            />
          </div>

          <div className='title'>{t('地铺厚度')}</div>
          <div className='leftInfo'>
            <Slider
              min={0}
              max={100}
              onChange={handleChange(setFloor_thickness, 'floor_thickness')}
              style={{ width: '100%' }}
              value={floor_thickness}
            />
            <InputNumber
              min={0}
              max={100}
              style={{ width: '100px' }}
              value={floor_thickness}
              onChange={(e) => handleChange(setFloor_thickness, 'floor_thickness')(Number(e))}
              suffix={'mm'}
            />
          </div>


        </div>
        <div className='right'>
          <div>
            <div className='title'>{t('最高柜顶高')}</div>
            <div>
              <Input disabled={true} style={{ width: '100%' }} value={max_cabinet_height} suffix={'mm'}></Input>
            </div>
          </div>
          <div className='title'>{t('吊顶下吊')}</div>
          <div className='rightInfo'>
            <Slider
              min={200}
              max={400}
              onChange={handleChange(setCeiling_height, 'ceiling_height')}
              style={{ width: '100%' }}
              value={ceiling_height}
            />
            <InputNumber
              min={200}
              max={400}
              style={{ width: '100px' }}
              value={ceiling_height}
              onChange={(e) => handleChange(setCeiling_height, 'ceiling_height')(Number(e))}
              suffix={'mm'}
            />
          </div>
        </div>
      </>
    )
  }

  const singleHorizontal = () => {
    return (
      <>
        <div className='title'>{t('空间类型')}</div>
        <div>
          <Select
            value={name}
            style={{ width: '100%' }}
            onChange={handleChange(setName, 'name')}
            options={TRoomEntity.getRoomNameOptions()}
            getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
          />
        </div>

        <div className='title'>{t('当前层高')}
        <InputNumber
            min={0}
            max={2800}
            disabled={true}
            style={{ width: '100px' }}
            value={storey_height}
            onChange={(e) => handleChange(setStorey_height, 'storey_height')(Number(e))}
            suffix={'mm'}
          />
        </div>
        {/* <div className='leftInfo'>
          <Slider
            min={0}
            max={2800}
            onChange={handleChange(setStorey_height, 'storey_height')}
            style={{ width: '100%' }}
            value={storey_height}
            disabled={true}
          />
          
        </div>

        <div className='title'>{t('地铺厚度')}
        <InputNumber
            min={0}
            max={100}
            style={{ width: '100px' }}
            value={floor_thickness}
            onChange={(e) => handleChange(setFloor_thickness, 'floor_thickness')(Number(e))}
            suffix={'mm'}
          />
        </div>
        <div className='leftInfo'>
          <Slider
            min={0}
            max={100}
            onChange={handleChange(setFloor_thickness, 'floor_thickness')}
            style={{ width: '100%' }}
            value={floor_thickness}
          /> 
        </div>

        <div>
          <div className='title'>{t('最高柜顶高')}</div>
          <div>
            <Input disabled={true} style={{ width: '100%' }} value={max_cabinet_height} suffix={'mm'}></Input>
          </div>
        </div>
        <div className='title'>{t('吊顶下吊')}
        <InputNumber
            min={200}
            max={400}
            style={{ width: '100px' }}
            value={ceiling_height}
            onChange={(e) => handleChange(setCeiling_height, 'ceiling_height')(Number(e))}
            suffix={'mm'}
          />
        </div>
        <div className='rightInfo'>
          <Slider
            min={200}
            max={400}
            onChange={handleChange(setCeiling_height, 'ceiling_height')}
            style={{ width: '100%' }}
            value={ceiling_height}
          />
          
        </div> */}
      </>
    )
  }


  return (
    <div className={styles.root} >
      {/* {ShowPanel()} */}
      {/* <div className={styles.roomListBar}>
        <RoomAreaBtns />
      </div> */}

      <div className={styles.attributeInfo}>
        {store.homeStore.selectedRoom ?
          <>

            {!store.homeStore.IsLandscape ? singleVertical() : singleHorizontal()}
          </> :

          <>
            {!store.homeStore.IsLandscape ? wholeVertical() : wholeHorizontal()}
          </>
        }
      </div>
    </div>
  );
};


export default observer(MyComponent);
