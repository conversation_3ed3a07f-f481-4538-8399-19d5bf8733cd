import GUI, { Controller } from "lil-gui";
import { Ray } from "three";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { BedRoomSubService } from "@/Apps/LayoutAI/Services/Layout/BedRoomSubService";
import { LayoutRoom } from "@/Apps/LayoutAI/Services/Layout/LayoutRoom";
import { WardrobeDirType } from "@/Apps/LayoutAI/Services/Layout/WardrobeDirType";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { ZEdge, ZPolyline } from "@layoutai/z_polygon";
import { AutoLayoutService } from "@/Apps/LayoutAI/Services/Layout/AutoLayoutService";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";


/**
* @description 自动布局测试 - 支持两种显示模式
* mode 0: 跟随2D画布 - 测试canvas与painter保持完全一致的变换
* mode 1: 屏幕居中 - 在测试canvas中居中显示房间布局
* <AUTHOR>
* @date 2025-07-22
* @lastEditTime 2025-07-22 14:10:36
* @lastEditors xuld
*/
export class AutoLayoutTest {
    private static _isInit: boolean = false;
    private static _gui: GUI;
    private static _canvas: HTMLCanvasElement;
    private static _actFolder: GUI;

    // 0: 跟2D画布一致 1: 屏幕中间点
    private static _mode: number = 0;

    // 当前选中的房间
    private static selRoom: LayoutRoom = null;

    // 卧室布局子服务
    private static _bedRoomSubService = new BedRoomSubService();

    // 显示房间轮廓
    private static showPoly: boolean = true;
    // 显示主功能区
    private static showMainArea: boolean = true;
    // 显示门禁区
    private static showDoorArea: boolean = true;
    // 显示动线边
    private static showActEdges: boolean = true;
    // 显示过道区
    private static showClearanceArea: boolean = true;
    // 显示布置区
    private static showLayoutArea: boolean = true;
    // 当前选中的动线索引
    private static selectedActIndex: number = 0;
    // 显示床头墙和床朝向
    private static showBedHeadWall: boolean = true;
    // 显示衣柜朝向
    private static showWardrobeDir: boolean = true;
    // 当前床的朝向
    private static selectedBedDirIndex: number = 0;
    // 当前柜子朝向
    private static selectedWardrobeDirIndex: number = 0;
    // 显示分区
    private static showSubAreas: boolean = true;


    public static get gui(): GUI {
        if (!this._gui) {
            this._gui = new GUI();
            this._canvas = this.createCanvas();
            // 根据模式选择添加位置
            if (this._mode === 0) {
                // mode 0: 添加到 painter canvas 的父容器中
                const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
                if (painter && painter._canvas && painter._canvas.parentElement) {
                    painter._canvas.parentElement.appendChild(this._canvas);
                } else {
                    document.body.appendChild(this._canvas);
                }
            } else {
                // mode 1: 添加到 body
                document.body.appendChild(this._canvas);
            }
        }
        return this._gui;
    }

    public static get bedRoomSubService(): BedRoomSubService {
        return this._bedRoomSubService;
    }

    public static showPanel() {
        if (this._isInit) {
            return;
        }
        this._isInit = true;

        // 添加模式切换
        this.gui.add({ mode: this._mode }, 'mode', { '跟随2D画布': 0, '屏幕居中': 1 }).name('显示模式').onChange((value: number) => {
            this._mode = value;
            // 重新创建 canvas 以应用新的尺寸设置
            if (this._canvas) {
                this._canvas.remove();
                this._canvas = this.createCanvas();
                // 根据模式选择添加位置
                if (this._mode === 0) {
                    // mode 0: 添加到 painter canvas 的父容器中
                    const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
                    if (painter && painter._canvas && painter._canvas.parentElement) {
                        painter._canvas.parentElement.appendChild(this._canvas);
                    } else {
                        document.body.appendChild(this._canvas);
                    }
                } else {
                    // mode 1: 添加到 body
                    document.body.appendChild(this._canvas);
                }
            }
            this.update();
        });

        // 获取所有房间名称
        const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const roomEntities = layout_container._room_entities;
        const roomOptions: { [key: string]: TRoomEntity } = {};
        roomEntities.forEach((roomEntity, index) => {
            roomOptions[index + "_" + roomEntity.name] = roomEntity;
        });
        if (roomEntities.length === 0) {
            return;
        }

        // 添加房间下拉列表
        this.selRoom = new LayoutRoom(roomEntities[0]);
        this.gui.add({ room: "0_" + this.selRoom.name }, 'room', Object.keys(roomOptions))
            .name('房间')
            .onChange((value: string) => {
                this.selRoom = new LayoutRoom(roomOptions[value]);
                this.selectedActIndex = 0;
                this.selectedBedDirIndex = 0;
                this.selectedWardrobeDirIndex = 0;
                this.updateActUI();
                this.update();
            });

        this.gui.add({ showPoly: this.showPoly }, 'showPoly').name('显示房间轮廓').onChange((value: boolean) => {
            this.showPoly = value;
            this.update();
        });

        this.gui.add({ showMainArea: this.showMainArea }, 'showMainArea').name('显示主功能区').onChange((value: boolean) => {
            console.log("showMainArea changed:", value);
            this.showMainArea = value;
            this.update();
        });

        this.gui.add({ showDoorArea: this.showDoorArea }, 'showDoorArea').name('显示门禁区').onChange((value: boolean) => {
            this.showDoorArea = value;
            this.update();
        });

        this.gui.add({ showActEdges: this.showActEdges }, 'showActEdges').name('显示动线边').onChange((value: boolean) => {
            this.showActEdges = value;
            this.update();
        });

        this.gui.add({ showClearanceArea: this.showClearanceArea }, 'showClearanceArea').name('显示过道区').onChange((value: boolean) => {
            this.showClearanceArea = value;
            this.update();
        });

        this.gui.add({ showFunctionArea: this.showLayoutArea }, 'showFunctionArea').name('显示布置区').onChange((value: boolean) => {
            this.showLayoutArea = value;
            this.update();
        });

        this.gui.add({ showBedHeadWall: this.showBedHeadWall }, 'showBedHeadWall').name('显示床朝向').onChange((value: boolean) => {
            this.showBedHeadWall = value;
            this.update();
        });

        this.gui.add({ showWardrobeDir: this.showWardrobeDir }, 'showWardrobeDir').name('显示衣柜朝向').onChange((value: boolean) => {
            this.showWardrobeDir = value;
            this.update();
        });

        this.gui.add({ showSubAreas: this.showSubAreas }, 'showSubAreas').name('显示分区').onChange((value: boolean) => {
            this.showSubAreas = value;
            this.update();
        });

        this._actFolder = this.gui.addFolder('动线');
        this.updateActUI();


        this.gui.add({
            refresh: () => {
                this.update();
            }
        }, 'refresh').name('刷新');

        // 初始化时调用一次update
        this.update();

        this.gui.add({
            closePanel: () => {
                this.closePanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static updateActUI() {
        if (!this._actFolder) {
            return;
        }
        // 移除之前的控制器
        if (this._actFolder.controllers.length > 0) {
            for (let i = this._actFolder.controllers.length - 1; i >= 0; i--) {
                this._actFolder.controllers[i].destroy();
            }
            this._actFolder.controllers.length = 0;
        }

        const actEdges = this.bedRoomSubService.getActEdges(this.selRoom);
        if (actEdges.length === 0) {
            return;
        }

        // 创建单选框选项对象
        const actOptions: { [key: string]: number } = {};
        for (let i = 0; i < actEdges.length; ++i) {
            actOptions['动线' + i] = i;
        }

        // 添加单选框控制器
        this._actFolder.add({ selectedAct: this.selectedActIndex }, 'selectedAct', actOptions)
            .name('选择动线')
            .onChange((value: number) => {
                this.selectedActIndex = value;
                this.selectedBedDirIndex = 0;
                this.updateActUI();
                this.update();
            });

        const layoutAreas = this.bedRoomSubService.getLayoutAreas(this.selRoom);
        const bedDirList: { bedRay: Ray, bedEdge: ZEdge }[] = this.bedRoomSubService.getBedDir(this.selRoom, layoutAreas[this.selectedActIndex]);
        const bedDirOptions: { [key: string]: number } = {};
        for (let i = 0; i < bedDirList.length; ++i) {
            bedDirOptions['床朝向' + i] = i;
        }

        this._actFolder.add({ selectedBedDir: this.selectedBedDirIndex }, 'selectedBedDir', bedDirOptions)
            .name('选择床朝向')
            .onChange((value: number) => {
                this.selectedBedDirIndex = value;
                this.selectedWardrobeDirIndex = 0;
                this.updateActUI();
                this.update();
            });

        const wardrobeDirList: { ray: Ray, type: WardrobeDirType }[] = this.bedRoomSubService.getWardrobeDir(bedDirList[this.selectedBedDirIndex]);
        const wardrobeDirOptions: { [key: string]: number } = {};
        for (let i = 0; i < wardrobeDirList.length; ++i) {
            const info = wardrobeDirList[i];
            wardrobeDirOptions[info.type] = i;
        }
        this._actFolder.add({ selectedWardrobeDir: this.selectedWardrobeDirIndex }, 'selectedWardrobeDir', wardrobeDirOptions)
            .name('选择衣柜朝向')
            .onChange((value: number) => {
                this.selectedWardrobeDirIndex = value;
                this.update();
            });
    }

    public static closePanel() {
        if (this._gui) {
            this._gui.destroy();
            this._gui = null;
            this._isInit = false;
        }
        if (this._canvas) {
            this.clean();
            this._canvas.remove();
            this._canvas = null;
        }
        if (this._actFolder) {
            this._actFolder.destroy();
            this._actFolder = null;
        }
    }

    private static clean() {
        const ctx = this._canvas.getContext('2d');
        if (ctx) {
            ctx.clearRect(0, 0, this._canvas.width, this._canvas.height);
        }
    }

    private static syncCanvasSize() {
        if (!this._canvas || this._mode !== 0) return;

        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
        if (painter && painter._canvas) {
            // 同步尺寸
            if (this._canvas.width !== painter._canvas.width || this._canvas.height !== painter._canvas.height) {
                this._canvas.width = painter._canvas.width;
                this._canvas.height = painter._canvas.height;
            }

            // 同步样式尺寸
            const painterStyleWidth = painter._canvas.style.width;
            const painterStyleHeight = painter._canvas.style.height;
            if (painterStyleWidth && this._canvas.style.width !== painterStyleWidth) {
                this._canvas.style.width = painterStyleWidth;
            }
            if (painterStyleHeight && this._canvas.style.height !== painterStyleHeight) {
                this._canvas.style.height = painterStyleHeight;
            }

            // 同步样式属性
            const painterCanvas = painter._canvas;
            if (painterCanvas.style.position && this._canvas.style.position !== painterCanvas.style.position) {
                this._canvas.style.position = painterCanvas.style.position;
            }
            if (painterCanvas.style.left && this._canvas.style.left !== painterCanvas.style.left) {
                this._canvas.style.left = painterCanvas.style.left;
            }
            if (painterCanvas.style.top && this._canvas.style.top !== painterCanvas.style.top) {
                this._canvas.style.top = painterCanvas.style.top;
            }
        }
    }

    public static update() {
        this.syncCanvasSize();
        this.clean();

        const layoutStyles = AutoLayoutService.instance.getLayoutStyles(this.selRoom);
        console.log(layoutStyles.length);

        const doorAreas = this.bedRoomSubService.getDoorRestrictedArea(this.selRoom);
        const actEdges = this.bedRoomSubService.getActEdges(this.selRoom);
        const clearanceAreas = this.bedRoomSubService.getClearanceArea(this.selRoom);
        const layoutAreas = this.bedRoomSubService.getLayoutAreas(this.selRoom);
        const bedHeadEdges = this.bedRoomSubService.getBedHeadEdges(this.selRoom, layoutAreas[this.selectedActIndex]);
        const bedDirList: { bedRay: Ray, bedEdge: ZEdge }[] = this.bedRoomSubService.getBedDir(this.selRoom, layoutAreas[this.selectedActIndex]);
        const wardrobeDirList: { ray: Ray, type: WardrobeDirType }[] = this.bedRoomSubService.getWardrobeDir(bedDirList[this.selectedBedDirIndex]);

        if (this.showPoly) {
            this.drawPolys([this.selRoom.roomPoly], '#000000');
        }

        if (this.showMainArea) {
            this.drawPolys([this.selRoom.roomEntity._main_rect], '#00ff00');
        }

        if (this.showDoorArea) {
            if (doorAreas && doorAreas.length > 0) {
                this.drawPolys(doorAreas, '#ff0000');
            }
        }

        if (this.showActEdges) {
            if (actEdges && actEdges.length > 0 && this.selectedActIndex < actEdges.length) {
                this.drawEdges([actEdges[this.selectedActIndex]], '#0000ff');
            }
        }

        if (this.showClearanceArea) {
            if (clearanceAreas && clearanceAreas.length > 0 && this.selectedActIndex < clearanceAreas.length) {
                this.drawPolys([clearanceAreas[this.selectedActIndex]], '#ffa500');
            }
        }

        if (this.showLayoutArea) {
            if (layoutAreas && layoutAreas.length > 0 && this.selectedActIndex < layoutAreas.length) {
                this.drawPolys([layoutAreas[this.selectedActIndex]], '#0000ff');
            }
        }

        if (this.showBedHeadWall) {
            if (bedHeadEdges && bedHeadEdges.length > 0) {
                this.drawEdges([bedHeadEdges[this.selectedBedDirIndex]], '#ff0000');
                this.drawRays([bedDirList[this.selectedBedDirIndex].bedRay], '#ff0000', 400);
            }
        }

        if (this.showWardrobeDir) {
            if (wardrobeDirList && wardrobeDirList.length > 0 && this.selectedWardrobeDirIndex < wardrobeDirList.length) {
                this.drawRays([wardrobeDirList[this.selectedWardrobeDirIndex].ray], '#00ffff', 200);
            }
        }

        if (this.showSubAreas) {
            const areaInfo = this.bedRoomSubService.getWardrobeAndBedArea(layoutAreas[this.selectedActIndex], bedDirList[this.selectedBedDirIndex], wardrobeDirList[this.selectedWardrobeDirIndex]);
            if (areaInfo) {
                if (areaInfo.wardrobeArea)
                    this.drawPolys([areaInfo.wardrobeArea], '#bbffff', true);
                if (areaInfo.bedArea)
                    this.drawPolys([areaInfo.bedArea], '#fffacd', true);
            }
        }
    }

    private static createCanvas() {
        const canvas = document.createElement('canvas');
        canvas.style.position = 'absolute';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '9999'; // 设置最高层级，避免被其他DOM元素遮挡

        // 根据模式设置 canvas 尺寸和位置
        if (this._mode === 0) {
            // mode 0: 与 painter 保持一致
            const painter = (LayoutAI_App.instance as TAppManagerBase).painter;
            if (painter && painter._canvas) {
                canvas.width = painter._canvas.width;
                canvas.height = painter._canvas.height;
                canvas.style.width = painter._canvas.style.width || painter._canvas.width + 'px';
                canvas.style.height = painter._canvas.style.height || painter._canvas.height + 'px';

                // 尝试将测试 canvas 添加到 painter canvas 的父容器中，这样可以保持相对位置
                const painterCanvas = painter._canvas;
                const parentContainer = painterCanvas.parentElement;
                if (parentContainer) {
                    // 使用与 painter canvas 相同的定位方式
                    canvas.style.position = painterCanvas.style.position || 'absolute';
                    canvas.style.left = painterCanvas.style.left || '0px';
                    canvas.style.top = painterCanvas.style.top || '0px';
                    canvas.style.right = painterCanvas.style.right || 'auto';
                    canvas.style.bottom = painterCanvas.style.bottom || 'auto';
                } else {
                    // 如果找不到父容器，使用绝对定位
                    canvas.style.left = '0';
                    canvas.style.top = '0';
                }
            } else {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                canvas.style.left = '0';
                canvas.style.top = '0';
            }
        } else {
            // mode 1: 屏幕居中模式
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            canvas.style.left = '0';
            canvas.style.top = '0';
        }

        return canvas
    }

    private static getMidScreenPos(pos: { x: number, y: number }): { x: number, y: number } {
        const points = this.selRoom.roomPoly.toPoints();
        // 计算坐标范围
        const minX = Math.min(...points.map(p => p.x));
        const maxX = Math.max(...points.map(p => p.x));
        const minY = Math.min(...points.map(p => p.y));
        const maxY = Math.max(...points.map(p => p.y));

        // 计算缩放比例，确保图形能完整显示在canvas中
        const padding = 200; // 留出边距
        const scaleX = (this._canvas.width - padding * 2) / (maxX - minX);
        const scaleY = (this._canvas.height - padding * 2) / (maxY - minY);
        const scale = Math.min(scaleX, scaleY, 1); // 不要放大，只缩小

        // 计算偏移量，使图形居中，Y轴翻转
        const offsetX = (this._canvas.width - (maxX - minX) * scale) / 2 - minX * scale;
        const offsetY = (this._canvas.height - (maxY - minY) * scale) / 2 + maxY * scale;

        return {
            x: pos.x * scale + offsetX,
            y: pos.y * -scale + offsetY
        };
    }

    private static getPainterPos(pos: { x: number, y: number }): { x: number, y: number } {
        const painter = (LayoutAI_App.instance as TAppManagerBase).painter;

        // 使用 project2D 方法，然后转换为相对于 painter canvas 的坐标
        const projected = painter.project2D(pos);

        // 将投影坐标转换为相对于 painter canvas 的坐标
        const canvasX = projected.x + painter._canvas.width / 2;
        const canvasY = projected.y + painter._canvas.height / 2;

        return { x: canvasX, y: canvasY };
    }

    private static drawPolys(polys: ZPolyline[], color: string, fill: boolean = false) {
        const ctx = this._canvas.getContext('2d');

        ctx.strokeStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < polys.length; i++) {
            ctx.beginPath();
            const poly = polys[i];
            const points = poly.toPoints();
            if (points && points.length > 0) {
                const pos0 = this.getLocalPos(points[0]);
                ctx.moveTo(pos0.x, pos0.y);
                for (let j = 1; j < points.length; j++) {
                    const pos = this.getLocalPos(points[j]);
                    ctx.lineTo(pos.x, pos.y);
                }
                ctx.closePath();
            }
            ctx.stroke();
            if (fill) {
                ctx.fillStyle = color;
                ctx.fill();
            }
        }
    }

    /**
    * @description 获取本地坐标, mode 0: 跟2D画布一致 1: 屏幕中间点
    * @param pos 屏幕坐标
    * @return 本地坐标
    */
    private static getLocalPos(pos: { x: number, y: number }): { x: number, y: number } {
        if (this._mode === 1) {
            return this.getMidScreenPos(pos);
        } else {
            return this.getPainterPos(pos);
        }
    }

    private static drawEdges(edges: ZEdge[], color: string) {
        const ctx = this._canvas.getContext('2d');

        ctx.strokeStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < edges.length; i++) {
            ctx.beginPath();
            const edge = edges[i];
            const points = [edge.start_pos, edge.end_pos];
            const pos0 = this.getLocalPos(points[0]);
            ctx.moveTo(pos0.x, pos0.y);

            for (let j = 1; j < points.length; j++) {
                const pos = this.getLocalPos(points[j]);
                ctx.lineTo(pos.x, pos.y);
            }
            ctx.closePath();
            ctx.stroke();
        }
    }

    private static drawRays(dirs: Ray[], color: string, len: number) {
        const ctx = this._canvas.getContext('2d');

        ctx.strokeStyle = color;
        ctx.fillStyle = color;
        ctx.lineWidth = 4;

        for (let i = 0; i < dirs.length; i++) {
            const dir = dirs[i];
            const startPoint = dir.origin;
            const endPoint = dir.origin.clone().add(dir.direction.clone().multiplyScalar(len));

            // 获取本地坐标
            const startPos = this.getLocalPos(startPoint);
            const endPos = this.getLocalPos(endPoint);

            // 绘制射线主体
            ctx.beginPath();
            ctx.moveTo(startPos.x, startPos.y);
            ctx.lineTo(endPos.x, endPos.y);
            ctx.stroke();

            // 计算箭头参数
            const arrowLength = 15; // 箭头长度
            const arrowAngle = Math.PI / 6; // 箭头角度 (30度)

            // 计算箭头方向
            const dx = endPos.x - startPos.x;
            const dy = endPos.y - startPos.y;
            const angle = Math.atan2(dy, dx);

            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(endPos.x, endPos.y);
            ctx.lineTo(
                endPos.x - arrowLength * Math.cos(angle - arrowAngle),
                endPos.y - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(endPos.x, endPos.y);
            ctx.lineTo(
                endPos.x - arrowLength * Math.cos(angle + arrowAngle),
                endPos.y - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
        }
    }
}