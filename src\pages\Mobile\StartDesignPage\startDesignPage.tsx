import React, { useState } from 'react';
import useStyles from './style';
import { PageContainer } from '@ant-design/pro-components';
import MobileHistoricalAtlas from '@/pages/AiDraw/components/MobileHistoricalAtlas';
import { Segmented, Button } from '@svg/antd';
import { observer } from "mobx-react-lite";
import InsertCard from './InsertCard/insterCard';
import SchemeContent from '@/components/MyLayoutSchemeList/Content/schemeContent';
import IconFont from '@/components/IconFont/iconFont';
import { useStore } from '@/models';
import { checkIsMobile, workDomainMap } from '@/config';
import { SdkService } from '@/services/SdkService';
interface StartDesignPageProps {
    toSelectHX: () => void;
    step: number;
}

const StartDesignPage: React.FC<StartDesignPageProps> = ({ toSelectHX, step }) => {
    const { styles } = useStyles();
    const [activeTab, setActiveTab] = useState<string>('我的方案');
    const store = useStore();
    /**
     * @description 是否显示返回图标
     * @description 海尔项目不需要显示回显返回图标
     */
    const showBackIcon = () =>{
        if(store.userStore.isHaiEr && step === -1){
            return 'none';
        } else if(!checkIsMobile() && step === 0)
        {
            return 'none';
        } else 
        {
            return 'flex';
        }
    }

    const backClick = () => {
        SdkService.exitSDK();
        window.parent.postMessage({
            origin: 'layoutai.api',
            type: 'canClose',
            data: {
                canClose: true
            }
        }, '*');
        // store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'null'});
        window.location.href = workDomainMap;
    }
    return (
        <div className={styles.root}>
            <PageContainer 
                title={
                    <div style={{ display: 'flex', alignItems: 'center', fontSize: 24}}>
                        <div className={'back'} onClick={backClick} style={{display: showBackIcon()}}>
                            <IconFont type="icon-line_left" />
                        </div>
                        <span>开始设计</span>
                    </div>
                }
            >
                <div style={{padding: '0 0 20px 0'}}>
                    <InsertCard toSelectHX={toSelectHX}></InsertCard>
                </div>
                <div className={'displayContent'}>
                    <div className='tab'>
                        <Segmented<string>
                            options={['我的方案', '我的图册']}
                            onChange={(value) => {
                                setActiveTab(value);
                            }}
                        />
                    </div>
                    <div className={styles.display}>
                        {activeTab === '我的方案' && (
                            <SchemeContent source='startDesignPage'></SchemeContent>
                        )}
                        {activeTab === '我的图册' && (
                            <MobileHistoricalAtlas setZIndexOfMobileAtlas={null} />
                        )}
                    </div>
                </div>
            </PageContainer>

        </div>
    );
};

export default observer(StartDesignPage);
