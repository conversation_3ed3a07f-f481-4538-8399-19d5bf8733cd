"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4255],{24255:function(e,n,t){t.r(n),t.d(n,{default:function(){return ce}});var r=t(13274),o=t(98612),a=t(84872),i=t(88934),c=t(27347),u=t(44466),s=t(19356),l=t(26966),f=t(51010),h=t(29933),m=t(75887),d=t(50710),p=t(23825),y=t(9003),b=t(31281),v=t(62634),g=t(37112),S=t(49450),w=t(17655),x=t(15696),j=t(41594),A=t(69802),_=t(49063),C=t(49816),I=t(3727),O=t(66742),L=t(84545),M=t(61928),k=t(90974),D=t(52898),N=t(72978),T=t(62954),P=t(25352);function U(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function E(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,c=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){c=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(c)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return U(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return U(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var W=function(e){var n=(0,P.A)().styles,t=E((0,j.useState)(e.item.defaultValue||""),2),o=t[0],a=t[1];(0,j.useEffect)(function(){e.item.onChange&&e.item.onChange(o)},[o]);var i=e.item.options||[],c=(0,r.jsx)(N.A,{popupClassName:n.popupClass,placement:"bottomRight",defaultValue:o,onChange:function(e){a(e)},children:i.map(function(e,n){return(0,r.jsx)("option",{value:e.value,children:e.label},"s_t"+n)})});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:n.label,children:e.item.title}),(0,r.jsx)(T.A,{value:o,size:"small",style:{width:100},addonAfter:c,onChange:function(e){a(e.target.value)},defaultValue:""})]})},R=function(){var e=(0,P.A)().styles,n=((0,_.Zp)(),(0,A.B)().t,E((0,j.useState)(!1),2)),t=n[0],o=n[1],a=E((0,j.useState)([]),2),u=a[0],s=a[1];(0,y.P)(),N.A.Option;return(0,j.useEffect)(function(){c.nb.on(i.U.TopBarItemChanged,function(e){o(e.visible),e.visible?s(e.topBarItems):s([])})},[]),t?(0,r.jsx)("div",{id:"HotelTopBar",className:e.top_container,children:u.map(function(n,t){return"select"===n.type?(0,r.jsx)(W,{item:n},"TopBarItem"+t):"button"===n.type?(0,r.jsx)("button",{className:e.button,id:"btn"+n.id,onClick:n.onClick||null,children:n.title},"TopBarItem"+t):(0,r.jsx)(r.Fragment,{})})}):(0,r.jsx)(r.Fragment,{})},F=t(84787),z=t(67513);function H(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function B(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function X(e,n){return n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):function(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,r)}return t}(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}),e}function G(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,c=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){c=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(c)throw o}}return a}}(e,n)||$(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e){return function(e){if(Array.isArray(e))return H(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||$(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e,n){if(e){if("string"==typeof e)return H(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?H(e,n):void 0}}var Y=function(e){var n=e.onConfirm,t=e.onClose,o=G((0,j.useState)(120),2),a=o[0],i=(o[1],G((0,j.useState)(1.3),2)),u=i[0],s=(i[1],G((0,j.useState)(z.J.spaceNameConfigs),2)),l=s[0],f=s[1],h={defaultWidth:c.nb.t("默认宽度"),minWidth:c.nb.t("最小宽度"),defaultDepth:c.nb.t("默认深度"),minDepth:c.nb.t("最小深度"),targetCount:c.nb.t("目标数量"),ratioWeight:c.nb.t("分布比重")},m=G((0,j.useState)(["布草间","回收间","消洗间"]),2),d=m[0],p=(m[1],function(e,n,t){var r=V(l);r[e]=X(function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.forEach(function(n){B(e,n,t[n])})}return e}({},r[e]),B({},n,"defaultWidth"===n||"defaultDepth"===n?Number(t):t)),z.J.spaceNameConfigs=r,f(r)});return(0,r.jsx)("div",{className:"hotel-room-config-container",children:(0,r.jsxs)("div",{className:"config-content",children:[(0,r.jsx)("div",{className:"closeBtn",onClick:function(){t()},children:"X"}),(0,r.jsx)("div",{className:"config-section",children:(0,r.jsx)("h4",{children:"排房参数配置"})}),(0,r.jsx)("div",{className:"config-section",style:{height:450,overflow:"auto"},children:l.map(function(e,n){return(0,r.jsxs)("div",{className:"config-row",children:[(0,r.jsx)("h4",{className:"config-name",children:e.name}),(0,r.jsx)("div",{className:"config-item-list",children:Object.keys(e).map(function(n,t){return"name"===n?null:(0,r.jsxs)("div",{className:"config-item",children:[(0,r.jsx)("span",{children:h[n]||n}),(0,r.jsx)("input",{type:"number",defaultValue:e[n],onChange:function(e){p(t,n,e.target.value)}})]})}).filter(function(e){return e})})]},"RoomType"+n)})}),(0,r.jsx)("button",{className:"confirm-button",onClick:function(){n({wallThickness:a,roomTypes:l,corridorWidth:u,otherFacilities:d})},children:"确认"})]})})},q=t(65640);function J(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function K(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void t(e)}c.done?n(u):Promise.resolve(u).then(r,o)}function Z(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,c=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){c=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(c)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return J(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return J(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(e,a)}catch(e){c=[6,e],r=0}finally{t=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var ee={roomSize:[{type:"标单/标双动态尺寸链",minArea:29.5,maxArea:33.5,minW:3.8,maxW:5,minL:6,maxL:7.9,minW_L:0,maxW_L:.5,rate:.6,character:"窄开间长条形"},{type:"标单/标双动态尺寸链",minArea:32.5,maxArea:36.5,minW:4.5,maxW:6,minL:5.5,maxL:7.3,minW_L:.667,maxW_L:.833,character:"均衡矩形"},{type:"套房/亲子房动态尺寸链",minArea:49.5,maxArea:53.5,minW:5.8,maxW:7.8,minL:6.4,maxL:9.1}],wallThick:120},ne=function(){var e=(0,P.A)().styles,n=Z((0,j.useState)(!1),2),t=n[0],o=n[1],a=Z((0,j.useState)(ee),2),u=(a[0],a[1],function(){return(e=function(){var e;return Q(this,function(n){return e=F.G.toXmlSchemeData(),JSON.stringify(e),o(!1),[2]})},function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){K(a,r,o,i,c,"next",e)}function c(e){K(a,r,o,i,c,"throw",e)}i(void 0)})})();var e});return(0,j.useEffect)(function(){c.nb.on(i.U.ShowHotelDivisionDialog,function(e){o(e||!1)})},[]),t?(0,r.jsx)("div",{className:e.dialog_container,children:(0,r.jsx)(Y,{onConfirm:function(e){q.log("配置数据:",e),u()},onClose:function(){return o(!1)}})}):(0,r.jsx)(r.Fragment,{})};function te(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function re(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void t(e)}c.done?n(u):Promise.resolve(u).then(r,o)}function oe(e){return function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){re(a,r,o,i,c,"next",e)}function c(e){re(a,r,o,i,c,"throw",e)}i(void 0)})}}function ae(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,c=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){c=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(c)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return te(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return te(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(e,a)}catch(e){c=[6,e],r=0}finally{t=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var ce=(0,x.observer)(function(){var e=(0,A.B)().t,n=(0,I.A)().styles,t=ae((0,j.useState)({opening:!1,title:""}),2),x=t[0],N=(t[1],ae((0,j.useState)(!1),2)),T=N[0],P=N[1],U=ae((0,j.useState)(!1),2),E=U[0],W=U[1],F=ae(v.A.useMessage(),2),z=F[0],H=F[1],B="SaveSchemeProgress",X=ae((0,j.useState)(!1),2),G=(X[0],X[1]),V=ae((0,j.useState)(!1),2),$=V[0],Y=V[1],q=ae((0,j.useState)(window.innerWidth<window.innerHeight),2),J=q[0],K=q[1],Z=ae((0,j.useState)(null),2),Q=Z[0],ee=Z[1],te=(0,_.Zp)();c.nb.UseApp(o.e.AppName),c.nb.instance&&(c.nb.instance._is_landscape=J,c.nb.t=e);var re=(0,j.useRef)(null),ce=(0,y.P)();c.nb.instance&&(c.nb.instance._is_website_debug=p.iG);var ue=function(){c.nb.instance&&(c.nb.instance.bindCanvas(document.getElementById("cad_canvas")),c.nb.instance.update()),c.nb.instance&&(c.nb.instance._is_landscape=J),K(window.innerWidth<window.innerHeight)},se=function(e){return oe(function(){return ie(this,function(e){return P(!1),setTimeout(function(){var e="/design?importType=importHouse&&from=2dedit";p.iG&&(e+="&debug=true"),te(e)},100),[2]})})()};(0,j.useEffect)(function(){if(ee(c.nb.instance.layout_container._layout_scheme_name),ce.homeStore.setAppOpenAsCadPlugin(!0),window.addEventListener("resize",ue),ue(),c.nb.instance){var n;if(c.nb.instance.initialized||(c.nb.instance.init(),c.nb.instance.Configs.prepare_auto_layout=!1,c.nb.instance.Configs.app_specific_domain="Hotel",c.nb.instance.Configs.saving_localstorage_layout_scheme=!0,c.nb.RunCommand(o.f.HotelLayoutMode),c.nb.instance.prepare().then(function(){c.nb.RunCommand(o.f.HotelLayoutMode),oe(function(){var e,n,t;return ie(this,function(r){switch(r.label){case 0:return p.uN?(e={isDelete:0,pageIndex:1,pageSize:9,keyword:p.uN},[4,a.D.getLayoutSchemeList(e)]):[2];case 1:return n=r.sent(),t=n.layoutSchemeDataList,n.total,t&&(c.nb.DispatchEvent(c.n0.OpenMyLayoutSchemeData,t[0]),c.nb.emit(i.U.OpenHouseSearching,!1)),[2]}})})()}),c.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,c.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var r="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(r)),c.nb.instance._debug_mode=r)}}c.nb.instance.update()}c.nb.on(i.U.ShowWallTopMenu,function(e){ce.homeStore.setIsShowWallTopMenu(e)}),b.A.BasicBiz.Room.bindOnOpenSchemeFinish(se),c.nb.on(i.U.setIssueReportVisible,G),c.nb.on(i.U.ShowSubHandlerBtn,W),c.nb.on(i.U.SwitchIntoDesign,se),c.nb.on_M(i.U.RoomList,"room_list1",function(e){ce.homeStore.setRoomInfos(e)}),c.nb.on_M(i.U.SelectingRoom,"cad_home",function(e){setTimeout(function(){ce.homeStore.setSelectData({rooms:null==e?void 0:e.current_rooms,clickOnRoom:!0})},20),ce.homeStore.setCurrenScheme(null==e?void 0:e.event_param)}),c.nb.on(i.U.PerformFurnishResult,function(n){"error"===n.progress?v.A.error(n.message):"info"===n.progress&&v.A.info({key:B,type:"info",content:e(n.message),duration:1,style:{marginTop:"4vh",zIndex:9999}})}),c.nb.on(i.U.OpenMySchemeList,function(){ce.homeStore.setShowMySchemeList(!0)}),c.nb.on(i.U.selectRoom,function(e){ce.homeStore.setSelectedRoom(e)}),c.nb.on(i.U.LayoutSchemeOpening,function(e){ee(e.name)}),c.nb.on(i.U.LayoutSchemeOpened,function(e){ee(e.name)}),c.nb.on(i.U.LayoutSchemeOpenFail,function(e){ee(e.name)}),c.nb.on(i.U.ShowWelcomePage,function(e){ce.homeStore.setShowWelcomePage(e)}),c.nb.on(i.U.SaveProgress,function(n){"success"===n.progress?z.open({key:B,type:"success",content:e("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"fail"===n.progress?z.open({key:B,type:"error",content:e("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===n.progress&&z.open({key:B,type:"loading",content:e("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})}),c.nb.on(i.U.MessageTip,function(e){v.A.config({top:50,duration:3,maxCount:1}),v.A.info(e)}),ce.userStore.getCheckCurrent()},[]);var le=[{key:"",label:"",icon:[""],children:(0,r.jsx)(u.Wx,{})}];return(0,r.jsxs)("div",{className:n.root+" "+(J?n.landscape:""),children:[(0,r.jsx)(u.pF,{create3DLayout:null,title:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{style:{color:"#FFFFFF0F"},children:"|"}),Q?" 【"+Q+"】":""]}),handler:function(n){var t=c.nb.instance.layout_container;switch(n){case c._I.SaveMyLayoutSchemeAs:0==t._room_entities.length?z.open({key:B,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):(Y(!0),ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"}));break;case c._I.SaveMyLayoutScheme:0==t._room_entities.length?z.open({key:B,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):null==t._layout_scheme_id?(Y(!1),ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"})):c.nb.DispatchEvent(c.n0.SaveLayoutScheme,null);break;case c._I.SaveSchemeAs:0==t._room_entities.length?z.open({key:B,type:"error",content:e("当前方案为空，无法保存！"),duration:2,style:{marginTop:"13vh",zIndex:9999}}):ce.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"})}}}),(0,r.jsx)(R,{}),(0,r.jsx)(ne,{}),(0,r.jsxs)("div",{id:"Canvascontent",className:n.content,children:[(0,r.jsxs)("div",{ref:re,id:"body_container",className:n.canvas_pannel,children:[(0,r.jsx)("div",{className:n.side_pannel,id:"side_pannel",children:ce.homeStore.designMode!==o.f.MeasurScaleMode&&ce.homeStore.designMode!==o.f.HouseCorrectionMode&&(0,r.jsx)(w.I5,{items:le,contentClassName:n.left_content})}),(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){ce.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){ce.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,r=Math.sqrt(n*n+t*t);ce.homeStore.setInitialDistance(r/ce.homeStore.scale)}},onTouchMove:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,r=Math.sqrt(n*n+t*t)/ce.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),ce.homeStore.setScale(r),c.nb.DispatchEvent(c.n0.scale,r)}},onTouchEnd:function(){ce.homeStore.setInitialDistance(null)}}),(0,r.jsx)("div",{className:"canvas_btns",children:E?(0,r.jsx)(g.A,{className:"btn",type:"primary",onClick:function(){var e,n;ce.homeStore.designMode!==o.f.ExDrawingMode?"SingleRoom"!=(null===(n=c.nb.instance)||void 0===n||null===(e=n.layout_container)||void 0===e?void 0:e._drawing_layer_mode)?c.nb.RunCommand(c._I.AcceptLeaveSubHandler):c.nb.DispatchEvent(c.n0.leaveSingleRoomLayout,{}):c.nb.RunCommand(c._I.LeaveSubHandler)},disabled:x.opening||T,children:e("完 成")}):(0,r.jsx)(k.A,{disabled:x.opening||T})})]}),ce.homeStore.designMode!==o.f.MeasurScaleMode&&(0,r.jsx)(u.Nt,{}),(0,r.jsx)(u.iX,{}),ce.homeStore.designMode==o.f.AiCadMode&&(0,r.jsx)(C.A,{})]}),(0,r.jsx)(u.RU,{}),(0,r.jsx)(u.ti,{}),(0,r.jsx)(D.A,{}),(0,r.jsx)(f.A,{}),(0,r.jsx)(S.A,{wrapClassName:"welcome_page",open:ce.homeStore.showWelcomePage,centered:!0,width:"60%",zIndex:999999,closable:!0,destroyOnClose:!0,title:"",footer:null,onCancel:function(){ce.homeStore.setShowWelcomePage(!1)},mask:!0,children:(0,r.jsx)(d.A,{isFixed:!1})}),(0,r.jsx)(s.A,{schemeNameCb:function(e){ee(e)}}),ce.homeStore.isShowWallTopMenu&&(0,r.jsx)(m.A,{}),ce.homeStore.showSaveLayoutSchemeDialog.show&&(0,r.jsx)("div",{className:n.overlay,children:(0,r.jsx)(h.A,{schemeName:Q,closeCb:function(){ce.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:"topMenu"})},isSaveAs:$,exitCb:function(){M.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),setTimeout(function(){window.location.href=p.O9},200)}})}),ce.homeStore.showReplace&&(0,r.jsx)(l.A,{}),ce.homeStore.showHouseSchemeAddForm&&(0,r.jsx)(L.A,{}),(0,r.jsx)(O.A,{}),H]})})},52898:function(e,n,t){var r=t(13274),o=t(33313),a=t(41594),i=t(27347),c=t(88934),u=t(17655),s=t(55111),l=t(62837);function f(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function h(e,n,t,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void t(e)}c.done?n(u):Promise.resolve(u).then(r,o)}function m(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,o,a=[],i=!0,c=!1;try{for(t=t.call(e);!(i=(r=t.next()).done)&&(a.push(r.value),!n||a.length!==n);i=!0);}catch(e){c=!0,o=e}finally{try{i||null==t.return||t.return()}finally{if(c)throw o}}return a}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return f(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return f(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,n){var t,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(t=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=n.call(e,a)}catch(e){c=[6,e],r=0}finally{t=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}n.A=function(){(0,o.A)().styles;var e=m((0,a.useState)(!1),2),n=e[0],t=e[1],f=m((0,a.useState)([]),2),p=f[0],y=f[1],b=i.nb.t,v=((0,a.useRef)(null),i.nb.instance.layout_container),g=function(){return(e=function(){var e,n;return d(this,function(t){return(e=v._selected_room)&&(n=s.lj.ComputeScoreInRoom(e),y(n)),[2]})},function(){var n=this,t=arguments;return new Promise(function(r,o){var a=e.apply(n,t);function i(e){h(a,r,o,i,c,"next",e)}function c(e){h(a,r,o,i,c,"throw",e)}i(void 0)})})();var e},S=function(e){t(e)};return(0,a.useEffect)(function(){return i.nb.on(c.U.ShowLayoutScoreDialog,function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];S(e),e&&g()}),i.nb.on_M(c.U.UpdateLayoutScore,"LayoutScoreDialog",function(){g()}),function(){}},[]),(0,r.jsx)(r.Fragment,{children:n&&(0,r.jsx)(u._w,{title:b("布局评分器"),right:250,width:265,height:600,resizable:!0,draggable:!0,onClose:function(){S(!1)},bodyStyle:{background:"#ffffff",border:"0",boxShadow:"0"},children:(0,r.jsx)(l.A,{layoutScoreList:p,style:0})})})}}}]);