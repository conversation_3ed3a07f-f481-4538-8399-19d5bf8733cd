import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useStore } from "@/models";
import { useEffect, useRef, useState } from "react";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";

const ViewSelectBox: React.FC = () => {
    const { styles } = useStyles();
    const store = useStore();
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const selectInfoRef = useRef<HTMLDivElement>(null)
    const itemInfoRef = useRef<HTMLDivElement[]>([])
    const [isScrolling, setIsScrolling] = useState<boolean>(false);
    const [touchStartX, setTouchStartX] = useState<number>(0);
    const [scrollStartX, setScrollStartX] = useState<number>(0);

    // 选中对象自动滚动到可视区域中心
    const scrollToCenter = (index: number) => {
        if(selectInfoRef.current && itemInfoRef.current[index]){
            const container = selectInfoRef.current
            const item = itemInfoRef.current[index]

            const containerRect = container.getBoundingClientRect()
            const itemRect = item.getBoundingClientRect()

            const scrollLeft = itemRect.left - containerRect.left + container.scrollLeft
            const centerOffset = (containerRect.width - itemRect.width) / 2
            container.scrollTo({
                left: scrollLeft - centerOffset,
                behavior: 'smooth'
            })
        }
    }

    // 选中对象自动滚动到可视区域
    useEffect(() => {
        scrollToCenter(currentIndex);
    }, [currentIndex])

    useEffect(() => {
        console.log('store.homeStore.currentViewCameras', store.homeStore.currentViewCameras);
    }, [store.homeStore.currentViewCameras]);

    const switchView = (index: number) => {
        setCurrentIndex(index);
        // 只在用户点击时触发 bindViewEntity
        const scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D;
        scene3D.active_controls.bindViewEntity(store.homeStore.currentViewCameras[index]);
        scene3D.update();
        // 点击时滚动到中心
        scrollToCenter(index);
    };

    // 处理触摸滑动
    const handleTouchStart = (e: React.TouchEvent) => {
        setIsScrolling(true);
        setTouchStartX(e.touches[0].clientX);
        setScrollStartX(selectInfoRef.current?.scrollLeft || 0);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        if (isScrolling && selectInfoRef.current) {
            e.preventDefault();
            const container = selectInfoRef.current;
            const currentX = e.touches[0].clientX;
            const deltaX = touchStartX - currentX;
            container.scrollLeft = scrollStartX + deltaX;
        }
    };

    const handleTouchEnd = () => {
        setIsScrolling(false);
    };

    return (
        <div className={styles.shijiaoBarContainer} ref={containerRef}>
            <div
                className='selectInfo'
                ref={selectInfoRef}
                onWheel={(e) => {
                    e.preventDefault();
                    const container = e.currentTarget;
                    const scrollSpeed = 30;
                    container.scrollLeft += e.deltaY * scrollSpeed;
                }}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
            >
                {store.homeStore.currentViewCameras.map((item, index) => (
                    <div
                        className='shijiaoItem'
                        key={index}
                        ref={el => itemInfoRef.current[index] = el as HTMLDivElement}
                        style={{ 
                            border: index === currentIndex ? '2px solid #147FFA' : '2px solid #FFFFFF',
                        }}
                        onClick={() => {
                            switchView(index);
                        }}
                    >
                        <img src={item._perspective_img.src} alt="" />
                        <div className='title'>视角{index + 1}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};


export default observer(ViewSelectBox);