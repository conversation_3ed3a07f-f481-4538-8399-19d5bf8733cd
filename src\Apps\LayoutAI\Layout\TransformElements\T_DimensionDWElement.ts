import { TPainter } from "@/Apps/LayoutAI/Drawing/TPainter";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { ZEdge } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { Line3, Vector3 } from "three";
import { AI2DesignManager } from "../../../AI2Design/AI2DesignManager";
import { T_MoveWinDoorOperationInfo } from "../../OperationInfos/Operations/T_MoveWindowOperationInfo";
import { T_TransformElement } from "./T_TransformElement";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { AI_PolyTargetType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { ZInputDimension } from "./ZInputDimension";
import { checkIsMobile } from "@/config";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { EventName } from "@/Apps/EventSystem";
import { message } from "@svg/antd";

// 门窗尺寸标尺
export class T_DimensionDWElement extends T_TransformElement {
    _manager: AI2DesignManager;
    private _leftDimension: ZInputDimension;
    private _centerDimension: ZInputDimension;
    private _rightDimension: ZInputDimension;
    // 选中墙体的边
    private _target_wall_edge: ZEdge | null = null;
    // 所有墙体
    private _wall_rects: ZRect[];

    constructor(manager: AI2DesignManager) {
        super();
        this._manager = manager;
        this._element_name = "T_DimensionDWElement"
        this._allow_entity_types = [AI_PolyTargetType.Door, AI_PolyTargetType.Window];
        this._leftDimension = new ZInputDimension(new Vector3(), new Vector3());
        this._centerDimension = new ZInputDimension(new Vector3(), new Vector3());
        this._rightDimension = new ZInputDimension(new Vector3(), new Vector3());
        this._target_wall_edge = null;
        this._wall_rects = [];
        this._isMovingVisible = true;
        this.IsDimensionElement = true;

        this._allow_entity_types =["Door","Window"];
        // 设置输入值变化的回调
        this._setupDimensionCallbacks();
    }

    get painter(): TPainter {
        return this._manager.layout_container.painter;
    }

    bindTargetRect(target_rect: ZRect, combination_entitys?: TFurnitureEntity[]): void {
        super.bindTargetRect(target_rect,combination_entitys);
        this.visible = true;
    }

    checkEditRect(): boolean {
        return false;
    }

    updateElement(): void {
        if (!this._target_rect) 
        {
            this._hideDimension();
            return;
        };

        // 更新墙体边缘
        this._wall_rects = this._manager.layout_container.getCandidateRects(["Wall"]);
        this._setTargetWallEdge(this._wall_rects);
        if (!this._target_wall_edge) {
            this._hideDimension();
            return;
        };

        const target_rect = this._target_rect;
        const isBayWindow = target_rect.ex_prop['label'] === 'baywindow';
        
        // 获取中心点和宽度
        const center = isBayWindow ? target_rect._back_center.clone() : target_rect.rect_center.clone();
        const width = target_rect.w;

        // 计算门窗的左右点
        const leftPoint = center.clone().sub(target_rect.dv.clone().multiplyScalar(width / 2));
        const rightPoint = center.clone().add(target_rect.dv.clone().multiplyScalar(width / 2));

        // 计算墙体起点和终点
        const wallStartPoint = this._target_wall_edge.unprojectEdge2d({ x: 0, y: 0 });
        const wallEndPoint = this._target_wall_edge.unprojectEdge2d({ x: this._target_wall_edge.length, y: 0 });
        
        // 检查墙体和门窗的方向是否一致
        const wallDirection = new Vector3().subVectors(wallEndPoint, wallStartPoint).normalize();
        const dwDirection = target_rect.dv.clone().normalize();
        const dotProduct = wallDirection.dot(dwDirection);

        // 如果方向相反（点积接近-1），交换墙体的起点和终点
        let finalWallStartPoint = wallStartPoint;
        let finalWallEndPoint = wallEndPoint;
        if (dotProduct < -0.1) {
            finalWallStartPoint = wallEndPoint;
            finalWallEndPoint = wallStartPoint;
        }

        let wallLine = new Line3(finalWallStartPoint, finalWallEndPoint);

        // 计算左侧点的垂线交点
        const leftPointOnWall = this.getClosestPointOnLine(leftPoint, wallLine);
        // 计算右侧点的垂线交点
        const rightPointOnWall = this.getClosestPointOnLine(rightPoint, wallLine);
        
        // 计算偏移方向
        const targetNor = target_rect.nor.clone();
        const wallNor = this._target_wall_edge.nor.clone();
        
        // 计算标尺的偏移方向
        let offsetDirection: Vector3;
        // 计算偏移量
        let outwardOffset: Vector3;
        if (isBayWindow) {
            // 飘窗：使用墙体法向量的反方向
            offsetDirection = wallNor.clone().multiplyScalar(-1);
            outwardOffset = offsetDirection.multiplyScalar(350);
        } else {
            // 普通门窗：使用目标法向量的反方向
            offsetDirection = targetNor.clone().multiplyScalar(1);
            outwardOffset = offsetDirection.multiplyScalar(450);
        }
        
        // 更新标尺点位置
        let wallRulerStartPoint = finalWallStartPoint.clone().add(outwardOffset);
        let wallRulerEndPoint = finalWallEndPoint.clone().add(outwardOffset);
        let dwRulerStartPoint = leftPointOnWall.clone().add(outwardOffset);
        let dwRulerEndPoint = rightPointOnWall.clone().add(outwardOffset);

        // 设置标尺点位置
        this._leftDimension.setPoints(wallRulerStartPoint, dwRulerStartPoint);
        this._centerDimension.setPoints(dwRulerStartPoint, dwRulerEndPoint);
        this._rightDimension.setPoints(dwRulerEndPoint, wallRulerEndPoint);
    }

    // 计算外部点到直线的垂线交点
    private getClosestPointOnLine(point: Vector3, line: Line3): Vector3 {
        const lineDirection = new Vector3().subVectors(line.end, line.start).normalize();
        const pointToLineStart = new Vector3().subVectors(point, line.start);
        const projectionLength = pointToLineStart.dot(lineDirection);
        return line.start.clone().add(lineDirection.multiplyScalar(projectionLength));
    }

    drawCanvas(painter: TPainter): void {
        if (!this.isTargetRectTypeValid() || !this.visible)  
        {
            this._hideDimension();
            return;
        };

        // 绘制标尺线
        this._leftDimension.draw(painter);
        this._centerDimension.draw(painter);
        this._rightDimension.draw(painter);

        // 如果有目标墙体边缘，显示输入框
        if (this._target_wall_edge && this._target_rect) {
            const target_rect = this._target_rect;
            const center = target_rect.rect_center.clone();
            const width = target_rect.w;

            // 计算门窗的左右点
            const leftPoint = center.clone().sub(target_rect.dv.clone().multiplyScalar(width / 2));
            const rightPoint = center.clone().add(target_rect.dv.clone().multiplyScalar(width / 2));

            // 检查墙体和门窗的方向是否一致
            const wallDirection = this._target_wall_edge.dv.clone().normalize();
            const dwDirection = target_rect.dv.clone().normalize();
            const dotProduct = wallDirection.dot(dwDirection);

            // 计算投影点
            const leftProj = this._target_wall_edge.projectEdge2d(leftPoint);
            const rightProj = this._target_wall_edge.projectEdge2d(rightPoint);

            let leftValue: number;
            let rightValue: number;

            if (dotProduct < -0.1) {
                // 如果方向相反，交换左右值的计算方式
                leftValue = this._target_wall_edge.length - leftProj.x;
                rightValue = rightProj.x;
            } else {
                // 方向一致，正常计算
                leftValue = leftProj.x;
                rightValue = this._target_wall_edge.length - rightProj.x;
            }

            // 显示三个输入框 移动时 设置不可编辑 防止鼠标事件被输入框挡住 导致下面的canvas 收不到事件
            this._leftDimension.showInput(painter, Math.abs(leftValue),false,!this.is_moving);
            this._centerDimension.showInput(painter, this._target_rect.w,false,!this.is_moving);
            this._rightDimension.showInput(painter, Math.abs(rightValue),false,!this.is_moving);
        }
    }

    // 隐藏标尺
    private _hideDimension(): void {
        if(this._leftDimension) this._leftDimension.hideInput();
        if(this._centerDimension) this._centerDimension.hideInput();
        if(this._rightDimension) this._rightDimension.hideInput();
        this.visible = false;
    }

    // 修改 dispose 方法
    private _disposeDimension(): void {
        if(this._leftDimension) this._leftDimension.dispose();
        if(this._centerDimension) this._centerDimension.dispose();
        if(this._rightDimension) this._rightDimension.dispose();
    }

    private _setTargetWallEdge(rects: ZRect[]) {
        if (!this._target_rect) return;

        const isBayWindow = this._target_rect.ex_prop['label'] === 'baywindow';
        // 对于飘窗使用_back_center
        const center = isBayWindow ? this._target_rect._back_center.clone() : this._target_rect.rect_center.clone();
        // 所有构件的法向量都使用原始方向（不反向）
        const nor = this._target_rect.nor.clone();
        const width = this._target_rect.w;

        // 重置目标墙体边缘
        this._target_wall_edge = null;

        // 首先找到包含门窗中心点的主墙体
        let mainWall: ZRect | null = null;
        for (let wall of rects) {
            if (!TBaseEntity.is_deleted(wall) && wall.containsPoint(center,1)) {
                mainWall = wall;
                break;
            }
        }

        if (!mainWall) return;

        // 获取主墙的所有边
        const allEdges = [...mainWall.edges];

        // 收集所有可能与主墙相交的墙体
        const intersectingWalls = rects.filter(wall => 
            wall !== mainWall && 
            !TBaseEntity.is_deleted(wall) &&
            this.doWallsIntersect(mainWall!, wall)
        );

        // 对于每个相交的墙体，将主墙切割成更小的边
        for (const intersectWall of intersectingWalls) {
            for (const edge of intersectWall.edges) {
                this.splitEdgesAtIntersection(allEdges, edge);
            }
        }

        // 存储所有合适的边及其信息
        const candidateEdges: Array<{
            edge: ZEdge, 
            distance: number,
            projX: number,
            containsCenter: boolean,
            directionMatch: boolean
        }> = [];

        // 在所有切割后的边中找到合适的边
        for (const edge of allEdges) {
            // 检查法向量是否合适（所有构件都要求法向量相同）
            const edgeNorDotTargetNor = edge.nor.dot(nor);
            const directionMatch = edgeNorDotTargetNor > -0.1;   // 统一要求法向量相同

            if (!directionMatch) continue;

            // 计算门窗中心点在边上的投影
            const proj = edge.projectEdge2d(center);
            const distance = Math.abs(proj.y);
            const projX = proj.x;

            // 检查边长是否足够容纳门窗
            if (edge.length < width) continue;

            // 检查投影点是否在边的范围内
            const halfWidth = width / 2;
            const containsCenter = projX >= 0 && projX <= edge.length;
            
            // 确保门窗的左右端点都在边的范围内
            if (projX - halfWidth >= 0 && projX + halfWidth <= edge.length) {
                candidateEdges.push({
                    edge, 
                    distance,
                    projX,
                    containsCenter,
                    directionMatch
                });
            }
        }

        if (candidateEdges.length > 0) {
            // 按以下优先级排序：
            // 1. 方向匹配
            // 2. 边长（选择较短的边）
            candidateEdges.sort((a, b) => {
                // 首先按方向匹配排序
                if (a.directionMatch !== b.directionMatch) {
                    return a.directionMatch ? -1 : 1;
                }
                // 然后按边长排序
                return a.edge.length - b.edge.length;
            });

            this._target_wall_edge = candidateEdges[0].edge;
        }
    }

    // 检查两个墙体是否相交
    private doWallsIntersect(wall1: ZRect, wall2: ZRect): boolean {
        for (const edge1 of wall1.edges) {
            for (const edge2 of wall2.edges) {
                // 使用 checkIntersection 方法检查边是否相交
                const intersection = edge1.checkIntersection(edge2);
                if (intersection) {
                    return true;
                }
            }
        }
        return false;
    }

    // 在交点处分割边
    private splitEdgesAtIntersection(edges: ZEdge[], intersectingEdge: ZEdge): void {
        const newEdges: ZEdge[] = [];
        for (let i = edges.length - 1; i >= 0; i--) {
            const edge = edges[i];
            // 使用 checkIntersection 检查相交
            const intersection = edge.checkIntersection(intersectingEdge);
            
            if (intersection) {
                // 创建两个新的边（从原始边的起点到交点，从交点到原始边的终点）
                const edge1 = new ZEdge(
                    {pos: edge.v0.pos.clone()}, 
                    {pos: intersection.point.clone()}
                );
                const edge2 = new ZEdge(
                    {pos: intersection.point.clone()}, 
                    {pos: edge.v1.pos.clone()}
                );
                
                // 复制原始边的法向量
                edge1._nor.copy(edge._nor);
                edge2._nor.copy(edge._nor);
                
                // 移除原始边，添加新的边
                edges.splice(i, 1);
                newEdges.push(edge1, edge2);
            }
        }
        
        edges.push(...newEdges);
    }

    // 处理左右距离变化
    private _handleLRDistanceChange(isLeft: boolean, value: number): void {
        if (!this._target_rect || !this._target_wall_edge) return;

        if (value > this._target_wall_edge.length) {
            message.error("距离不能超过墙体长度");
            return;
        }
        value = Number(value.toFixed(1));

        const origin_rect = this._target_rect.clone();
        let moved_rect = this._target_rect;
        const isBayWindow = moved_rect.ex_prop['label'] === 'baywindow';
        
        // 计算墙体和门窗的方向
        const wallDirection = this._target_wall_edge.dv.clone().normalize();
        const dwDirection = moved_rect.dv.clone().normalize();
        const dotProduct = wallDirection.dot(dwDirection);
        
        // 计算当前点位置和投影
        const center = isBayWindow ? moved_rect._back_center.clone() : moved_rect.rect_center.clone();
        const point = center.clone()[isLeft ? 'sub' : 'add'](
            moved_rect.dv.clone().multiplyScalar(moved_rect.w / 2)
        );
        const proj = this._target_wall_edge.projectEdge2d(point);
        
        // 根据方向关系计算原始距离和移动方向
        let origin_num: number;
        let moveDirection: Vector3;
        
        if (dotProduct < -0.1) {
            // 方向相反的情况
            if (isLeft) {
                origin_num = this._target_wall_edge.length - proj.x;
                moveDirection = this._target_wall_edge.dv.clone();
            } else {
                origin_num = proj.x;
                moveDirection = this._target_wall_edge.dv.clone().multiplyScalar(-1);
            }
        } else {
            // 方向一致的情况
            if (isLeft) {
                origin_num = proj.x;
                moveDirection = this._target_wall_edge.dv.clone().multiplyScalar(-1);
            } else {
                origin_num = this._target_wall_edge.length - proj.x;
                moveDirection = this._target_wall_edge.dv.clone();
            }
        }
        
        // 计算移动距离
        const edit_num = origin_num - value;
        
        // 计算新的中心点
        const t_center = {
            x: moved_rect.rect_center.x + moveDirection.x * edit_num,
            y: moved_rect.rect_center.y + moveDirection.y * edit_num,
            z: moved_rect.rect_center.z
        };
        
        moved_rect.rect_center = t_center;
        moved_rect.updateRect();

        this._executeOperaionInfo(origin_rect, moved_rect);
        this._manager.update();
    }
    // 处理宽度变化
    private _handleDWWidthChange(value: number): void {
        if (!this._target_rect || !this._target_wall_edge) return;

        // 值限制：最小100mm，最大不超过墙体长度，保留1位小数
        const MIN_WIDTH = 100;

        if (value < MIN_WIDTH) {
            message.error("宽度不能小于100mm");
            return; 
        }
        if (value > this._target_wall_edge.length) {
            message.error("宽度不能超过墙体长度");
            return;
        }
        value = Number(value.toFixed(1));
        
        // 保存原始矩形
        const origin_rect = this._target_rect.clone();
        let moved_rect = this._target_rect;
        
        // 更新矩形宽度，保持中心点不变
        moved_rect._w = value;
        moved_rect.updateRect();

        // 如果是飘窗，需要更新_back_center
        if (moved_rect.ex_prop['label'] === 'baywindow') {
            moved_rect._back_center = moved_rect._back_center || moved_rect.rect_center.clone();
        }

        let entity = TBaseEntity.getEntityOfRect(moved_rect);
        if(entity)
        {
            entity.update();
            entity.forcePanelUpdate(true);
        }
        
        // 执行操作并记录历史
        this._executeOperaionInfo(origin_rect, moved_rect);
        this._manager.update();
    }

    // 设置标尺值变化回调
    private _setupDimensionCallbacks(): void {
        // 左侧标尺的值变化
        this._leftDimension.setOnEnterPressed((value) => {
            this._handleLRDistanceChange(true, value);
        });

        // 中间标尺的值变化（门窗宽度）
        this._centerDimension.setOnEnterPressed((value) => {
            this._handleDWWidthChange(value);
        });

        // 右侧标尺的值变化
        this._rightDimension.setOnEnterPressed((value) => {
            this._handleLRDistanceChange(false, value);
        });

        // 添加输入框焦点事件处理
        this._leftDimension.setOnFocus(() => {
            if(checkIsMobile()) {
                const input = this._leftDimension.getInput();
                LayoutAI_App.emit(EventName.showCustomKeyboard, {
                    visible: true, 
                    input: input,
                    onValueChange: (value: number) => this._handleLRDistanceChange(true, value)
                });
            }
        });

        this._centerDimension.setOnFocus(() => {
            if(checkIsMobile()) {
                const input = this._centerDimension.getInput();
                LayoutAI_App.emit(EventName.showCustomKeyboard, {
                    visible: true, 
                    input: input,
                    onValueChange: (value: number) => this._handleDWWidthChange(value)
                });
            }
        });

        this._rightDimension.setOnFocus(() => {
            if(checkIsMobile()) {
                const input = this._rightDimension.getInput();
                LayoutAI_App.emit(EventName.showCustomKeyboard, {
                    visible: true, 
                    input: input,
                    onValueChange: (value: number) => this._handleLRDistanceChange(false, value)
                });
            }
        });
    }

    // 执行操作并记录历史
    private _executeOperaionInfo(origin_rect: ZRect, moved_rect: ZRect): void {
        let move_rect_opertion_info = new T_MoveWinDoorOperationInfo(this._manager);
        move_rect_opertion_info.target_rect = moved_rect;
        move_rect_opertion_info._history_info.previous_rect = origin_rect.clone();
        move_rect_opertion_info._history_info.current_rect = moved_rect.clone();
        this._manager.appendOperationInfo(move_rect_opertion_info);
    }
}