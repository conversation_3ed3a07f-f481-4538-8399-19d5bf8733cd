import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { useState, useMemo, useEffect } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { Segmented } from "@svg/antd";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import useStyles from './HouseLeftPanel.style';
import { IRoomEntityRealType, IRoomEntityType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { Image } from '@svg/antd';
import { FigureDataList } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import React from "react";


/**
 * 户型编辑器左侧面板组件
 */
const HouseLeftPanels: React.FC<HouseLeftPanelsProps> = ({ isCollapse, onCollapseChange }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [currentTab, setCurrentTab] = useState<string>("doors");

    const { doorWindowItems, structureItems } = useOrganizedFigureData();
    const imgPath = 'https://3vj-fe.3vjia.com/layoutai/figures_imgs/';
    useEffect(() => {
        // 通知AppManagerBase组件已经渲染完成
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (manager) {
            manager.addHouseLeftPanelEvent();
        }
    }, []);

    const tabItems = [
        {
            value: "doors",
            label: t('门窗'),
        },
        {
            value: "structure",
            label: t('结构件'),
        }
    ];

    const handleItemClick = (label: string) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
    };

    const isLeftPanelOpen = !isCollapse;

    return (
        <>
            <div id="pad_house_left_panel" className={styles.leftPanelRoot + " leftPanelRoot " + (!isLeftPanelOpen ? "panel_hide" : "")} >
                {isLeftPanelOpen && <div className="closeBtn iconfont iconclose1" onClick={() => onCollapseChange(true)}></div>}
                {isLeftPanelOpen && (
                    <>
                        <div className={styles.tabBar}>
                            <div className="tab-content">
                                <Segmented
                                    value={currentTab}
                                    onChange={(val) => setCurrentTab(val.toString())}
                                    block
                                    options={tabItems}
                                />
                            </div>
                        </div>
                        <div className={styles.popupContainer}>
                            <div className={styles.itemGrid}>
                                {currentTab === "doors" ?
                                    doorWindowItems.map((item, index) => (
                                        <div  key={index} className={styles.gridItem}>
                                            <div
                                                className={styles.itemIcon}
                                                onPointerDown={(e) => {
                                                    handleItemClick(item.label);
                                                }}
                                            >
                                                <Image
                                                    src={`${imgPath}${item.image}`} preview={false}
                                                    title={item.label}
                                                    alt={item.label}
                                                     />
                                            </div>
                                            <div className={styles.itemLabel}>{t(item.label)}</div>
                                        </div>
                                    ))
                                    :
                                    structureItems.map((item, index) => (
                                        <div  key={index} className={styles.gridItem}>
                                            <div
                                                className={styles.itemIcon}
                                                onPointerDown={(e) => {
                                                    handleItemClick(item.label);
                                                }}
                                                onPointerUp={(e) => {
                                                    LayoutAI_App.DispatchEvent(LayoutAI_Events.mobileAddFurniture, item.label);
                                                }}
                                            >
                                                <Image
                                                    src={`${imgPath}${item.image}`} preview={false}
                                                    title={item.label}
                                                    alt={item.label}
                                                     />
                                            </div>
                                            <div className={styles.itemLabel}>{t(item.label)}</div>
                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                    </>
                )}
            </div>
            {<div
                className={styles.collapseBtn + (!isLeftPanelOpen ? " panel_hide iconfont iconfill_right" : " iconfont iconfill_left")}
                onClick={() => onCollapseChange(!isCollapse)}
            />}
        </>
    );
};

export default observer(HouseLeftPanels); 

interface HouseLeftPanelsProps {
    isCollapse: boolean;
    onCollapseChange: (collapse: boolean) => void;
}

interface FigureItem {
    icon: string;
    label: string;
    type: IRoomEntityType;
    realType: IRoomEntityRealType;
    image: string;
}

/**
 * 重新组织户型数据，分为门窗和结构件两类
 */
const useOrganizedFigureData = () => {
    return useMemo(() => {
        const houseTypeData = FigureDataList.find(item => item.label === '户型');
        if (!houseTypeData || !houseTypeData.child) return { doorWindowItems: [], structureItems: [] };

        const structureData = houseTypeData.child.find(item => item.label === '结构件');
        if (!structureData || !structureData.figureList) return { doorWindowItems: [], structureItems: [] };

        const doorWindowItems: FigureItem[] = [];
        const structureItems: FigureItem[] = [];

        structureData.figureList.forEach((item: any) => {
            // 如果没有 icon，则使用 image 字段作为备选
            const iconName = item.icon || item.image?.split('.')[0] || '';

            const commonProps: FigureItem = {
                icon: iconName,
                label: item.title,
                type: isDoorOrWindow(item.label) ? 'Door' as IRoomEntityType : 'StructureEntity' as IRoomEntityType,
                realType: item.label as IRoomEntityRealType,
                image: item.image,
            };

            // 根据类型分类
            if (isDoorOrWindow(item.label)) {
                doorWindowItems.push(commonProps);
            } else {
                structureItems.push(commonProps);
            }
        });

        return { doorWindowItems, structureItems };
    }, []);
};

/**
 * 判断是否为门窗类型
 */
const isDoorOrWindow = (label: string): boolean => {
    const doorWindowTypes = ['Door', 'Window', 'door', 'window', 'Railing'];
    return doorWindowTypes.some(type => label.includes(type));
};