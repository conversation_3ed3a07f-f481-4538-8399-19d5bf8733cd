import { UI_FormatType } from "../IUIInterface";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TLayoutGraph } from "../TLayoutGraph/TLayoutGraph";
import { TRoomLayoutScheme } from "../TLayoutScheme/TRoomLayoutScheme";
import { TLayoutBaseRuleParamConfiguration } from "../TLayoutScoreConfigurationTool/TLayoutBaseRuleParamConfiguration";
import { TRoom } from "../TRoom";
import { TCheckRule } from "./CheckRules/TCheckRule";

// 调整类型
export enum TrimType
{
    k_none,
    k_move,
    k_size,
    k_leftOrRightMove,
    k_frontOrBackMove,
    k_frontScaleSize,
    k_backScaleSize,
    k_leftScaleSize,
    k_rightScaleSize,
}


/**
 *  布局评分器
 */
export interface I_LayoutScore
{
    /**
     *  评分器标识
     */
    name : string; // 评分器名称
    /**
     *  得分数值
     */
    score : number; // 得分
    /**
     *  函数值
     */
    value ?: number; 
    /**
     *  统计次数
     */
    count ?: number; 
    /**
     *  显示次序
     */
    order ?: number;
    /**
     *  详细描述
     */
    info ?: string;
    /**
     *  是否可见
     */
    visible ?: boolean;


    /**
     *  UI Format
     */
    ui_format ?: UI_FormatType[];

    /**
     *  子得分列表
     */
    children ?: I_LayoutScore[]; 


    /**
     *  是否checked
     */
    checked ?: boolean;


    /**
     *  等级
     */
    grade ?: number;


    /**
     *  占比
     */
    percent ?: number;

    /**
     *  父亲
     */
    parent ?: I_LayoutScore;
    /**
     *  父亲名称
     */
    parent_name ?: string;

    /**
     * 需要进行调整的图元
     */ 
    fineTuningFigures ?: TFigureElement[];

    /**
     * 调整图元方向, "move", "size"
     * 这个是要被废弃掉的
     */
    fineTuningTypes ?: TrimType[];

    /**
     * 根据连续的总得分
    */
   fineTuningValue ?: number;
}

export class TLayoutJudge
{


    /**
     *  家居布局 = 安全合理 + 隐私性 
     */
    static BasicScore = "家居布局";
    /**
     *   无干涉、遮挡、碰撞
     */
    static OcclusionScore = "安全合理";

    /**
     *  隐私性
     */
    static PrivacyScore = "隐私性";

    /**
     *  采光度
     */
    static DayLightingScore = "采光";

    /**
     *  布局动线
     */
    static Flowline = "布局动线";

    /**
     *  功能丰富度
     */
    static FunctionScore = "功能丰富度";
    /**
     *   动线合理性
     */
    static MovingLineScore = "布局动线";
    /**
     *   收纳容量占比
     */
    static StorageScore = "收纳容量";
    /**
     *   空间利用率
     */
    static SpaceUsedScore = "空间利用率";

    static FlatPlaneScore = "操作台面";

    static SplitSpaceScore = "分区合理性";

    static TotalScore = "整体布局得分";

    static VisualAppeal = "美观度";

    name : string;
    order : number;

    _check_rules : TCheckRule[];

    ruleParamConfig: TLayoutBaseRuleParamConfiguration;

    constructor()
    {
        this.order = 0;
        this._check_rules =[];
    }
    computeSchemeScore(room:TRoom,layout_scheme:TRoomLayoutScheme, layout_graph : TLayoutGraph = null) : I_LayoutScore[]
    {
        return [{name:this.name,score:0,count:0,order:this.order}];
    }

    computeScoreInRoom(room:TRoom, figure_elements:TFigureElement[]): I_LayoutScore[]
    {
        return [];
    }
}


export class TLayoutJudgeContainter
{
    judge_list : TLayoutJudge[];

    layout_scheme: TRoomLayoutScheme;
    
    private static _instance : TLayoutJudgeContainter;
    constructor()
    {
        this.judge_list = [];
    }

    public static get instance() : TLayoutJudgeContainter
    {
        if(!TLayoutJudgeContainter._instance)
        {
            TLayoutJudgeContainter._instance = new TLayoutJudgeContainter();
        }
        return TLayoutJudgeContainter._instance;
    }

    protected  _computeSchemeScore(room:TRoom,layout_scheme:TRoomLayoutScheme, layout_graph : TLayoutGraph = null) : I_LayoutScore[]

    {
        this.layout_scheme = layout_scheme;
        return this._computeScoreInRoom(room,layout_scheme.figure_list.figure_elements);
    }
    protected  _computeScoreInRoom(room:TRoom,figure_elements:TFigureElement[] =null) : I_LayoutScore[]

    {
        let res : I_LayoutScore[] = [];
        for(let judge of this.judge_list)
        {
            let ans = judge.computeScoreInRoom(room,figure_elements);
            for(let sc of ans)
            {
                res.push(sc);
            }
        }
        res.sort((a,b)=>(a.order||0) - (b.order||0));
        return res;
    }
    static appendJudge(judge:TLayoutJudge)
    {
        TLayoutJudgeContainter.instance.judge_list.push(judge);
    }
    static ComputeSchemeScore(room:TRoom,layout_scheme:TRoomLayoutScheme, layout_graph : TLayoutGraph = null) : I_LayoutScore[]
    {
        if(!TLayoutJudgeContainter.instance)
        {
            return [];
        }
        let container = TLayoutJudgeContainter.instance;
        return container._computeSchemeScore(room,layout_scheme,layout_graph);
    }
    static ComputeScoreInRoom(room:TRoom,figure_elements:TFigureElement[]=null) : I_LayoutScore[]
    {
        if(!TLayoutJudgeContainter.instance)
        {
            return [];
        }
        let container = TLayoutJudgeContainter.instance;
        return container._computeScoreInRoom(room,figure_elements);
    } 
}