import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
    return {
        schemeListContainer: css`
            position: absolute;
            top: ${checkIsMobile?.() ? '64px' : '1px'};
            right: ${checkIsMobile?.() ? '68px' : '244px'};
            bottom: ${checkIsMobile?.() ? '12px' : '0px'};
            width: 218px;
            background: ${token.colorBgContainer};
            overflow: hidden;
            ${checkIsMobile?.() && 'border-radius: 12px;'};
            padding: 16px;
            display: flex;
            flex-direction: column;
        `,
        title: css`
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: ${token.colorBgContainer};
        `,
        closeIcon: css`
            font-size: 18px;
            color: ${token.colorTextSecondary};
            cursor: pointer;
            display: flex;
            align-items: center;
            line-height: 1;
            height: 18px;
            &:hover {
                color: ${token.colorText};
            }
        `,
        schemeList: css`
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: hidden;
            flex: 1;
            width: 100%;
            align-items: center;
        `,
        schemeItem: css`
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            cursor: pointer;
        `,
        selectedSchemeImage: css`
            width: 180px;
            height: 135px;
            background-color: #38295710;
            border-radius: 8px;
            border: 1px solid ${token.colorPrimary};
            display: flex;
            justify-content: center;
            align-items: center;
        `,
        schemeImage: css`
            width: 180px;
            height: 135px;
            background-color: #3829570A;;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
        `,
        addSchemeImage: css`
            width: 180px;
            height: 135px;
            background-color: #3829570A;
            border-radius: 8px;
            border: 1px dashed ${token.colorBorder};
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            text-align: center; 
        `,
        addIcon: css`
            font-size: 20px;
        `,
        addSchemeText: css`
            font-size: 12px;
            margin-top: 8px;
            display: block;
            text-align: center;
            color: gray;
        `,
        schemeBottom: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 180px;
            height: 36px;
            padding: 0 6px;
        `,
        schemeName: css`
            font-size: 14px;
            font-weight: 600;
            color: ${token.colorText};
            padding-left: 6px;
        `,
        deleteIcon: css`
            color: ${token.colorTextSecondary};
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding-right: 6px;
            &:hover {
                color: ${token.colorError};
            }
        `,
        spinContainer: css`
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.6);
            .ant-spin {
                pointer-events: auto;
            }
        `,
        textEllipsis: {
            maxWidth: '140px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: 'inline-block'
        }
    }
});
