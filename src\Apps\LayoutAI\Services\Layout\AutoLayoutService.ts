import { BedRoomSubService } from "./BedRoomSubService";
import { ILayoutRoom } from "./ILayoutRoom";
import { ILayoutStyle } from "./ILayoutStyle";
import { ILayoutSubService } from "./ILayoutSubService";
import { RoomTypeConfig } from "./LayoutConfig";

/**
* @description 自动布局服务
* <AUTHOR>
* @date 2025-07-22
* @lastEditTime 2025-07-22 16:05:12
* @lastEditors xuld
*/
export class AutoLayoutService {
    private static _instance: AutoLayoutService;

    /**
    * @description 获取该类的单例
    * @return 该类的单例
    */
    public static get instance(): AutoLayoutService {
        if (!this._instance) {
            this._instance = new AutoLayoutService();
        }
        return this._instance;
    }

    // 不同房间的布局子服务
    private _subServices: Map<string, ILayoutSubService> = new Map();

    constructor() {
        this.registerService(RoomTypeConfig.CATEGORY_BEDROOM, new BedRoomSubService());
    }

    /**
    * @description 获取布局样式列表
    * @param room 房间
    * @return 布局样式列表
    */
    public getLayoutStyles(room: ILayoutRoom): ILayoutStyle[] {
        const subService = this._subServices.get(room.name);
        if (subService) {
            return subService.getLayoutStyles(room);
        } else {
            console.warn(`未找到布局子服务: ${room.name}`);
            return [];
        }
    }

    public registerService(roomType: string, service: ILayoutSubService): void {
        this._subServices.set(roomType, service);
    }

    public unregisterService(roomType: string): void {
        this._subServices.delete(roomType);
    }
}
