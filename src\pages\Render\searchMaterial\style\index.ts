import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      height: 510px;
      @media screen and (max-width: 450px) {
        height: 380px;
      }
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100);
        width: 224px;
      }
    `,
    styleInfo: css`
      display: flex;
      padding: 0 24px;
      margin-top: 20px;
      justify-content: space-between;
      @media screen and (orientation: landscape) {
        display: block;
      }
    `,
    styleItem: css`
      width: 32%;
      height: 80px;
      border-radius: 8px;
      background: #F4F5F5;
      position: relative;
      overflow: hidden;
      border: 1px solid #0000000F;
      @media screen and (orientation: landscape) {
        width: 100%;
        margin-bottom: 8px;
      }
      .add
      {
        left: 50%;
        top: 50%;
        position: absolute;
        transform: translate(-50%, -50%);
        color: #282828;
        font-size: 12px;
        width: 100%;
        text-align: center;
      }
      img{
        width: 50%;
        height: 80px;
        margin-right: 8px;
        @media screen and (orientation: landscape) {
          width: 30%;
          height: 80px;
        }
      }
      .item
      {
        display: flex;
        justify-content: space-between;
        position: relative;
        @media screen and (orientation: landscape) {
          justify-content: start;
        }
        .title
        {
          width: 50%;
          position: absolute;
          text-align: center;
          bottom: 0px;
          height: 20px;
          color: #FFF;
          border-radius: 0px 0px 4px 4px;
          background: #00000066;
          padding-top: 2px;
          @media screen and (orientation: landscape) {
            width: 30%;
          }
        }
      }
      .rightitem
      {

        width: 50%;
        display: flex;
        flex-direction: column;
        padding: 8px 4px;
        justify-content: space-between;
        position: relative;
        color: #282828;
        .icon
        {
          position: absolute;
          right: 10px;
          bottom: 10px;
          font-size: 18px;
          color: #5B5E60;
        }
        .seriesStyle
        {
          border-radius: 4px;
          background: #FFFFFF;
          padding: 4px 8px;
          font-size: 12px;
          color: #5B5E60;
          width: 40px;
          text-align: center;
          height: 24px;
        }
      }
    `,
    materialInfo: css`
     overflow-y: scroll;
     height: calc(100% - 100px);
     margin-top: 24px;
     padding: 0 24px;
     @media screen and (orientation: landscape) {
      height: calc(var(--vh, 1vh) * 100 - 350px);
     }
    .itemInfo {
        margin-bottom: 16px; /* 每个 item 之间的间距 */
        overflow: hidden; /* 隐藏溢出内容 */

        .itemList
        {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          .item{
            text-align: center;
            overflow: hidden;
            width: 104px;
            position: relative;
            .redIcon
            {
              position: absolute;
              top: 5px;
              left: 5px;
              color: #FF4D4F;
            }
            @media screen and (max-width: 450px){
              width: 82px;
            }
            @media screen and (max-width: 400px){
              width: 69px;
            }
            @media screen and (orientation: landscape) {
              width: 69px;
            }
            img{
              aspect-ratio: 1 / 1;
              margin-bottom: 4px;
            }
            div{
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
          
        }
    }

    .header {
        display: flex;
        justify-content: space-between; /* 使 label 和 icon 分开 */
        padding: 12px 0px; /* 内边距 */
        .title
        {
          font-weight: 600;
          font-size: 14px;
        }
    }

    .item {
        
    }

    .item img {
        width: 100%; /* 图片宽度占满 */
        height: auto; /* 高度自适应 */
        background-color: #F4F5F5;
        border-radius: 4px;
    }
    &::-webkit-scrollbar {
        display: none;
    }
  
    scrollbar-width: none;
    -ms-overflow-style: none;
    `,
    visible: css`
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: flex-end;
      z-index: 10002;
      @media screen and (orientation: landscape) {
        justify-content: end;
        position: fixed;

      }
    `,
    serialsInfo: css`
      background-color: white;
      width: 100%;
      padding: 20px;
      height: 300px;
      padding-top: 56px;
      border-radius: 16px 16px 0px 0px;
      background: #FFFFFF;
      box-shadow: 0px -16px 16px 0px #00000005;
      position: relative;
      @media screen and (orientation: landscape) {
        position: fixed;
        bottom: 12px;
        right: 0;
        top: 45px;
        left: auto;
        padding: 0;
        width: 224px;
        border-radius: 0px;
        overflow: hidden;
        height: auto;
        border-radius: 6px;
      }
    `,
    sideVisible: css`
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 586px;
      background-color: white;
      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
      transition: transform 0.2s ease;
      border-radius: 16px 16px 0px 0px;
      box-shadow: 0px -16px 16px 0px #00000005;
      @media screen and (orientation: landscape) {
        height: calc(100%);
        border-radius: 0px;
        width: 224px;
        left : 0px;
        right : auto;
      }
      .sideTopInfo
      {
        padding: 24px;
        display: flex;
        justify-content: space-between;
        div{
          font-size: 20px;
          font-weight: 600;
          color: #282828;
          margin-left: 6px;
        }
      }
    `,
    topInfo: css`
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 0 24px;
        .info
        {
          display: flex;
          img{
            width: 72px;
            height: 72px;
            border-radius: 4px;
            margin-right: 16px;
          }
        }
         .sizeInfo
         {
          padding: 8px 0px;
            .size
            {
              color: #5B5E60;
              margin-top: 4px;
            }
         } 
      `,
    slideIn: css`
      transform: translateX(0);
      @media screen and (orientation: landscape) {
        transform: translateX(0);        
      }
    `,

    slideOut: css`
      transform: translateX(100%);
      @media screen and (orientation: landscape) {
        transform: translateX(-100%);
      }
    `,
    lock_icon: css`
      position: absolute;
      right: 7px;
      top: 57%;
      font-size: 16px;
      color: #5B5E60;
    `,
    warn_icon: css`
      position: absolute;
      left: 5px;
      top: 5px;
      font-size: 16px;
      color: #FFAA00;
    `
  }
});
