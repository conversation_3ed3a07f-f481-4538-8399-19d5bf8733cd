import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { ZEdge, ZPolygon, ZR<PERSON>t, compareNames } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { I_MaterialMatchingItem } from "../../../IMaterialInterface";
import { I_Window } from "../../../IRoomInterface";
import { WPolygon } from "../../../TFeatureShape/WPolygon";
import { FigureCategoryManager } from "../../../TFigureElements/FigureCategoryManager";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoomEntity } from "../../../TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "../../../TLayoutEntities/TSubSpaceAreaEntity";
import { TRoom } from "../../../TRoom";
import { TGraphBasicConfigs } from "../../TGraphBasicConfigs";

export class TPostLayoutCeiling
{
    private static _instance : TPostLayoutCeiling = null;
    private DefaultCeilingHeight:number = 200;
    private DefaultCabinetHeight:number = 2600;

    constructor()
    {
    }

    static get instance()
    {
        if(!TPostLayoutCeiling._instance)
        {
            TPostLayoutCeiling._instance = new TPostLayoutCeiling();
        }
        return TPostLayoutCeiling._instance;
    }

    creatSimpleCeiling():TFigureElement {
        let simpleCeiling = TFigureElement.createSimple("吊顶");
        simpleCeiling.params.topOffSet = this.DefaultCeilingHeight;
        return simpleCeiling;
    }

    postAddCeilingFigures(room:TRoom,figure_elements:TFigureElement[]=null, params : {storey_height:number} = {storey_height:2800})
    {
        if(!figure_elements) figure_elements = room._furniture_list;

        let matched_material = {
            modelId: "162169383",
            topOffset: 200,
            figureElement: null,
            modelLoc: "吊顶"
        } as I_MaterialMatchingItem;

        if(room._ceilling_list)
        {
            let ceiling0 = room._ceilling_list[0];
            if(ceiling0?._matched_material)
            {
                matched_material = {
                    ...ceiling0._matched_material
                }
                matched_material.figureElement = null;
            }
        }
        if(compareNames([room.roomname],["客餐厅"]))
        {

            this.postAddLivingRoomCeiling(room,figure_elements);
            room._ceilling_list.forEach((ceiling) => {
                ceiling._matched_material = {
                    ...matched_material
                }
                ceiling._matched_material.figureElement = ceiling;
            });
            
        }
        else if(compareNames([room.roomname],["书房","卧室"]))
        {
            this.postAddBedRoomCeiling(room,figure_elements);
            room._ceilling_list.forEach((ceiling) => {
                ceiling._matched_material = {
                    ...matched_material
                }
                ceiling._matched_material.figureElement = ceiling;
            });
        }
        else if(compareNames([room.roomname],["厨房","卫生间","阳台"]))
        {
            matched_material.modelId = "131827360";
            this.postAddSimpleRoomCeiling(room,figure_elements);
            room._ceilling_list.forEach((ceiling) => {
                ceiling._matched_material = {
                    ...matched_material
                }
                ceiling._matched_material.figureElement = ceiling;
            });
        }
        else
        {
            room._ceilling_list = [];
        }

        

        if(room._ceilling_list)
        {
            room._ceilling_list.forEach((ceiling)=>{
                ceiling._room = room;
                ceiling._matched_material.topOffset = room.ceilingHeight;
            })
        }

    
        // this.adjustCeilingRectByCabinets(room,params.storey_height||2800);

        if(room._room_entity)
        {
            room._room_entity.updateCeilingEntity();
        }
    }

    postAdjustHeightForCeilingAndFurnitures(room:TRoom) {
        let cabinets = room._furniture_list.filter((ele)=>FigureCategoryManager.isCustomCabinet(ele));
        let backgroundWalls = room._furniture_list.filter((ele)=>FigureCategoryManager.isBackgroundWallFigure(ele));

        if(room._room_entity.is_auto_ceiling)
        {
            let max_z = 0;
            for(let cabinet of cabinets)
            {
                max_z = Math.max(max_z, cabinet.max_zval);
            }
            room._room_entity.max_cabinet_height = Math.min(max_z, room._room_entity.storey_height);
            room._room_entity.ceiling_height = Math.min(room._room_entity.storey_height - room._room_entity.max_cabinet_height, TRoomEntity.MAX_CEILING_HEIGHT);
        }
        else{
            let max_furniture_height : number = room._room_entity.storey_height - room._room_entity.ceiling_height - room._room_entity.floor_thickness;
            for(let cabinet of cabinets)
            {
                if(cabinet.max_zval >= room._room_entity.storey_height - TRoomEntity.MAX_CEILING_HEIGHT)
                {
                    cabinet.height = max_furniture_height;
                }
            }
            for(let backgroundWall of backgroundWalls)
            {
                if(backgroundWall.max_zval >= room._room_entity.storey_height - TRoomEntity.MAX_CEILING_HEIGHT)
                {
                    backgroundWall.height = max_furniture_height;
                }
            }
        }
    }


    /**
     * 添加窗帘盒吊顶图元
     * @param room  
     * @param figure_elements 
     * @param params 
     */
    addCurtainBoxCeiling(room:TRoom)
    {
        if(!compareNames([room.roomname],["客餐厅","卧室","书房"]))
        {
            return;
        }

        room._curtain_ceiling_list = [];

        const curtain_figures = room.curtainFigureElemens;
        const ceilingTopOffset = 1;
        const ceilingWidth = 200;

        let curtainCeilingMap  = new Map();
        let curtainWallMap = new Map();
        let curtainWindowMap = new Map();
        let curtainSet = new Set();

        for(let curtain of curtain_figures)
        {
            curtainSet.add(curtain);
            let backWallEdge: ZEdge = room.findOutFigureElementBackWall(curtain);
            let adjacentWindow = room.findOutCurtainWindow(curtain);
            if (adjacentWindow == null) continue;
            curtainWallMap.set(curtain, backWallEdge);
            curtainWindowMap.set(curtain, adjacentWindow);
            if (backWallEdge) {
                let wallCenter2FigureRect = curtain.rect.project(backWallEdge.center);
                if (Math.abs(wallCenter2FigureRect.x) <= curtain.rect.length / 2) {
                    let curtainBoxRect = new ZRect(backWallEdge.length, ceilingWidth);
                    curtainBoxRect.nor = curtain.rect.nor;
                    curtainBoxRect.back_center = backWallEdge.center;
                    curtainBoxRect.updateRect();

                    let curtainBoxCeiling = TFigureElement.createSimple("吊顶");

                    let ceilingMatchedMaterial = {
                        modelId: "204935809",
                        topOffset: ceilingTopOffset,
                        figureElement: curtainBoxCeiling,
                        modelLoc: "吊顶"
                    } as I_MaterialMatchingItem;

                    curtainBoxCeiling.rect.copy(curtainBoxRect);
                    curtainBoxCeiling._matched_rect = curtainBoxRect.clone();
                    curtainBoxCeiling._matched_material = ceilingMatchedMaterial;
                    curtainCeilingMap.set(curtain, curtainBoxCeiling);
                }
            }
        }

        curtainWindowMap.forEach((window:I_Window, curtain:TFigureElement) => {
            if (!curtainSet.has(curtain)) return;
            curtainWindowMap.forEach((window2:I_Window, curtain2:TFigureElement) => {
                if (curtain === curtain2 || !curtainSet.has(curtain2)) return;
                    let ccd = curtain.rect.project(curtain2.rect.rect_center);
                    if (Math.abs(ccd.x) <= (curtain.rect.length / 2 + 200) 
                        && Math.abs(ccd.y) <= (curtain2.rect.length / 2 + 200)) {
                        if (window.length > window2.length) {
                            curtainSet.delete(curtain2);
                        } else {
                            curtainSet.delete(curtain);
                        }
                    }
            });
        });

        curtainCeilingMap.forEach((curtainBox, curtain) => {
            if (!curtainSet.has(curtain)) return;
            room._curtain_ceiling_list.push(curtainBox);
            curtain.rect._attached_elements[TFigureElement.CeilingElement] = curtainBox;
        });
    }

    /**
     * 调整窗帘图元尺寸
     * @param room  房间对象
     * @param storey_height 层高
     * @param floor_z 地板布置高度
     */
    adjustCurtainSize(room:TRoom, storey_height:number, floor_z:number)
    {
        let curtain_figures = room._furniture_list.filter((ele)=>compareNames([ele.category],["窗帘"]));
        if (!curtain_figures || curtain_figures.length == 0) return;

        const main_ceiling_height = room.ceilingHeight;

        for(let curtain of curtain_figures)
        {
            // 这段代码的作用是调整窗帘的高度，主要逻辑如下:
            // 1. 获取窗帘对应的吊顶图元
            let curtain_box_ceiling:TFigureElement = curtain.rect._attached_elements[TFigureElement.CeilingElement];
            let ceiling_height;

            // 2. 确定吊顶高度:
            // - 如果有匹配的吊顶素材,使用吊顶素材的下吊高度
            // - 否则使用房间的主吊顶高度
            if(curtain_box_ceiling && curtain_box_ceiling.haveMatchedMaterial())
            {
                ceiling_height = curtain_box_ceiling._matched_material.topOffset;
            } else {
                ceiling_height = main_ceiling_height;
            }

            // 3. 计算窗帘实际高度 = 层高 - 吊顶高度 - 地板高度 - 窗帘中心点Z坐标
            let curtain_height = storey_height - ceiling_height - floor_z - curtain.rect.rect_center_3d.z;

            // 4. 更新窗帘参数和匹配素材的目标高度
            if(curtain.params)
            {
                curtain.params.height = curtain_height;
            }
            if(curtain.haveMatchedMaterial())
            {
                curtain._matched_material.targetSize.height = curtain_height;
            }
        }
    }

    /**
     * 根据吊顶高度调整空调和筒灯的位置
     *   若空调离地放置时，空调离地高度位置为：吊顶往下400mm，即层高 - 吊顶高度 - 400mm，确保空调安装在吊顶下方400mm的位置
     *   对于筒灯,则将其高度设置为:层高 - 吊顶高度 - 筒灯自身高度
     * @param room 房间对象
     * @param storeyHeight 层高
     */
    public adjustMaterialPositionByCeiling(room:TRoom, storeyHeight:number)
    {
        let airConditions = room._furniture_list.filter((ele)=>compareNames([ele.category],["空调","筒灯"]));
        if (!airConditions || airConditions.length == 0) return;
        for(let airCondition of airConditions)
        {
            let material = airCondition._matched_material;
            // 调整空调的高度位置:
            // 1. 如果当前图元是空调,并且有离地规则
            // 2. 如果离地规则为"离地",则将空调的高度设置为:层高 - 吊顶高度 - 400mm
            // 这样可以确保空调安装在吊顶下方400mm的位置
            if (material && material.offLandRule && material.modelLoc == "空调") {
                if (material.offLandRule == "离地") {
                    material.targetPosition.z = storeyHeight - room.ceilingHeight - 400;
                }
            }
            // 调整筒灯的高度位置:
            // 1. 如果当前图元是筒灯,则将其高度设置为:层高 - 吊顶高度 - 筒灯自身高度
            // 2. 同时更新图元的Z轴坐标值,使其与筒灯的目标位置保持一致
            if(material && material.modelLoc=="筒灯")
            {
                material.targetPosition.z = storeyHeight - room.ceilingHeight - material.targetSize.height;
                airCondition.matched_rect.zval = material.targetPosition.z;
            }   
        }
    }

    private postAddLivingRoomCeiling(room:TRoom,figure_elements:TFigureElement[])
    {
        // this.postAddSimpleRoomCeiling(room,figure_elements);
        room._ceilling_list = [];
        if(room._room_entity && room._room_entity._sub_room_areas.length > 0 && room._room_entity.is_auto_ceiling)
        {
            let living_area = room._room_entity._sub_room_areas.find((area)=>area.space_area_type==="LivingArea");
            if(living_area)
            {
                let figure = this.creatSimpleCeiling();
                figure.sub_category = "客厅";
                figure.rect.copy(living_area.rect);
                figure.rect._attached_elements[TSubSpaceAreaEntity.EntityType] = living_area;
                room._ceilling_list.push(figure);
            }
            let dinning_area = room._room_entity._sub_room_areas.find((area)=>area.space_area_type==="DiningArea");
            if(dinning_area)
            {
                let figure = this.creatSimpleCeiling();
                figure.sub_category = "餐厅";
                figure.rect.copy(dinning_area.rect);
                figure.rect._attached_elements[TSubSpaceAreaEntity.EntityType] = dinning_area;
                room._ceilling_list.push(figure);
            }            
        }
        else{
            let sofa_element = figure_elements.find(ele=>compareNames([ele.category],["沙发"]));
            let dinning_table_element = figure_elements.find((ele)=>compareNames([ele.category],["餐桌"]));
    
            if(!sofa_element || !dinning_table_element)
            {
                this.postAddSimpleRoomCeiling(room,figure_elements);
                return;
            }
            let sofa_neighbor_elements = figure_elements.filter((ele)=>{
                if(compareNames([ele.category],["休闲椅","脚踏","茶几","地毯","边几","落地灯","绿植"]))
                {
                    let pp = sofa_element.rect.project(ele.rect.rect_center,true);
                    if(pp.y < 0) return false;
    
                    if(Math.abs(pp.x) < sofa_element.rect.w) return true;
                }
                
                return false;
            });
            sofa_neighbor_elements.push(sofa_element);
            if(room.valid_shape_list && room.valid_shape_list.length > 0)
            {
                let sofa_valid_shape = room.valid_shape_list.find((shape)=>shape._poly.containsPoint(sofa_element.rect.rect_center));
                let sofa_valid_rect:ZRect = null;
                if(sofa_valid_shape) sofa_valid_rect = sofa_valid_shape.getRect();
    
                let dinning_valid_shape = room.valid_shape_list.find((shape)=>shape._poly.containsPoint(dinning_table_element.rect.rect_center));
                
                if(sofa_valid_rect) // 沙发所在的区域
                {
                    if(!sofa_valid_rect.checkSameNormal(sofa_element.rect.nor))
                    {
                        sofa_valid_rect.swapWidthAndHeight();
                    }
    
                
    
    
                    let points :Vector3[] = [];
    
                    sofa_neighbor_elements.forEach((ele)=>points.push(...ele.rect.positions));
    
                    let sofa_area_rect = ZRect.fromPoints(points, sofa_element.rect.nor);
                    sofa_area_rect._h += 5000;
                    sofa_area_rect.updateRect();
                    sofa_area_rect.reOrderByOrientation(true);
                    sofa_valid_rect.reOrderByOrientation(true);
    
                    let  t_sofa_area_rect = sofa_area_rect.intersect_rect(sofa_valid_rect);
    
    
                    if (t_sofa_area_rect) {
                        sofa_area_rect = t_sofa_area_rect;
    
                        let rect1 = sofa_valid_rect.clip_side_rect(sofa_area_rect);
    
                        room._ceilling_list.push(this.creatSimpleCeiling());
    
                        room._ceilling_list[0].rect.copy(sofa_area_rect);
    
    
                        if(dinning_valid_shape && dinning_valid_shape !== sofa_valid_shape)
                        {
                            room._ceilling_list.push(this.creatSimpleCeiling());
                            room._ceilling_list[1].rect.copy(ZRect.computeMainRect(dinning_valid_shape._poly));
    
                        }
                        else
                        {
                            room._ceilling_list.push(this.creatSimpleCeiling());
                            room._ceilling_list[1].rect.copy(rect1);
                        }
                    }
                }
    
    
                if(room._ceilling_list[1] && dinning_table_element)
                {
                    let t_rect = room._ceilling_list[1].rect;
    
                    let dinning_rect = dinning_table_element.rect;
    
    
                    let t_d_rect = dinning_rect.clone();
                    let r_center = t_d_rect.rect_center;
                    t_d_rect._w += 6000;
                    t_d_rect._h += 1680;
                    t_d_rect.rect_center = r_center;
    
                    t_d_rect.reOrderByOrientation();
                    let res_rect = t_d_rect.intersect_rect(t_rect);
    
                    if(res_rect)
                    {
                        if(res_rect.w < res_rect.h)
                        {
                            res_rect.swapWidthAndHeight();
                        }
                        room._ceilling_list[1].rect.copy(res_rect);
                    }
    
      
    
                    if(dinning_valid_shape === sofa_valid_shape)
                    {
                        let dinning_ceiling_rect = room._ceilling_list[1].rect;
                        let w_poly = sofa_valid_shape._feature_shape?._w_poly;
                        let table_rect = dinning_table_element.rect;
                        if(w_poly)
                        {
                            let cutted_rects : ZRect[] = [];
                            for(let edge of w_poly.edges)
                            {
                                let win = WPolygon.getWindowOnEdge(edge);
                                if(win && (win.type==="Door" || win.type==="Hallway") && (!compareNames(win.room_names||[],["厨房","阳台"])))
                                {
                                    if(table_rect.checkSameNormal(edge.dv,true))
                                    {
                                        let t_rect = new ZRect(edge.length, 900);
                                        t_rect.nor = edge.nor.clone().negate();
                                        t_rect.back_center = edge.center;
                                        t_rect.updateRect();
                                        cutted_rects.push(t_rect);
                                    }
                                }
                            }
    
                            for(let rect of cutted_rects)
                            {
                                let t_rect = dinning_ceiling_rect.clip_side_rect(rect,table_rect.dv);
                                if(t_rect)
                                {
    
                                    if(t_rect.containsPoly(table_rect) )
                                    {
                                        dinning_ceiling_rect.copy(t_rect);
                                    }
    
                                }
                            }
                           
                        }
                    }
                    
    
    
    
    
    
    
                }
    
    
            }
        }
      
        if(room._ceilling_list.length == 0)
        {
            this.postAddSimpleRoomCeiling(room,figure_elements);
        }



        if(compareNames([room.roomname],["客餐厅"]))
        {
            if(room._ceilling_list[0])
            {
                room._ceilling_list[0].sub_category = "客厅";
            }
            if(room._ceilling_list[1])
            {
                room._ceilling_list[1].sub_category = "餐厅";
            }
        }
        else{
            if(room._ceilling_list[0])
            {
                room._ceilling_list[0].sub_category = room.roomname;
            }
        }

        this.expandCeilingsInRoom(room);
        for(let ceiling of room._ceilling_list)
        {
            this.adjustCeilingByCurtainBoxs(ceiling, room._curtain_ceiling_list);
        }
    }

    private expandCeilingsInRoom(room:TRoom)
    {
        let ceiling_list = [...room._ceilling_list];
        let poly = room.room_shape._poly; // 延拓的时候, 只需要考虑原始poly就好了

        // 先做无脑延拓试试看

        let target_polys : ZPolygon[] = [poly];
        
        ceiling_list.forEach((ceiling,index)=>{
            let other_ceilings = ceiling_list.filter(c=>c!=ceiling);

            let align_edges : ZEdge[] = [];
            target_polys.forEach((poly)=>align_edges.push(...poly.edges));
            other_ceilings.forEach((c)=>align_edges.push(...c.rect.edges));

            let origin_rect = ceiling.rect.clone();
            let target_rect = ceiling.rect.clone();

            // 分成四条边来延拓
            
            let iter = 1;
            let tol = 1;
            while(iter--)
            {
                let dir_edges = [...target_rect.edges];
                dir_edges.sort((a,b)=>b.length - a.length); // 先延拓长边, 这样会让区域更接近正方形
                let edge_nors = dir_edges.map((e)=>e.nor.clone());
                let max_hh = target_rect.max_hh;
                edge_nors.forEach((edge_nor)=>{
                    // dir_edge 主要是为了定位方向, target_rect的edges会不断发生变化
                    let target_edge = target_rect.edges.find((e=>e.checkSameNormal(edge_nor,false,0.1))); 

                    let resultEdge = align_edges.reduce((prevResultEdge,currentEdge)=>{
                        if(currentEdge.islayOn(target_edge,max_hh,(50/target_edge.length) ))
                        {
                            let pp = target_edge.projectEdge2d(currentEdge.center);
                            if(pp.y >= -tol)
                            {
                                if(!prevResultEdge || pp.y < target_edge.projectEdge2d(prevResultEdge.center).y)
                                {
                                    return currentEdge;
                                }
                            }
                        }
                        return prevResultEdge;
                    },null);

                    if(resultEdge)
                    {
                        let pp = target_edge.projectEdge2d(resultEdge.center);
                        let pos = target_edge.unprojectEdge2d({x:0,y:pp.y});
                        target_rect = ZRect.fromPoints([...target_rect.positions,pos],target_rect.nor,target_rect.u_dv_flag);
                    }
                })                                
            }
            ceiling.rect.copy(target_rect);            
        })


    }
    private adjustCeilingByCurtainBoxs(ceiling:TFigureElement, curtainBoxList:TFigureElement[])
    {
        if(!ceiling || !curtainBoxList) return;
        let ceilingRect = ceiling.rect.clone();
        for(let curtainBox of curtainBoxList)
        {
            let curtainBox_rect =  curtainBox.rect.clone();
            let clip_result_rect = ceilingRect.clip_side_rect(curtainBox_rect);
            if(clip_result_rect)
            {
                ceilingRect.copy(clip_result_rect);
            }
        }

        ceiling.rect.copy(ceilingRect);
    }

    //根据房间内的定制柜，调整吊顶区域矩形
    public adjustCeilingRectByCabinets(room:TRoom, storey_height:number) {
        if(!LayoutAI_App.instance.Configs.needs_adjust_ceiling_after_matching) return;
        if (!room._ceilling_list) return;
        for (let ceiling of room._ceilling_list) {
            //根据房间内的定制柜，调整吊顶区域矩形
            this.adjustSingleCeilingByCabinets(ceiling, room._furniture_list, storey_height);
        }
    }

    //根据房间的吊顶高度属性，调整吊顶素材的下吊高度
    public adjustCeilingTopOffsetByRoom(room:TRoom) {
        if (!room._ceilling_list) return;
        for (let ceiling of room._ceilling_list) {
            if(ceiling._matched_material)
            {
                ceiling._matched_material.topOffset = room.ceilingHeight;
            }
        }
        if(room._room_entity)
        {
            room._room_entity.room_ceiling_entity.update();
        }
    }

    protected adjustSingleCeilingByCabinets(ceiling:TFigureElement, figure_elements:TFigureElement[], storey_height:number)
    {
        // 找出所有符合以下条件的定制柜图元：匹配到的素材的高度大于或等于2米
        let cabinet_elements = figure_elements.filter((ele)=>{
            if(compareNames([ele.category], [...TGraphBasicConfigs.MainCabinetsCategories,"沙发背景墙"], false)) {
                if((ele.matchedMaterialId && ele._matched_material?.targetSize?.height||0) > (2000 -0.1) )
                {
                    return true;
                }
                if(!ele.matchedMaterialId && ele.height > (2000 -0.1))
                {
                    return true;
                }
            }
            return false;
        });

        // 遍历筛选出来的定制柜图元，计算出定制柜3D矩形（限定深度为200mm至600mm）,然后用吊顶区域矩形减去定制柜区域矩形，算出新的吊顶区域。
        // 重新计算吊顶区域的目的是为了防止吊顶和定制柜发生冲突。
        let rect = ceiling.rect.clone();
        for(let cabinet of cabinet_elements)
        {
            let t_rect =  cabinet.rect.clone();
            if(cabinet._matched_rect)
            {
                t_rect = cabinet.matched_rect.clone();
            }
            if(t_rect.h < 100)
            {
                t_rect._h = 100;
                t_rect.updateRect();
            }
            else {
                // 产品要求定制柜和吊顶之间不要多余的缝隙 因此取整这个逻辑先去掉
                // let hh =  Math.ceil(t_rect.h / 100) * 100;
                // t_rect._h = hh;
                t_rect._h = Math.min(t_rect.h,600);
                t_rect.updateRect();

            }

            let res_rect = rect.clip_side_rect(t_rect);
            if(res_rect)
            {
                rect.copy(res_rect);
            }
        }

        ceiling.rect.copy(rect);
    }

    private postAddBedRoomCeiling(room:TRoom,figure_elements:TFigureElement[])
    {
        let max_R_shape = room.max_R_shape;
        if(!max_R_shape)
        {
            room.computeShapeList();
            max_R_shape = room.max_R_shape;
        }
        if(!room._ceilling_list || room._ceilling_list.length == 0)
        {
            room._ceilling_list = [this.creatSimpleCeiling()];
        }
        if(room._ceilling_list.length > 1) room._ceilling_list.length = 1;

        let rect = ZRect.computeMainRect(max_R_shape._poly);

        let bed_element = figure_elements.find(val=>compareNames([val.category],["床"],false));

        if(bed_element) // 有床就重定向
        {
            let r_center = rect.rect_center;

            if(Math.abs(rect.nor.dot(bed_element.rect.nor)) < 0.5)
            {
                rect.swapWidthAndHeight();
            }

            if(rect.nor.dot(bed_element.rect.nor) < 0)
            {
                rect.nor = rect.nor.clone().negate();
                rect.rect_center = r_center;
            }
        }
        let win_elements = figure_elements.filter((val)=>compareNames([val.category],["窗帘","衣柜","书柜"]));

        for(let ele of win_elements)
        {
            let t_rect = ele.rect.clone();
            if(t_rect.h < 200)
            {
                t_rect._h = 200;
                t_rect.updateRect();
            }

            let t_edge = rect.edges.find((edge)=>{
                return edge.islayOn(t_rect.backEdge,t_rect.h*2,0.6);
            })

            if(t_edge)
            {
                let pp = t_edge.projectEdge2d(t_rect.frontEdge.center);
                if(pp.y < 0)
                {
                    t_edge.moveEdge(t_edge.nor.clone().multiplyScalar(pp.y),0);
                    rect.reParaFromVertices();
                }

            }
        }

        let ceiling = room._ceilling_list[0];
        ceiling.rect.copy( rect);
        ceiling.rect.updateRect();

        this.adjustCeilingByCurtainBoxs(room._ceilling_list[0], room._curtain_ceiling_list);
    }

    private  postAddSimpleRoomCeiling(room:TRoom,figure_elements:TFigureElement[])
    {
        let max_R_shape = room.max_R_shape;
        if(!max_R_shape)
        {
            room.computeShapeList();
            max_R_shape = room.max_R_shape;
        }
        if(!room._ceilling_list || room._ceilling_list.length == 0)
        {
            room._ceilling_list = [this.creatSimpleCeiling()];
        }
        if(room._ceilling_list.length > 1) room._ceilling_list.length = 1;

        let ceiling = room._ceilling_list[0];
        ceiling.rect.copy( ZRect.computeMainRect(max_R_shape._poly));
        ceiling.rect.updateRect();

        if(ceiling._matched_material)
        {
            ceiling._matched_rect = ceiling.rect.clone();
        }
    }




}