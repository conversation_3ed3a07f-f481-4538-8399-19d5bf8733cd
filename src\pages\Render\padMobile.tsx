import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Button, Modal, message } from "@svg/antd";
import { useEffect, useState, useRef } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2DesignBasicModes, AI2DesignManager } from "@/Apps/AI2Design/AI2DesignManager";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import Scene3DDiv from "@/components/Scene3DDiv/scene3DDiv";
import MobileNavigation, { NavigationEvent, PageStates } from "./navigation/navigation";
import { _createHxId, _editHxId, checkIsMobile, is_debugmode_website, is_dreamer_mini_App, mini_APP, mode_type, scheme_Id, workDomainMap } from "@/config";
import { HomeProgress } from '@/components';
import HouseSearchPopup from './houseSearchPopup/houseSearchPopup';
import SaveLayoutSchemeDialog from "./SaveLayoutSchemeDialog/SaveLayoutSchemeDialog";
import DreamerPopup from "../Home/DreamerPopup/dreamerPopup";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { LayoutSchemeService } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { loadFile } from "@/IndexDB";
import { getHouseScheme } from "@/services/home/<USER>";
import CustomKeyboard from "@/components/CustomKeyBoard/customKeyBoard";
import MultiSchemeList from "./multiScheme/multiSchemeList/multiSchemeList";
import DreamerHxSearch from '@/components/HxSearch/dreamerHxSearch';
import RoomImagePredict from '@/components/RoomImagePredict/roomImagePredict';
import PadPanels from "./padPanel/padPanels";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { SdkService } from "@/services/SdkService";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import EnterPage from "./EnterPage/enterPage";
import { If } from "react-if";
import CabinetCompute from "./CabinetCompute/cabinetCompute";
import { GuidanceChannel } from "@svg/antd-cloud-design";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { Vector3 } from "three";
import ViewSelectBox from "./ViewSelectBox/viewSelectBox";
import HouseMatch from "./houseMatch/index";
import MyCase from "./myCase";
import HouseDetail from "./EnterPage/houseDetail/houseDetail";
import { OutlineMode } from '@/Apps/LayoutAI/Scene3D/SceneMode';
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";

/**
 * @description 主页
 */

const App: React.FC = () => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [layoutSchemeName, setLayoutSchemeName] = useState<string>("");
  const [zIndexOf3DViewer, setZIndexOf3DViwer] = useState<number>(-2);
  const store = useStore();
  const { confirm } = Modal;
  // const schemeNameRef = useRef(null);
  const messageKey = 'SaveSchemeProgress';
  const [messageApi, contextHolder] = message.useMessage();
  const [isSaveAs, setIsSaveAs] = useState<boolean>(false);
  const [showKeyboard, setShowKeyboard] = useState<boolean>(false);
  const [input, setInput] = useState<HTMLInputElement | null>(null);
  const [inputValue, setInputValue] = useState<string>('');
  const [updateKey, setUpdateKey] = useState<number>(0);
  const hxSearchRef = useRef<any>();

  const [isPanelOpen, setIsPanelOpen] = useState<boolean>(true); // 是否打开pad面板
  const [chosedMode, setChosedMode] = useState<string>(''); // AI生成或者是户型出图
  const [hxId, setHxId] = useState<string>(''); // 打开方案选中的户型id

  LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
  if (LayoutAI_App.instance) {
    LayoutAI_App.t = t;
  }

  const object_id = "PadMobile";

  const getChosedMode = (mode: string) => {
    setChosedMode(mode);
  }

  // 统一的画布管理工具函数
  const updateCanvasForViewMode = (targetViewMode: string, delay = 100) => {
    if (!LayoutAI_App.instance) return;

    // 先停止3D渲染
    const scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    if (scene3D) {
      scene3D.stopRender();
    }

    setTimeout(() => {
      const canvas = document.getElementById("cad_canvas") as HTMLCanvasElement;
      const container = document.getElementById("body_container");
      
      if (!canvas) return;

      // 重新绑定画布
      LayoutAI_App.instance.bindCanvas(canvas);
      
      // 根据目标模式设置画布尺寸
      if (targetViewMode === '2D') {
        // 2D模式：画布占满全屏
        if (container) {
          container.style.width = '100%';
          container.style.height = '100%';
        }
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        // 使用窗口尺寸确保正确计算
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      } else {
        // 3D模式：画布占40%宽度（左右分屏）
        if (container) {
          container.style.width = '40%';
          container.style.height = '100%';
        }
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        // 计算40%宽度的实际像素值
        const containerWidth = window.innerWidth * 0.4;
        canvas.width = containerWidth;
        canvas.height = window.innerHeight;
      }
      
      // 更新应用状态
      LayoutAI_App.instance.update();
      
      // 如果是3D模式，重新启动渲染
      if (targetViewMode !== '2D' && scene3D) {
        scene3D.startRender();
      }
    }, delay);
  };

  const updateCanvasSize = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
      LayoutAI_App.instance.update();
    }
    updateLandscape();
  };
  const updateIsWebSiteDebug = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_website_debug = is_debugmode_website;
    }
  }

  const updateLandscape = () => {
    if (LayoutAI_App.instance) {
      LayoutAI_App.instance._is_landscape = window.innerWidth > window.innerHeight;
    }
    let t_is_lanscape = store.homeStore.IsLandscape;
    store.homeStore.setIsLandscape(window.innerWidth > window.innerHeight);

    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  }

  const getSchemeData = async () => {
    if (!scheme_Id) return;
    const params = {
      isDelete: 0,
      pageIndex: 1,
      pageSize: 9,
      keyword: scheme_Id
    }
    const { layoutSchemeDataList, total } = await LayoutSchemeService.getLayoutSchemeList(params);
    if (layoutSchemeDataList) {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, layoutSchemeDataList[0]);
      LayoutAI_App.emit(EventName.OpenHouseSearching, false);
    }
  }

  const layoutWorkOpen = async () => {
    // 个人工作台跳转逻辑
    
    if (mode_type === 'HouseId') {
      (async () => {
        try {
          const file = await loadFile('HouseId');
          console.log(file);
          const fileData = (file as { data: any }).data;
          // pad端走的新的流程，选需求
          if(checkIsMobile())
          {
            // store.trialStore.houseData.id = '7JCEHCICKJFIDLJGFC';    //本地调试用
            store.trialStore.houseData.id = fileData;
          } else 
          {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, { id: fileData, name: '' });
          }
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'DwgBase64') {
      (async () => {
        try {
          LayoutAI_App.RunCommand(LayoutAI_Commands.OpenDwgFilefromWork);
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'CopyingBase64') {
      (async () => {
        try {
          const file = await loadFile('CopyingBase64');
          const fileData = (file as { data: any }).data;
          store.homeStore.setImgBase64(fileData);
          if(checkIsMobile())
          {
            console.log('fileData',fileData);    
          } else 
          {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, fileData);
          }
        } catch (error) {
          console.error('Error loading file:', error);
        }
      })();
    } else if (mode_type === 'hxcreate') {
      if (_createHxId) {
        const res = await getHouseScheme({ id: _createHxId });
        res.result.contentUrl = res.result.dataUrl;
        LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, res.result);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);

      }
    } else if (mode_type === 'hxedit') {
      if (_editHxId) {
        const res: any = await getHouseScheme({ id: _editHxId });
        if (res.success && res.result && res.result.dataUrl) {
          res.result.contentUrl = res.result.dataUrl;
          LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, res.result);
        }
      }
      if (LayoutAI_App.instance) {
        if ((LayoutAI_App.instance as TAppManagerBase)?.layout_container?._drawing_layer_mode == 'SingleRoom') {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
        }
      }
      LayoutAI_App.instance._current_handler_mode = AI2DesignBasicModes.HouseDesignMode;
      LayoutAI_App.RunCommand(AI2DesignBasicModes.HouseDesignMode);
      store.homeStore.setDesignMode(AI2DesignBasicModes.HouseDesignMode);
    } 
  }

  const handleKeyPress = (key: string) => {
    if (input) {
      input.value = input.value + key;
      setInputValue(input.value);
    }
  };

  const handleDelete = () => {
    if (input) {
      input.value = input.value.slice(0, -1);
      setInputValue(input.value);
    }
  };

  const handleConfirm = () => {
    if (input) {
      LayoutAI_App.DispatchEvent(LayoutAI_Events.DimensionInput, input);
      setShowKeyboard(false);
      setInputValue('');
    }
  };

  const handleClose = () => {
    setShowKeyboard(false);
    setInputValue('');
  };

  // 是去效果还是去布局, 默认去布局
  const [toLayoutType, setToLayoutType] = useState<'layout' | 'view'>('layout');
  const toLayout = (type: 'layout' | 'view') => {
    setToLayoutType(type);
    if(type === 'view')
    {
      // 看效果 - 直接进入左右分屏模式
      (async () => {
        // 先确保2D画布加载完成
        store.homeStore.setViewMode('2D');
        
        // 延迟切换到3D模式，触发左右分屏显示
        setTimeout(async () => {
          await TSeriesFurnisher.instance.autoApplySeries();
          store.homeStore.setViewMode('3D_FirstPerson');
          // 设置3D画布显示
          setZIndexOf3DViwer(2);
          store.homeStore.setZIndexOf3DViewer(2);
          // 使用统一的画布更新函数
          updateCanvasForViewMode('3D_FirstPerson');
          const scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
          if(scene3D)
          {
            scene3D.outlineMaterialMode = OutlineMode.MaterialOnly;
            scene3D.update();
          }
        }, 300);
      })();
    } else if(type === 'layout')
    {
      // 去布局 - 先进入2D全屏模式
      (async () => {
        // 隐藏3D画布
        setZIndexOf3DViwer(-2);
        store.homeStore.setZIndexOf3DViewer(-2);
        // 切换到2D模式，2D画布占满全屏
        store.homeStore.setViewMode('2D');
        // 使用统一的画布更新函数
        updateCanvasForViewMode('2D', 300);
      })();
    }
  }

  const getHxId = (hxId: string) => {
    store.homeStore.setIsShowHouseDetail({show: true, source: 'myCase'});
    setHxId(hxId);
  }

  /**
   * 截取3D场景
   * @returns Promise<string> 返回截图数据的base64字符串
   */
  const capture3DScene = (): Promise<string> => {
    return new Promise((resolve, reject) => {
      const scene3D = LayoutAI_App.instance.scene3D as Scene3D;
      
      if (!scene3D || !scene3D.isValid()) {
        console.warn('3D场景不可用，返回空图片');
        resolve('');
        return;
      }

      try {
        // 直接使用Scene3D的截图方法，保持原始格式
        const screenshotDataUrl = scene3D.sreenShot();
        console.log('screenshotDataUrl1111', screenshotDataUrl);
        resolve(screenshotDataUrl);
      } catch (error) {
        console.error('3D截图失败:', error);
        reject(error);
      }
    });
  };

  const handleAIGeneration = async () => {
    const container = (LayoutAI_App.instance as AI2DesignManager).layout_container;
    if (container._room_entities.length == 0) {
      message.error(t('当前方案为空，无法保存！'));
      return;
    }
    
    if (container._layout_scheme_id == null) {
      store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: ''});
      return;
    }
    
    try {
      // 保存方案
      LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);
      
      // 截取3D场景
      const screenshotDataUrl = await capture3DScene();
      
      // 发送给父窗口
      window.parent.postMessage({
        origin: 'layoutai.api',
        type: 'aiGeneration',
        data: {
          aiGeneration: true,
          img: screenshotDataUrl,
        }
      }, '*');
      console.log('aiGeneration', screenshotDataUrl);
      
    } catch (error) {
      console.error('生图过程出错:', error);
      message.error(t('生图失败，请重试'));
    }
  }

  useEffect(() => {
    // 默认第一次打开就进入选户型页面
    store.homeStore.setShowEnterPage({show: true, source: ''});
    // 确保初始状态为2D全屏模式
    store.homeStore.setViewMode('2D');
    setZIndexOf3DViwer(-2);
    store.homeStore.setZIndexOf3DViewer(-2);
  },[])

  useEffect(() => {
    LayoutContainerUtils.updateAliasName();
    store.homeStore.setRoomEntites((LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities);
  }, [(LayoutAI_App.instance as TAppManagerBase).layout_container._room_entities])

  useEffect(() => {
    updateIsWebSiteDebug();
    // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
    window.addEventListener('resize', updateCanvasSize);
    updateCanvasSize();

    if (LayoutAI_App.instance) {
      if (!LayoutAI_App.instance.initialized) {
        if(checkIsMobile() && (mode_type === 'HouseId' || mode_type === 'CopyingBase64')){
          LayoutAI_App.emit(EventName.Initializing, { initializing: true });
        }
        LayoutAI_App.instance.init();
        // 默认先进入AiCadMode
        LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
        LayoutAI_App.instance.prepare().then(() => {
          // 个人工作台跳转过来的相关逻辑
          getSchemeData();
          layoutWorkOpen();
          LayoutAI_App.emit(EventName.Initializing, { initializing: false });
          // 添加安全检查，确保 LayoutAI_App.instance 和 scene3D 都存在
          if (LayoutAI_App.instance && LayoutAI_App.instance.scene3D) {
            let scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
            if (scene3D) {
              scene3D.stopRender();
            }
          }
        })
        LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);

        // 梦想家进入就打开户型库
        // if (mini_APP) {
        //   LayoutAI_App.emit(EventName.OpenHouseSearching, true);
        // }
      }
      if (window?.URLSearchParams) {
        const urlParams = new URLSearchParams(window.location.search);
        const debug = urlParams.get("debug");
        if (debug !== null) { // 只有在 debug 存在时才执行赋值
          const debugValue = debug === '1' ? 1 : 0; // 根据 debug 的值设置 debugValue
          if (localStorage) {
            localStorage.setItem("LayoutAI_Debug", String(debugValue));
            LayoutAI_App.instance._debug_mode = debugValue;
          }
        }
      }

      LayoutAI_App.instance.update();
    }
    LayoutAI_App.on_M('showLight3DViewer', object_id, (t: boolean) => {
      if (t) {

        if (zIndexOf3DViewer < 0) {
          setZIndexOf3DViwer(2);
          store.homeStore.setZIndexOf3DViewer(2);
          LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, true);
        }
      }
      else {
        setZIndexOf3DViwer(-2);
        store.homeStore.setZIndexOf3DViewer(-2);
      }
    });
    LayoutAI_App.on(EventName.ShowDreamerPopup, (t: boolean) => {
      store.homeStore.setShowDreamerPopup(t);
    })
    LayoutAI_App.on(EventName.LayoutSchemeOpened, (event: any) => {
      setLayoutSchemeName(event.name);
      LayoutAI_App.emit(NavigationEvent, PageStates.Default);
    });
    LayoutAI_App.on(EventName.ClearLayout, () => {
      confirm({
        title: t('清空布局'),
        content: t('确定清空单空间布局？'),
        okText: t('确定'),
        cancelText: t('取消'),
        onOk() {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearLayout, this);
        },
        onCancel() { },
      });
    });
    LayoutAI_App.on(EventName.OpenMySchemeList, () => {
      store.homeStore.setShowMySchemeList(true);
    });

    // 这里的showWelcomPage修改
    // LayoutAI_App.on(EventName.ShowWelcomePage,(t:boolean)=>{
    //   LayoutAI_App.emit(EventName.OpenHouseSearching,t);
    // });
    LayoutAI_App.on_M(EventName.RoomList, 'room_list', (roomList: TRoom[]): void => {
      store.homeStore.setRoomInfos(roomList);
    });
    LayoutAI_App.on(EventName.Room2SeriesSampleRoom, (array: [TRoom, TSeriesSample][]) => {
      store.homeStore.setRoom2SeriesSampleArray(array);
    });

    LayoutAI_App.on(EventName.showCustomKeyboard, (event: any) => {
      setTimeout(() => {
        setShowKeyboard(true);
      }, 50); // 50ms 延迟
      if (event.input) {
        setInputValue(event.input.value);
        setInput(event.input);
      }
    });


    LayoutAI_App.on_M(EventName.updateAllMaterialScene3D, 'padMobile', async (state: number) => {
      // console.log("SceneContentStateChanged",state);
      let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
      // 添加安全检查，确保 LayoutAI_App.instance 和 scene3D 都存在
      if (!LayoutAI_App.instance || !LayoutAI_App.instance.scene3D) {
        return;
      }
      let scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
      if (state && store.homeStore.viewMode === '3D_FirstPerson') {
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "更新视角中..." });
        const viewsToUpdate: TViewCameraEntity[] = [];
        container._room_entities.forEach((room) => {
            room._view_cameras.forEach((view) => {
                viewsToUpdate.push(view);
            });
        });
        // 并行执行所有更新操作
        const updatePromises = viewsToUpdate.map(async (view) => {
            if(!view._perspective_img.src)
            {
              scene3d.active_controls.bindViewEntity(view);
              scene3d.update();
              await view.updatePerspectiveViewImg(container.painter);
            }
        });
        await Promise.allSettled(updatePromises);

        let room = container._room_entities.reduce((maxRoom: TRoomEntity | null, currentRoom: TRoomEntity) => {
          if (!maxRoom) return currentRoom;
          return currentRoom._area > maxRoom._area ? currentRoom : maxRoom;
        }, null);
        if (room) {
          scene3d.active_controls.bindViewEntity(room._view_cameras[0]);
          store.homeStore.setCurrentViewCameras(room._view_cameras);
        } else 
        {
          scene3d.setCenter(room?._main_rect?.rect_center || new Vector3(0, 0, 0));
        }
        
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });
      }
    });

    LayoutAI_App.on(EventName.SaveProgress, (event: { progress: string, id: string, name: string }) => {
      if (event.progress === "success") {
        messageApi.open({
          key: messageKey,
          type: 'success',
          content: t('布局方案保存成功'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
        if(store.homeStore.isAutoExit === 'autoExit')
        {
          SdkService.exitSDK();
          window.parent.postMessage({
              origin: 'layoutai.api',
              type: 'canClose',
              data: {
              canClose: true
              }
          }, '*');
          window.location.href = workDomainMap;
        }
        store.homeStore.setIsAutoExit('');

      } else if (event.progress === "fail") {
        messageApi.open({
          key: messageKey,
          type: 'error',
          content: t('布局方案保存失败'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      } else if (event.progress === "ongoing") {
        messageApi.open({
          key: messageKey,
          type: 'loading',
          content: t('正在保存布局方案'),
          duration: 3,
          style: {
            marginTop: '6vh',
            zIndex: 9999,
          }
        });
      }
    });

    LayoutAI_App.on_M(EventName.xmlSchemeLoaded, 'padMobile', (data: any) => {
      const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
      if(container)
      {
        TViewCameraEntity.updateViewCameraEntities(container,null,{methods: 2});
      }
    });
  }, [store.homeStore.isAutoExit]);


  useEffect(() => {
    if (store.homeStore.zIndexOf3DViewer === 4) {
      setZIndexOf3DViwer(2);
    } else 
    {
      setZIndexOf3DViwer(store.homeStore.zIndexOf3DViewer);
    }
  }, [store.homeStore.zIndexOf3DViewer]);

  // 监听viewMode变化，自动更新isPanelOpen状态
  useEffect(() => {
    // 当viewMode为2D时显示面板，其他情况隐藏面板
    if (store.homeStore.viewMode === '2D') {
      setIsPanelOpen(true);
    } else {
      setIsPanelOpen(false);
    }
  }, [store.homeStore.viewMode]);

  // 监听viewMode变化，确保画布正确更新
  useEffect(() => {
    if (LayoutAI_App.instance) {
      // 使用统一的画布更新函数
      updateCanvasForViewMode(store.homeStore.viewMode);
    }
  }, [store.homeStore.viewMode]);

  // 专门监听从3D模式切换到2D模式时的画布重新加载
  useEffect(() => {
    if (store.homeStore.viewMode === '2D' && LayoutAI_App.instance) {
      // 使用统一的画布更新函数，延迟400ms确保DOM更新完成
      updateCanvasForViewMode('2D', 400);
    }
  }, [store.homeStore.viewMode]);

  return (
    <div className={styles.root} style={{backgroundColor: store.homeStore.viewMode === '3D_FirstPerson' ? '#fff' : 'transparent'}}>
      <PadPanels
        updateKey={updateKey}
        isPanelOpen={isPanelOpen}
        layoutType={toLayoutType}
        setChosedModeFun={getChosedMode}
        updateCanvasForViewMode={updateCanvasForViewMode}
      ></PadPanels>

      <If condition={store.homeStore.showEnterPage.show}>
        <EnterPage></EnterPage>
      </If>
      <CabinetCompute></CabinetCompute>
      <HouseSearchPopup></HouseSearchPopup>
      <HomeProgress></HomeProgress>
      <MultiSchemeList></MultiSchemeList>

      {store.homeStore.showDreamerPopup && <DreamerPopup></DreamerPopup>}

      {/* {store.homeStore.isSingleRoom &&
        <div className={styles.RoomAreaBtns}>
          <RoomAreaBtns mode={1} />
        </div>} */}
      {store.homeStore.showSaveLayoutSchemeDialog.show &&
        <div className={styles.overlay}>
          <SaveLayoutSchemeDialog
            schemeName={layoutSchemeName || ''}
            closeCb={() => { store.homeStore.setShowSaveLayoutSchemeDialog({show: false, source: ''}) }}
            isSaveAs={isSaveAs}
          />
        </div>
      }
      {store.homeStore.showMySchemeList && <div className={styles.myCase} onClick={() => store.homeStore.setShowMySchemeList(false)}>
        <MyCase getHxId={getHxId}></MyCase>
      </div>}
      {store.homeStore.isShowHouseDetail.show && <div className={styles.houseDetail} onClick={() => store.homeStore.setIsShowHouseDetail({show: false, source: ''})}>
        <HouseDetail
          closeHouseDetail={() => store.homeStore.setIsShowHouseDetail({show: false, source: ''})}
          toLayout={toLayout}
          hxId={hxId}
        ></HouseDetail>
      </div>}
      {/* {store.homeStore.showReplace && <Replace></Replace>} */}
      <CustomKeyboard
        onKeyPress={handleKeyPress}
        onDelete={handleDelete}
        onConfirm={handleConfirm}
        onClose={handleClose}
        inputValue={inputValue}
        isVisible={showKeyboard}
      />
      {/* {store.homeStore.showAiDraw && <div className={styles.aiDraw}>
        <AiDraw modelType={true}></AiDraw>
      </div>} */}
      {store.homeStore.showDreamerPopup && <DreamerPopup></DreamerPopup>}

      <DreamerHxSearch ref={hxSearchRef} />
      <RoomImagePredict></RoomImagePredict>
      <GuidanceChannel channelCode='Helptips-006' />
      {contextHolder}
      <div id='Canvascontent' className={store.homeStore.viewMode === '3D_FirstPerson' ? styles.seleceView : styles.content}>
          {/* 2D画布 */}
          <div 
            id="body_container" 
            className={styles.canvas_pannel + " left_panel_layout"}
            style={{
              flex: store.homeStore.viewMode === '2D' ? '1' : '0 0 40%',
              height: '100%',
              transition: 'flex 0.3s ease-in-out'
            }}
          >
            <canvas
              id="cad_canvas"
              className="canvas"
              onMouseEnter={() => {
                store.homeStore.setIsmoveCanvas(false);
              }}
              onMouseLeave={() => {
                store.homeStore.setIsmoveCanvas(true);
              }}
              onTouchStart={(e) => {
                if (e.touches.length === 2) {
                  const dx = e.touches[0].clientX - e.touches[1].clientX;
                  const dy = e.touches[0].clientY - e.touches[1].clientY;
                  const distance = Math.sqrt(dx * dx + dy * dy)
                  store.homeStore.setInitialDistance(distance / store.homeStore.scale);
                }
              }}
              onTouchMove={(e) => {
                e.stopPropagation();
                if (e.touches[e.touches.length - 1].identifier == 2) return;
                if (e.touches.length === 2) {
                  const dx = e.touches[0].clientX - e.touches[1].clientX;
                  const dy = e.touches[0].clientY - e.touches[1].clientY;
                  const distance = Math.sqrt(dx * dx + dy * dy);
                  let newScale = distance / store.homeStore.initialDistance;
                  if (newScale > 5) {
                    newScale = 5;
                  } else if (newScale < 0.001) {
                    newScale = 0.001;
                  }
                  store.homeStore.setScale(newScale);

                  LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
                }
              }}
              onTouchEnd={(e) => {
                if (e.touches.length > 0) {
                  LayoutAI_App.DispatchEvent(LayoutAI_Events.updateLast_pos, e)
                }
                store.homeStore.setInitialDistance(null);
              }}
            />
            {!isPanelOpen && <div className='layoutBtn'>
              <div
                onClick={() => {
                  // 从2D/3D分屏切换到2D全屏
                  store.homeStore.setViewMode('2D');
                  setZIndexOf3DViwer(-2);
                  store.homeStore.setZIndexOf3DViewer(-2);
                  
                  // 使用统一的画布更新函数
                  setTimeout(() => {
                    updateCanvasForViewMode('2D');
                  }, 300);
                }}
              >去布局设计</div>
            </div>}
            {
              store.homeStore.designMode === AI2DesignBasicModes.MeasurScaleMode && isPanelOpen &&
              <div className="canvas_btns" style={{ zIndex: 999999, marginBottom: '10vh', gap: '20px' }}>
                <Button
                  className="btn"
                  type="primary"
                  onClick={() => {
                    if (LayoutAI_App.instance) {
                      LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
                      store.homeStore.setDesignMode(AI2DesignBasicModes.AiCadMode);
                    }
                  }}
                >{t("取消")}</Button>
                <Button
                  className="btn"
                  type="primary"
                  onClick={() => {
                    if (LayoutAI_App.instance) {
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.ConfirmScale, { img_base64: store.homeStore.img_base64 });
                      store.homeStore.setDesignMode(AI2DesignBasicModes.AiCadMode);
                    }
                  }}
                >{t("确定")}</Button>
              </div>
            }
          </div>

          {/* 3D画布 - 只在3D模式下显示 */}
          {store.homeStore.viewMode !== '2D' &&
            <div 
              className={styles.canvas3d}
              style={{
                width: 'calc(60% - 12px)',
                height: '100%'
              }}
            >
              <div className='scene3d_container'>
                <Scene3DDiv defaultViewMode={4}></Scene3DDiv>
                <div className='view_select_box'>
                  <ViewSelectBox></ViewSelectBox>
                </div>
              </div>
              <div className='canvas3d_btns'>
                <div className='canvas3d_btn'
                  onClick={handleAIGeneration}
                >去生图</div>
              </div>
              <div className='house_match_container' style={{display: chosedMode === '户型匹配' ? 'block' : 'none'}}>
                <HouseMatch></HouseMatch>
              </div>
            </div>
          }
      </div>
    </div>
  );
};

export default observer(App);
