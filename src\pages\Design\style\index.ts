import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }, length: number) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      background-color: #fff;
    `,
    content: css`
      display: flex;
      background-color: #EEEEF2;
      overflow: hidden;
      position: absolute;
      top: 48px;
      left: 0; 
      right: 0;
      bottom: 0;
    `,
    top: css`
      width: 100%;
      height: 38px;
      background-color: #1f2124;
      display: flex;
      align-items: center;
    `,
    leftClose: css`
      z-index: 99;
      cursor: pointer;
      color: #fff;
      font-weight: 500;
      font-size: 16px;
      margin-left: 10px;
      line-height: 38px;
      display: flex;
      align-items: center;
    `,
    BtnInfo: css`
      width: auto;
      position: absolute;
      left: 50%;
      bottom: 35px;
      transform: translate(-50%);

      margin: 0 auto;
      display: flex;
      justify-content: center;
      z-index: 10;
    `,
    LayoutButton: css`
      position: absolute;
      left: 20%;
      top: 80px;
      display: flex;
      width: 200px;
      justify-content: space-between;
      z-index:2;
    `,
    executeButton: css`
      ${checkIsMobile() ? 
        `
          min-width: 120px;
          height: 36px;
          font-size: 14px;
        ` 
        : 
        `
          min-width: 200px;
          height: 48px;
          font-size: 16px;
        `
      }
      border-radius: 6px;
      margin-right: 10px;
      margin-left: 10px;
      z-index:2;
      font-weight: 600;
    `,
    toEdit_button: css`
      border-radius: 6px;

      ${checkIsMobile() ? 
        `
          width: 120px;
          height: 36px;
          font-size: 14px;
        ` 
        : 
        `
          width: 200px;
          height: 48px;
          font-size: 16px;
        `
      }
      margin-right: 10px;
      margin-left: 10px;
      z-index:2;
      border: none;
      font-weight: 600;
      // border: 1px solid rgba(0,0,0,0.15);
      box-sizing: border-box;
      box-shadow: none;
    `,
    Unselected: css`
      font-size: 12px;
      letter-spacing: 1px;
      line-height: 18px;
    `,
    disabled: css`
      border-color: #DDDFE4;
      color: #BCBEC2;
      background: #F2F3F5;
      box-shadow: none;
      :hover{
        border-color: #DDDFE4 !important;
        color: #BCBEC2 !important;
        background: #F2F3F5 !important;
        box-shadow: none !important;
      }
    `,
    overlay: css`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    aiDraw: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
    `,
    progressInfo: css`
      position: absolute;
      top: 0%;
      padding-top: 17%;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    top_bar_container: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      background : #1F1F1F;
      color:#fff;
    `,
    top_bar_tabs_root: css`
      color : #fff;
      margin-left: -105px;
      width: 180px;
      .ant-tabs-tab {
        color :#fff;
        font-size: 18px;
      }
    `,
    top_bar_left_title: css`
      margin-right: 0;
      margin-left: 20px;
      font-size: 16px;
    `,

    top_bar_left_button: css`
      margin-right: 16px;
      margin-left: 0;
      width: 80px;
      height: 28px;
      border-radius: 2px;
      background: transparent;
      border: 1px solid #565656;
      cursor: pointer;
    `,
    top_bar_center_button: css`
      width: 80px;
      height: 28px;
      border-radius: 2px;
      background: blue;
      border: none;
      font-size: 16px;
      cursor: pointer;
      margin: 0px;
      vertical-align: middle;
    `,
    top_bar_right_button: css`
      margin-right: 20px;
      margin-left: 0;
      width: 80px;
      height: 28px;
      border-radius: 2px;
      background: #565656;
      border: none;
      cursor: pointer;
    `,
    left_panel: css`
      left: 0;
      position: fixed;
      color: #6c7175;
      background-color: #FFF;
      width: 360px;
      z-index: 999;
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);
      height: 100%;
    `,
    left_panel_title: css`
      color: #000;
      font-weight: bold;
      font-size: 20px;
      line-height: 1.67;
      padding: 14px 0 16px 16px;
      height: 60px;
      background-color: #fff;
      width: 100%;
    `,
    left_panel_placeholder: css`
      display: flex;
      justify-content: center;
      align-items: center;
      height: 850px;
      color: #6c7175;
      text-align: center;
      flex-direction: column;
      img{
        width: 56px;
        height: 56px;
      }
    `,
    left_panel_placeholder_image: css`
      margin-bottom: '12px'
    `,
    left_panel_placeholder_text: css`    
      font-size: 12px;
      line-height: 18px;
    `,
    left_panel_layout_list: css`
      width: 100%;
      padding-top: 60px;
      background-color: #FFF;
      height: calc(100vh - 110px);
      overflow-y: auto;
      padding: 2px 16px;
      canvas {
        cursor : pointer;
        box-sizing: border-box;
        
      }
      canvas:hover {
        outline: 2px solid #147FFA;
      }
    `,
    active: css`
      outline: 2px solid #147FFA;
    `,
    canvasItem: css`
      height: ${length * 0.9}px;
      width: 100%;
      padding: 10px;
      background-color: #F2F3F5;
    `,
    _scheme_name: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 14px;
      line-height: 1.57;
      letter-spacing: 0px;
      text-align: left;
      font-weight: 600;
      margin: 8px 0px;
      text-align: center;
    `,
    landscape:css`
      position:absolute;
      -webkit-transform:rotate(90deg);
      -webkit-transform-origin:0% 0%;/*1.重置旋转中心*/
      
      -moz-transform: rotate(90deg);
      -moz-transform-origin:0% 0%;
      
      -ms-transform: rotate(90deg);
      -ms-transform-origin:0% 0%;
      
      transform: rotate(90deg);
      transform-origin:0% 0%;
      
      width: 100vh;/*2.利用 vh 重置 '宽度' */
      height: 100vw;/* 3.利用 vw 重置 '高度' */
      top: 0;
      left: 100vw;
    `,
    canvas_pannel: css`
      position: absolute;
      left: 0px;
      top: -100px;
      width : calc(100% + 100px);
      height : calc(100% + 200px);
      overflow: hidden;
      background-color: #EAEAEB;
      .canvas {
        position : absolute;
        left:0px;
        top: 0px;
        &.canvas_drawing {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
        }
        &.canvas_moving {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;
        }
        &.canvas_leftmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
        }
        &.canvas_rightmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
        }
        &.canvas_acrossmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
        }
        &.canvas_verticalmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
        }
        &.canvas_splitWall {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 16 16,auto;
        }
      }

      .canvas_btns {
        position: absolute;
        left: calc(50% - 160px);
        bottom: 35px;
        .btn {
          width: 160px;
          height: 48px;
          border-radius: 6px;
          border: none;
          font-size: 16px;
          font-weight: 600;
        }
        .design_btn {
          background: #e6e6e6;
          margin-right: 20px;
        }
      }
    `,
    line: css`
      height: 100%;
      position: absolute;
      right: 0;
      position: absolute;
      top: 0;
      width: 4px;
      cursor: col-resize;
      z-index: 998;
    `,
    materialTypeOptions: css`
      position:absolute;
      right:320px;
      bottom:40px;
    `,
    scene3d: css`
      width: 400px;
      height: 300px;
      position:absolute;
      right:325px;
      top:55px;
    `,
    miniBtn: css`
      position: absolute;
      bottom: 14px;
      left:  380px;
      width: 100px;
      height: 32px;
      background-color: #fff;
      font-size: 16px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    `,
    comfirmFurnishDialog: css`
      position: fixed;
      display: flex;
      flex-direction: column;
      align-items: center;
      align-content: space-between;
      div#unfurnish-tip-container {
        margin-top: 25px;
        flex: 1;
        span#unfurnish-tip {
          margin-top: 30px;
          font-size: 16px;
          font-weight: 600;
          color: #000;
        }
      }
      div#unfurnish-btn-container {
        flex: 1;
        Button {
          margin-left: 16px;
          margin-right: 16px;
        }
      }
      top: 40%;
      left: 40%;
      width: 450px;
      height: 130px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.9); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    buttonWrapper: {
      position: 'relative',
      display: 'inline-block',
    },

    hoverTip: {
      position: 'absolute',
      bottom: '100%',
      left: '50%',
      transform: 'translateX(-50%)',
      padding: '8px 12px',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: '#fff',
      borderRadius: '4px',
      fontSize: '12px',
      whiteSpace: 'nowrap',
      marginBottom: '8px',
      '&::after': {
        content: '""',
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        borderWidth: '6px 6px 0',
        borderStyle: 'solid',
        borderColor: 'rgba(0, 0, 0, 0.8) transparent transparent',
      }
    },

  }
});
