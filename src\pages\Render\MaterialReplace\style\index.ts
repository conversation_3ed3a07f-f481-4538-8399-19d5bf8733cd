import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      height: 500px;
      padding: 12px 12px;
      border-radius: 12px;
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100);
        width: 100%;
        padding: 12px 12px;
      }
      .ant-segmented
      {
        background-color: #EAEBEA;
        color: #282828 !important;
      }
    `,
    title: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.60);
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    `,
    topInfo: css`
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 0 24px;
        @media screen and (orientation: landscape) {
          margin-top: 12px;
          padding: 0 0px;
        }
        .info
        {
          display: flex;
          img{
            width: 72px;
            height: 72px;
            border-radius: 4px;
            margin-right: 16px;
            border-radius: 4px;
            border: 1px solid #EEE;
            background: #C3C4C7;
            @media screen and (orientation: landscape) {
              width: 48px;
              height: 48px;
              margin-right: 12px;
            }
          }
        }
         .sizeInfo
         {
          padding: 8px 0px;
          color: rgba(255, 255, 255, 0.85);
          @media screen and (orientation: landscape) {
            padding: 0px 0px;
          }
            .size
            {
              color: rgba(255, 255, 255, 0.60);
              margin-top: 4px;
              user-select: text;
              @media screen and (orientation: landscape) {
                margin-top: 4px;
                font-size: 10px;
              }
            }
         } 
      `,
    divider: css`
      margin: 0px 0 14px 0px;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media screen and (orientation: landscape) {
        margin: 12px 0 8px 0px;
      }
    `,
    tabContainer: css`
      display: flex;
    `,
    tabItem: css`
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.40);
      height: 24px;
      padding: 2px 8px;
      display: flex;
      width: 70px;
      align-items: center;
      justify-content: center;
      gap: 10px;
      font-size: 12px;
      margin-right: 8px;
      color: rgba(255, 255, 255, 0.85);
    `,
    active: css`
      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
      color: #fff;
    `,
    goodsInfo: css`
      display: flex;
      flex-wrap: wrap;
      overflow-y: scroll;
      max-height: calc(var(--vh, 1vh) * 100 - 240px);
      margin-top: 10px;
      align-items: flex-start;
       /* 隐藏滚动条 */
      &::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
      }
      
      /* 对于 Firefox */
      scrollbar-width: none; /* 隐藏滚动条 */
      -ms-overflow-style: none; /* IE 和 Edge */
      @media screen and (orientation: portrait) {
        overflow-x: scroll;
        flex-wrap: nowrap;
        width: 100%; 
      }
    `,
    loading: css`
      text-align: center;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    `,
    goodsItem: css`
      width: 104px;
      margin: 6px 12px 0 12px;
      text-align: center;
      img{
        width: 100%;
        aspect-ratio: 1 / 1;
        border-radius: 4px;
        background-color: #eaeaea;
        border-radius: 8px;
      }
      @media screen and (max-width: 800px){
         width: 112px;
         img{
          width: 112px;
         }
      }
      @media screen and (max-width: 450px){
         width: 106px;
      }
      @media screen and (max-width: 400px){
         width: 94px;
      }
      @media screen and (orientation: landscape) {
        margin: 6px 6px 0 6px;
        width: 88px;
        font-size: 10px;
        text-align: left;
      }

    `,
    selectIcon: css`
    
    `,
    sizeInfo: css`
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 4px;
      color: rgba(255, 255, 255, 0.85);
    `,
    noData: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      margin: 0 auto;
      margin-top: 100%;
      transform: translateY(-50%);
      .emptyImg{
        width: 60px;
        height: 60px;
        margin-bottom: 12px;
      }
      span{
        color: #fff;
      }
    `
  }
});
