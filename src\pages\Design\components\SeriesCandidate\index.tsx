import { AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import { getKgSchemeList, getStyles } from "@/Apps/AI2Design/Services";
import { QuoteService } from '@/Apps/AI2Design/Services/QuoteService';
import { EventName } from '@/Apps/EventSystem';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import Icon from '@/components/Icon/icon';
import ScreenboxBlack from '@/components/ScreenboxBlack/screenboxBlack';
import SkeletonInfo from '@/components/SkeletonInfo/index';
import { checkIsMobile } from '@/config';
import { useStore } from '@/models';
import { DesignContext } from '@/pages/Design/design';
import { getaiCongig } from '@/services/user';
import { LoadingOutlined } from '@ant-design/icons';
import { Radio, Spin } from '@svg/antd';
import { observer } from "mobx-react-lite";
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getQuote } from '@/services/design/index';
import PopoverComponent from '../Popover/index';
import style from './index.module.less';
import useStyles from './style';
import { If } from 'react-if';
import { PERMISSIONS } from '@/config/permissions';
import { Permissions } from '@/Apps/LayoutAI/setting/Permissions';

/**
 * @description 筛选页
 */
const SeriesCandidate: React.FC = () => {
  const { t } = useTranslation();

  interface Params {
    orderBy: string;
    ruleType: number;
    pageSize: number;
    pageIndex: number;
    schemeKeyWord: string;
    ruleKeyWord: string;
    spaceName: any;
    schemeStyleId: string;
    ruleStyleId: string;
    queryType: number;
  }
  interface ScreenMethods {
    reSetScreenOut: () => void;
    reSetTypeScreenOut: () => void;
    scrollTo: (x: number, y: number) => void;
  }
  let store = useStore();
  const { styles } = useStyles();

  const {
    waitingToFurnishRemaining,
    setWaitingToFurnishRemaining
  } = useContext(DesignContext);


  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />
  const [preparation, setPreparation] = useState<boolean>(false); //是否加载中
  const [scroll, setScroll] = useState<boolean>(false); //左侧列表数据加载中，不让滚动
  const [recordCount, setTotal] = useState<number>(0); //总数
  const [loading, setLoading] = useState(false);
  const roomRef = useRef(null);
  const styleRef = useRef<ScreenMethods>(null);
  const spaceRef = useRef<ScreenMethods>(null);
  const lineRef = useRef(null);
  const [recordList, setRecordList] = useState<any>([]);
  const [styleList, setStyleList] = useState<any>([]); // 风格列表
  const [spaceList, setSpaceList] = useState<any>([]); //空间列表
  const [inputValue, setInputValue] = useState("");
  const [showDelete, setShowDelete] = useState(false);
  const [options, setOptions] = useState(['全案风格']); /*[i18n:ignore]*/
  const [width, setWidth] = useState(checkIsMobile() ? 260 : 360);
  const [tabPosition, setTabPosition] = useState<string>(store.userStore.userInfo?.isFactory ? '2' : '1');
  const [isActive, setIsActive] = useState(false);
  const [showPopover, setShowPopover] = useState<any>({});
  const [seriesScopeAppliedMap, setSeriesScopeAppliedMap] = useState<{ [key: string]: { soft?: boolean, hard?: boolean, cabinet?: boolean } }>({});
  const [prams, setPrams] = useState<Params>({
    orderBy: 'sort asc',
    ruleType: store.userStore.userInfo?.isFactory ? 2 : 1,
    pageSize: 10,
    pageIndex: 1,
    schemeKeyWord: '',
    ruleKeyWord: '',
    spaceName: null,
    schemeStyleId: '',
    ruleStyleId: '',
    queryType: 2,
  });

  /**
  * @description 获取左侧列表数据
  */
  const getRoomList = async () => {
    setScroll(true);
    setLoading(true);
    const params = prams;
    const res = await getKgSchemeList(params);
    if (store.designStore.segmentedValue === '风格套系')  /*[i18n:ignore]*/
    {
      if (res) {
        res?.result?.map((item: any) => {
          item.roomList = item?.ruleImageList?.map((imgPath: any) => ({ imgPath }));
        })
      }

    }
    setLoading(false);
    setScroll(false);
    if (res?.result) {
      const data = Array.isArray(recordList) && recordList.length > 0 && (prams.pageIndex > 1)
        ? [...recordList, ...(res?.result || [])]
        : res?.result || [];
      setRecordList(data);
    }
    else {
      setRecordList([]);
    }

    setPreparation(false);
    setTotal(res?.recordCount);
  }
  /**
  * @description 滚动加载
  */
  const scrollHandle = async (even: any) => {
    let e = even.target;
    // +5 增大触发滚动加载的范围
    if (Math.ceil(e.scrollTop) + e.clientHeight + 5 >= e.scrollHeight) {
      // 添加loading，防止在+5的访问内多次请求 
      if (recordCount > recordList?.length && !loading) {
        setPrams({
          ...prams,
          pageIndex: prams.pageIndex + 1,
        })
      };
    }
  };
  /**
  * @description 获取空间列表
  */
  const getSpaceData = async () => {
    const res = await getaiCongig();
    if (!res) return;
    const list = Object?.keys(res).map((key: any, index: number) => ({
      id: index + 1,
      screenName: key
    }));
    setSpaceList(list);
  }
  /**
  * @description 获取风格列表
  */
  const getStyleList = async () => {
    const res = await getStyles();
    if (!res) return;

    const transformedData = res?.map((item: any) => ({
      value: item.key,
      screenName: item.label
    }));
    setStyleList(transformedData);
  }

  const tabChange = (e: any) => {
    setTabPosition(e.target.value);
    const value = e.target.value == "1" ? 1 : 2;
    setPrams({
      ...prams,
      pageIndex: 1,
      ruleType: value
    })
    if (roomRef.current) {
      roomRef.current.scrollTop = 0;
    }
  }

  useEffect(() => {
    getRoomList();
  }, [prams]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key.toLowerCase() === 'q') {
        setOptions(options[0] === '全案风格' ? ['风格套系', '样板间'] : ['全案风格']); //[i18n:ignore] 
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [options]);

  useEffect(() => {
    setPreparation(true);
    getSpaceData();
    getStyleList();

    LayoutAI_App.on(EventName.quoteDataSeries, (event: any) => {
      if (event) {
        let params = {};
        let manager = (LayoutAI_App.instance) as AI2DesignManager;
        let quoteService = QuoteService.instance;
        let series = event;
        if (!series) return;
        let quoteData = quoteService.makeQuoteData(manager.layout_container, new TSeriesSample(series));
        if (quoteData) {
          params = {
            schemeId: manager.layout_container.current_swj_layout_data.scheme_id,
            seriesId: series.ruleId,
            seriesName: '',
            sourceContent: JSON.stringify(quoteData),
            outputType: 1
          }

        }
        else {
          return;
        }
        fetchQuoteData(params);
        fetchQuoteExcel(params);
      }
    });
  }, [])

  /* ———————————————————————————————— 左侧布局列表宽度可拉伸 ———————————————————————————————— */
  let containerRef = useRef(null);
  let dragging = false; // 是否正在拉伸
  let startX = 0;       // 初始位置
  let startWidth = 0;   // 初始宽度

  const onMouseDown = (e: any) => {
    dragging = true;
    startX = e.clientX;
    startWidth = containerRef.current.offsetWidth;
  };

  const onMouseMove = (e: any) => {
    if (!dragging) return;
    const dx = e.clientX - startX;      // 偏移量
    let newWidth = startWidth + dx;
    if (newWidth > 480) newWidth = 480; // 最大拉伸宽度为480
    if (newWidth < 360) newWidth = 360; // 最小拉伸宽度为360
    setWidth(newWidth);
  };

  const onMouseUp = () => {
    dragging = false;
  };


  const fetchQuoteData = async (params: any) => {
    // 报价计算
    const res = await getQuote(params);
    if (res != null && res.success) {

      let quoteInfo = JSON.parse(res.data);
      let itemPrices: any[] = [];
      let otherItems: any = {};
      quoteInfo.content.map((item: any) => {
        if (item.itemType) {
          // 如果 item 有 itemType 属性，那么将它添加到 itemPrices 数组
          itemPrices.push(item);
        } else {
          // 否则，将它的所有属性添加到 otherItems 对象
          otherItems = { ...otherItems, ...item };
        }
      })
      otherItems = { ...otherItems, itemPrices: itemPrices };
      store.designStore.setQuoteInfo(otherItems);
    } else {
      store.designStore.setQuoteInfo(null);
    }
  };

  const fetchQuoteExcel = async (params: any) => {
    params.outputType = 0;
    const res = await getQuote(params);
    if (res != null && res.success) {
      let secureUrl = res.data.replace("http:", "https:");
      store.designStore.setQuoteExcel(secureUrl);
    } else {
      store.designStore.setQuoteExcel(null);
    }
  }

  const applySeriesToRoom = (series: TSeriesSample, applySoft: boolean, applyHard: boolean, applyCabinet: boolean) => {
    store.schemeStatusStore.layoutSchemeSaved = false;
    store.schemeStatusStore.pendingOpenSchemeIn3D = false;
    LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: series, scope: { soft: applySoft, hard: applyHard, cabinet: applyCabinet, remaining: false } });
    if (store.homeStore.selectData) {
      if (store.homeStore.selectData.rooms) {
        updateSeriesScopeMap(store.homeStore.selectData.rooms);
      }
    }
  }

  const applySeriesToAllRemaining = (series: TSeriesSample) => {
    store.schemeStatusStore.layoutSchemeSaved = false;
    store.schemeStatusStore.pendingOpenSchemeIn3D = false;
    LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: series, scope: { remaining: true } });
  }

  const updateSeriesScopeMap = (rooms: TRoom[]) => {
    let dict = seriesScopeAppliedMap;
    dict = {};
    if (rooms != null) {
      for (let room of rooms) {
        if (room._scope_series_map) {
          for (let apply_type in room._scope_series_map) {
            let series = room._scope_series_map[apply_type as keyof typeof room._scope_series_map] as TSeriesSample;
            if (series && series.ruleId) {
              if (!dict[series.ruleId]) dict[series.ruleId] = {};
              dict[series.ruleId][apply_type as keyof typeof seriesScopeAppliedMap.value] = true;
            }
          }
        }
      }
    }
    setSeriesScopeAppliedMap(dict);
  }

  const isCheckAppliedScope = (serialItem: TSeriesSample, type: string) => {
    return (seriesScopeAppliedMap[serialItem.ruleId] && seriesScopeAppliedMap[serialItem.ruleId][type as keyof typeof seriesScopeAppliedMap.value]);
  }
  const object_id = "SeriesCandidate";
  useEffect(() => {
    LayoutAI_App.on_M(EventName.SelectingRoom, object_id, (event: { current_rooms: TRoom[], event_param: any }) => {
      updateSeriesScopeMap(event.current_rooms || []);
    });
    const line = lineRef.current;
    if (!line) return;
    line.addEventListener('mousedown', onMouseDown);
    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
    return () => {
      line.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mousedown', onMouseDown);
      window.removeEventListener('mouseup', onMouseUp);
    };
  }, []);

  if (store.homeStore.preview3D) {
    return <div />;
  }

  return (
    <div className={`${styles.container} ${style.container}`} style={{ width }} ref={containerRef}>
      {preparation ? (
        <SkeletonInfo></SkeletonInfo>
      ) : (
        <div className={styles.sidePanel}>
          <div style={{ position: 'relative' }}>
            <div className={styles.titleContainer}>
              <div className={styles.title}>{t('风格系列')}</div>
            </div>
            <Radio.Group value={tabPosition} onChange={tabChange}>
              <Radio.Button value="2">{t('企业')}</Radio.Button>
              <If condition={Permissions.instance.hasPermission(PERMISSIONS.SERIES.SHOW_PLATFORM_SERIES)}>
                <Radio.Button value="1">{t('平台')}</Radio.Button>
              </If>
            </Radio.Group>
            <div style={{ width: '248px' }}>           
            </div>
            <div className={styles.findInfo}>
              <input
                value={inputValue}
                onKeyDown={(data) => {
                  if (data.key != 'Enter') {
                    return;
                  }
                  if (store.designStore.segmentedValue === '样板间')  //[i18n:ignore]
                  {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      schemeKeyWord: inputValue
                    })
                  } else {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      ruleKeyWord: inputValue,
                    })
                  }
                }}
                onChange={(event) => {
                  setInputValue(event.currentTarget.value);
                }}
                onMouseEnter={() => {
                  setShowDelete(true);
                }}
                onMouseLeave={() => {
                  setShowDelete(false);
                }}
                className={styles.container_input}
                placeholder={`${t("请输入关键词搜索")}`} />
              <Icon
                onMouseEnter={() => {
                  setShowDelete(true);
                }}
                onClick={() => {
                  if (store.designStore.segmentedValue === '样板间')  //[i18n:ignore]
                  {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      schemeKeyWord: inputValue
                    })
                  } else {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      ruleKeyWord: inputValue,
                    })
                  }
                }}
                className={styles.Icon}
                iconClass="iconsearch"
                style={{
                  fontSize: '20px',
                  color: '#6C7175'
                }}
              >
              </Icon>
              <Icon
                onMouseEnter={() => {
                  setShowDelete(true);
                }}
                onClick={() => {
                  setInputValue("");
                  setPrams({
                    ...prams,
                    pageIndex: 1,
                    schemeKeyWord: "",
                    ruleKeyWord: "",
                  })
                }}
                className={styles.IconDelete}
                iconClass="iconclosecirle_fill"
                style={{
                  color: '#595959',
                  fontSize: '16px',
                  marginTop: '2px',
                  marginLeft: '2px',
                  display: inputValue !== "" && showDelete ? "block" : "none",
                }}
              />
              {/* 筛选项 */}
              <div className={styles.selectInfo}>
                <ScreenboxBlack ref={styleRef} data={{
                  screenTitle: t('风格'),
                  screenList: styleList
                }} onChange={(data: any, index: number) => {
                  if (store.designStore.segmentedValue === '样板间')  //[i18n:ignore]
                  {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      schemeStyleId: data?.value,
                    })
                  } else {
                    setPrams({
                      ...prams,
                      pageIndex: 1,
                      ruleStyleId: data?.value,
                    })
                  }
                  if (roomRef.current) {
                    roomRef.current.scrollTop = 0;
                  }
                }}></ScreenboxBlack>
            
              </div>
            </div>
          </div>
          <Spin spinning={loading} size={'large'} style={{ height: '100%' }}>
            <div className={`${styles.container_listInfo} ${scroll ? styles.noScroll : ''}`} onScroll={scrollHandle} ref={roomRef}>

              {
                recordList && recordList.length > 0 ?
                  <>
                    {recordList?.map?.((item: TSeriesSample, id: any) => (
                      <div key={id} className={styles.container_list} style={{ height: width / 360 * 218 }}
                        onMouseEnter={() => setShowPopover((prev: any) => ({ ...prev, [id]: true }))}
                        onMouseLeave={() => setShowPopover((prev: any) => ({ ...prev, [id]: false }))}
                      >
                        {showPopover[id] && <PopoverComponent noShow={true} isClick={true} seriesSample={item} height={width / 360 * 218}></PopoverComponent>}

                        <div className={styles.Popover_hoverInfo}
                          style={{ height: width / 360 * 218 }}>
                          <img src={`${item.thumbnail}?x-oss-process=image/resize,m_fixed,h_218,w_318`} alt={`series-${item.ruleId}`} />
                          {(showPopover[id] || checkIsMobile()) && <button onClick={() => applySeriesToRoom(item, true, true, true)} className={style.apply_all_button}>{t('全部应用')}</button>}
                        </div>
                        <div className={style.apply_category_button_container}>
                          <div onClick={() => applySeriesToRoom(item, false, false, true)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "cabinet") ? style.checked : "")}>{t('定制')}</div>
                          <div onClick={() => applySeriesToRoom(item, true, false, false)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "soft") ? style.checked : "")}>{t('软装')}</div>
                          <div onClick={() => applySeriesToRoom(item, false, true, false)} className={style.apply_category_button + " " + (isCheckAppliedScope(item, "hard") ? style.checked : "")}>{t('硬装')}</div>
                        </div>

                        <div className={styles.textInfo}>
                          <div className={styles.container_title} title={item.seedSchemeName || item.ruleName}>{t(item.seedSchemeName) || t(item.ruleName)}</div>
                          <div className={styles.container_desc}>
                            <span className={styles.seriesStyle}>{t(item["seriesStyle"])}</span><span style={{ display: `${store.designStore.segmentedValue === '风格套系' ? 'none' : 'inline-block'}` }}>&nbsp;| {(item as any).schemeArea}{'㎡'}</span>
                          </div>
                        </div>

                      </div>
                    ))}
                  </>
                  :
                  <div className={styles.emptyInfo}>
                    <div>
                      <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
                      <div className={'desc'}>{t('暂无数据')}</div>
                    </div>
                  </div>
              }


            </div>
          </Spin>
        </div>
      )}
      <div className={styles.line} ref={lineRef}></div>
    </div>
  );
};

export default observer(SeriesCandidate);
