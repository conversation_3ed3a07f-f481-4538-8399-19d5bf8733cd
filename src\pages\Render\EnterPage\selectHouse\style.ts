import { createStyles } from '@svg/antd/es/theme/utils';

const useStyles = createStyles(({ css }) => ({
    container: css`
        width: 100%;
        height: calc(100vh - 52px);
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
    `,

    title: css`
        font-size: 24px;
        font-weight: 600;
        color: #000000;
        margin: 0;
    `,

    iframe: css`
        width: 100%;
        height: 100%;
        position: relative;
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    `,
    dialog: css`
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.50);
        z-index: 1000;
    `,

    bottom: css`
        /* position: fixed;
        bottom: 0;
        left: 0;
        right: 0; */
        width: 100%;
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 200px;
        padding: 10px 0;
        background-color: #ffffff;
        /* border-top: 1px solid #e0e0e0; */
        /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */
    `,

    bottomButton: css`
        width: 150px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        text-align: center;
        color: #282828;
        background-color: #f4f4f4;
        border-radius: 10px;
        cursor: pointer;
        transition: background-color 0.3s;
        border: 1px solid #d0d0d0;
        font-weight: 500;
        
        &:hover {
            background-color: #e8e8e8;
        }
    `
}));

export default useStyles; 