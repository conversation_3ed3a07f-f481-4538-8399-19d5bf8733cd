
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    navigation : css`
      /* position: fixed;
      top: 0px;
      background-color: #fff;
      width: 100%;
      height: 56px;

      display: flex;
      padding: 0 16px;
      justify-content: space-between;
      align-items: center;
      z-index: 9;
      max-width: 1174px;
      @media screen and (max-width: 450px) { // 手机宽度
        height: 46px;
      } */
    `,
    backBtn : css`
      position: fixed;
      top: 12px;
      left: 12px;
      color: #282828;
      font-size: 16px;
      font-weight: 600;
      @media screen and (max-width: 450px) { // 手机宽度
        font-size: 12px;
      }
      z-index: 9;
    `,
    forwardBtn : css`
      position:absolute;
      right:0;
      z-index:2;
      padding-right:10px;
      font-size:14px;
      line-height:40px;
      color:#333;
    `,
    schemeNameSpan:css`
      width:100%;
      font-size:16px;
      line-height:40px;
      text-align:center;
    `
  }

});
