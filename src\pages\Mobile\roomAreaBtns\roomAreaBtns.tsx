import React, { useEffect, useState } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { useStore } from '@/models';
import { observer } from 'mobx-react-lite';
import { useTranslation } from 'react-i18next'
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { EventName } from '@/Apps/EventSystem';
import { TLayoutEntityContainer } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { Select } from '@svg/antd';
import { Else, If, Then } from 'react-if';
import { SdkService } from '@/services/SdkService';
import { is_dreamer_mini_App, workDomainMap } from '@/config';
import { Icon } from '@svg/antd-cloud-design';

const RoomAreaBtns: React.FC<{mode?:number}> = (props:{mode?:number}={mode:0}) => {
    const [btnList, setBtnList] = useState<any>([]);
    const { styles } = useStyles();
    const store = useStore();
    const { t } = useTranslation();
    const [options, setOptions] = useState<any>([]);
    const [singleSelectRoom, setSingleSelectRoom] = useState<TRoomEntity>(null);
    const [isLandscape, setIsLandscape] = useState<boolean>(window.innerWidth < window.innerHeight);
    const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const [isWholeHouse, setisWholeHouse] = useState<boolean>(true) // 全屋展示时，不因为点击某个房间导致selectedRoom变化而改变按钮的激活状态
    useEffect(() => {
        setBtnList(store.homeStore.roomEntities);
    }, [store.homeStore.roomEntities]);

    const handleAreaClick = (area: TRoomEntity | null) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, area);

        store.homeStore.setSelectedRoom(area); // 设置选中的房间
        setSingleSelectRoom(area); // 更新本地状态

        area === null ? setisWholeHouse(true) : setisWholeHouse(false)

        // if(store.homeStore.viewMode === '3D_FirstPerson'){
        //     // 3D模式下，点击按钮切换相机位置到指定房间的“视角1”
        //     // const manager = LayoutAI_App.instance as TAppManagerBase;
        //     // if (!manager?.layout_container) return
        //     let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        //     if (!scene3d || !scene3d.active_controls) return

        //     if(area._view_cameras && area._view_cameras[0]){
        //         scene3d.active_controls.bindViewEntity(area._view_cameras[0])
        //     } else {
        //         scene3d.active_controls.setCenterAndUpdate(area._main_rect.rect_center_3d) // 房间中心点
        //     }
        // }
    };

    useEffect(() => {
        let options = store.homeStore.roomEntities.map((item: TRoomEntity) => {
            return {
                label: item.aliasName,
                value: item.uidN
            }
        })
        options.unshift(
        {
            label: t('全屋'),
            value: 'all'
        })
        setOptions(options);
    }, [store.homeStore.roomEntities]);

    
    useEffect(() => {
        LayoutAI_App.on(EventName.selectRoom, (event: TRoomEntity) => {
            if (event) {
                store.homeStore.setSelectedRoom(event);
            } else {
                store.homeStore.setSelectedRoom(null);
            }

        });
    }, [])

    useEffect(() => {
        // if((store.homeStore.selectedRoom))
        // {
        //     setSingleSelectRoom(store.homeStore.selectedRoom);
        // }
        if(store.homeStore.selectedRoom !== singleSelectRoom){
            setSingleSelectRoom(store.homeStore.selectedRoom)
        }
    }, [store.homeStore.selectedRoom])

    const isSingleRoomMode = (props.mode==1 && store.homeStore.isSingleRoom);

    const roomAreaBtns = () => {
        // if(store.homeStore.viewMode === '3D_FirstPerson'){
        //     return (<div className={styles.container_listInfo}>
        //         {btnList.map((area: TRoomEntity) => (
        //         <div
        //             className={`${styles.btn} ${singleSelectRoom?.aliasName === area.aliasName ? styles.selected : ''}`}
        //             key={area.aliasName}
        //             onClick={() => {
        //                 if (store.homeStore.isSingleRoom) {
        //                     LayoutAI_App.DispatchEvent(LayoutAI_Events.SingleRoomLayout, area);
        //                     handleAreaClick(area);
        //                 } else {
        //                     handleAreaClick(area);
        //                 }
        //             }}
        //         >
        //             {t(area.aliasName)}
        //         </div>
        //     ))}
        //     </div>)
        // }
        return (
            <div className={styles.container_listInfo}>
            {!store.homeStore.isdrawPicture && 
                <div className={''} onClick={() => {
                // 如果已经是2D模式, 则需要保存
                    onExit();
                    // store.homeStore.setShowEnterPage({show: true, source: 'pad_exit'})
                }}>
                <div className={styles.container_listInfo}>
                    <div className={styles.btn}>
                        <Icon style={{fontSize: '16px', marginRight: '4px', color: store.homeStore.viewMode !== "2D" ? '#fff' : '#595959'}} iconClass="icon-a-fangxiangzuo" />
                        {t("退出")}
                    </div>
                </div>
            </div>
            }
            {!isSingleRoomMode?
                <div
                    className={`${styles.btn} ${isWholeHouse ? styles.selected : ''}`}
                    onClick={() => {
                        handleAreaClick(null)
                    }}
                >
                    {t('全屋')}
                </div> :
                <div className={`${styles.btn} ${isWholeHouse ? styles.selected : ''}`} onClick={() => {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
                    // store.homeStore.setIsSingleRoom(false);
                    handleAreaClick(null)
                }}>
                    {t('全屋')}
                </div>
            }
            {btnList.map((area: TRoomEntity) => (
                <div
                    className={`${styles.btn} ${singleSelectRoom?.aliasName === area.aliasName && !isWholeHouse ? styles.selected : ''}`}
                    key={area.aliasName}
                    onClick={() => {
                        if (store.homeStore.isSingleRoom) {
                            LayoutAI_App.DispatchEvent(LayoutAI_Events.SingleRoomLayout, area);
                            handleAreaClick(area);
                        } else {
                            handleAreaClick(area);
                        }
                    }}
                >
                    {t(area.aliasName)}
                </div>
            ))}
        </div>
        )
    }

    const onExit = () => {
        if (!layoutContainer._layout_scheme_id && layoutContainer._room_entities.length > 0) {
            store.homeStore.setShowSaveLayoutSchemeDialog({show: true, source: 'padExitBtn'}); // 先要保存
            if(is_dreamer_mini_App)
            {
                store.homeStore.setIsAutoExit('autoExit');
            }
        } else if(layoutContainer._layout_scheme_id) {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutScheme, null);

            if(is_dreamer_mini_App)
            {
                store.homeStore.setIsAutoExit('autoExit');
            }
   
            store.homeStore.setShowEnterPage({show: true, source: 'padExitBtn'})
        }
        else {
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'canClose',
                data: {
                canClose: true
                }
            }, '*');
            SdkService.exitSDK();
            window.location.href = workDomainMap;
        }
    }

    return (
        <div className={styles.root} id='RoomAreaBtns'>

            <If condition = {isSingleRoomMode}>
                <Then>
                    {roomAreaBtns()}
                </Then>
                <Else>
                    <If condition = {!store.homeStore.IsLandscape}>
                        <Then>
                            {roomAreaBtns()}
                        </Then>
                        <Else>
                        <div className={styles.selectListBar}>
                                <Select
                                    value={store.homeStore.selectedRoom?.uidN || 'all'}
                                    style={{ width: '100%' }}
                                    size='small'
                                    options={options}
                                    dropdownStyle={{zIndex: 9999}}
                                    onChange={(value: any) => {
                                        if (value === 'all') {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, null);
                                        }
                                        else {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, store.homeStore.roomEntities.find((item: TRoomEntity) => item.uidN === value));
                                        }
                                    }}
                                ></Select>
                            </div>
                        </Else>
                    </If>
                </Else>
            </If>
        </div>
    )
}

export default observer(RoomAreaBtns);