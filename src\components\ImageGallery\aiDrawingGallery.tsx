
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import useStyles from "./style"
import { AIGCService } from "@/Apps/LayoutAI/Services/AIGC/AIGCService";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { SdkService } from "@/services/SdkService";
import { observer } from "mobx-react-lite";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { message } from "@svg/antd";
interface DivStyles {
    left?: string;
    top?: string;
    width?: string;
    height?: string;
    border?: string;
    [key: string]: string;
}
export const aiDrawingTukuBtnId = "aidrawing_tuku_btn";
/**
 * Ai绘图相册
 * @returns  
 */
const AiDrawingGallery: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const store = useStore();
    const [captureDivStyle, setCaptureDivStyles] = useState<DivStyles>({ left: "0", top: "0", width: "0", height: "0", border: "0px solid #ffffea", transition: "all 0.3s ease" });

    const object_id = "AiDrawingGallery";


    const layoutContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;

    const submitAiDraw = async () => {
        const scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
        let roomname = (store.homeStore.guideMapCurrentRoom?.name) || (layoutContainer._selected_room?.roomname) || "";
        let target_room = store.homeStore.guideMapCurrentRoom || (layoutContainer._selected_room) || null;
        let aiModel = 3;
        if (target_room && target_room.furnitureList && target_room.furnitureList.length > 0) {
            aiModel = 0;
        }
        let selectbox_visible = false;
        if (scene3D.selection_box?.visible) {
            selectbox_visible = scene3D.selection_box.visible;
            scene3D.selection_box.visible = false;
        }
        scene3D.update();
        let res = await AIGCService.instance.submitAiDraw({ room_name: roomname, roomUid:target_room?.uid||"", aiModel: aiModel,  }, store.homeStore.aspectRatioMode);

        if (selectbox_visible) {
            scene3D.selection_box.visible = selectbox_visible;

        }
    }

    const tryToSubmit = async () => {
        let interval: any = null;
        message.loading('提交渲染中...', 0);
        if (!layoutContainer._layout_scheme_id) {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);

            // 清除已有定时器避免重复创建
            if (interval) clearInterval(interval);
            
            interval = setInterval(async () => {
                if (layoutContainer._layout_scheme_id) {
                // 执行前立即清除定时器
                clearInterval(interval!);
                interval = null;
                await submitAiDraw();
                message.destroy();
                }
            }, 500);
            return;
        } else
        {
            await submitAiDraw();
        }
        await store.homeStore.query_genCount()
        message.destroy();
        message.success('提交AI绘图成功！');
    }
    useEffect(() => {
        LayoutAI_App.on_M(Scene3DEvents.AiDrawingCapture, object_id, () => {
            const scene3D = (LayoutAI_App.instance).scene3D as Scene3D;
            const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            const capturingCaptureDivStyles: DivStyles = { left: "0", top: "0", width: "100vw", height: "100vh", transition: "none" }
            const targetCaptureDivStyles: DivStyles = { left: "0", top: "0", width: "0", height: "0", border: "0px solid #ffffea", transition: "all 0.3s ease" };


            tryToSubmit();


            if (scene3D.parent_div) {
                let left = scene3D.parent_div.offsetLeft;
                let top = scene3D.parent_div.offsetTop;
                let width = scene3D.parent_div.offsetWidth;
                let hegiht = scene3D.parent_div.offsetHeight;

                capturingCaptureDivStyles.left = left + "px";
                capturingCaptureDivStyles.top = top + "px";
                capturingCaptureDivStyles.width = width + "px";
                capturingCaptureDivStyles.height = hegiht + "px";
            }

            let targetDiv = document.getElementById(aiDrawingTukuBtnId);
            if (targetDiv && targetDiv.getBoundingClientRect) {
                let rect = targetDiv.getBoundingClientRect();
                if (rect) {
                    let left = rect.left;
                    let top = rect.top;
                    let width = rect.width;
                    let height = rect.height;
                    left += (width / 2);
                    top += (height / 2);
                    targetCaptureDivStyles.left = left + "px";
                    targetCaptureDivStyles.top = top + "px";
                }

            }

            setCaptureDivStyles({ ...capturingCaptureDivStyles });
            setTimeout(() => {
                setCaptureDivStyles({ ...targetCaptureDivStyles });
            }, 300);
        });

        LayoutAI_App.on_M(EventName.diffuseImage, 'aiDrawingGallery', (imgUrl: string) => {
            SdkService.diffuseImageSDK(imgUrl, store.homeStore.guideMapCurrentRoom?.roomname);
        });
    }, [])
    return <>

        <div className={styles.photo_capture_div + " photo_capture_div"} style={captureDivStyle}>

        </div>



    </>
}
export default observer(AiDrawingGallery);