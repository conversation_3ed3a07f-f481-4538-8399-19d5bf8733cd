import { createStyles } from '@svg/antd/es/theme/utils';

const useStyles = createStyles(({ css }) => ({
  exitBarContainer: css`
    position: fixed;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    max-width: 200px;
    width: auto;
    height: 32px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 999;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.40);
    backdrop-filter: blur(50px);;
    justify-content: space-between;
    @media screen and (max-width: 450px) { // 手机宽度
      min-width: 250px;
      height: 36px;
    }
  `,

  currentMode: css`
    color: #333;
    font-size: 14px;
    margin-right: 12px;
    max-width: 40%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,

  exitButton: css`
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 6px 20px;
    height: 28px;
    line-height: 16px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 12px;
    &:hover {
      background-color: #40a9ff;
    }
  `,

  exitHint: css`
    color: #999;
    font-size: 14px;
    margin-left: 12px;
    white-space: nowrap;
  `,
  topTabs: css`
    width: 100px;
    color: #d6d1d1;
    font-size: 14px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    @media screen and (max-width: 450px) { // 手机宽度
      width: 60px;
      font-size: 12px;
    }
    @media screen and (max-width: 350px) { // 手机宽度
    }
    z-index: 9;
  `,
  active: css`
    color: #fff;
    background: rgba(255, 255, 255, 0.20);
    backdrop-filter: blur(50px);
    border-radius: 8px;
  `,
}));

export default useStyles;
