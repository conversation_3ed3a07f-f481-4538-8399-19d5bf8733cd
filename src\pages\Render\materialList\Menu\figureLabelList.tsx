import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
import { EventName } from '@/Apps/EventSystem';
import { FigureDataList } from '@/Apps/LayoutAI/Drawing/FigureImagePaths';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { SearchList } from '@/components';
import { useStore } from '@/models';
import { getPrefix } from '@/utils/common';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from '@svg/antd';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FigureMenu from '../FigureMenu/figureMenu';
import useStyles from './style';

/**
 * @description 按钮组件
 */
const FigureLabelList: React.FC = () => {
  const store = useStore();
  const { t } = useTranslation()
  const { styles } = useStyles();
  const [figureDataList, setFigureDataList] = useState<any[]>([]);
  const [showSpin, setShowSpin] = useState<boolean>(false);
  const [rootName, setRootName] = useState('');
  const [filterName, setFliterName] = useState('');
  const [currentMenuList, setCurrentMenuList] = useState([]);
  const [showSearch, setShowSearch] = useState(false);
  const [searchList, setSearchList] = useState([]);
  const menuList = (LayoutAI_App.IsDebug || store.userStore.beta) ? FigureDataList : FigureDataList.filter((item: any) => item.label !== '视角' && item.label !== '户型' && item.label !== '定制'); /*[i18n:ignore]*/
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

  let allList: any = [];
  let finitureList: any = [];
  let finitureAllList: any = [];

  menuList.map((item) => {
    item.child.map(item => {
      allList = allList.concat(item.figureList);
    })
  });

  finitureList = menuList.filter((item: any) => item.label !== '户型');  /*[i18n:ignore]*/
  finitureList.map((item: any, index: any) => {
    item.child.map((item: any) => {
      finitureAllList = finitureAllList.concat(item.figureList);
    })
  });

  const initData = () => {
    handleLabelClick(menuList[0]);
    setRootName(menuList[0].label);
    setFliterName(menuList[0].child[0].label);
  };

  const isExpanded = (name: string) => {
    return rootName === name;
  };

  const handleLabelClick = (data: any) => {
    setFigureDataList(data.child);
  };
  const object_id = "FiguresMenu";

  const updateCurrentMenuList = () => {

    let mode = LayoutAI_App.instance._current_handler_mode;
    if (mode === AI2DesignBasicModes.HouseDesignMode) {
      let t_menu_list = menuList.filter((data) => data.label.includes("户型"));  /*[i18n:ignore]*/
      setCurrentMenuList(t_menu_list);
      setFigureDataList(t_menu_list[0]?.child[0]?.figureList);
    }
    else {
      let t_menu_list = menuList.filter((data) => true);
      setCurrentMenuList(t_menu_list);
    }
  }
  useEffect(() => {
    initData();

    LayoutAI_App.on_M(EventName.AIDesignModeChanged, object_id, () => {

      updateCurrentMenuList();
    });

    updateCurrentMenuList();

  }, []);

  useEffect(() => {
    setShowSpin(true);
    setTimeout(() => {
      setShowSpin(false);
    }, 200);
  }, [figureDataList]);

  useEffect(() => {
    if (LayoutAI_App.instance._current_handler_mode === AI2DesignBasicModes.HouseDesignMode) return;
  }, [store.homeStore.selectedRoom])



  return (
    <div className={styles.root}>
      {/* <div className={styles.searchInfo}>
          <div className={styles.inputInfo}>
            {inputValue && <Icon
              className={styles.deleteIcon}
              iconClass="iconclosecirle_fill"
              style={{
                fontSize: '16px',
                color: '#6C7175', 
              }}
              onClick={() => {
                setInputValue('');
                initData();
                setShowSearch(false);
              }}
            >
            </Icon>}
            
            <Icon
              onMouseEnter={() => {
              }}
              onClick={() => {
                onSearch(inputValue);
              }}
              className={styles.Icon}
              iconClass="iconsearch"
              style={{
                fontSize: '16px',
                color: '#6C7175', 
                cursor: 'pointer'
              }}
            />
            <input 
              value={inputValue}
              onKeyDown={(data) => {
                if(data.key != 'Enter') {
                  return;
                }
                onSearch(inputValue);
              }}

              onChange={(event) => {
                setShowSearch(true);
                setInputValue(event.currentTarget.value);
                setSearchList(allList.filter((item: any) => t(item.title).includes(event.currentTarget.value)));
                if(event.currentTarget.value === '') {
                  initData();
                  setShowSearch(false);
                }
              }}
              onMouseEnter={() => {
              }}
              onMouseLeave={() => {
              }}
              className={styles.container_input} 
              placeholder={t("搜索全部素材")}
            />
          </div>
        </div> */}

      {/* 列表页 */}
      {
        !showSearch ?
          <div className={styles.menu_container} style={{ opacity: showSearch ? 0 : 1 }}>
            <div className={styles.tab_box}>
              {menuList.map((item) => (
                <div
                  key={item.label}
                  className={`item ${isExpanded(item.label) ? 'active' : ''}`}
                  onClick={() => {
                    handleLabelClick(item);
                    setRootName(item.label);
                    setFliterName(item.child[0].label);
                  }}
                >
                  <img src={`https://3vj-fe.3vjia.com/layoutai/figures_imgs/${item.png}`} alt={(item as any).title} />
                  <span className="label_name">{t(item.label)}</span>
                </div>
              ))}
            </div>
            {/* { !checkIsMobile() && <div className={styles.line}></div>} */}
            {/* <div className={styles.filterlist}>
                  {
                    figureDataList.map((item, index) => (
                      <div 
                        key={item.label}
                        className={`item ${isFilterExpanded(item.label) ? 'active' : ''}`}
                        onClick={() => {
                          handleFilterClick(item);
                          setFliterName(item.label);
                        }}
                      >
                        <div className="title">{t(item.label)}</div>
                      </div>
                    ))
                  }
              </div> */}
            <div className={styles.figure_box}>
              <Spin indicator={antIcon} spinning={showSpin}>
                <FigureMenu data={figureDataList} filterName={filterName} />
              </Spin>
            </div>
          </div>
          :
          <SearchList data={searchList}></SearchList>
      }
    </div>
  );
};
export default observer(FigureLabelList);
