.dialog_root {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 414px;
    max-height: 304px;
    transform: translate(-50%, -50%);
    z-index: 1000; /* 确保蒙层在其他元素之上 */
    background-color: #f5f5f5;
    border-radius: 12px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.dialog_header {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 6px 20px;
    gap: 10px;
    align-self: stretch;
}

.dialog_tile {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;

    color: #282828;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}
.dialog_close {
    width: 20px;
    height: 20px;
}
.dialog_content {
    height: 100%;
    width: 100%;
    margin: 0px 0px 0px 0px;
    padding: 20px;
    background-color: #f5f5f5;
    display: flex;
    padding: 20px;
    align-items: flex-start;
    gap: 28px;
    border-radius: 12px;
}

.dialog_form {
    width: 100%;
}

.dialog_save_button {
    display: flex;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 4px;
    background: linear-gradient(91deg, #BA63F0 -0.97%, #5C42FB 100%);
}

.dialog_exit_button {
    display: flex;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 4px;
    background: #E9EBEB;
}