import { ZRect } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions } from "../TCheckRule";
import { TLivingRoomClassfierCheckRule } from "./TLivingRoomClassfierCheckRule";

export class TLivingSofaAndTVMinDistanceCheckRule extends TLivingRoomClassfierCheckRule
{
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any
    {
        let sofaFigures: TFigureElement[] = this.categoryFigures.get(this.sofaName);
        let TVFigures: TFigureElement[] = this.categoryFigures.get(this.TVName);
        let minFrontLen: number = (sofaFigures.length && TVFigures.length)? Number.POSITIVE_INFINITY : -1;
        for(let sofaFigure of sofaFigures)
        {
            for(let TVFigure of TVFigures)
            {
                // TODO 这个还需要考虑重叠的情况
                let otherRect: ZRect = TVFigure.rect;
                let isInFrontArea: boolean = TBaseRoomToolUtil.instance.polygonIsInFrontArea(sofaFigure.rect.frontEdge, sofaFigure.rect.backEdge, otherRect, 0);
                if(isInFrontArea)
                {
                    let frontAreaLength: number = Math.abs((sofaFigure.rect.frontEdge.center.clone().sub(TVFigure.rect.frontEdge.center)).dot(TVFigure.rect.nor));
                    // let frontAreaLength: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(sofaFigure.rect.frontEdge, otherRect);
                    if(frontAreaLength < minFrontLen)
                    {
                        minFrontLen = frontAreaLength;
                    }
                }
            }
        }
        return {score: minFrontLen};
    }
}