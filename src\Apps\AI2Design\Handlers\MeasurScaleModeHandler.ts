import { CadDrawingLayerType } from "@/Apps/LayoutAI/Drawing/TDrawingLayer";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2BaseModeHandler } from "../AI2BaseModeHandler";
import { AI2DesignBasicModes, AI2DesignManager } from "../AI2DesignManager";
import { MeasureScaleSubHandler } from "./SubHandlers/MeasurScaleSubHandlers/MeasureScaleSubHandler";

export class MeasurScaleModeHandler extends AI2BaseModeHandler {

    constructor(manager:AI2DesignManager)
    {
        super(manager,AI2DesignBasicModes.MeasurScaleMode);
        this._transform_elements = [];
        this._cad_default_sub_handler = new MeasureScaleSubHandler(this as any);
    }


    get exsorb_rects()
    {
        return this._exsorb_target_rects;
    }

    get container()
    {
        return this.manager.layout_container;
    }

    enter(state?: number): void {
        super.enter(state);
        console.log("enter MeasurScaleMode");

        for(let layer of this.manager._visible_layers)
        {
            layer.visible = false;
        }
        this.manager.layer_CadCopyImageLayer.visible = true;
    
        if(this.manager.drawing_layers[CadDrawingLayerType.CadDecorates])
        {
            this.manager.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;
        }
        if(this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing])
        {
            this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = false;
        }
        

        if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.updateCandidateRects();
            this._cad_default_sub_handler.updateAttributes("hide",0);

            this._cad_default_sub_handler.enter();
        }
        this.manager.onLayerVisibilityChanged();
        this.painter.p_scale = 0.05;
        this.manager.layout_container.focusCenter();
        this.update();
    }
    leave(state?: number): void {
        super.leave(state);
        if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.leave();
        }
    }
    
    updateEzdxfLayers()
    {

    }
    
    onmousedown(ev: I_MouseEvent): void {

        if(this._active_sub_handler)
        {
            this._active_sub_handler.onmousedown(ev);
        }
        else if(this._cad_default_sub_handler)
        {
            // 默认方法mouse_down后, 有可能触发进入新的active_handler
            this._cad_default_sub_handler.onmousedown(ev);

            if(this._active_sub_handler)
            {
                this._active_sub_handler.onmousedown(ev);
            }


        }

        this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

        this._is_painter_center_moving = false;
        // super.onmousedown(ev);

    }
    onmousemove(ev: I_MouseEvent): void {

        if(this._active_sub_handler)
        {
            super.onmousemove(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onmousemove(ev);
            }
            super.onmousemove(ev);
        }
        // super.onmousemove(ev);
    }
    onmouseup(ev: I_MouseEvent): void {
        if(this._active_sub_handler)
        {
            super.onmouseup(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onmouseup(ev);
            }
            super.onmouseup(ev);
        }

    }

    onwheel(ev: WheelEvent): void {
        if(this._active_sub_handler)
        {
            super.onwheel(ev);
            this._active_sub_handler.onwheel(ev);
        }
        else{
            if(this._cad_default_sub_handler)
            {
                this._cad_default_sub_handler.onwheel(ev);
            }
            super.onwheel(ev);
        }
    }

    onkeydown(ev: KeyboardEvent): boolean {
        if(this._active_sub_handler)
        {
            this._active_sub_handler.onkeydown(ev);
        }
        else if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.onkeydown(ev);
        }
        
    
        if(ev.ctrlKey)
        {
            if(ev.code ==='KeyZ')
            {
                this.runCommand(LayoutAI_Commands.Undo);
            }
            else if(ev.code === "KeyY")
            {
                this.runCommand(LayoutAI_Commands.Redo);

            }
        }

        return true;
    }

    async runCommand(cmd_name: string): Promise<void> {
        if(!cmd_name) return;

        super.runCommand(cmd_name); 
        if(cmd_name === LayoutAI_Commands.Reset)
        {
            this.reset();
        }
    }
    handleEvent(event_name:string, event_param:any) {
        
        super.handleEvent(event_name,event_param);
    }
    reset()
    {

    }
    drawCanvas(): void {
        

        if(this.manager.layer_DefaultBatchLayer)
        {
            let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
            if(!batchDirty)
            {
                if(this._is_painter_center_moving 
                    && this.manager.layer_DefaultBatchLayer.cleaned_drawing_count > 5)
                {
                    // batchDirty = true;
                }
            }
            this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);

        }
        if(this._active_sub_handler)
        {
            this._active_sub_handler.drawCanvas();
        }
        else if(this._cad_default_sub_handler)
        {
            this._cad_default_sub_handler.drawCanvas();
        }
    }

}