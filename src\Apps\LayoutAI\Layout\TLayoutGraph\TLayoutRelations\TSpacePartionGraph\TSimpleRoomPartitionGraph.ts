import { TRoom } from "../../../TRoom";
import { TLayoutGraph } from "../../TLayoutGraph";
import { TSimpleRoomPartRelation as TSimpleRoomPartRelation } from "./TSimpleRoomRelations/TSimpleRoomPartRelation";
import { compareNames } from "@layoutai/z_polygon";
import { TKitchenPartRelation } from "./TSimpleRoomRelations/TKitchenPartRelation";
import { TKitchenPart2Relation } from "./TSimpleRoomRelations/TKitchenPart2Relation";
import { TTinyBedRoomRelation } from "./TSimpleRoomRelations/TTinyBedRoomRelation";
import { TTinyLivingAreaRelation } from "./TSimpleRoomRelations/TTinyLivingAreaRelation";
import { TTinyDiningAreaRelation } from "./TSimpleRoomRelations/TTinyDiningAreaRelation";
import { TTinyLivingRoomRelation } from "./TSimpleRoomRelations/TTinyLivingRoomRelation";
import { TTinyWashingRoomRelation } from "./TSimpleRoomRelations/TTinyWashingRoomRelation";
import { TTinyEntranceRoomRelation } from "./TSimpleRoomRelations/TTinyEntranceRoomRelation";
import { TSimpleRoomRelationToolUtil } from "./TSImpleRoomRelationTool/TSimpleRoomRelationToolUtil";
import { THuaweiKitchenRelation } from "./TSimpleRoomRelations/THuaweiKitchenRelation";
import { LayoutAI_Configs } from "../../../TLayoutEntities/configures/LayoutAIConfigs";
import { TTinyLivingRoomByPathRelation } from "./TSimpleRoomRelations/TTinyLivingRoomByPathRelation";

const debug : boolean = true;

export class TSimpleRoomPartionGraph extends TLayoutGraph
{


    constructor(room_name:string="")
    {
        super();

        this._curr_scheme_id = 0;


        // console.log("init simple room graph",room_name);

        if(compareNames([room_name],["客餐厅"]))
        {
            this.relations.push(new TTinyLivingRoomByPathRelation());
            // this.relations.push(new TTinyLivingRoomRelation());
        }
        else if(compareNames([room_name],["厨房"]))
        {
            if(LayoutAI_Configs.Configs.app_specific_domain === "Huawei")
            {
                // 华为厨房
                console.log("华为厨房");
                this.relations.push(new THuaweiKitchenRelation());

            }
            else{
                this.relations.push(new TKitchenPart2Relation());

            }
        }
        else if(compareNames([room_name],["客厅区"]))
        {
            this.relations.push(new TTinyLivingAreaRelation());
        }
        else if(compareNames([room_name],["餐厅区"]))
        {
            this.relations.push(new TTinyDiningAreaRelation());
        }
        else if(compareNames([room_name],["卫生间"]))
        {
            this.relations.push(new TTinyWashingRoomRelation());
        }
        else if(compareNames([room_name],["卧室"]))
        {
            this.relations.push(new TTinyBedRoomRelation());
        }
        else if(compareNames([room_name],["入户花园"]))
        {
            this.relations.push(new TTinyEntranceRoomRelation());
        }
        else{
            this.relations.push(new TSimpleRoomPartRelation());

        }

    }

  
    /**
     *  再汇总结果的时候 做一次后处理
     */
    _save_result(): void {

        // if(!this.checkIntegrity(this._room,this._group_template_list)){
        //     return;
        // }
        
        super._save_result();
    }
    
    applyRoom(room: TRoom): void {

        this._room = room;  
        if(!this._room.feature_shapes)
        {
            this._room.updateFeatures();
        }
        
        for(let relation of this.relations)
        {
            relation._room = room;
            relation._graph = this;
        
        }
        // console.log(room.max_rect_shape._rect.nor,room.max_rect_shape._rect.dv);
        this._group_template_list = [];
        this._result_scheme_list = [];
        if (debug) console.time("TSimpleRoom ApplyRoom! "+room.roomname);
        this._dfs_apply_relations(0);
        this.postProcessResultSchemeList(room);
        if (debug) console.timeEnd("TSimpleRoom ApplyRoom! "+room.roomname);
    }
}

