"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[1742],{71742:function(e,a,l){l.r(a),l.d(a,{default:function(){return c}});var r={items_per_page:"/ sayfa",jump_to:"Git",jump_to_confirm:"onayla",page:"Sayfa",prev_page:"Önceki Sayfa",next_page:"Sonraki Sayfa",prev_5:"Önceki 5 Sayfa",next_5:"Sonraki 5 Sayfa",prev_3:"Önceki 3 Sayfa",next_3:"Sonraki 3 Sayfa",page_size:"sayfa boyutu"},n=l(70989),i=l(44340),t=(0,n.A)((0,n.A)({},i.I),{},{locale:"tr_TR",today:"Bugün",now:"Şimdi",backToToday:"<PERSON><PERSON><PERSON>ne <PERSON>",ok:"Tamam",clear:"Temizle",week:"Hafta",month:"Ay",year:"Yıl",timeSelect:"<PERSON>aman Seç",dateSelect:"<PERSON><PERSON><PERSON>",monthSelect:"Ay Seç",yearSelect:"Yıl Seç",decadeSelect:"On Yıl Seç",dateFormat:"DD/MM/YYYY",dateTimeFormat:"DD/MM/YYYY HH:mm:ss",previousMonth:"Önceki Ay (PageUp)",nextMonth:"Sonraki Ay (PageDown)",previousYear:"Önceki Yıl (Control + Sol)",nextYear:"Sonraki Yıl (Control + Sağ)",previousDecade:"Önceki On Yıl",nextDecade:"Sonraki On Yıl",previousCentury:"Önceki Yüzyıl",nextCentury:"Sonraki Yüzyıl",shortWeekDays:["Paz","Pzt","Sal","Çar","Per","Cum","Cmt"],shortMonths:["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara"]});var o={placeholder:"Zaman seç",rangePlaceholder:["Başlangıç zamanı","Bitiş zamanı"]};const m={lang:Object.assign({placeholder:"Tarih seç",yearPlaceholder:"Yıl seç",quarterPlaceholder:"Çeyrek seç",monthPlaceholder:"Ay seç",weekPlaceholder:"Hafta seç",rangePlaceholder:["Başlangıç tarihi","Bitiş tarihi"],rangeYearPlaceholder:["Başlangıç yılı","Bitiş yılı"],rangeMonthPlaceholder:["Başlangıç ayı","Bitiş ayı"],rangeWeekPlaceholder:["Başlangıç haftası","Bitiş haftası"]},t),timePickerLocale:Object.assign({},o)};const s="${label} geçerli bir ${type} değil";var c={locale:"tr",Pagination:r,DatePicker:m,TimePicker:o,Calendar:m,global:{placeholder:"Lütfen seçiniz"},Table:{filterTitle:"Filtre menüsü",filterConfirm:"Tamam",filterReset:"Sıfırla",filterEmptyText:"Filtre yok",filterCheckAll:"Tümünü seç",selectAll:"Tüm sayfayı seç",selectInvert:"Tersini seç",selectionAll:"Tümünü seç",sortTitle:"Sırala",expand:"Satırı genişlet",collapse:"Satırı daralt",triggerDesc:"Azalan düzende sırala",triggerAsc:"Artan düzende sırala",cancelSort:"Sıralamayı kaldır"},Modal:{okText:"Tamam",cancelText:"İptal",justOkText:"Tamam"},Popconfirm:{okText:"Tamam",cancelText:"İptal"},Transfer:{titles:["",""],searchPlaceholder:"Arama",itemUnit:"Öğe",itemsUnit:"Öğeler",remove:"Kaldır",selectCurrent:"Tüm sayfayı seç",removeCurrent:"Sayfayı kaldır",selectAll:"Tümünü seç",deselectAll:"Tümünün seçimini kaldır",removeAll:"Tümünü kaldır",selectInvert:"Tersini seç"},Upload:{uploading:"Yükleniyor...",removeFile:"Dosyayı kaldır",uploadError:"Yükleme hatası",previewFile:"Dosyayı önizle",downloadFile:"Dosyayı indir"},Empty:{description:"Veri Yok"},Icon:{icon:"ikon"},Text:{edit:"Düzenle",copy:"Kopyala",copied:"Kopyalandı",expand:"Genişlet"},Form:{optional:"(opsiyonel)",defaultValidateMessages:{default:"Alan doğrulama hatası ${label}",required:"${label} gerekli bir alan",enum:"${label} şunlardan biri olmalı: [${enum}]",whitespace:"${label} sadece boşluk olamaz",date:{format:"${label} tarih biçimi geçersiz",parse:"${label} bir tarihe dönüştürülemedi",invalid:"${label} geçersiz bir tarih"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label} ${len} karakter olmalı",min:"${label} en az ${min} karakter olmalı",max:"${label} en çok ${max} karakter olmalı",range:"${label} ${min}-${max} karakter arası olmalı"},number:{len:"${label} ${len} olmalı",min:"${label} en az ${min} olmalı",max:"${label} en çok ${max} olmalı",range:"${label} ${min}-${max} arası olmalı"},array:{len:"${label} sayısı ${len} olmalı",min:"${label} sayısı en az ${min} olmalı",max:"${label} sayısı en çok ${max} olmalı",range:"${label} sayısı ${min}-${max} arası olmalı"},pattern:{mismatch:"${label} şu kalıpla eşleşmeli: ${pattern}"}}},Image:{preview:"Önizleme"}}}}]);