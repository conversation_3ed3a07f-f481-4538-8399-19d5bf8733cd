import { <PERSON>, G<PERSON> } from "lil-gui";
import { Object3D } from "three";
import { RoomSpaceAreaType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { AutoLightingService } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightService";
import { TRoomType } from "@/Apps/LayoutAI/Layout/RoomType";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { LightManager } from "@/Apps/LayoutAI/Scene3D/LightManager";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";

export class EnvLightTest {
    private _gui: GUI;
    private _sunHelper: Object3D;
    private _subSunHelper: Object3D;

    constructor(gui: GUI) {
        this._gui = gui;

        this._sunHelper = LightManager.addLightHelper(LightManager.sunlight, LayoutAI_App.instance.scene3D.dayLightsGroup, 400, 0xff0000);
        this._sunHelper.visible = false;
        this._subSunHelper = LightManager.addLightHelper(LightManager.subSunLight, LayoutAI_App.instance.scene3D.dayLightsGroup, 200, 0xee0000);
        this._subSunHelper.visible = false;
    }

    public addEnvLightCtr() {
        let envLightFolder = this._gui.addFolder('环境光');
        envLightFolder.close();

        let dayAmbient = LightManager.getDayAmbientLight();
        envLightFolder.add({ ambientIntensity: dayAmbient.intensity }, 'ambientIntensity', 0, 10, 0.01)
            .name('环境光强度')
            .onChange((value: number) => {
                dayAmbient.intensity = value;
            });

        envLightFolder.addColor({ ambientColor: [dayAmbient.color.r, dayAmbient.color.g, dayAmbient.color.b] }, 'ambientColor')
            .name('颜色')
            .onChange((value: number[]) => {
                dayAmbient.color.setRGB(value[0], value[1], value[2]);
            });

        envLightFolder.add({ sunlightIntensity: LightManager.sunlight.intensity }, 'sunlightIntensity', 0, 10, 0.01)
            .name('阳光强度')
            .onChange((value: number) => {
                LightManager.sunlight.intensity = value;
            });

        envLightFolder.addColor({ sunlightColor: [LightManager.sunlight.color.r, LightManager.sunlight.color.g, LightManager.sunlight.color.b] }, 'sunlightColor')
            .name('颜色')
            .onChange((value: number[]) => {
                LightManager.sunlight.color.setRGB(value[0], value[1], value[2]);
            });

        envLightFolder.add({ showHelper: this._sunHelper.visible }, 'showHelper')
            .name('外显')
            .onChange((value: boolean) => {
                this._sunHelper.visible = value;
            });

        let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        layout_container._room_entities.forEach((roomEntity) => {
            if (roomEntity.room_type === TRoomType.livingRoom) {
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.LivingArea);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照客厅`);
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity, RoomSpaceAreaType.DiningArea);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照餐厅`);
            } else {
                envLightFolder.add({
                    autoRoomSunLighting: () => {
                        AutoLightingService.instance.autoRoomSunLighting(roomEntity);
                        // (sunHelper as DirectionalLightHelper).update();
                    }
                }, 'autoRoomSunLighting').name(`照${roomEntity.name}`);
            }
        });

        let sunRotationControllerX: Controller;
        let sunRotationControllerY: Controller;
        let sunRotationControllerZ: Controller;
        let syncSunRotation = function () {
            if (sunRotationControllerX) {
                sunRotationControllerX.setValue(LightManager.sunlight.rotation.x);
            }
            if (sunRotationControllerY) {
                sunRotationControllerY.setValue(LightManager.sunlight.rotation.y);
            }
            if (sunRotationControllerZ) {
                sunRotationControllerZ.setValue(LightManager.sunlight.rotation.z);
            }
        }
        envLightFolder.add(LightManager.sunlight.position, 'x').name('位置 x').onChange(syncSunRotation);
        envLightFolder.add(LightManager.sunlight.position, 'y').name('位置 y').onChange(syncSunRotation);
        envLightFolder.add(LightManager.sunlight.position, 'z').name('位置 z').onChange(syncSunRotation);

        sunRotationControllerX = envLightFolder.add({ x: LightManager.sunlight.rotation.x * 180 / Math.PI }, 'x').name('方向 x').onChange((value: number) => {
            LightManager.sunlight.rotation.x = value * Math.PI / 180;
        })
        sunRotationControllerY = envLightFolder.add({ y: LightManager.sunlight.rotation.y * 180 / Math.PI }, 'y').name('方向 y').onChange((value: number) => {
            LightManager.sunlight.rotation.y = value * Math.PI / 180;
        })
        sunRotationControllerZ = envLightFolder.add({ z: LightManager.sunlight.rotation.z * 180 / Math.PI }, 'z').name('方向 z').onChange((value: number) => {
            LightManager.sunlight.rotation.z = value * Math.PI / 180;
        })

        envLightFolder.add({ sublightIntensity: LightManager.subSunLight.intensity }, 'sublightIntensity', 0, 10, 0.01)
            .name('辅光强度')
            .onChange((value: number) => {
                LightManager.subSunLight.intensity = value;
            });

        envLightFolder.addColor({ sublightColor: [LightManager.subSunLight.color.r, LightManager.subSunLight.color.g, LightManager.subSunLight.color.b] }, 'sublightColor')
            .name('颜色')
            .onChange((value: number[]) => {
                LightManager.subSunLight.color.setRGB(value[0], value[1], value[2]);
            });

        envLightFolder.add({ showHelper: this._subSunHelper.visible }, 'showHelper')
            .name('外显')
            .onChange((value: boolean) => {
                this._subSunHelper.visible = value;
            });

        let subSunRotationControllerX: Controller;
        let subSunRotationControllerY: Controller;
        let subSunRotationControllerZ: Controller;
        let syncSubSunRotation = function () {
            if (subSunRotationControllerX) {
                subSunRotationControllerX.setValue(LightManager.subSunLight.rotation.x);
            }
            if (subSunRotationControllerY) {
                subSunRotationControllerY.setValue(LightManager.subSunLight.rotation.y);
            }
            if (subSunRotationControllerZ) {
                subSunRotationControllerZ.setValue(LightManager.subSunLight.rotation.z);
            }
        }
        envLightFolder.add(LightManager.subSunLight.position, 'x').name('位置 x').onChange(syncSubSunRotation);
        envLightFolder.add(LightManager.subSunLight.position, 'y').name('位置 y').onChange(syncSubSunRotation);
        envLightFolder.add(LightManager.subSunLight.position, 'z').name('位置 z').onChange(syncSubSunRotation);

        subSunRotationControllerX = envLightFolder.add({ x: LightManager.subSunLight.rotation.x * 180 / Math.PI }, 'x').name('方向 x').onChange((value: number) => {
            LightManager.subSunLight.rotation.x = value * Math.PI / 180;
        })
        subSunRotationControllerY = envLightFolder.add({ y: LightManager.subSunLight.rotation.y * 180 / Math.PI }, 'y').name('方向 y').onChange((value: number) => {
            LightManager.subSunLight.rotation.y = value * Math.PI / 180;
        })
        subSunRotationControllerZ = envLightFolder.add({ z: LightManager.subSunLight.rotation.z * 180 / Math.PI }, 'z').name('方向 z').onChange((value: number) => {
            LightManager.subSunLight.rotation.z = value * Math.PI / 180;
        })
    }

    public removeEnvLight() {
        this._sunHelper.removeFromParent();
        this._subSunHelper.removeFromParent();
    }
}