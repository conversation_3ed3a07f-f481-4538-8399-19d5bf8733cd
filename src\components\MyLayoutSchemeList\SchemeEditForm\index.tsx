import React, { useState, useEffect } from 'react';
import { Button, DefaultOptionType, Form, Input, message, Modal } from '@svg/antd';
import useStyles from "./style"
import { CitySelect } from '@svg/antd-basic';
import { editSchemeInfo } from '../services/scheme';
// const { CityCascader } = CitySelect

type dataType = {
  area: number;
  layoutSchemeName: string;
  id: string;
  coverImage: string;
  updateDate: string;
  address?: string;
  province?: string;
  city?: string;
  district?: string;
  cntactMan?: string;
  mobile?: string;
  panoLink?: string;
};
const SchemeEditForm: React.FC<{
  data: dataType;
  refresh: () => void;
  handleEditClose: () => void;
}> = ({ data, refresh, handleEditClose }) => {
  const {styles} = useStyles()
  const [form] = Form.useForm<{
    address: string;
    name: string;
    province: string;
    city: string;
    district: string;
    area: string;
    cntactMan: string;
    mobile: string;
    panoLink: string;
  }>();

  const [addressValue, setAddressValue] = useState<string[]>([]);

  const onChange = (value: (string | number)[], selectOptions: DefaultOptionType[]) => {
    setAddressValue(value.map(String));
    console.log('onChange', value, selectOptions);
  };

  const submitEdit = async (values: any, data: any) => {
    const param = {
      id: data.id,
      layoutSchemeName: values.name,
      address: values.address,
      province: addressValue[0],
      city: addressValue[1],
      district: addressValue[2],
      customerInfo:{
        cntactMan: values.cntactMan,
        mobile: values.mobile,
        ...data.customerInfo
      },
      panoLink: values.panoLink,
    };
    console.log(param);
    const res = await editSchemeInfo(param);
    return res;
  };

  const handleOk = async () => {
    const values = await form.validateFields();
    try {
      const res = await submitEdit(values, data);
      if (res.success) {
        message.success('编辑成功');
        refresh();
      } else {
        message.error(res.errorMessage || '编辑失败');
      }
    } catch (error) {
      console.error('Failed to submit:', error);
    }
    handleEditClose();
  };

  useEffect(() => {
    console.log('data', data);
    if (data && data.province && data.city && data.district) {
      setAddressValue([data.province || '', data.city || '', data.district || '']);
    }
  }, []);

  return (
    <>
      <Modal
        title="编辑方案信息"
        open={true}
        destroyOnClose
        onCancel={handleEditClose}
        footer={[
          <Button key="back" onClick={handleEditClose} className={styles.customCancelButton}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleOk}
            className={styles.customSubmitButton}
          >
            确定
          </Button>,
        ]}
      >
        <Form
          form={form}
          initialValues={{
            address: data.address ? data.address : '',
            name: data.layoutSchemeName ? data.layoutSchemeName : '',
            area: data.area ? data.area : '',
            cntactMan: data.cntactMan ? data.cntactMan : '',
            mobile: data.mobile ? data.mobile : '',
            panoLink: data.panoLink ? data.panoLink : '',
          }}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
        >
          <Form.Item
            name="name"
            label={<span style={{ fontWeight: 'bold' }}>方案名称</span>}
            rules={[
              { required: true, message: '方案名称为必填项' },
              { max: 30, message: '最多支持输入30个字' },
            ]}
          >
            <Input showCount maxLength={30} placeholder="例如:碧桂园601王总" />
          </Form.Item>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Form.Item
              label={<span style={{ fontWeight: 'bold' }}>所属小区</span>}
              style={{ marginRight: '16px', flex: 1 }}
            >
              {/* <CityCascader
                placeholder="请选择"
                onChange={onChange}
                value={addressValue}
              /> */}
            </Form.Item>
            <Form.Item
              name="address"
              label={<span style={{ fontWeight: 'bold' }}></span>}
              style={{ flex: 1 }}
              rules={[{ max: 30, message: '最多支持输入30个字' }]}
            >
              <Input placeholder="例如:碧桂园" />
            </Form.Item>
          </div>
            <Form.Item
              name="area"
              label={<span style={{ fontWeight: 'bold' }}>使用面积</span>}
              style={{ flex: 1 }}
            >
              <Input disabled={true} suffix={'m²'}/>
            </Form.Item>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Form.Item
                name="cntactMan"
                label={<span style={{ fontWeight: 'bold' }}>姓名</span>}
                style={{ flex: 1, marginRight: '16px',  }}
                rules={[{ max: 30, message: '最多支持输入30个字' }]}
              >
                <Input/>
              </Form.Item>
              <Form.Item
                name="mobile"
                label={<span style={{ fontWeight: 'bold' }}>手机号</span>}
                style={{ flex: 1 }}
                rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }]}
              >
                <Input/>
              </Form.Item>
            </div>

            <Form.Item
              name="panoLink"
              label={<span style={{ fontWeight: 'bold' }}>全景</span>}
              style={{ flex: 1 }}
              rules={[
                {
                  required: true,
                  message: '请输入网址链接!',
                },
                {
                  type: 'url',
                  message: '请输入有效的网址链接!',
                },
              ]}
            >
              <Input/>
            </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SchemeEditForm;
