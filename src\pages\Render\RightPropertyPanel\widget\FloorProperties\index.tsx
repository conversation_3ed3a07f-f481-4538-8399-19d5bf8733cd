import React, { useEffect, useState } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Commands } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import MenuList from '../menuList';
import { useStore } from '@/models';
// import { useLocation } from 'react-router-dom';
import { Modal } from '@svg/antd';
import { AI2DesignBasicModes } from '@/Apps/AI2Design/AI2DesignManager';
import { useTranslation } from 'react-i18next'
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
interface RoomSeriesPlanProps {
    roomInfos: TRoom[];
    showLayoutList: boolean;
}
const FloorProperties: React.FC<RoomSeriesPlanProps> = ({ roomInfos, showLayoutList }) => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const [area, setArea] = useState(0);
    const [bedroomNum, setBedroomNum] = useState(0);
    const [livingRoomNum, setRivingRoomNum] = useState(0);
    const [toiletNum, setToiletNum] = useState(0);
    const [menuList, setMenuList] = useState([]);
    const store = useStore();
    // const location = useLocation();
    // const currentPath = location.pathname;
    const [mode, setMode] = useState<any>(AI2DesignBasicModes.DesignMode);
    const { confirm } = Modal;
    const clearWholeHouse = () => {
        if (LayoutAI_App.HandlerMode === "DesignMode") {
            confirm({
                title: t('清空套系'),
                content: t('确定清空套系？'),
                okText: t('确定'),
                cancelText: t('取消'),
                onOk() {
                    LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteRoomSeriesData);
                },
                onCancel() { },
            });
        }
        else {
            confirm({
                title: t('清空图元'),
                content: t('确定清空图元？'),
                okText: t('确定'),
                cancelText: t('取消'),
                onOk() {
                    LayoutAI_App.RunCommand(LayoutAI_Commands.Empty);
                },
                onCancel() { },
            });
        }
    }
    const ai_layout_for_whole_house = () => {
        LayoutAI_App.RunCommand(LayoutAI_Commands.WholeHouseAILayout);
    }

    const modifyStoreyHeight = (valueStr: string) => {
        const storeyHeight: number = Number(valueStr);
        if (storeyHeight >= 2200 && storeyHeight <= 6000) {
            (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height = storeyHeight;
        }
    }

    const getStoryHeight = () => {
        return (LayoutAI_App.instance as TAppManagerBase).layout_container._storey_height;
    }

    useEffect(() => {
        if (!roomInfos) return;
        let area = 0;
        let bedroomCount = 0;
        let livingRoomCount = 0;
        let toiletCount = 0;

        roomInfos.forEach((room: any) => {
            area += room.area;
            if (room.name.includes('室') || room.name.includes('卧')) {
                bedroomCount++;
            }
            if (room.name.includes('厅')) {
                livingRoomCount++;
            }
            if (room.name.includes('厕所') || room.name.includes('卫生间')) {
                toiletCount++;
            }
        });
        setArea(parseFloat(area.toFixed(2)));
        setBedroomNum(bedroomCount);
        setRivingRoomNum(livingRoomCount);
        setToiletNum(toiletCount);

    }, [roomInfos])

    useEffect(() => {
        if (store.homeStore.room2SeriesSampleArray.length > 0 || roomInfos.length > 0) {
            let list: any = [];
            for (let room of (LayoutAI_App.instance as TAppManagerBase).layout_container._rooms) {
                list.push({
                    image_path: null,
                    isFloor: true,
                    title: room._scope_series_map?.soft?.ruleName,
                    title2: room._scope_series_map?.cabinet?.ruleName,
                    title3: room._scope_series_map?.hard?.ruleName,
                    img: room._scope_series_map?.soft?.thumbnail,
                    img1: room._scope_series_map?.cabinet?.thumbnail,
                    img2: room._scope_series_map?.hard?.thumbnail,
                    centerTitle: null,
                    bottomTitle: room.name,
                    area: room.area?.toFixed(2),
                    room: room
                })
            }
            list.sort((a: any, b: any) => b.area - a.area);
            // let maxAreaItem = store.homeStore.room2SeriesSampleArray.reduce((max: any, item: any) => {
            //     if (item[0]?.area > max[0]?.area) {
            //         return item;
            //     } else {
            //         return max;
            //     }
            // }, store.homeStore.room2SeriesSampleArray[0]);

            // let singleRoom = store.homeStore.room2SeriesSampleArray[0];
            // const room:TRoom = maxAreaItem[0];
            // list.push({
            //     image_path: maxAreaItem[1].thumbnail,
            //     title: room._scope_series_map?.soft?.ruleName,
            //     title2: room._scope_series_map?.cabinet?.ruleName,
            //     title3: room._scope_series_map?.hard?.ruleName,
            //     centerTitle: maxAreaItem[1].seriesName,
            //     bottomTitle: store.homeStore.room2SeriesSampleArray.length == 1 ? singleRoom[0].name : '全屋',
            // })
            setMenuList([
                {
                    label: '风格', /*[i18n:ignore]*/
                    figureList: list,
                },
            ]);
        } else {
            setMenuList([
                {
                    label: '风格',  /*[i18n:ignore]*/
                    figureList: [],
                },
            ]);
        }
    }, [store.homeStore.room2SeriesSampleArray])


    useEffect(() => {
        setMode(LayoutAI_App.instance._current_handler_mode);
    }, [store.homeStore.designMode])

    return (
        <div className={styles.root}>
            <div style={{flex: 1}}>
                <div className={styles.rootItem}>
                    <div>{t("房屋使用面积")}</div>
                    <div>{area}m²</div>
                </div>
                <div className={styles.rootItem}>
                    <div>{t('户型')}</div>
                    <div style={{ textAlign: 'end' }}>{bedroomNum}{t('室')} {livingRoomNum}{t('厅')} {toiletNum}{t('卫')}</div>
                </div>
                <div className={styles.rootItem}>
                    <div>{t('户型朝向')}</div>
                    <div><select>
                        <option>{t('未知')}</option>
                        <option>{t('南')}</option>
                        <option>{t('北')}</option>
                        <option>{t('东')}</option>
                        <option>{t('西')}</option>
                        <option>{t('东南')}</option>
                        <option>{t('东北')}</option>
                        <option>{t('西北')}</option>
                        <option>{t('西南')}</option>
                    </select></div>
                </div>
                <div className={styles.rootItem}>
                    <div>{t('当前层高')}</div>
                    <div><input defaultValue={getStoryHeight()} type='number' max="3500" min="2000" step={10} onChange={(ev) => { modifyStoreyHeight(ev.currentTarget.value) }} ></input><span className="unit">mm</span></div>
                </div>
            </div>
            <div style={{marginLeft: 50}}>
                {

                    mode == AI2DesignBasicModes.AiCadMode &&
                    <button
                        className={styles.clearWholeHouse}
                        onClick={ai_layout_for_whole_house}
                    >{t('全屋推荐')}</button>
                }
                {

                    mode == AI2DesignBasicModes.AiCadMode &&
                    <button
                        className={styles.clearWholeHouse}
                        onClick={clearWholeHouse}
                    >{t('清空全屋')}</button>
                }
                {
                    mode == AI2DesignBasicModes.DesignMode &&
                    <MenuList menuList={menuList} showLayoutList={showLayoutList}></MenuList>
                }
            </div>

        </div>
    );
};


export default observer(FloorProperties);
