import { I_StyleSeriesItem } from "./IRoomInterface";

export class TSeriesSample {
    seriesKgId: number|string;
    ruleId: number;
    seedSchemeId: string;

    roomName:string;
    ruleName: string;
    seedSchemeName:string;
    seriesName: string;

    status: number;
    thumbnail: string;
    roomList: any;
    ruleImageList: any;
    ruleComment?: string;
    lightTemplateDataUrl?: string;

    layoutTemplates: any;
    _selected: boolean;
    constructor(data: I_StyleSeriesItem) {
        if (data) {
            this.seriesKgId = data.kgId;
            this.ruleId = data.ruleId;
            this.seedSchemeId = data.seedSchemeId || data.schemeId;

            this.ruleName = data.ruleName;
            this.seedSchemeName = data.schemeName;
            this.roomName = data.name;
            this.seriesName = data.seriesStyle;

            this.status = data.status;
            this.thumbnail = data.thumbnail || "";
            this.roomList = data.roomList;
            this.ruleImageList = data.ruleImageList;

            this.layoutTemplates = data.layoutTemplates;
        }
        this._selected = false;
    }

    get kgId() : string
    {
        return this.seriesKgId as string;
    }
    set kgId(t:number)
    {
        this.seriesKgId = t;
    }
    get name()
    {
        return this.roomName;
    }
    set name(t:string)
    {
        this.roomName = t;
    }

    get seriesStyle()
    {
        return this.seriesName;
    }

    set seriesStyle(t:string)
    {
        this.seriesStyle = t;
    }

    get schemeName()
    {
        return this.seedSchemeName;
    }

    set schemeName(t:string)
    {
        this.seedSchemeName = t;
    }

    exportData()
    {
        return {
            kgId : this.kgId,
            ruleId : this.ruleId,
            ruleName : this.ruleName,
            seriesStyle : this.seriesName,
            seriesKgId : this.seriesKgId,
            status : this.status,
            thumbnail : this.thumbnail,
            name : this.name,
            roomList : [] as any[],
            seedSchemeId:this.seedSchemeId,
            schemeName : this.schemeName

        }
    }

    static fromJsonData(json:any)
    {
        let newInstance = new TSeriesSample(null);
        newInstance.seriesKgId = json.seriesKgId;
        newInstance.ruleId = json.ruleId;
        newInstance.seedSchemeId = json.seedSchemeId
        newInstance.ruleName = json.ruleName;
        newInstance.seedSchemeName = json.seedSchemeName;
        newInstance.roomName = json.roomName;
        newInstance.seriesName = json.seriesName;
        newInstance.status = json.status;
        newInstance.thumbnail = json.thumbnail;
        newInstance.roomList = json.roomList;
        return newInstance;
    }

    toString():string {
        return "SeriesSample  " +
            (this.seriesKgId ? "seriesKgId=" + this.seriesKgId : "") +
            (this.seriesName ? ",seriesName=" + this.seriesName : "") +
            (this.roomName ? ",roomName=" + this.roomName : "") +
            (this.ruleId ? ",ruleId=" + this.ruleId : "") +
            (this.ruleName ? ",ruleName=" + this.ruleName : "") +
            (this.seedSchemeId ? ",seedSchemeId=" + this.seedSchemeId : "") +
            (this.seedSchemeName ? ",seedSchemeName=" + this.seedSchemeName : "");
            // (this.thumbnail ? ",thumbnail=" + this.thumbnail : "")
    }
}
