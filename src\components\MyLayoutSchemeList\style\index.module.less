.mySchemeList {
    z-index: 999; /* 确保蒙层在其他元素之上 */
    position: fixed;
    inset: 0px;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.45);

}

.panel
{
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    // 平板样式
    @media screen and (max-width: 1024px) { // 平板宽度
        width: 700px !important;
    }

    @media screen and (max-width: 768px){
        width: 600px !important;
    }

    // 手机样式
    @media screen and (max-width: 450px) { // 手机宽度
        width: 300px !important;
    }
    .swj-baseComponent-Containersbox-body div
    {
        @media screen and (max-width: 1024px) { // 平板宽度
            height: 450px !important;
        }
    
        // @media screen and (max-width: 768px){
        //     width: 600px !important;
        // }
    
        // // 手机样式
        // @media screen and (max-width: 450px) { // 手机宽度
        //     width: 300px !important;
        // }
    }
}
.schemeListContainer {
    display: flex;
    flex-basis: 270px;
    flex-wrap: wrap;
    justify-content: left;
    padding: 0px 10px;
    border-radius: 5px;
    background-color: #ffffff;
    user-select:text;
    margin-top: 20px;
    overflow-y: scroll;
 }
 .schemeListItemImage
 {
    position: relative;
    background: #fafafa;
    text-align: center;
    img{
        background-color: #f8f8f8;
        height: 200px;
        border-radius: 4px;
    }
    &:hover {

        .itemOvelay
        {
            transition: all .1s;
            position: absolute;
            width: 100%;
            height: 200px;
            background: rgba(0, 0, 0, 0.45);
        }
        .itemOpenButton {
            position: absolute;
            left: 37%;
            top: 42%;
            background: #f2f2f2!important;
            border: none!important;
            box-shadow: none;
            color: #262626!important;
            display: block;
        }
        .itemDeleteButton {
            position: absolute;
            right: 10px;
            bottom: 5px;
            display: block;
            font-size: 20px;
            color: #fff;
            transition: all .1s;
            cursor: pointer;
            &:hover {
                background-color: #eee;
                color: #262626;
                border-radius: 2px;
            }
        }
        .itemOpen3dButton {
            position: absolute;
            right: 40px;
            bottom: 5px;
            display: block;
            font-size: 20px;
            color: #fff;
            transition: all .1s;
            cursor: pointer;
            &:hover {
                background-color: #eee;
                color: #262626;
                border-radius: 2px;
            }
        }
        .itemDreamerButton {
            position: absolute;
            right: 70px;
            bottom: 5px;
            display: block;
            font-size: 20px;
            color: #fff;
            transition: all .1s;
            cursor: pointer;
            &:hover {
                background-color: #eee;
                color: #262626;
                border-radius: 2px;
            }
        }
    }
 }
.schemeItem {
    position: relative;
    height: 240px;
    width: 30%;
    margin: 0px 10px 15px 10px;
    @media screen and (max-width: 768px){
        width: 45% !important;
    }
    @media screen and (max-width: 450px) { // 手机宽度
        width: 92%;
    }
}
.schemeListItemName {
    font-size: 13px;
    font-weight: bold;
    margin: 10px 5px 5px 0px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.schemeListItemText {
    width: 100%;
    color: #8c8c8c;
    display: flex;
    font-size: 12px;
    font-weight: 400;
    justify-content: space-between;
    div{
        overflow: hidden;
        margin-right: 10px;
    }
}
.itemOpenButton {
    display: none;
}
.itemDeleteButton {
    display: none;
}
.itemOpen3dButton {
    display: none;
}
.paginatio_box
{
    bottom: 20px;
    right: 10px;
    position: absolute;
}
.tools_box
{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 0 15px;
}
.tools_box_search
{
    width: 250px;
}

.schemeContainer{
    // height: 100%;
    overflow-y: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .loading{
        position: absolute;
        justify-content: center;
        align-items: center;
    }
}
