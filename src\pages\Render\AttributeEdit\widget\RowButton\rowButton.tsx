import useStyles from './style';
import { Button } from '@svg/antd';

type BtnListObj = {
    type: 'default' | 'primary';
    size: 'large' | 'middle' | 'small';
    text: string,
    id?: Number,
    [key: string]: any
}

/**
 * @description 按钮组件
 * @param btnList 参数
 * @returns
 */
const WidgetItem = (props: any) => {
  const { schema } = props;

  // 核心配置
  const { onClick, btnList } = schema;

  const { styles }: any = useStyles();

  return (
    <div className={styles.root}>
      {btnList.map((item: BtnListObj) => (
        <Button
          className="btn"
          type={item.type}
          size={item.size || 'small'}
          onClick={() => {
            onClick(item);
          }}
        >
          {item.text}
        </Button>
      ))}
    </div>
  );
};

export default WidgetItem;
