import { Vector3 } from "three";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { splitSpaceForLivingRoom } from "../../../TLayoutEntities/utils/SplitSpaceForLivingRoom";
import { TRoom } from "../../../TRoom";
import { TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { TLivingRoomClassfierCheckRule } from "../LivingRoomCheckRules/TLivingRoomClassfierCheckRule";
import { I_CheckRuleOptions } from "../TCheckRule";
import { ZRect } from "@layoutai/z_polygon";

export class TLivingRoomSplitByPathClassfierCheckRule extends TLivingRoomClassfierCheckRule
{
    private _livingSpaceRect: ZRect;

    private _diningSpaceRect: ZRect;

    private _otherSpaceRects: ZRect[];

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
        this._livingSpaceRect = null;
        this._diningSpaceRect = null;
        this._otherSpaceRects = null;
    }

    protected clear()
    {
        super.clear();
        this._livingSpaceRect = null;
        this._diningSpaceRect = null;
        this._otherSpaceRects = null;
    }

    protected processFigure(room: TRoom, figureElements: TFigureElement[]): void
    {
        if(this._livingSpaceRect && this._diningSpaceRect)
        {
            return;
        }
        let splitSpaceInfo = splitSpaceForLivingRoom(room, figureElements);
        if(splitSpaceInfo)
        {
            if(splitSpaceInfo.livingSpace && splitSpaceInfo.livingSpace.length)
            {
                let livingRange: any = splitSpaceInfo.livingSpace[0];
                this._livingSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(livingRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(livingRange));
                let sofaRect: ZRect = figureElements.find(figure => figure.sub_category.includes("沙发") && !figure.sub_category.includes("背景墙"))?.rect;
                if(sofaRect)
                {
                    let oldCenter: Vector3 = this._livingSpaceRect.rect_center.clone();
                    if(Math.abs(sofaRect.nor.dot(this._livingSpaceRect.nor)) < 0.9)
                    {
                        let oldLen: number = this._livingSpaceRect.length;
                        let oldWidth: number = this._livingSpaceRect.depth;
                        this._livingSpaceRect.length = oldWidth;
                        this._livingSpaceRect.depth = oldLen;
                    }
                    this._livingSpaceRect.nor = sofaRect.nor.clone();
                    this._livingSpaceRect.rect_center = oldCenter;
                    this._livingSpaceRect.updateRect();
                }
            }
            if(splitSpaceInfo.diningSpace && splitSpaceInfo.diningSpace.length)
            {
                let diningRange: any = splitSpaceInfo.diningSpace[0];
                this._diningSpaceRect = TBaseRoomToolUtil.instance.getRectByRange2d(diningRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(diningRange));
                let diningTableRect: ZRect = figureElements.find(figure => figure.sub_category.includes("餐桌"))?.rect;
                if(diningTableRect)
                {
                    let oldCenter: Vector3 = this._diningSpaceRect.rect_center.clone();
                    if(Math.abs(diningTableRect.nor.dot(this._diningSpaceRect.nor)) < 0.9)
                    {
                        let oldLen: number = this._diningSpaceRect.length;
                        let oldWidth: number = this._diningSpaceRect.depth;
                        this._diningSpaceRect.length = oldWidth;
                        this._diningSpaceRect.depth = oldLen;
                    }
                    this._diningSpaceRect.nor = diningTableRect.nor.clone();
                    this._diningSpaceRect.rect_center = oldCenter;
                    this._diningSpaceRect.updateRect();
                }
            }
        }
    }

    public get livingSpaceRect(): ZRect
    {
        return this._livingSpaceRect;
    }

    public get diningSpaceRect(): ZRect
    {
        return this._diningSpaceRect;
    }

    public set livingSpaceRect(rect: ZRect)
    {
        this._livingSpaceRect = rect;
    }

    public set diningSpaceRect(rect: ZRect)
    {
        this._diningSpaceRect = rect;
    }

    protected get otherSpaceRects(): ZRect[]
    {
        return this._otherSpaceRects;
    }

    protected decreaseLinearLenAndWidthRatioFunc(value: number, minValue: number, maxValue: number): number
    {
        return Math.ceil((value < 1 ? value : 1) * (minValue - maxValue) + maxValue);
    }
}