import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    titleTag: css`
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.60);
    `,
    submitContainer: css`
      position: fixed;
      left: 50%;
      transform: translate(-50%, 0);
      z-index: 999;
      bottom: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* width: 120px; */
      color: #fff;
    `,
    shijiaoBarContainer: css`
      z-index:999;
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(0, 0, 0, 0.40);
      backdrop-filter: blur(50px);
      border-radius: 20px;
      cursor: pointer;
      .selectInfo {
        z-index: 1;
        position: absolute;
        left: 50%;
        bottom: 50px;
        transform: translate(-50%);
        max-width: 500px;
        overflow-x: scroll;
        transition: height 0.3;
        display: flex;
        scroll-behavior: smooth;
        ::-webkit-scrollbar
        {
          display: none;
        }
        .shijiaoItem
        {
          width: 100%;
          font-size: 16px;
          text-align: center;
          position: relative;
          margin-right: 8px;
          transition: all .3s;
          border-radius: 8px;
          width: 127px;
          height: 95px;
          img{
            width: 122px;
            height: 100%;
            border-radius: 8px;
          }
          .title
          {
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translate(-50%);
            color: #fff;
            font-size: 14px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);
            width: 123px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
          }
        }
        .shijiaoItem:hover
        {
          background-color: #ffffff1a;
          transition: all 0.3;
        }
      }
    `,
    leftArrow: css`
      border-right: 1px solid rgba(255,255,255,.1);
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 45px;
    `,
    rightArrow: css`
      border-left: 1px solid rgba(255,255,255,.1);
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 45px;
    `,
    shijiaoBar: css`
      width: 80px;
      height: 42px;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      @media screen and (max-width: 450px) { // 手机宽度
        width: 100px;
      }
    `,
    submitBtn: css`
      z-index:999;
      background: #2323234d;
      border-radius: 20px;
      cursor: pointer;
      color: #eee;
      font-size: 16px;
      width: 120px;
      height: 42px;
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      align-items: center;
      background: linear-gradient(90deg,#d07bff,#7a5bff);
      .submit
      {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
      }
      @media screen and (max-width: 450px) { // 手机宽度
        width: 120px;
        left: 75%;
      }
    `,
    label: css`
      font-size: 14px;
      color: rgba(255, 255, 255, 0.80);
      font-weight: 600;
      margin-bottom: 15px; 
    `,
    lensContainer: css`
    `,
    submitInfo: css`
      z-index: 99;
      position: fixed;
      left: 12px;
      top: 52px;
      width: 200px;
      height: 90%;
      background: rgba(0, 0, 0, 0.40);
      backdrop-filter: blur(50px);
      border-radius: 8px;
      overflow: hidden;
      transition: height 0.3;
      padding: 16px;
      overflow-x: hidden;
      .ant-tag-checkable-checked 
      {
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
      }
      .ant-tag-checkable-checked:hover
      {
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
      }
      .ant-slider-track 
      {
        background: #9156FF !important;
      }
      .ant-slider .ant-slider-dot-active
      {
        border-color: #9156FF !important;
      }
      .ant-tag:not(.ant-tag-checkable-checked)
      {
        background: rgba(0, 0, 0, 0.40);
        color: #fff;
      }
      .ant-tag{
        width: 76px;
        text-align: center;
        margin-bottom: 8px;
      }
      .ant-slider-mark-text
      {
        color: #ffffffbf;
        font-size: 10px;
      }
    `
  }
});