"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2020],{32020:function(e,a,l){l.r(a),l.d(a,{default:function(){return c}});var t={items_per_page:"/ الصفحة",jump_to:"الذهاب إلى",jump_to_confirm:"تأكيد",page:"الصفحة",prev_page:"الصفحة السابقة",next_page:"الصفحة التالية",prev_5:"خمس صفحات سابقة",next_5:"خمس صفحات تالية",prev_3:"ثلاث صفحات سابقة",next_3:"ثلاث صفحات تالية",page_size:"مقاس الصفحه"},r=l(70989),n=l(44340),o=(0,r.A)((0,r.A)({},n.I),{},{locale:"ar_EG",today:"اليوم",now:"الأن",backToToday:"العودة إلى اليوم",ok:"تأكيد",clear:"مسح",week:"الأسبوع",month:"الشهر",year:"السنة",timeSelect:"اختيار الوقت",dateSelect:"اختيار التاريخ",monthSelect:"اختيار الشهر",yearSelect:"اختيار السنة",decadeSelect:"اختيار العقد",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"الشهر السابق (PageUp)",nextMonth:"الشهر التالى(PageDown)",previousYear:"العام السابق (Control + left)",nextYear:"العام التالى (Control + right)",previousDecade:"العقد السابق",nextDecade:"العقد التالى",previousCentury:"القرن السابق",nextCentury:"القرن التالى"});var i={placeholder:"اختيار الوقت"};const m={lang:Object.assign({placeholder:"اختيار التاريخ",rangePlaceholder:["البداية","النهاية"],yearFormat:"YYYY",dateFormat:"D/M/YYYY",dayFormat:"D",dateTimeFormat:"DD/MM/YYYY HH:mm:ss",monthFormat:"MMMM",monthBeforeYear:!0,shortWeekDays:["الأحد","الإثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"],shortMonths:["يناير","فبراير","مارس","إبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"]},o),timePickerLocale:Object.assign({},i),dateFormat:"DD-MM-YYYY",monthFormat:"MM-YYYY",dateTimeFormat:"DD-MM-YYYY HH:mm:ss",weekFormat:"wo-YYYY"};const s="ليس ${label} من نوع ${type} صالحًا";var c={locale:"ar",Pagination:t,DatePicker:m,TimePicker:i,Calendar:m,global:{placeholder:"يرجى التحديد"},Table:{filterTitle:"الفلاتر",filterConfirm:"تأكيد",filterReset:"إعادة ضبط",selectAll:"اختيار الكل",selectInvert:"إلغاء الاختيار",selectionAll:"حدد جميع البيانات",sortTitle:"رتب",expand:"توسيع الصف",collapse:"طي الصف",triggerDesc:"ترتيب تنازلي",triggerAsc:"ترتيب تصاعدي",cancelSort:"إلغاء الترتيب"},Tour:{Next:"التالي",Previous:"السابق",Finish:"إنهاء"},Modal:{okText:"تأكيد",cancelText:"إلغاء",justOkText:"تأكيد"},Popconfirm:{okText:"تأكيد",cancelText:"إلغاء"},Transfer:{titles:["",""],searchPlaceholder:"ابحث هنا",itemUnit:"عنصر",itemsUnit:"عناصر"},Upload:{uploading:"جاري الرفع...",removeFile:"احذف الملف",uploadError:"مشكلة فى الرفع",previewFile:"استعرض الملف",downloadFile:"تحميل الملف"},Empty:{description:"لا توجد بيانات"},Icon:{icon:"أيقونة"},Text:{edit:"تعديل",copy:"نسخ",copied:"نقل",expand:"وسع"},Form:{defaultValidateMessages:{default:"خطأ في حقل الإدخال ${label}",required:"يرجى إدخال ${label}",enum:"${label} يجب أن يكون واحدا من [${enum}]",whitespace:"${label} لا يمكن أن يكون حرفًا فارغًا",date:{format:"${label} تنسيق التاريخ غير صحيح",parse:"${label} لا يمكن تحويلها إلى تاريخ",invalid:"تاريخ ${label} غير صحيح"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"يجب ${label} ان يكون ${len} أحرف",min:"${label} على الأقل ${min} أحرف",max:"${label} يصل إلى ${max} أحرف",range:"يجب ${label} ان يكون مابين ${min}-${max} أحرف"},number:{len:"${len} ان يساوي ${label} يجب",min:"${min} الأدنى هو ${label} حد",max:"${max} الأقصى هو ${label} حد",range:"${max}-${min} ان يكون مابين ${label} يجب"},array:{len:"يجب أن يكون ${label} طوله ${len}",min:"يجب أن يكون ${label} طوله الأدنى ${min}",max:"يجب أن يكون ${label} طوله الأقصى ${max}",range:"يجب أن يكون ${label} طوله مابين ${min}-${max}"},pattern:{mismatch:"لا يتطابق ${label} مع ${pattern}"}}},Image:{preview:"معاينة"},QRCode:{expired:"انتهت صلاحية رمز الاستجابة السريعة",refresh:"انقر للتحديث",scanned:"تم المسح"},ColorPicker:{presetEmpty:"لا يوجد",transparent:"شفاف",singleColor:"لون واحد",gradientColor:"تدرج لوني"}}}}]);