import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useStore } from "@/models";
import { useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import Icon from "@/components/Icon/icon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";

const ViewSelect: React.FC = () => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const navigate = useNavigate();
    const store = useStore();
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [showSectedInfo, setShowSectedInfo] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const selectInfoRef = useRef<HTMLDivElement>(null)
    const itemInfoRef = useRef<HTMLDivElement[]>([])

    // 添加外部点击检测
    useEffect(() => {
        function handleOutsideInteraction(event: MouseEvent | TouchEvent) {
            const target = (event as TouchEvent).touches 
                ? (event as TouchEvent).target 
                : (event as MouseEvent).target;
                
            if (containerRef.current && !containerRef.current.contains(target as Node)) {
                setShowSectedInfo(false);
            }
        }

        if (showSectedInfo) {
            document.addEventListener('mousedown', handleOutsideInteraction);
            document.addEventListener('touchstart', handleOutsideInteraction, { passive: true });
        }

        return () => {
            document.removeEventListener('mousedown', handleOutsideInteraction);
            document.removeEventListener('touchstart', handleOutsideInteraction);
        };
    }, [showSectedInfo]);

    // 选中对象自动滚动到可视区域
    useEffect(() => {
        if(selectInfoRef.current && itemInfoRef.current[currentIndex]){
            const container = selectInfoRef.current
            const item = itemInfoRef.current[currentIndex]

            const containerRect = container.getBoundingClientRect()
            const itemRect = item.getBoundingClientRect()

            const scrollLeft = itemRect.left - containerRect.left + container.scrollLeft
            const centerOffset = (containerRect.width - itemRect.width) / 2
            container.scrollTo({
                left: scrollLeft - centerOffset,
                behavior: 'smooth'
            })
        }
    }, [currentIndex, showSectedInfo])

    const switchView = (index: number) => {
        setCurrentIndex(index);
        // 只在用户点击时触发 bindViewEntity
        let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D as Scene3D;
        scene3D.active_controls.bindViewEntity(store.homeStore.currentViewCameras[index]);
        scene3D.update();
    };

    return (
        <div className={styles.shijiaoBarContainer} ref={containerRef}>
            <div 
                onClick={() => {
                    setShowSectedInfo(true);
                    if (currentIndex > 0) {
                        switchView(currentIndex - 1);
                    } else {
                        switchView(store.homeStore.currentViewCameras.length - 1);
                    }
                }} 
                className={styles.leftArrow}
            >
                <Icon style={{ color: '#bcb9b9', fontSize: 14 }} iconClass='iconfill_left' />
            </div>

            <div
                className={styles.shijiaoBar}
                onClick={() => {
                    setShowSectedInfo(!showSectedInfo);
                }}
            >
                视角{currentIndex + 1}
            </div>

            <div 
                onClick={() => {
                    setShowSectedInfo(true);
                    if (currentIndex < store.homeStore.currentViewCameras.length - 1) {
                        switchView(currentIndex + 1);
                    } else {
                        switchView(0);
                    }
                }} 
                className={styles.rightArrow}
            >
                <Icon style={{ color: '#bcb9b9', fontSize: 14 }} iconClass='iconfill_right' />
            </div>

            <div
                className='selectInfo'
                ref={selectInfoRef}
                onWheel={(e) => {
                    e.preventDefault();
                    const container = e.currentTarget;
                    const scrollSpeed = 30;
                    container.scrollLeft += e.deltaY * scrollSpeed;
                }}
                style={{ height: showSectedInfo ? 'auto' : 0 }}
            >
                {store.homeStore.currentViewCameras.map((item, index) => (
                    <div
                        className='shijiaoItem'
                        key={index}
                        ref={el => itemInfoRef.current[index] = el as HTMLDivElement}
                        style={{ border: index === currentIndex ? '2px solid #147FFA' : '2px solid #fff' }}
                        onClick={() => {
                            switchView(index);
                        }}
                    >
                        <img src={item._perspective_img.src} alt="" />
                        <div className='title'>视角{index + 1}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};


export default observer(ViewSelect);