export interface ILayoutAIConfigs
{
    /**
     *  素材匹配后是否自动调整吊顶
     */
    needs_adjust_ceiling_after_matching ?: boolean;
    /**
     *  是否默认自动子分区
     */
    default_is_auto_sub_area ?: boolean;
    /**
     *  使用默认数据的时候,是否调用自动布局
     */
    prepare_auto_layout  ?: boolean;


    /**
     *  PC端默认的设备像素比
     */
    default_pc_device_pixel_ratio : number;
    /**
     *  移动端默认的设备像素比
     */
    default_mobile_device_pixel_ratio : number;


    /**
     * 是否在吊顶中绘制筒灯
     */
    drawing_down_lights_in_ceiling ?: boolean;

    /**
     *  是否保存方案到localstorage
     */
    saving_localstorage_layout_scheme ?: boolean;

    update3d_when_xmlscheme_onloaded ?: boolean;

    /**
     *  自动预测房间名: 是否自动预测房间名
     */
    is_auto_predict_roomname ?: boolean;

    is_drawing_ceiling_lines_in_2d ?: boolean;

    is_post_add_main_lights ?: boolean;

    loading_figure_model_timeout ?: number;
    
    /**
     *  更新选中状态的时机:
     */
    mouse_state_to_update_selections ?: "OnMouseDown"|"OnMouseUp";


    isClickDrawPic ?: boolean;

    /**
     *  应用的特定领域
     *     Default : 家装
     *     Hotel : 酒管
     *     Huawei : 暂时用 华为 来指代通用智能家居领域 --- 未来再改
     */
    app_specific_domain ?: "Default"|"Hotel"|"Huawei";
    
    [key:string]:any;
}

/**
 *  全局默认函数
 */
export class LayoutAI_Configs
{
    /**
     *  先默认为空, 
     */
    static getConfigures : ()=>ILayoutAIConfigs = null;

    static get Configs()
    {
        return LayoutAI_Configs.getConfigures();
    } 
}
