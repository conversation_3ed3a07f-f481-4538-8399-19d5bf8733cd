(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4787],{2714:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o;const r=(o=n(8431))&&o.__esModule?o:{default:o};t.default=r,e.exports=r},3764:function(e,t){"use strict";function n(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}function o(e){return n(e)instanceof ShadowRoot}Object.defineProperty(t,"__esModule",{value:!0}),t.getShadowRoot=function(e){return o(e)?n(e):null},t.inShadow=o},8431:function(e,t,n){"use strict";var o=n(43815).default,r=n(35932).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n(32748)),a=o(n(41594)),l=r(n(80004)),c=r(n(11875)),s=function(e,t){return a.createElement(c.default,(0,i.default)({},e,{ref:t,icon:l.default}))},d=a.forwardRef(s);t.default=d},11875:function(e,t,n){"use strict";var o=n(35932).default,r=n(43815).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n(32748)),a=o(n(5809)),l=o(n(40279)),c=o(n(51257)),s=r(n(41594)),d=o(n(97500)),u=n(36467),p=o(n(30722)),f=o(n(89977)),m=n(52222),g=n(74205),h=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,m.setTwoToneColor)(u.blue.primary);var b=s.forwardRef(function(e,t){var n=e.className,o=e.icon,r=e.spin,u=e.rotate,m=e.tabIndex,b=e.onClick,v=e.twoToneColor,y=(0,c.default)(e,h),x=s.useContext(p.default),S=x.prefixCls,C=void 0===S?"anticon":S,A=x.rootClassName,T=(0,d.default)(A,C,(0,l.default)((0,l.default)({},"".concat(C,"-").concat(o.name),!!o.name),"".concat(C,"-spin"),!!r||"loading"===o.name),n),k=m;void 0===k&&b&&(k=-1);var P=u?{msTransform:"rotate(".concat(u,"deg)"),transform:"rotate(".concat(u,"deg)")}:void 0,F=(0,g.normalizeTwoToneColors)(v),w=(0,a.default)(F,2),j=w[0],M=w[1];return s.createElement("span",(0,i.default)({role:"img","aria-label":o.name},y,{ref:t,tabIndex:k,onClick:b,className:T}),s.createElement(f.default,{icon:o,primaryColor:j,secondaryColor:M,style:P}))});b.displayName="AntdIcon",b.getTwoToneColor=m.getTwoToneColor,b.setTwoToneColor=m.setTwoToneColor;t.default=b},30722:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=(0,n(41594).createContext)({});t.default=o},32748:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},33820:function(e,t,n){"use strict";n.r(t),n.d(t,{defaultToken:function(){return l},emptyTheme:function(){return s},hashCode:function(){return c},token:function(){return d},useToken:function(){return u}});var o,r=n(70989),i=n(65071),a=n(51505),l={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff7875",colorInfo:"#1677ff",colorTextBase:"#000",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:4,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,"blue-1":"#e6f4ff","blue-2":"#bae0ff","blue-3":"#91caff","blue-4":"#69b1ff","blue-5":"#4096ff","blue-6":"#1677ff","blue-7":"#0958d9","blue-8":"#003eb3","blue-9":"#002c8c","blue-10":"#001d66","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorText:"rgba(0, 0, 0, 0.88)",colorTextSecondary:"rgba(0, 0, 0, 0.65)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorFill:"rgba(0, 0, 0, 0.15)",colorFillSecondary:"rgba(0, 0, 0, 0.06)",colorFillTertiary:"rgba(0, 0, 0, 0.04)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",colorBgLayout:"hsl(220,23%,97%)",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#95de64",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ffa39e",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffd666",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f4ff",colorInfoBgHover:"#bae0ff",colorInfoBorder:"#91caff",colorInfoBorderHover:"#69b1ff",colorInfoHover:"#69b1ff",colorInfoActive:"#0958d9",colorInfoTextHover:"#4096ff",colorInfoText:"#1677ff",colorInfoTextActive:"#0958d9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorWhite:"#fff",sizeXXL:48,sizeXL:32,sizeLG:24,sizeMD:20,sizeMS:16,size:16,sizeSM:12,sizeXS:8,sizeXXS:4,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],lineWidthBold:2,borderRadiusXS:1,borderRadiusSM:4,borderRadiusLG:8,borderRadiusOuter:4,colorLink:"#1677ff",colorLinkHover:"#69b1ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.06)",colorFillContentHover:"rgba(0, 0, 0, 0.15)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.04)",colorBorderBg:"#ffffff",colorSplit:"rgba(5, 5, 5, 0.06)",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.88)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.45)",colorTextLightSolid:"#fff",colorHighlight:"#ff7875",colorBgTextHover:"rgba(0, 0, 0, 0.06)",colorBgTextActive:"rgba(0, 0, 0, 0.15)",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.88)",colorErrorOutline:"rgba(255, 38, 5, 0.06)",colorWarningOutline:"rgba(255, 215, 5, 0.1)",fontSizeSM:12,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.04)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.15)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"rgba(5, 145, 255, 0.1)",fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:4,paddingXS:8,paddingSM:12,padding:16,paddingMD:20,paddingLG:24,paddingXL:32,paddingContentHorizontalLG:24,paddingContentVerticalLG:16,paddingContentHorizontal:16,paddingContentVertical:12,paddingContentHorizontalSM:16,paddingContentVerticalSM:8,marginXXS:4,marginXS:8,marginSM:12,margin:16,marginMD:20,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",boxShadowSecondary:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowCard:"0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",boxShadowDrawerRight:"-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerLeft:"6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerUp:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerDown:"0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"19w80ff",_hashId:"css-dev-only-do-not-override-i2zu9q"},c=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=3735928559^n,r=1103547991^n,i=0;i<e.length;i++)t=e.charCodeAt(i),o=Math.imul(o^t,2654435761),r=Math.imul(r^t,1597334677);return o=Math.imul(o^o>>>16,2246822507)^Math.imul(r^r>>>13,3266489909),4294967296*(2097151&(r=Math.imul(r^r>>>16,2246822507)^Math.imul(o^o>>>13,3266489909)))+(o>>>0)},s=(0,i.an)(function(e){return e}),d={theme:s,token:(0,r.A)((0,r.A)({},l),null===a.A||void 0===a.A||null===(o=a.A.defaultAlgorithm)||void 0===o?void 0:o.call(a.A,null===a.A||void 0===a.A?void 0:a.A.defaultSeed)),hashId:"pro-".concat(c(JSON.stringify(l)))},u=function(){return d}},41282:function(e,t,n){"use strict";n.d(t,{A:function(){return Z}});var o=n(70989),r=n(97961),i=n(46774),a=n(14290),l=n(9067),c=n(85552),s=n(63974),d=n(41594),u=n.n(d),p={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"},f=n(46778),m=function(e,t){return d.createElement(f.A,(0,s.A)({},e,{ref:t,icon:p}))};var g=d.forwardRef(m),h=n(34669),b=n(81239),v=n(97500),y=n.n(v),x=n(48381),S=function(e){return(0,r.A)({},e.componentCls,{display:"inline-flex",alignItems:"center",maxWidth:"100%","&-icon":{display:"block",marginInlineStart:"4px",cursor:"pointer","&:hover":{color:e.colorPrimary}},"&-title":{display:"inline-flex",flex:"1"},"&-subtitle ":{marginInlineStart:8,color:e.colorTextSecondary,fontWeight:"normal",fontSize:e.fontSize,whiteSpace:"nowrap"},"&-title-ellipsis":{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"keep-all"}})};var C=n(13274),A=u().memo(function(e){var t,n=e.label,i=e.tooltip,a=e.ellipsis,l=e.subTitle,c=(0,(0,d.useContext)(h.Ay.ConfigContext).getPrefixCls)("pro-core-label-tip"),s=(t=c,(0,x.X3)("LabelIconTip",function(e){var n=(0,o.A)((0,o.A)({},e),{},{componentCls:".".concat(t)});return[S(n)]})),p=s.wrapSSR,f=s.hashId;if(!i&&!l)return(0,C.jsx)(C.Fragment,{children:n});var m="string"==typeof i||u().isValidElement(i)?{title:i}:i,v=(null==m?void 0:m.icon)||(0,C.jsx)(g,{});return p((0,C.jsxs)("div",{className:y()(c,f),onMouseDown:function(e){return e.stopPropagation()},onMouseLeave:function(e){return e.stopPropagation()},onMouseMove:function(e){return e.stopPropagation()},children:[(0,C.jsx)("div",{className:y()("".concat(c,"-title"),f,(0,r.A)({},"".concat(c,"-title-ellipsis"),a)),children:n}),l&&(0,C.jsx)("div",{className:"".concat(c,"-subtitle ").concat(f).trim(),children:l}),i&&(0,C.jsx)(b.A,(0,o.A)((0,o.A)({},m),{},{children:(0,C.jsx)("span",{className:"".concat(c,"-icon ").concat(f).trim(),children:v})}))]}))}),T=n(75609),k=n(94857),P=n(50961),F=n(35369),w=function(e){var t=e.componentCls,n=e.antCls;return(0,r.A)({},"".concat(t,"-actions"),(0,r.A)((0,r.A)({marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none",display:"flex",gap:e.marginXS,background:e.colorBgContainer,borderBlockStart:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit),minHeight:42},"& > *",{alignItems:"center",justifyContent:"center",flex:1,display:"flex",cursor:"pointer",color:e.colorTextSecondary,transition:"color 0.3s","&:hover":{color:e.colorPrimaryHover}}),"& > li > div",{flex:1,width:"100%",marginBlock:e.marginSM,marginInline:0,color:e.colorTextSecondary,textAlign:"center",a:{color:e.colorTextSecondary,transition:"color 0.3s","&:hover":{color:e.colorPrimaryHover}},div:(0,r.A)((0,r.A)({position:"relative",display:"block",minWidth:32,fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimaryHover,transition:"color 0.3s"}},"a:not(".concat(n,"-btn),\n            > .anticon"),{display:"inline-block",width:"100%",color:e.colorTextSecondary,lineHeight:"22px",transition:"color 0.3s","&:hover":{color:e.colorPrimaryHover}}),".anticon",{fontSize:e.cardActionIconSize,lineHeight:"22px"}),"&:not(:last-child)":{borderInlineEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)}}))};var j=function(e){var t=e.actions,n=e.prefixCls,r=function(e){return(0,x.X3)("ProCardActions",function(t){var n=(0,o.A)((0,o.A)({},t),{},{componentCls:".".concat(e),cardActionIconSize:16});return[w(n)]})}(n),i=r.wrapSSR,a=r.hashId;return Array.isArray(t)&&null!=t&&t.length?i((0,C.jsx)("ul",{className:y()("".concat(n,"-actions"),a),children:t.map(function(e,o){return(0,C.jsx)("li",{style:{width:"".concat(100/t.length,"%"),padding:0,margin:0},className:y()("".concat(n,"-actions-item"),a),children:e},"action-".concat(o))})})):i((0,C.jsx)("ul",{className:y()("".concat(n,"-actions"),a),children:t}))},M=n(81458),B=n(74352),O=new(n(65071).Mo)("card-loading",{"0%":{backgroundPosition:"0 50%"},"50%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),z=function(e){return(0,r.A)({},e.componentCls,(0,r.A)((0,r.A)({"&-loading":{overflow:"hidden"},"&-loading &-body":{userSelect:"none"}},"".concat(e.componentCls,"-loading-content"),{width:"100%",p:{marginBlock:0,marginInline:0}}),"".concat(e.componentCls,"-loading-block"),{height:"14px",marginBlock:"4px",background:"linear-gradient(90deg, rgba(54, 61, 64, 0.2), rgba(54, 61, 64, 0.4), rgba(54, 61, 64, 0.2))",backgroundSize:"600% 600%",borderRadius:e.borderRadius,animationName:O,animationDuration:"1.4s",animationTimingFunction:"ease",animationIterationCount:"infinite"}))};var E=function(e){var t,n=e.style,r=e.prefix;return(0,(t=r||"ant-pro-card",(0,x.X3)("ProCardLoading",function(e){var n=(0,o.A)((0,o.A)({},e),{},{componentCls:".".concat(t)});return[z(n)]})).wrapSSR)((0,C.jsxs)("div",{className:"".concat(r,"-loading-content"),style:n,children:[(0,C.jsx)(M.A,{gutter:8,children:(0,C.jsx)(B.A,{span:22,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})})}),(0,C.jsxs)(M.A,{gutter:8,children:[(0,C.jsx)(B.A,{span:8,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,C.jsx)(B.A,{span:15,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,C.jsxs)(M.A,{gutter:8,children:[(0,C.jsx)(B.A,{span:6,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,C.jsx)(B.A,{span:18,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,C.jsxs)(M.A,{gutter:8,children:[(0,C.jsx)(B.A,{span:13,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,C.jsx)(B.A,{span:9,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})})]}),(0,C.jsxs)(M.A,{gutter:8,children:[(0,C.jsx)(B.A,{span:4,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,C.jsx)(B.A,{span:3,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})}),(0,C.jsx)(B.A,{span:16,children:(0,C.jsx)("div",{className:"".concat(r,"-loading-block")})})]})]}))},I=n(23906),L=n(62094),R=n(42046),H=(n(68558),["tab","children"]),N=["key","tab","tabKey","disabled","destroyInactiveTabPane","children","className","style","cardProps"];var D=function(e){var t=(0,d.useContext)(h.Ay.ConfigContext).getPrefixCls;if(I.A.startsWith("5"))return(0,C.jsx)(C.Fragment,{});var n=e.key,r=e.tab,i=e.tabKey,a=e.disabled,c=e.destroyInactiveTabPane,s=e.children,u=e.className,p=e.style,f=e.cardProps,m=(0,l.A)(e,N),g=t("pro-card-tabpane"),b=y()(g,u);return(0,C.jsx)(T.A.TabPane,(0,o.A)((0,o.A)({tabKey:i,tab:r,className:b,style:p,disabled:a,destroyInactiveTabPane:c},m),{},{children:(0,C.jsx)(U,(0,o.A)((0,o.A)({},f),{},{children:s}))}),n)},_=function(e){return{backgroundColor:e.controlItemBgActive,borderColor:e.controlOutline}},X=function(e){var t=e.componentCls;return(0,r.A)((0,r.A)((0,r.A)({},t,(0,o.A)((0,o.A)({position:"relative",display:"flex",flexDirection:"column",boxSizing:"border-box",width:"100%",marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,backgroundColor:e.colorBgContainer,borderRadius:e.borderRadius,transition:"all 0.3s"},null===x.dF||void 0===x.dF?void 0:(0,x.dF)(e)),{},(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({"&-box-shadow":{boxShadow:"0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017",borderColor:"transparent"},"&-col":{width:"100%"},"&-border":{border:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)},"&-hoverable":(0,r.A)({cursor:"pointer",transition:"box-shadow 0.3s, border-color 0.3s","&:hover":{borderColor:"transparent",boxShadow:"0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017"}},"&".concat(t,"-checked:hover"),{borderColor:e.controlOutline}),"&-checked":(0,o.A)((0,o.A)({},_(e)),{},{"&::after":{visibility:"visible",position:"absolute",insetBlockStart:2,insetInlineEnd:2,opacity:1,width:0,height:0,border:"6px solid ".concat(e.colorPrimary),borderBlockEnd:"6px solid transparent",borderInlineStart:"6px solid transparent",borderStartEndRadius:2,content:'""'}}),"&:focus":(0,o.A)({},_(e)),"&&-ghost":(0,r.A)({backgroundColor:"transparent"},"> ".concat(t),{"&-header":{paddingInlineEnd:0,paddingBlockEnd:e.padding,paddingInlineStart:0},"&-body":{paddingBlock:0,paddingInline:0,backgroundColor:"transparent"}}),"&&-split > &-body":{paddingBlock:0,paddingInline:0},"&&-contain-card > &-body":{display:"flex"}},"".concat(t,"-body-direction-column"),{flexDirection:"column"}),"".concat(t,"-body-wrap"),{flexWrap:"wrap"}),"&&-collapse",(0,r.A)({},"> ".concat(t),{"&-header":{paddingBlockEnd:e.padding,borderBlockEnd:0},"&-body":{display:"none"}})),"".concat(t,"-header"),{display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:e.paddingLG,paddingBlock:e.padding,paddingBlockEnd:0,"&-border":{"&":{paddingBlockEnd:e.padding},borderBlockEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)},"&-collapsible":{cursor:"pointer"}}),"".concat(t,"-title"),{color:e.colorText,fontWeight:500,fontSize:e.fontSizeLG,lineHeight:e.lineHeight}),"".concat(t,"-extra"),{color:e.colorText}),"".concat(t,"-type-inner"),(0,r.A)({},"".concat(t,"-header"),{backgroundColor:e.colorFillAlter})),"".concat(t,"-collapsible-icon"),{marginInlineEnd:e.marginXS,color:e.colorIconHover,":hover":{color:e.colorPrimaryHover},"& svg":{transition:"transform ".concat(e.motionDurationMid)}}),"".concat(t,"-body"),{display:"block",boxSizing:"border-box",height:"100%",paddingInline:e.paddingLG,paddingBlock:e.padding,"&-center":{display:"flex",alignItems:"center",justifyContent:"center"}}),"&&-size-small",(0,r.A)((0,r.A)({},t,{"&-header":{paddingInline:e.paddingSM,paddingBlock:e.paddingXS,paddingBlockEnd:0,"&-border":{paddingBlockEnd:e.paddingXS}},"&-title":{fontSize:e.fontSize},"&-body":{paddingInline:e.paddingSM,paddingBlock:e.paddingSM}}),"".concat(t,"-header").concat(t,"-header-collapsible"),{paddingBlock:e.paddingXS})))),"".concat(t,"-col"),(0,r.A)((0,r.A)({},"&".concat(t,"-split-vertical"),{borderInlineEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)}),"&".concat(t,"-split-horizontal"),{borderBlockEnd:"".concat(e.lineWidth,"px ").concat(e.lineType," ").concat(e.colorSplit)})),"".concat(t,"-tabs"),(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({},"".concat(e.antCls,"-tabs-top > ").concat(e.antCls,"-tabs-nav"),(0,r.A)({marginBlockEnd:0},"".concat(e.antCls,"-tabs-nav-list"),{marginBlockStart:e.marginXS,paddingInlineStart:e.padding})),"".concat(e.antCls,"-tabs-bottom > ").concat(e.antCls,"-tabs-nav"),(0,r.A)({marginBlockEnd:0},"".concat(e.antCls,"-tabs-nav-list"),{paddingInlineStart:e.padding})),"".concat(e.antCls,"-tabs-left"),(0,r.A)({},"".concat(e.antCls,"-tabs-content-holder"),(0,r.A)({},"".concat(e.antCls,"-tabs-content"),(0,r.A)({},"".concat(e.antCls,"-tabs-tabpane"),{paddingInlineStart:0})))),"".concat(e.antCls,"-tabs-left > ").concat(e.antCls,"-tabs-nav"),(0,r.A)({marginInlineEnd:0},"".concat(e.antCls,"-tabs-nav-list"),{paddingBlockStart:e.padding})),"".concat(e.antCls,"-tabs-right"),(0,r.A)({},"".concat(e.antCls,"-tabs-content-holder"),(0,r.A)({},"".concat(e.antCls,"-tabs-content"),(0,r.A)({},"".concat(e.antCls,"-tabs-tabpane"),{paddingInlineStart:0})))),"".concat(e.antCls,"-tabs-right > ").concat(e.antCls,"-tabs-nav"),(0,r.A)({},"".concat(e.antCls,"-tabs-nav-list"),{paddingBlockStart:e.padding})))},V=function(e){return Array(25).fill(1).map(function(t,n){return function(e,t){var n=t.componentCls;return 0===e?(0,r.A)({},"".concat(n,"-col-0"),{display:"none"}):(0,r.A)({},"".concat(n,"-col-").concat(e),{flexShrink:0,width:"".concat(e/24*100,"%")})}(n,e)})};var W=["className","style","bodyStyle","headStyle","title","subTitle","extra","wrap","layout","loading","gutter","tooltip","split","headerBordered","bordered","boxShadow","children","size","actions","ghost","hoverable","direction","collapsed","collapsible","collapsibleIconRender","colStyle","defaultCollapsed","onCollapse","checked","onChecked","tabs","type"],U=u().forwardRef(function(e,t){var n,s,p,f=e.className,m=e.style,g=e.bodyStyle,b=e.headStyle,v=e.title,S=e.subTitle,w=e.extra,M=e.wrap,B=void 0!==M&&M,O=e.layout,z=e.loading,I=e.gutter,N=void 0===I?0:I,D=e.tooltip,_=e.split,$=e.headerBordered,K=void 0!==$&&$,G=e.bordered,Z=void 0!==G&&G,q=e.boxShadow,Q=void 0!==q&&q,J=e.children,Y=e.size,ee=e.actions,te=e.ghost,ne=void 0!==te&&te,oe=e.hoverable,re=void 0!==oe&&oe,ie=e.direction,ae=e.collapsed,le=e.collapsible,ce=void 0!==le&&le,se=e.collapsibleIconRender,de=e.colStyle,ue=e.defaultCollapsed,pe=void 0!==ue&&ue,fe=e.onCollapse,me=e.checked,ge=e.onChecked,he=e.tabs,be=e.type,ve=(0,l.A)(e,W),ye=(0,d.useContext)(h.Ay.ConfigContext).getPrefixCls,xe=(0,k.A)()||{lg:!0,md:!0,sm:!0,xl:!1,xs:!1,xxl:!1},Se=(0,P.A)(pe,{value:ae,onChange:fe}),Ce=(0,a.A)(Se,2),Ae=Ce[0],Te=Ce[1],ke=["xxl","xl","lg","md","sm","xs"],Pe=function(e,t,n){return e?e.map(function(e){return(0,o.A)((0,o.A)({},e),{},{children:(0,C.jsx)(U,(0,o.A)((0,o.A)({},null==n?void 0:n.cardProps),{},{children:e.children}))})}):((0,R.g9)(!n,"Tabs.TabPane is deprecated. Please use `items` directly."),function(e){return e.filter(function(e){return e})}((0,L.A)(t).map(function(e){if(u().isValidElement(e)){var t=e.key,r=e.props||{},i=r.tab,a=r.children,c=(0,l.A)(r,H);return(0,o.A)((0,o.A)({key:String(t)},c),{},{children:(0,C.jsx)(U,(0,o.A)((0,o.A)({},null==n?void 0:n.cardProps),{},{children:a})),label:i})}return null})))}(null==he?void 0:he.items,J,he),Fe=function(e,t){return e?t:{}},we=ye("pro-card"),je=function(e){return(0,x.X3)("ProCard",function(t){var n=(0,o.A)((0,o.A)({},t),{},{componentCls:".".concat(e)});return[X(n),V(n)]})}(we),Me=je.wrapSSR,Be=je.hashId,Oe=(s=N,p=[0,0],(Array.isArray(s)?s:[s,0]).forEach(function(e,t){if("object"===(0,i.A)(e))for(var n=0;n<ke.length;n+=1){var o=ke[n];if(xe[o]&&void 0!==e[o]){p[t]=e[o];break}}else p[t]=e||0}),p),ze=(0,a.A)(Oe,2),Ee=ze[0],Ie=ze[1],Le=!1,Re=u().Children.toArray(J),He=Re.map(function(e,t){var n;if(null!=e&&null!==(n=e.type)&&void 0!==n&&n.isProCard){Le=!0;var a=function(e){var t=e;if("object"===(0,i.A)(e))for(var n=0;n<ke.length;n+=1){var o=ke[n];if(null!=xe&&xe[o]&&void 0!==(null==e?void 0:e[o])){t=e[o];break}}return{span:t,colSpanStyle:Fe("string"==typeof t&&/\d%|\dpx/i.test(t),{width:t,flexShrink:0})}}(e.props.colSpan),l=a.span,c=a.colSpanStyle,s=y()(["".concat(we,"-col")],Be,(0,r.A)((0,r.A)((0,r.A)({},"".concat(we,"-split-vertical"),"vertical"===_&&t!==Re.length-1),"".concat(we,"-split-horizontal"),"horizontal"===_&&t!==Re.length-1),"".concat(we,"-col-").concat(l),"number"==typeof l&&l>=0&&l<=24)),d=Me((0,C.jsx)("div",{style:(0,o.A)((0,o.A)((0,o.A)((0,o.A)({},c),Fe(Ee>0,{paddingInlineEnd:Ee/2,paddingInlineStart:Ee/2})),Fe(Ie>0,{paddingBlockStart:Ie/2,paddingBlockEnd:Ie/2})),de),className:s,children:u().cloneElement(e)}));return u().cloneElement(d,{key:"pro-card-col-".concat((null==e?void 0:e.key)||t)})}return e}),Ne=y()("".concat(we),f,Be,(n={},(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)(n,"".concat(we,"-border"),Z),"".concat(we,"-box-shadow"),Q),"".concat(we,"-contain-card"),Le),"".concat(we,"-loading"),z),"".concat(we,"-split"),"vertical"===_||"horizontal"===_),"".concat(we,"-ghost"),ne),"".concat(we,"-hoverable"),re),"".concat(we,"-size-").concat(Y),Y),"".concat(we,"-type-").concat(be),be),"".concat(we,"-collapse"),Ae),(0,r.A)(n,"".concat(we,"-checked"),me))),De=y()("".concat(we,"-body"),Be,(0,r.A)((0,r.A)((0,r.A)({},"".concat(we,"-body-center"),"center"===O),"".concat(we,"-body-direction-column"),"horizontal"===_||"column"===ie),"".concat(we,"-body-wrap"),B&&Le)),_e=g,Xe=u().isValidElement(z)?z:(0,C.jsx)(E,{prefix:we,style:0===(null==g?void 0:g.padding)||"0px"===(null==g?void 0:g.padding)?{padding:24}:void 0}),Ve=ce&&void 0===ae&&(se?se({collapsed:Ae}):(0,C.jsx)(c.A,{onClick:function(){"icon"===ce&&Te(!Ae)},rotate:Ae?void 0:90,className:"".concat(we,"-collapsible-icon ").concat(Be).trim()}));return Me((0,C.jsxs)("div",(0,o.A)((0,o.A)({className:Ne,style:m,ref:t,onClick:function(e){var t;null==ge||ge(e),null==ve||null===(t=ve.onClick)||void 0===t||t.call(ve,e)}},(0,F.A)(ve,["prefixCls","colSpan"])),{},{children:[(v||w||Ve)&&(0,C.jsxs)("div",{className:y()("".concat(we,"-header"),Be,(0,r.A)((0,r.A)({},"".concat(we,"-header-border"),K||"inner"===be),"".concat(we,"-header-collapsible"),Ve)),style:b,onClick:function(){"header"!==ce&&!0!==ce||Te(!Ae)},children:[(0,C.jsxs)("div",{className:"".concat(we,"-title ").concat(Be).trim(),children:[Ve,(0,C.jsx)(A,{label:v,tooltip:D,subTitle:S})]}),w&&(0,C.jsx)("div",{className:"".concat(we,"-extra ").concat(Be).trim(),onClick:function(e){return e.stopPropagation()},children:w})]}),he?(0,C.jsx)("div",{className:"".concat(we,"-tabs ").concat(Be).trim(),children:(0,C.jsx)(T.A,(0,o.A)((0,o.A)({onChange:he.onChange},(0,F.A)(he,["cardProps"])),{},{items:Pe,children:z?Xe:J}))}):(0,C.jsx)("div",{className:De,style:_e,children:z?Xe:He}),ee?(0,C.jsx)(j,{actions:ee,prefixCls:we}):null]})))}),$=function(e){var t=e.componentCls;return(0,r.A)({},t,{"&-divider":{flex:"none",width:e.lineWidth,marginInline:e.marginXS,marginBlock:e.marginLG,backgroundColor:e.colorSplit,"&-horizontal":{width:"initial",height:e.lineWidth,marginInline:e.marginLG,marginBlock:e.marginXS}},"&&-size-small &-divider":{marginBlock:e.marginLG,marginInline:e.marginXS,"&-horizontal":{marginBlock:e.marginXS,marginInline:e.marginLG}}})};var K=function(e){var t=(0,(0,d.useContext)(h.Ay.ConfigContext).getPrefixCls)("pro-card"),n="".concat(t,"-divider"),i=function(e){return(0,x.X3)("ProCardDivider",function(t){var n=(0,o.A)((0,o.A)({},t),{},{componentCls:".".concat(e)});return[$(n)]})}(t),a=i.wrapSSR,l=i.hashId,c=e.className,s=e.style,u=void 0===s?{}:s,p=e.type,f=y()(n,c,l,(0,r.A)({},"".concat(n,"-").concat(p),p));return a((0,C.jsx)("div",{className:f,style:u}))},G=U;G.isProCard=!0,G.Divider=K,G.TabPane=D,G.Group=function(e){return(0,C.jsx)(U,(0,o.A)({bodyStyle:{padding:0}},e))};var Z=G},43815:function(e,t,n){var o=n(9952).default;function r(t,n){if("function"==typeof WeakMap)var i=new WeakMap,a=new WeakMap;return(e.exports=r=function(e,t){if(!t&&e&&e.__esModule)return e;var n,r,l={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return l;if(n=t?a:i){if(n.has(e))return n.get(e);n.set(e,l)}for(var c in e)"default"!==c&&{}.hasOwnProperty.call(e,c)&&((r=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,c))&&(r.get||r.set)?n(l,c,r):l[c]=e[c]);return l},e.exports.__esModule=!0,e.exports.default=e.exports)(t,n)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports},44404:function(e,t,n){"use strict";n.d(t,{LN:function(){return st}});var o=n(97961),r=n(9067),i=n(70989),a=n(46774),l=n(68496),c=n(75609),s=n(23906),d=n(34669),u=n(41594),p=n.n(u),f=n(97500),m=n.n(f),g=n(55074),h=n(24174),b=n(8607);var v=function(e){let t;const n=function(){if(null==t){for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];t=(0,b.A)((n=>()=>{t=null,e.apply(void 0,(0,h.A)(n))})(o))}};return n.cancel=()=>{b.A.cancel(t),t=null},n},y=n(77553),x=n(36576);var S=(0,x.OF)("Affix",e=>{const{componentCls:t}=e;return{[t]:{position:"fixed",zIndex:e.zIndexPopup}}},e=>({zIndexPopup:e.zIndexBase+10}));function C(e){return e!==window?e.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function A(e,t,n){if(void 0!==n&&Math.round(t.top)>Math.round(e.top)-n)return n+t.top}function T(e,t,n){if(void 0!==n&&Math.round(t.bottom)<Math.round(e.bottom)+n){return n+(window.innerHeight-t.bottom)}}var k=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const P=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function F(){return"undefined"!=typeof window?window:null}const w=p().forwardRef((e,t)=>{var n;const{style:o,offsetTop:r,offsetBottom:i,prefixCls:a,className:l,rootClassName:c,children:s,target:d,onChange:u,onTestUpdatePosition:f}=e,h=k(e,["style","offsetTop","offsetBottom","prefixCls","className","rootClassName","children","target","onChange","onTestUpdatePosition"]),{getPrefixCls:b,getTargetContainer:x}=p().useContext(y.QO),w=b("affix",a),[j,M]=p().useState(!1),[B,O]=p().useState(),[z,E]=p().useState(),I=p().useRef(0),L=p().useRef(null),R=p().useRef(null),H=p().useRef(null),N=p().useRef(null),D=p().useRef(null),_=null!==(n=null!=d?d:x)&&void 0!==n?n:F,X=void 0===i&&void 0===r?0:r,V=()=>{I.current=1,(()=>{if(1!==I.current||!N.current||!H.current||!_)return;const e=_();if(e){const t={status:0},n=C(H.current);if(0===n.top&&0===n.left&&0===n.width&&0===n.height)return;const o=C(e),r=A(n,o,X),a=T(n,o,i);void 0!==r?(t.affixStyle={position:"fixed",top:r,width:n.width,height:n.height},t.placeholderStyle={width:n.width,height:n.height}):void 0!==a&&(t.affixStyle={position:"fixed",bottom:a,width:n.width,height:n.height},t.placeholderStyle={width:n.width,height:n.height}),t.lastAffix=!!t.affixStyle,j!==t.lastAffix&&(null==u||u(t.lastAffix)),I.current=t.status,O(t.affixStyle),E(t.placeholderStyle),M(t.lastAffix)}})()},W=v(()=>{V()}),U=v(()=>{if(_&&B){const e=_();if(e&&H.current){const t=C(e),n=C(H.current),o=A(n,t,X),r=T(n,t,i);if(void 0!==o&&B.top===o||void 0!==r&&B.bottom===r)return}}V()}),$=()=>{const e=null==_?void 0:_();e&&(P.forEach(t=>{var n;R.current&&(null===(n=L.current)||void 0===n||n.removeEventListener(t,R.current)),null==e||e.addEventListener(t,U)}),L.current=e,R.current=U)};p().useImperativeHandle(t,()=>({updatePosition:W})),p().useEffect(()=>(D.current=setTimeout($),()=>(()=>{D.current&&(clearTimeout(D.current),D.current=null);const e=null==_?void 0:_();P.forEach(t=>{var n;null==e||e.removeEventListener(t,U),R.current&&(null===(n=L.current)||void 0===n||n.removeEventListener(t,R.current))}),W.cancel(),U.cancel()})()),[]),p().useEffect(()=>{$()},[d,B]),p().useEffect(()=>{W()},[d,r,i]);const[K,G,Z]=S(w),q=m()(c,G,w,Z),Q=m()({[q]:B});return K(p().createElement(g.A,{onResize:W},p().createElement("div",Object.assign({style:o,className:l,ref:H},h),B&&p().createElement("div",{style:z,"aria-hidden":"true"}),p().createElement("div",{className:Q,ref:N,style:B},p().createElement(g.A,{onResize:W},s)))))});var j=w,M=(0,u.createContext)({}),B=n(68558),O=void 0!==B&&null!=B.versions&&null!=B.versions.node,z=n(35369),E=n(75206),I=n(48381),L=function(e){return(0,o.A)({},e.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,I.X9)(e.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(e.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:e.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:e.colorText},"&-right":{color:e.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};var R=n(13274),H=["children","className","extra","portalDom","style","renderContent"],N=function(e){var t=e.children,n=e.className,a=e.extra,l=e.portalDom,c=void 0===l||l,s=e.style,f=e.renderContent,g=(0,r.A)(e,H),h=(0,u.useContext)(d.Ay.ConfigContext),b=h.getPrefixCls,v=h.getTargetContainer,y=e.prefixCls||b("pro"),x="".concat(y,"-footer-bar"),S=function(e){return(0,I.X3)("ProLayoutFooterToolbar",function(t){var n=(0,i.A)((0,i.A)({},t),{},{componentCls:".".concat(e)});return[L(n)]})}(x),C=S.wrapSSR,A=S.hashId,T=(0,u.useContext)(M),k=(0,u.useMemo)(function(){var e=T.hasSiderMenu,t=T.isMobile,n=T.siderWidth;if(e)return n?t?"100%":"calc(100% - ".concat(n,"px)"):"100%"},[T.collapsed,T.hasSiderMenu,T.isMobile,T.siderWidth]),P=(0,u.useMemo)(function(){return"undefined"==typeof window||"undefined"==typeof document?null:(null==v?void 0:v())||document.body},[]),F=function(e,t){var n=t.stylish;return(0,I.X3)("ProLayoutFooterToolbarStylish",function(t){var r=(0,i.A)((0,i.A)({},t),{},{componentCls:".".concat(e)});return n?[(0,o.A)({},"".concat(r.componentCls),null==n?void 0:n(r))]:[]})}("".concat(x,".").concat(x,"-stylish"),{stylish:e.stylish}),w=(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)("div",{className:"".concat(x,"-left ").concat(A).trim(),children:a}),(0,R.jsx)("div",{className:"".concat(x,"-right ").concat(A).trim(),children:t})]});(0,u.useEffect)(function(){return T&&null!=T&&T.setHasFooterToolbar?(null==T||T.setHasFooterToolbar(!0),function(){var e;null==T||null===(e=T.setHasFooterToolbar)||void 0===e||e.call(T,!1)}):function(){}},[]);var j=(0,R.jsx)("div",(0,i.A)((0,i.A)({className:m()(n,A,x,(0,o.A)({},"".concat(x,"-stylish"),!!e.stylish)),style:(0,i.A)({width:k},s)},(0,z.A)(g,["prefixCls"])),{},{children:f?f((0,i.A)((0,i.A)((0,i.A)({},e),T),{},{leftWidth:k}),w):w})),B="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.matchMedia&&!O&&c&&P?(0,E.createPortal)(j,P,x):j;return F.wrapSSR(C((0,R.jsx)(p().Fragment,{children:B},x)))},D=function(e){return(0,o.A)({},e.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};var _=function(e){var t=(0,u.useContext)(M),n=e.children,r=e.contentWidth,a=e.className,l=e.style,c=(0,u.useContext)(d.Ay.ConfigContext).getPrefixCls,s=e.prefixCls||c("pro"),p=r||t.contentWidth,f="".concat(s,"-grid-content"),g=function(e){return(0,I.X3)("ProLayoutGridContent",function(t){var n=(0,i.A)((0,i.A)({},t),{},{componentCls:".".concat(e)});return[D(n)]})}(f),h=g.wrapSSR,b=g.hashId,v="Fixed"===p&&"top"===t.layout;return h((0,R.jsx)("div",{className:m()(f,b,a,(0,o.A)({},"".concat(f,"-wide"),v)),style:l,children:(0,R.jsx)("div",{className:"".concat(s,"-grid-content-children ").concat(b).trim(),children:n})}))},X=n(14290),V=n(2714),W=n.n(V),U=n(56685),$=n.n(U),K=n(62094),G=n(74893),Z=n(88472),q=n(16748),Q=n(84380);const J=e=>{let{children:t}=e;const{getPrefixCls:n}=u.useContext(y.QO),o=n("breadcrumb");return u.createElement("li",{className:`${o}-separator`,"aria-hidden":"true"},""===t?t:t||"/")};J.__ANT_BREADCRUMB_SEPARATOR=!0;var Y=J,ee=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function te(e,t,n,o){if(null==n)return null;const{className:r,onClick:i}=t,a=ee(t,["className","onClick"]),l=Object.assign(Object.assign({},(0,G.A)(a,{data:!0,aria:!0})),{onClick:i});return void 0!==o?u.createElement("a",Object.assign({},l,{className:m()(`${e}-link`,r),href:o}),n):u.createElement("span",Object.assign({},l,{className:m()(`${e}-link`,r)}),n)}function ne(e,t){return(n,o,r,i,a)=>{if(t)return t(n,o,r,i);const l=function(e,t){if(void 0===e.title||null===e.title)return null;const n=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(new RegExp(`:(${n})`,"g"),(e,n)=>t[n]||e)}(n,o);return te(e,n,l,a)}}var oe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const re=e=>{const{prefixCls:t,separator:n="/",children:o,menu:r,overlay:i,dropdownProps:a,href:l}=e;const c=(e=>{if(r||i){const n=Object.assign({},a);if(r){const e=r||{},{items:t}=e,o=oe(e,["items"]);n.menu=Object.assign(Object.assign({},o),{items:null==t?void 0:t.map((e,t)=>{var{key:n,title:o,label:r,path:i}=e,a=oe(e,["key","title","label","path"]);let c=null!=r?r:o;return i&&(c=u.createElement("a",{href:`${l}${i}`},c)),Object.assign(Object.assign({},a),{key:null!=n?n:t,label:c})})})}else i&&(n.overlay=i);return u.createElement(Q.A,Object.assign({placement:"bottom"},n),u.createElement("span",{className:`${t}-overlay-link`},e,u.createElement(q.A,null)))}return e})(o);return null!=c?u.createElement(u.Fragment,null,u.createElement("li",null,c),n&&u.createElement(Y,null,n)):null},ie=e=>{const{prefixCls:t,children:n,href:o}=e,r=oe(e,["prefixCls","children","href"]),{getPrefixCls:i}=u.useContext(y.QO),a=i("breadcrumb",t);return u.createElement(re,Object.assign({},r,{prefixCls:a}),te(a,r,n,o))};ie.__ANT_BREADCRUMB_ITEM=!0;var ae=ie,le=n(65071),ce=n(56307),se=n(15391);var de=(0,x.OF)("Breadcrumb",e=>(e=>{const{componentCls:t,iconCls:n,calc:o}=e;return{[t]:Object.assign(Object.assign({},(0,ce.dF)(e)),{color:e.itemColor,fontSize:e.fontSize,[n]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,le.zA)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:o(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,ce.K8)(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`\n          > ${n} + span,\n          > ${n} + a\n        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,le.zA)(e.paddingXXS)}`,marginInline:o(e.marginXXS).mul(-1).equal(),[`> ${n}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}})((0,se.oX)(e,{})),e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS})),ue=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function pe(e){const{breadcrumbName:t,children:n}=e,o=ue(e,["breadcrumbName","children"]),r=Object.assign({title:t},o);return n&&(r.menu={items:n.map(e=>{var{breadcrumbName:t}=e,n=ue(e,["breadcrumbName"]);return Object.assign(Object.assign({},n),{title:t})})}),r}var fe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const me=e=>{const{prefixCls:t,separator:n="/",style:o,className:r,rootClassName:i,routes:a,items:l,children:c,itemRender:s,params:d={}}=e,p=fe(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:f,direction:g,breadcrumb:h}=u.useContext(y.QO);let b;const v=f("breadcrumb",t),[x,S,C]=de(v),A=function(e,t){return(0,u.useMemo)(()=>e||(t?t.map(pe):null),[e,t])}(l,a);const T=ne(v,s);if(A&&A.length>0){const e=[],t=l||a;b=A.map((o,r)=>{const{path:i,key:a,type:l,menu:c,overlay:s,onClick:p,className:f,separator:m,dropdownProps:g}=o,h=((e,t)=>{if(void 0===t)return t;let n=(t||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{n=n.replace(`:${t}`,e[t])}),n})(d,i);void 0!==h&&e.push(h);const b=null!=a?a:r;if("separator"===l)return u.createElement(Y,{key:b},m);const y={},x=r===A.length-1;c?y.menu=c:s&&(y.overlay=s);let{href:S}=o;return e.length&&void 0!==h&&(S=`#/${e.join("/")}`),u.createElement(re,Object.assign({key:b},y,(0,G.A)(o,{data:!0,aria:!0}),{className:f,dropdownProps:g,href:S,separator:x?"":n,onClick:p,prefixCls:v}),T(o,d,t,e,S))})}else if(c){const e=(0,K.A)(c).length;b=(0,K.A)(c).map((t,o)=>{if(!t)return t;const r=o===e-1;return(0,Z.Ob)(t,{separator:r?"":n,key:o})})}const k=m()(v,null==h?void 0:h.className,{[`${v}-rtl`]:"rtl"===g},r,i,S,C),P=Object.assign(Object.assign({},null==h?void 0:h.style),o);return x(u.createElement("nav",Object.assign({className:k,style:P},p),u.createElement("ol",null,b)))};me.Item=ae,me.Separator=Y;var ge=me,he=n(87883),be=n(46947),ve=n(16964),ye=n(28659),xe=n(94857);var Se=u.createContext({});const Ce=e=>{const{antCls:t,componentCls:n,iconCls:o,avatarBg:r,avatarColor:i,containerSize:a,containerSizeLG:l,containerSizeSM:c,textFontSize:s,textFontSizeLG:d,textFontSizeSM:u,borderRadius:p,borderRadiusLG:f,borderRadiusSM:m,lineWidth:g,lineType:h}=e,b=(e,t,r)=>({width:e,height:e,borderRadius:"50%",[`&${n}-square`]:{borderRadius:r},[`&${n}-icon`]:{fontSize:t,[`> ${o}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,ce.dF)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${(0,le.zA)(g)} ${h} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),b(a,s,p)),{"&-lg":Object.assign({},b(l,d,f)),"&-sm":Object.assign({},b(c,u,m)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Ae=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:o,groupSpace:r}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:o}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:r}}}};var Te=(0,x.OF)("Avatar",e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,o=(0,se.oX)(e,{avatarBg:n,avatarColor:t});return[Ce(o),Ae(o)]},e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:o,fontSize:r,fontSizeLG:i,fontSizeXL:a,fontSizeHeading3:l,marginXS:c,marginXXS:s,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:o,textFontSize:Math.round((i+a)/2),textFontSizeLG:l,textFontSizeSM:r,groupSpace:s,groupOverlapping:-c,groupBorderColor:d}}),ke=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const Pe=(e,t)=>{const[n,o]=u.useState(1),[r,i]=u.useState(!1),[a,l]=u.useState(!0),c=u.useRef(null),s=u.useRef(null),d=(0,he.K4)(t,c),{getPrefixCls:p,avatar:f}=u.useContext(y.QO),h=u.useContext(Se),b=()=>{if(!s.current||!c.current)return;const t=s.current.offsetWidth,n=c.current.offsetWidth;if(0!==t&&0!==n){const{gap:r=4}=e;2*r<n&&o(n-2*r<t?(n-2*r)/t:1)}};u.useEffect(()=>{i(!0)},[]),u.useEffect(()=>{l(!0),o(1)},[e.src]),u.useEffect(b,[e.gap]);const v=()=>{const{onError:t}=e;!1!==(null==t?void 0:t())&&l(!1)},{prefixCls:x,shape:S,size:C,src:A,srcSet:T,icon:k,className:P,rootClassName:F,alt:w,draggable:j,children:M,crossOrigin:B}=e,O=ke(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),z=(0,ye.A)(e=>{var t,n;return null!==(n=null!==(t=null!=C?C:null==h?void 0:h.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"}),E=Object.keys("object"==typeof z&&z||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),I=(0,xe.A)(E),L=u.useMemo(()=>{if("object"!=typeof z)return{};const e=be.ye.find(e=>I[e]),t=z[e];return t?{width:t,height:t,fontSize:t&&(k||M)?t/2:18}:{}},[I,z]);const R=p("avatar",x),H=(0,ve.A)(R),[N,D,_]=Te(R,H),X=m()({[`${R}-lg`]:"large"===z,[`${R}-sm`]:"small"===z}),V=u.isValidElement(A),W=S||(null==h?void 0:h.shape)||"circle",U=m()(R,X,null==f?void 0:f.className,`${R}-${W}`,{[`${R}-image`]:V||A&&a,[`${R}-icon`]:!!k},_,H,P,F,D),$="number"==typeof z?{width:z,height:z,fontSize:k?z/2:18}:{};let K;if("string"==typeof A&&a)K=u.createElement("img",{src:A,draggable:j,srcSet:T,onError:v,alt:w,crossOrigin:B});else if(V)K=A;else if(k)K=k;else if(r||1!==n){const e=`scale(${n})`,t={msTransform:e,WebkitTransform:e,transform:e};K=u.createElement(g.A,{onResize:b},u.createElement("span",{className:`${R}-string`,ref:s,style:Object.assign({},t)},M))}else K=u.createElement("span",{className:`${R}-string`,style:{opacity:0},ref:s},M);return delete O.onError,delete O.gap,N(u.createElement("span",Object.assign({},O,{style:Object.assign(Object.assign(Object.assign(Object.assign({},$),L),null==f?void 0:f.style),O.style),className:U,ref:d}),K))};var Fe=u.forwardRef(Pe),we=n(96671);const je=e=>{const{size:t,shape:n}=u.useContext(Se),o=u.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return u.createElement(Se.Provider,{value:o},e.children)};var Me=e=>{var t,n,o,r;const{getPrefixCls:i,direction:a}=u.useContext(y.QO),{prefixCls:l,className:c,rootClassName:s,style:d,maxCount:p,maxStyle:f,size:g,shape:h,maxPopoverPlacement:b,maxPopoverTrigger:v,children:x,max:S}=e;const C=i("avatar",l),A=`${C}-group`,T=(0,ve.A)(C),[k,P,F]=Te(C,T),w=m()(A,{[`${A}-rtl`]:"rtl"===a},F,T,c,s,P),j=(0,K.A)(x).map((e,t)=>(0,Z.Ob)(e,{key:`avatar-key-${t}`})),M=(null==S?void 0:S.count)||p,B=j.length;if(M&&M<B){const e=j.slice(0,M),i=j.slice(M,B),a=(null==S?void 0:S.style)||f,l=(null===(t=null==S?void 0:S.popover)||void 0===t?void 0:t.trigger)||v||"hover",c=(null===(n=null==S?void 0:S.popover)||void 0===n?void 0:n.placement)||b||"top",s=Object.assign(Object.assign({content:i},null==S?void 0:S.popover),{classNames:{root:m()(`${A}-popover`,null===(r=null===(o=null==S?void 0:S.popover)||void 0===o?void 0:o.classNames)||void 0===r?void 0:r.root)},placement:c,trigger:l});return e.push(u.createElement(we.A,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},s),u.createElement(Fe,{style:a},"+"+(B-M)))),k(u.createElement(je,{shape:h,size:g},u.createElement("div",{className:w,style:d},e)))}return k(u.createElement(je,{shape:h,size:g},u.createElement("div",{className:w,style:d},j)))};const Be=Fe;Be.Group=Me;var Oe=Be,ze=n(21524),Ee=n(42046),Ie=function(e){var t;return(0,o.A)({},e.componentCls,(0,i.A)((0,i.A)({},null===I.dF||void 0===I.dF?void 0:(0,I.dF)(e)),{},(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({position:"relative",backgroundColor:e.colorWhite,paddingBlock:e.pageHeaderPaddingVertical+2,paddingInline:e.pageHeaderPadding,"&&-ghost":{backgroundColor:e.pageHeaderBgGhost},"&-no-children":{height:null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:e.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,o.A)({marginInlineEnd:e.margin,fontSize:16,lineHeight:1,"&-button":(0,i.A)((0,i.A)({fontSize:16},null===I.Y1||void 0===I.Y1?void 0:(0,I.Y1)(e)),{},{color:e.pageHeaderColorBack,cursor:"pointer"})},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:e.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:e.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,i.A)((0,i.A)({marginInlineEnd:e.marginSM,marginBlockEnd:0,color:e.colorTextHeading,fontWeight:600,fontSize:e.pageHeaderFontSizeHeaderTitle,lineHeight:e.controlHeight+"px"},{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),{},(0,o.A)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:e.marginSM})),"&-avatar":(0,o.A)({marginInlineEnd:e.marginSM},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:e.marginSM}),"&-tags":(0,o.A)({},"".concat(e.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,i.A)((0,i.A)({marginInlineEnd:e.marginSM,color:e.colorTextSecondary,fontSize:e.pageHeaderFontSizeHeaderSubTitle,lineHeight:e.lineHeight},{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),{},(0,o.A)({},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,o.A)((0,o.A)({marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,o.A)({"white-space":"unset"},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:e.marginSM,marginInlineStart:0})},"".concat(e.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,o.A)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:e.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:e.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};var Le=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=arguments.length>3?arguments[3]:void 0,r=t.title,a=t.avatar,l=t.subTitle,c=t.tags,s=t.extra,d=t.onBack,u="".concat(e,"-heading"),p=r||l||c||s;if(!p)return null;var f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ltr";return void 0!==e.backIcon?e.backIcon:"rtl"===t?(0,R.jsx)($(),{}):(0,R.jsx)(W(),{})}(t,n),g=function(e,t,n,o){return n&&o?(0,R.jsx)("div",{className:"".concat(e,"-back ").concat(t).trim(),children:(0,R.jsx)("div",{role:"button",onClick:function(e){null==o||o(e)},className:"".concat(e,"-back-button ").concat(t).trim(),"aria-label":"back",children:n})}):null}(e,o,f,d),h=g||a||p;return(0,R.jsxs)("div",{className:u+" "+o,children:[h&&(0,R.jsxs)("div",{className:"".concat(u,"-left ").concat(o).trim(),children:[g,a&&(0,R.jsx)(Oe,(0,i.A)({className:m()("".concat(u,"-avatar"),o,a.className)},a)),r&&(0,R.jsx)("span",{className:"".concat(u,"-title ").concat(o).trim(),title:"string"==typeof r?r:void 0,children:r}),l&&(0,R.jsx)("span",{className:"".concat(u,"-sub-title ").concat(o).trim(),title:"string"==typeof l?l:void 0,children:l}),c&&(0,R.jsx)("span",{className:"".concat(u,"-tags ").concat(o).trim(),children:c})]}),s&&(0,R.jsx)("span",{className:"".concat(u,"-extra ").concat(o).trim(),children:(0,R.jsx)(ze.A,{children:s})})]})},Re=function e(t){return null==t?void 0:t.map(function(t){var n;return(0,Ee.g9)(!!t.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,i.A)((0,i.A)({},t),{},{breadcrumbName:void 0,children:void 0,title:t.title||t.breadcrumbName},null!==(n=t.children)&&void 0!==n&&n.length?{menu:{items:e(t.children)}}:{})})},He=function(e){var t,n=u.useState(!1),r=(0,X.A)(n,2),a=r[0],l=r[1],c=u.useContext(d.Ay.ConfigContext),s=c.getPrefixCls,p=c.direction,f=e.prefixCls,h=e.style,b=e.footer,v=e.children,y=e.breadcrumb,x=e.breadcrumbRender,S=e.className,C=e.contentWidth,A=e.layout,T=e.ghost,k=void 0===T||T,P=s("page-header",f),F=function(e){return(0,I.X3)("ProLayoutPageHeader",function(t){var n=(0,i.A)((0,i.A)({},t),{},{componentCls:".".concat(e),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:t.paddingSM,pageHeaderColorBack:t.colorTextHeading,pageHeaderFontSizeHeaderTitle:t.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:t.paddingSM});return[Ie(n)]})}(P),w=F.wrapSSR,j=F.hashId,M=(!y||null!=y&&y.items||null==y||!y.routes||((0,Ee.g9)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),y.items=Re(y.routes)),null!=y&&y.items?function(e,t){var n;return null!==(n=e.items)&&void 0!==n&&n.length?(0,R.jsx)(ge,(0,i.A)((0,i.A)({},e),{},{className:m()("".concat(t,"-breadcrumb"),e.className)})):null}(y,P):null),B=y&&"props"in y,O=null!==(t=null==x?void 0:x((0,i.A)((0,i.A)({},e),{},{prefixCls:P}),M))&&void 0!==t?t:M,z=B?y:O,E=m()(P,j,S,(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(P,"-has-breadcrumb"),!!z),"".concat(P,"-has-footer"),!!b),"".concat(P,"-rtl"),"rtl"===p),"".concat(P,"-compact"),a),"".concat(P,"-wide"),"Fixed"===C&&"top"==A),"".concat(P,"-ghost"),k)),L=Le(P,e,p,j),H=v&&function(e,t,n){return(0,R.jsx)("div",{className:"".concat(e,"-content ").concat(n).trim(),children:t})}(P,v,j),N=function(e,t,n){return t?(0,R.jsx)("div",{className:"".concat(e,"-footer ").concat(n).trim(),children:t}):null}(P,b,j);return z||L||N||H?w((0,R.jsx)(g.A,{onResize:function(e){var t=e.width;return l(t<768)},children:(0,R.jsxs)("div",{className:E,style:h,children:[z,L,H,N]})})):(0,R.jsx)("div",{className:m()(j,["".concat(P,"-no-children")])})},Ne=n(60827),De=["isLoading","pastDelay","timedOut","error","retry"],_e=function(e){e.isLoading,e.pastDelay,e.timedOut,e.error,e.retry;var t=(0,r.A)(e,De);return(0,R.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,R.jsx)(Ne.A,(0,i.A)({size:"large"},t))})},Xe=n(65640),Ve=function(e){var t=(0,I.rd)().token,n=e.children,o=e.style,r=e.className,a=e.markStyle,l=e.markClassName,c=e.zIndex,s=void 0===c?9:c,p=e.gapX,f=void 0===p?212:p,g=e.gapY,h=void 0===g?222:g,b=e.width,v=void 0===b?120:b,y=e.height,x=void 0===y?64:y,S=e.rotate,C=void 0===S?-22:S,A=e.image,T=e.offsetLeft,k=e.offsetTop,P=e.fontStyle,F=void 0===P?"normal":P,w=e.fontWeight,j=void 0===w?"normal":w,M=e.fontColor,B=void 0===M?t.colorFill:M,O=e.fontSize,z=void 0===O?16:O,E=e.fontFamily,L=void 0===E?"sans-serif":E,H=e.prefixCls,N=(0,(0,u.useContext)(d.Ay.ConfigContext).getPrefixCls)("pro-layout-watermark",H),D=m()("".concat(N,"-wrapper"),r),_=m()(N,l),V=(0,u.useState)(""),W=(0,X.A)(V,2),U=W[0],$=W[1];return(0,u.useEffect)(function(){var t=document.createElement("canvas"),n=t.getContext("2d"),o=function(e){if(!e)return 1;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||1;return(window.devicePixelRatio||1)/t}(n),r="".concat((f+v)*o,"px"),i="".concat((h+x)*o,"px"),a=T||f/2,l=k||h/2;if(t.setAttribute("width",r),t.setAttribute("height",i),n){n.translate(a*o,l*o),n.rotate(Math.PI/180*Number(C));var c=v*o,s=x*o,d=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=Number(z)*o;n.font="".concat(F," normal ").concat(j," ").concat(i,"px/").concat(s,"px ").concat(L),n.fillStyle=B,Array.isArray(e)?null==e||e.forEach(function(e,t){return n.fillText(e,0,t*i+r)}):n.fillText(e,0,r?r+i:0),$(t.toDataURL())};if(A){var u=new Image;return u.crossOrigin="anonymous",u.referrerPolicy="no-referrer",u.src=A,void(u.onload=function(){n.drawImage(u,0,0,c,s),$(t.toDataURL()),e.content&&d(e.content,u.height+8)})}e.content&&d(e.content)}else Xe.error("当前环境不支持Canvas")},[f,h,T,k,C,F,j,v,x,L,B,A,e.content,z]),(0,R.jsxs)("div",{style:(0,i.A)({position:"relative"},o),className:D,children:[n,(0,R.jsx)("div",{className:_,style:(0,i.A)((0,i.A)({zIndex:s,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(f+v,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},U?{backgroundImage:"url('".concat(U,"')")}:{}),a)})]})},We=[576,768,992,1200].map(function(e){return"@media (max-width: ".concat(e,"px)")}),Ue=(0,X.A)(We,4),$e=Ue[0],Ke=Ue[1],Ge=Ue[2],Ze=Ue[3],qe=function(e){var t,n,r,i,a,l,c,s,d,u,p,f,m,g,h,b,v,y;return(0,o.A)({},e.componentCls,(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({position:"relative","&-children-container":{paddingBlockStart:0,paddingBlockEnd:null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.paddingBlockPageContainerContent,paddingInline:null===(n=e.layout)||void 0===n||null===(n=n.pageContainer)||void 0===n?void 0:n.paddingInlinePageContainerContent},"&-children-container-no-header":{paddingBlockStart:null===(r=e.layout)||void 0===r||null===(r=r.pageContainer)||void 0===r?void 0:r.paddingBlockPageContainerContent},"&-affix":(0,o.A)({},"".concat(e.antCls,"-affix"),(0,o.A)({},"".concat(e.componentCls,"-warp"),{backgroundColor:null===(i=e.layout)||void 0===i||null===(i=i.pageContainer)||void 0===i?void 0:i.colorBgPageContainerFixed,transition:"background-color 0.3s",boxShadow:"0 2px 8px #f0f1f2"}))},"& &-warp-page-header",(0,o.A)((0,o.A)((0,o.A)((0,o.A)({paddingBlockStart:(null!==(a=null===(l=e.layout)||void 0===l||null===(l=l.pageContainer)||void 0===l?void 0:l.paddingBlockPageContainerContent)&&void 0!==a?a:40)/4,paddingBlockEnd:(null!==(c=null===(s=e.layout)||void 0===s||null===(s=s.pageContainer)||void 0===s?void 0:s.paddingBlockPageContainerContent)&&void 0!==c?c:40)/2,paddingInlineStart:null===(d=e.layout)||void 0===d||null===(d=d.pageContainer)||void 0===d?void 0:d.paddingInlinePageContainerContent,paddingInlineEnd:null===(u=e.layout)||void 0===u||null===(u=u.pageContainer)||void 0===u?void 0:u.paddingInlinePageContainerContent},"& ~ ".concat(e.proComponentsCls,"-grid-content"),(0,o.A)({},"".concat(e.proComponentsCls,"-page-container-children-content"),{paddingBlock:(null!==(p=null===(f=e.layout)||void 0===f||null===(f=f.pageContainer)||void 0===f?void 0:f.paddingBlockPageContainerContent)&&void 0!==p?p:24)/3})),"".concat(e.antCls,"-page-header-breadcrumb"),{paddingBlockStart:(null!==(m=null===(g=e.layout)||void 0===g||null===(g=g.pageContainer)||void 0===g?void 0:g.paddingBlockPageContainerContent)&&void 0!==m?m:40)/4+10}),"".concat(e.antCls,"-page-header-heading"),{paddingBlockStart:(null!==(h=null===(b=e.layout)||void 0===b||null===(b=b.pageContainer)||void 0===b?void 0:b.paddingBlockPageContainerContent)&&void 0!==h?h:40)/4}),"".concat(e.antCls,"-page-header-footer"),{marginBlockStart:(null!==(v=null===(y=e.layout)||void 0===y||null===(y=y.pageContainer)||void 0===y?void 0:y.paddingBlockPageContainerContent)&&void 0!==v?v:40)/4})),"&-detail",(0,o.A)({display:"flex"},$e,{display:"block"})),"&-main",{width:"100%"}),"&-row",(0,o.A)({display:"flex",width:"100%"},Ke,{display:"block"})),"&-content",{flex:"auto",width:"100%"}),"&-extraContent",(0,o.A)((0,o.A)((0,o.A)((0,o.A)({flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},Ze,{marginInlineStart:44}),Ge,{marginInlineStart:20}),Ke,{marginInlineStart:0,textAlign:"start"}),$e,{marginInlineStart:0})))};var Qe=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,Je=function(e){return"*"===e||"x"===e||"X"===e},Ye=function(e){var t=parseInt(e,10);return isNaN(t)?e:t},et=function(e,t){if(Je(e)||Je(t))return 0;var n=function(e,t){return(0,a.A)(e)!==(0,a.A)(t)?[String(e),String(t)]:[e,t]}(Ye(e),Ye(t)),o=(0,X.A)(n,2),r=o[0],i=o[1];return r>i?1:r<i?-1:0},tt=function(e){var t,n=e.match(Qe);return null==n||null===(t=n.shift)||void 0===t||t.call(n),n},nt=function(e,t){var n=tt(e),o=tt(t),r=n.pop(),i=o.pop(),a=function(e,t){for(var n=0;n<Math.max(e.length,t.length);n++){var o=et(e[n]||"0",t[n]||"0");if(0!==o)return o}return 0}(n,o);return 0!==a?a:r||i?r?-1:1:0},ot=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","childrenContentStyle","style","prefixCls","hashId","value","breadcrumbRender"],rt=["children","loading","className","style","footer","affixProps","token","fixedHeader","breadcrumbRender","footerToolBarProps","childrenContentStyle"];var it=function(e){var t=e.tabList,n=e.tabActiveKey,o=e.onTabChange,r=e.hashId,a=e.tabBarExtraContent,l=e.tabProps,d=e.prefixedClassName;return Array.isArray(t)||a?(0,R.jsx)(c.A,(0,i.A)((0,i.A)({className:"".concat(d,"-tabs ").concat(r).trim(),activeKey:n,onChange:function(e){o&&o(e)},tabBarExtraContent:a,items:null==t?void 0:t.map(function(e,t){var n;return(0,i.A)((0,i.A)({label:e.tab},e),{},{key:(null===(n=e.key)||void 0===n?void 0:n.toString())||(null==t?void 0:t.toString())})})},l),{},{children:nt(s.A,"4.23.0")<0?null==t?void 0:t.map(function(e,t){return(0,R.jsx)(c.A.TabPane,(0,i.A)({tab:e.tab},e),e.key||t)}):null})):null},at=function(e,t,n,o){return e||t?(0,R.jsx)("div",{className:"".concat(n,"-detail ").concat(o).trim(),children:(0,R.jsx)("div",{className:"".concat(n,"-main ").concat(o).trim(),children:(0,R.jsxs)("div",{className:"".concat(n,"-row ").concat(o).trim(),children:[e&&(0,R.jsx)("div",{className:"".concat(n,"-content ").concat(o).trim(),children:e}),t&&(0,R.jsx)("div",{className:"".concat(n,"-extraContent ").concat(o).trim(),children:t})]})})}):null},lt=function(e){var t,n=e.title,o=e.content,a=e.pageHeaderRender,l=e.header,c=e.prefixedClassName,s=e.extraContent,d=(e.childrenContentStyle,e.style,e.prefixCls),u=e.hashId,p=e.value,f=e.breadcrumbRender,m=(0,r.A)(e,ot);if(!1===a)return null;if(a)return(0,R.jsxs)(R.Fragment,{children:[" ",a((0,i.A)((0,i.A)({},e),p))]});var g=n;n||!1===n||(g=p.title);var h=(0,i.A)((0,i.A)((0,i.A)({},p),{},{title:g},m),{},{footer:it((0,i.A)((0,i.A)({},m),{},{hashId:u,breadcrumbRender:f,prefixedClassName:c}))},l),b=h.breadcrumb,v=!(b&&(null!=b&&b.itemRender||null!=b&&null!==(t=b.items)&&void 0!==t&&t.length)||f);return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function(e){return!h[e]})&&v&&!o&&!s?null:(0,R.jsx)(He,(0,i.A)((0,i.A)({},h),{},{className:"".concat(c,"-warp-page-header ").concat(u).trim(),breadcrumb:!1===f?void 0:(0,i.A)((0,i.A)({},h.breadcrumb),p.breadcrumbProps),breadcrumbRender:function(){if(f)return f}(),prefixCls:d,children:(null==l?void 0:l.children)||at(o,s,c,u)}))},ct=function(e){var t,n,c=e.children,s=e.loading,f=void 0!==s&&s,g=e.className,h=e.style,b=e.footer,v=e.affixProps,y=e.token,x=e.fixedHeader,S=e.breadcrumbRender,C=e.footerToolBarProps,A=e.childrenContentStyle,T=(0,r.A)(e,rt),k=(0,u.useContext)(M);(0,u.useEffect)(function(){var e;return k&&null!=k&&k.setHasPageContainer?(null==k||null===(e=k.setHasPageContainer)||void 0===e||e.call(k,function(e){return e+1}),function(){var e;null==k||null===(e=k.setHasPageContainer)||void 0===e||e.call(k,function(e){return e-1})}):function(){}},[]);var P=(0,u.useContext)(l.Lx).token,F=(0,u.useContext)(d.Ay.ConfigContext).getPrefixCls,w=e.prefixCls||F("pro"),B="".concat(w,"-page-container"),O=function(e,t){return(0,I.X3)("ProLayoutPageContainer",function(n){var o,r=(0,i.A)((0,i.A)({},n),{},{componentCls:".".concat(e),layout:(0,i.A)((0,i.A)({},null==n?void 0:n.layout),{},{pageContainer:(0,i.A)((0,i.A)({},null==n||null===(o=n.layout)||void 0===o?void 0:o.pageContainer),t)})});return[qe(r)]})}(B,y),z=O.wrapSSR,E=O.hashId,L=function(e,t){var n=t.stylish;return(0,I.X3)("ProLayoutPageContainerStylish",function(t){var r=(0,i.A)((0,i.A)({},t),{},{componentCls:".".concat(e)});return n?[(0,o.A)({},"div".concat(r.componentCls),null==n?void 0:n(r))]:[]})}("".concat(B,".").concat(B,"-stylish"),{stylish:e.stylish}),H=(0,u.useMemo)(function(){var e;return 0!=S&&(S||(null==T||null===(e=T.header)||void 0===e?void 0:e.breadcrumbRender))},[S,null==T||null===(t=T.header)||void 0===t?void 0:t.breadcrumbRender]),D=lt((0,i.A)((0,i.A)({},T),{},{breadcrumbRender:H,ghost:!0,hashId:E,prefixCls:void 0,prefixedClassName:B,value:k})),X=(0,u.useMemo)(function(){if(p().isValidElement(f))return f;if("boolean"==typeof f&&!f)return null;var e=function(e){return"object"===(0,a.A)(e)?e:{spinning:e}}(f);return e.spinning?(0,R.jsx)(_e,(0,i.A)({},e)):null},[f]),V=(0,u.useMemo)(function(){return c?(0,R.jsx)(R.Fragment,{children:(0,R.jsx)("div",{className:m()(E,"".concat(B,"-children-container"),(0,o.A)({},"".concat(B,"-children-container-no-header"),!D)),style:A,children:c})}):null},[c,B,A,E]),W=(0,u.useMemo)(function(){var t=X||V;if(e.waterMarkProps||k.waterMarkProps){var n=(0,i.A)((0,i.A)({},k.waterMarkProps),e.waterMarkProps);return(0,R.jsx)(Ve,(0,i.A)((0,i.A)({},n),{},{children:t}))}return t},[e.waterMarkProps,k.waterMarkProps,X,V]),U=m()(B,E,g,(0,o.A)((0,o.A)((0,o.A)({},"".concat(B,"-with-footer"),b),"".concat(B,"-with-affix"),x&&D),"".concat(B,"-stylish"),!!T.stylish));return z(L.wrapSSR((0,R.jsxs)(R.Fragment,{children:[(0,R.jsxs)("div",{style:h,className:U,children:[x&&D?(0,R.jsx)(j,(0,i.A)((0,i.A)({offsetTop:k.hasHeader&&k.fixedHeader?null===(n=P.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader:1},v),{},{className:"".concat(B,"-affix ").concat(E).trim(),children:(0,R.jsx)("div",{className:"".concat(B,"-warp ").concat(E).trim(),children:D})})):D,W&&(0,R.jsx)(_,{children:W})]}),b&&(0,R.jsx)(N,(0,i.A)((0,i.A)({stylish:T.footerStylish,prefixCls:w},C),{},{children:b}))]})))},st=function(e){return(0,R.jsx)(l.TY,{needDeps:!0,children:(0,R.jsx)(ct,(0,i.A)({},e))})}},48381:function(e,t,n){"use strict";n.d(t,{Y1:function(){return y},JM:function(){return h},dF:function(){return v},X9:function(){return g},X3:function(){return x},rd:function(){return b}});var o=n(70989),r=n(65071),i=n(45273),a=n(27861),l=n(35483),c=n(7365),s=function(){function e(t,n){var o;if(void 0===t&&(t=""),void 0===n&&(n={}),t instanceof e)return t;"number"==typeof t&&(t=(0,i.oS)(t)),this.originalInput=t;var r=(0,l.RO)(t);this.originalInput=t,this.r=r.r,this.g=r.g,this.b=r.b,this.a=r.a,this.roundA=Math.round(100*this.a)/100,this.format=null!==(o=n.format)&&void 0!==o?o:r.format,this.gradientType=n.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=r.ok}return e.prototype.isDark=function(){return this.getBrightness()<128},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},e.prototype.getLuminance=function(){var e=this.toRgb(),t=e.r/255,n=e.g/255,o=e.b/255;return.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(e){return this.a=(0,c.TV)(e),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){return 0===this.toHsl().s},e.prototype.toHsv=function(){var e=(0,i.wE)(this.r,this.g,this.b);return{h:360*e.h,s:e.s,v:e.v,a:this.a}},e.prototype.toHsvString=function(){var e=(0,i.wE)(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),o=Math.round(100*e.v);return 1===this.a?"hsv(".concat(t,", ").concat(n,"%, ").concat(o,"%)"):"hsva(".concat(t,", ").concat(n,"%, ").concat(o,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var e=(0,i.K6)(this.r,this.g,this.b);return{h:360*e.h,s:e.s,l:e.l,a:this.a}},e.prototype.toHslString=function(){var e=(0,i.K6)(this.r,this.g,this.b),t=Math.round(360*e.h),n=Math.round(100*e.s),o=Math.round(100*e.l);return 1===this.a?"hsl(".concat(t,", ").concat(n,"%, ").concat(o,"%)"):"hsla(".concat(t,", ").concat(n,"%, ").concat(o,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(e){return void 0===e&&(e=!1),(0,i.Ob)(this.r,this.g,this.b,e)},e.prototype.toHexString=function(e){return void 0===e&&(e=!1),"#"+this.toHex(e)},e.prototype.toHex8=function(e){return void 0===e&&(e=!1),(0,i.H)(this.r,this.g,this.b,this.a,e)},e.prototype.toHex8String=function(e){return void 0===e&&(e=!1),"#"+this.toHex8(e)},e.prototype.toHexShortString=function(e){return void 0===e&&(e=!1),1===this.a?this.toHexString(e):this.toHex8String(e)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),n=Math.round(this.b);return 1===this.a?"rgb(".concat(e,", ").concat(t,", ").concat(n,")"):"rgba(".concat(e,", ").concat(t,", ").concat(n,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var e=function(e){return"".concat(Math.round(100*(0,c.Cg)(e,255)),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var e=function(e){return Math.round(100*(0,c.Cg)(e,255))};return 1===this.a?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+(0,i.Ob)(this.r,this.g,this.b,!1),t=0,n=Object.entries(a.D);t<n.length;t++){var o=n[t],r=o[0];if(e===o[1])return r}return!1},e.prototype.toString=function(e){var t=Boolean(e);e=null!=e?e:this.format;var n=!1,o=this.a<1&&this.a>=0;return t||!o||!e.startsWith("hex")&&"name"!==e?("rgb"===e&&(n=this.toRgbString()),"prgb"===e&&(n=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(n=this.toHexString()),"hex3"===e&&(n=this.toHexString(!0)),"hex4"===e&&(n=this.toHex8String(!0)),"hex8"===e&&(n=this.toHex8String()),"name"===e&&(n=this.toName()),"hsl"===e&&(n=this.toHslString()),"hsv"===e&&(n=this.toHsvString()),n||this.toHexString()):"name"===e&&0===this.a?this.toName():this.toRgbString()},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l+=t/100,n.l=(0,c.J$)(n.l),new e(n)},e.prototype.brighten=function(t){void 0===t&&(t=10);var n=this.toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(-t/100*255))),n.g=Math.max(0,Math.min(255,n.g-Math.round(-t/100*255))),n.b=Math.max(0,Math.min(255,n.b-Math.round(-t/100*255))),new e(n)},e.prototype.darken=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.l-=t/100,n.l=(0,c.J$)(n.l),new e(n)},e.prototype.tint=function(e){return void 0===e&&(e=10),this.mix("white",e)},e.prototype.shade=function(e){return void 0===e&&(e=10),this.mix("black",e)},e.prototype.desaturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s-=t/100,n.s=(0,c.J$)(n.s),new e(n)},e.prototype.saturate=function(t){void 0===t&&(t=10);var n=this.toHsl();return n.s+=t/100,n.s=(0,c.J$)(n.s),new e(n)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var n=this.toHsl(),o=(n.h+t)%360;return n.h=o<0?360+o:o,new e(n)},e.prototype.mix=function(t,n){void 0===n&&(n=50);var o=this.toRgb(),r=new e(t).toRgb(),i=n/100;return new e({r:(r.r-o.r)*i+o.r,g:(r.g-o.g)*i+o.g,b:(r.b-o.b)*i+o.b,a:(r.a-o.a)*i+o.a})},e.prototype.analogous=function(t,n){void 0===t&&(t=6),void 0===n&&(n=30);var o=this.toHsl(),r=360/n,i=[this];for(o.h=(o.h-(r*t>>1)+720)%360;--t;)o.h=(o.h+r)%360,i.push(new e(o));return i},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var n=this.toHsv(),o=n.h,r=n.s,i=n.v,a=[],l=1/t;t--;)a.push(new e({h:o,s:r,v:i})),i=(i+l)%1;return a},e.prototype.splitcomplement=function(){var t=this.toHsl(),n=t.h;return[this,new e({h:(n+72)%360,s:t.s,l:t.l}),new e({h:(n+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var n=this.toRgb(),o=new e(t).toRgb(),r=n.a+o.a*(1-n.a);return new e({r:(n.r*n.a+o.r*o.a*(1-n.a))/r,g:(n.g*n.a+o.g*o.a*(1-n.a))/r,b:(n.b*n.a+o.b*o.a*(1-n.a))/r,a:r})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var n=this.toHsl(),o=n.h,r=[this],i=360/t,a=1;a<t;a++)r.push(new e({h:(o+a*i)%360,s:n.s,l:n.l}));return r},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}();var d=n(51505),u=n(34669),p=n(41594),f=n(68496),m=n(33820),g=function(e,t){return new s(e).setAlpha(t).toRgbString()},h=void 0!==d.A&&d.A?d.A:m,b=h.useToken,v=function(e){return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none"}},y=function(e){return{color:e.colorLink,outline:"none",cursor:"pointer",transition:"color ".concat(e.motionDurationSlow),"&:focus, &:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive}}};function x(e,t){var n,i=(0,p.useContext)(f.Lx).token,a=void 0===i?{}:i,l=(0,p.useContext)(f.Lx).hashed,c=b(),s=c.token,d=c.hashId,m=(0,p.useContext)(f.Lx).theme,g=(0,p.useContext)(u.Ay.ConfigContext),h=g.getPrefixCls,v=g.csp;return a.layout||(a=(0,o.A)({},s)),a.proComponentsCls=null!==(n=a.proComponentsCls)&&void 0!==n?n:".".concat(h("pro")),a.antCls=".".concat(h()),{wrapSSR:(0,r.IV)({theme:m,token:a,path:[e],nonce:null==v?void 0:v.nonce,layer:{name:"antd-pro"}},function(){return t(a)}),hashId:l?d:""}}},52222:function(e,t,n){"use strict";var o=n(35932).default;Object.defineProperty(t,"__esModule",{value:!0}),t.getTwoToneColor=function(){var e=i.default.getTwoToneColors();if(!e.calculated)return e.primaryColor;return[e.primaryColor,e.secondaryColor]},t.setTwoToneColor=function(e){var t=(0,a.normalizeTwoToneColors)(e),n=(0,r.default)(t,2),o=n[0],l=n[1];return i.default.setTwoToneColors({primaryColor:o,secondaryColor:l})};var r=o(n(5809)),i=o(n(89977)),a=n(74205)},56685:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o;const r=(o=n(82846))&&o.__esModule?o:{default:o};t.default=r,e.exports=r},68496:function(e,t,n){"use strict";n.d(t,{TY:function(){return dt},Lx:function(){return ut}});var o=n(24174),r=n(14290),i=n(9067),a=n(70989),l=n(65071),c=n(34669),s=n(39515),d=n(41594),u=n.n(d);var p=Object.prototype.hasOwnProperty;const f=new WeakMap,m=()=>{},g=m(),h=Object,b=e=>e===g,v=e=>"function"==typeof e,y=(e,t)=>({...e,...t}),x=e=>v(e.then),S={},C={},A="undefined",T=typeof window!=A,k=typeof document!=A,P=T&&"Deno"in window,F=(e,t)=>{const n=f.get(e);return[()=>!b(t)&&e.get(t)||S,o=>{if(!b(t)){const r=e.get(t);t in C||(C[t]=r),n[5](t,y(r,o),r||S)}},n[6],()=>!b(t)&&t in C?C[t]:!b(t)&&e.get(t)||S]};let w=!0;const[j,M]=T&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[m,m],B={isOnline:()=>w,isVisible:()=>{const e=k&&document.visibilityState;return b(e)||"hidden"!==e}},O={initFocus:e=>(k&&document.addEventListener("visibilitychange",e),j("focus",e),()=>{k&&document.removeEventListener("visibilitychange",e),M("focus",e)}),initReconnect:e=>{const t=()=>{w=!0,e()},n=()=>{w=!1};return j("online",t),j("offline",n),()=>{M("online",t),M("offline",n)}}},z=!d.useId,E=!T||P,I=e=>T&&typeof window.requestAnimationFrame!=A?window.requestAnimationFrame(e):setTimeout(e,1),L=E?d.useEffect:d.useLayoutEffect,R="undefined"!=typeof navigator&&navigator.connection,H=!E&&R&&(["slow-2g","2g"].includes(R.effectiveType)||R.saveData),N=new WeakMap,D=(e,t)=>e===`[object ${t}]`;let _=0;const X=e=>{const t=typeof e,n=(o=e,h.prototype.toString.call(o));var o;const r=D(n,"Date"),i=D(n,"RegExp"),a=D(n,"Object");let l,c;if(h(e)!==e||r||i)l=r?e.toJSON():"symbol"==t?e.toString():"string"==t?JSON.stringify(e):""+e;else{if(l=N.get(e),l)return l;if(l=++_+"~",N.set(e,l),Array.isArray(e)){for(l="@",c=0;c<e.length;c++)l+=X(e[c])+",";N.set(e,l)}if(a){l="#";const t=h.keys(e).sort();for(;!b(c=t.pop());)b(e[c])||(l+=c+":"+X(e[c])+",");N.set(e,l)}}return l},V=e=>{if(v(e))try{e=e()}catch(t){e=""}const t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?X(e):"",t]};let W=0;const U=()=>++W;async function $(...e){const[t,n,o,r]=e,i=y({populateCache:!0,throwOnError:!0},"boolean"==typeof r?{revalidate:r}:r||{});let a=i.populateCache;const l=i.rollbackOnError;let c=i.optimisticData;const s=i.throwOnError;if(v(n)){const e=n,o=[],r=t.keys();for(const n of r)!/^\$(inf|sub)\$/.test(n)&&e(t.get(n)._k)&&o.push(n);return Promise.all(o.map(d))}return d(n);async function d(n){const[r]=V(n);if(!r)return;const[d,u]=F(t,r),[p,m,h,y]=f.get(t),S=()=>{const e=p[r];return(v(i.revalidate)?i.revalidate(d().data,n):!1!==i.revalidate)&&(delete h[r],delete y[r],e&&e[0])?e[0](2).then(()=>d().data):d().data};if(e.length<3)return S();let C,A=o,T=!1;const k=U();m[r]=[k,0];const P=!b(c),w=d(),j=w.data,M=w._c,B=b(M)?j:M;if(P&&(c=v(c)?c(B,j):c,u({data:c,_c:B})),v(A))try{A=A(B)}catch(e){C=e,T=!0}if(A&&x(A)){if(A=await A.catch(e=>{C=e,T=!0}),k!==m[r][0]){if(T)throw C;return A}T&&P&&(e=>"function"==typeof l?l(e):!1!==l)(C)&&(a=!0,u({data:B,_c:g}))}if(a&&!T)if(v(a)){const e=a(A,B);u({data:e,error:g,_c:g})}else u({data:A,error:g,_c:g});if(m[r][1]=U(),Promise.resolve(S()).then(()=>{u({_c:g})}),!T)return A;if(s)throw C}}const K=(e,t)=>{for(const n in e)e[n][0]&&e[n][0](t)},G=(e,t)=>{if(!f.has(e)){const n=y(O,t),o=Object.create(null),r=$.bind(g,e);let i=m;const a=Object.create(null),l=(e,t)=>{const n=a[e]||[];return a[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},c=(t,n,o)=>{e.set(t,n);const r=a[t];if(r)for(const e of r)e(n,o)},s=()=>{if(!f.has(e)&&(f.set(e,[o,Object.create(null),Object.create(null),Object.create(null),r,c,l]),!E)){const t=n.initFocus(setTimeout.bind(g,K.bind(g,o,0))),r=n.initReconnect(setTimeout.bind(g,K.bind(g,o,1)));i=()=>{t&&t(),r&&r(),f.delete(e)}}};return s(),[e,r,s,i]}return[e,f.get(e)[4]]},Z=function e(t,n){var o,r;if(t===n)return!0;if(t&&n&&(o=t.constructor)===n.constructor){if(o===Date)return t.getTime()===n.getTime();if(o===RegExp)return t.toString()===n.toString();if(o===Array){if((r=t.length)===n.length)for(;r--&&e(t[r],n[r]););return-1===r}if(!o||"object"==typeof t){for(o in r=0,t){if(p.call(t,o)&&++r&&!p.call(n,o))return!1;if(!(o in n)||!e(t[o],n[o]))return!1}return Object.keys(n).length===r}}return t!=t&&n!=n},[q,Q]=G(new Map),J=y({onLoadingSlow:m,onSuccess:m,onError:m,onErrorRetry:(e,t,n,o,r)=>{const i=n.errorRetryCount,a=r.retryCount,l=~~((Math.random()+.5)*(1<<(a<8?a:8)))*n.errorRetryInterval;!b(i)&&a>i||setTimeout(o,l,r)},onDiscarded:m,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:H?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:H?5e3:3e3,compare:Z,isPaused:()=>!1,cache:q,mutate:Q,fallback:{}},B),Y=(e,t)=>{const n=y(e,t);if(t){const{use:o,fallback:r}=e,{use:i,fallback:a}=t;o&&i&&(n.use=o.concat(i)),r&&a&&(n.fallback=y(r,a))}return n},ee=(0,d.createContext)({}),te=T&&window.__SWR_DEVTOOLS_USE__,ne=te?window.__SWR_DEVTOOLS_USE__:[],oe=e=>v(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],re=()=>y(J,(0,d.useContext)(ee)),ie=ne.concat(e=>(t,n,o)=>e(t,n&&((...e)=>{const[o]=V(t),[,,,r]=f.get(q);if(o.startsWith("$inf$"))return n(...e);const i=r[o];return b(i)?n(...e):(delete r[o],i)}),o));te&&(window.__SWR_DEVTOOLS_REACT__=d);var ae=n(97862);const le=()=>{};le(),new WeakMap;const ce=d.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),se={dedupe:!0},de=h.defineProperty(e=>{const{value:t}=e,n=(0,d.useContext)(ee),o=v(t),r=(0,d.useMemo)(()=>o?t(n):t,[o,n,t]),i=(0,d.useMemo)(()=>o?r:Y(n,r),[o,n,r]),a=r&&r.provider,l=(0,d.useRef)(g);a&&!l.current&&(l.current=G(a(i.cache||q),r));const c=l.current;return c&&(i.cache=c[0],i.mutate=c[1]),L(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,d.createElement)(ee.Provider,y(e,{value:i}))},"defaultValue",{value:J});ue=(e,t,n)=>{const{cache:o,compare:r,suspense:i,fallbackData:a,revalidateOnMount:l,revalidateIfStale:c,refreshInterval:s,refreshWhenHidden:u,refreshWhenOffline:p,keepPreviousData:m}=n,[h,S,C,A]=f.get(o),[T,k]=V(e),P=(0,d.useRef)(!1),w=(0,d.useRef)(!1),j=(0,d.useRef)(T),M=(0,d.useRef)(t),B=(0,d.useRef)(n),O=()=>B.current,R=()=>O().isVisible()&&O().isOnline(),[H,N,D,_]=F(o,T),X=(0,d.useRef)({}).current,W=b(a)?b(n.fallback)?g:n.fallback[T]:a,K=(e,t)=>{for(const n in X){const o=n;if("data"===o){if(!r(e[o],t[o])){if(!b(e[o]))return!1;if(!r(ne,t[o]))return!1}}else if(t[o]!==e[o])return!1}return!0},G=(0,d.useMemo)(()=>{const e=!!T&&!!t&&(b(l)?!O().isPaused()&&!i&&!1!==c:l),n=t=>{const n=y(t);return delete n._k,e?{isValidating:!0,isLoading:!0,...n}:n},o=H(),r=_(),a=n(o),s=o===r?a:n(r);let d=a;return[()=>{const e=n(H());return K(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>s]},[o,T]),Z=(0,ae.useSyncExternalStore)((0,d.useCallback)(e=>D(T,(t,n)=>{K(n,t)||e()}),[o,T]),G[0],G[1]),q=!P.current,Q=h[T]&&h[T].length>0,J=Z.data,Y=b(J)?W&&x(W)?ce(W):W:J,ee=Z.error,te=(0,d.useRef)(Y),ne=m?b(J)?b(te.current)?Y:te.current:J:Y,oe=!(Q&&!b(ee))&&(q&&!b(l)?l:!O().isPaused()&&(i?!b(Y)&&c:b(Y)||c)),re=!!(T&&t&&q&&oe),ie=b(Z.isValidating)?re:Z.isValidating,le=b(Z.isLoading)?re:Z.isLoading,de=(0,d.useCallback)(async e=>{const t=M.current;if(!T||!t||w.current||O().isPaused())return!1;let o,i,a=!0;const l=e||{},c=!C[T]||!l.dedupe,s=()=>z?!w.current&&T===j.current&&P.current:T===j.current,d={isValidating:!1,isLoading:!1},u=()=>{N(d)},p=()=>{const e=C[T];e&&e[1]===i&&delete C[T]},f={isValidating:!0};b(H().data)&&(f.isLoading=!0);try{if(c&&(N(f),n.loadingTimeout&&b(H().data)&&setTimeout(()=>{a&&s()&&O().onLoadingSlow(T,n)},n.loadingTimeout),C[T]=[t(k),U()]),[o,i]=C[T],o=await o,c&&setTimeout(p,n.dedupingInterval),!C[T]||C[T][1]!==i)return c&&s()&&O().onDiscarded(T),!1;d.error=g;const e=S[T];if(!b(e)&&(i<=e[0]||i<=e[1]||0===e[1]))return u(),c&&s()&&O().onDiscarded(T),!1;const l=H().data;d.data=r(l,o)?l:o,c&&s()&&O().onSuccess(o,T,n)}catch(e){p();const t=O(),{shouldRetryOnError:n}=t;t.isPaused()||(d.error=e,c&&s()&&(t.onError(e,T,t),(!0===n||v(n)&&n(e))&&(O().revalidateOnFocus&&O().revalidateOnReconnect&&!R()||t.onErrorRetry(e,T,t,e=>{const t=h[T];t&&t[0]&&t[0](3,e)},{retryCount:(l.retryCount||0)+1,dedupe:!0}))))}return a=!1,u(),!0},[T,o]),ue=(0,d.useCallback)((...e)=>$(o,j.current,...e),[]);if(L(()=>{M.current=t,B.current=n,b(J)||(te.current=J)}),L(()=>{if(!T)return;const e=de.bind(g,se);let t=0;if(O().revalidateOnFocus){const e=Date.now();t=e+O().focusThrottleInterval}const n=((e,t,n)=>{const o=t[e]||(t[e]=[]);return o.push(n),()=>{const e=o.indexOf(n);e>=0&&(o[e]=o[o.length-1],o.pop())}})(T,h,(n,o={})=>{if(0==n){const n=Date.now();O().revalidateOnFocus&&n>t&&R()&&(t=n+O().focusThrottleInterval,e())}else if(1==n)O().revalidateOnReconnect&&R()&&e();else{if(2==n)return de();if(3==n)return de(o)}});return w.current=!1,j.current=T,P.current=!0,N({_k:k}),oe&&(C[T]||(b(Y)||E?e():I(e))),()=>{w.current=!0,n()}},[T]),L(()=>{let e;function t(){const t=v(s)?s(H().data):s;t&&-1!==e&&(e=setTimeout(n,t))}function n(){H().error||!u&&!O().isVisible()||!p&&!O().isOnline()?t():de(se).then(t)}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[s,u,p,T]),(0,d.useDebugValue)(ne),i&&b(Y)&&T){if(!z&&E)throw new Error("Fallback data is required when using Suspense in SSR.");M.current=t,B.current=n,w.current=!1;const e=A[T];if(!b(e)){const t=ue(e);ce(t)}if(!b(ee))throw ee;{const e=de(se);b(ne)||(e.status="fulfilled",e.value=!0),ce(e)}}return{mutate:ue,get data(){return X.data=!0,ne},get error(){return X.error=!0,ee},get isValidating(){return X.isValidating=!0,ie},get isLoading(){return X.isLoading=!0,le}}};var ue,pe=n(23186),fe=function(e,t){return{getMessage:function(n,o){var r=(0,pe.Jt)(t,n.replace(/\[(\d+)\]/g,".$1").split("."))||"";if(r)return r;if("zh-CN"===e.replace("_","-"))return o;var i=Ke["zh-CN"];return i?i.getMessage(n,o):o},locale:e}},me=fe("mn_MN",{moneySymbol:"₮",form:{lightFilter:{more:"Илүү",clear:"Цэвэрлэх",confirm:"Баталгаажуулах",itemUnit:"Нэгжүүд"}},tableForm:{search:"Хайх",reset:"Шинэчлэх",submit:"Илгээх",collapsed:"Өргөтгөх",expand:"Хураах",inputPlaceholder:"Утга оруулна уу",selectPlaceholder:"Утга сонгоно уу"},alert:{clear:"Цэвэрлэх",selected:"Сонгогдсон",item:"Нэгж"},pagination:{total:{range:" ",total:"Нийт",item:"мөр"}},tableToolBar:{leftPin:"Зүүн тийш бэхлэх",rightPin:"Баруун тийш бэхлэх",noPin:"Бэхлэхгүй",leftFixedTitle:"Зүүн зэрэгцүүлэх",rightFixedTitle:"Баруун зэрэгцүүлэх",noFixedTitle:"Зэрэгцүүлэхгүй",reset:"Шинэчлэх",columnDisplay:"Баганаар харуулах",columnSetting:"Тохиргоо",fullScreen:"Бүтэн дэлгэцээр",exitFullScreen:"Бүтэн дэлгэц цуцлах",reload:"Шинэчлэх",density:"Хэмжээ",densityDefault:"Хэвийн",densityLarger:"Том",densityMiddle:"Дунд",densitySmall:"Жижиг"},stepsForm:{next:"Дараах",prev:"Өмнөх",submit:"Дуусгах"},loginForm:{submitText:"Нэвтрэх"},editableTable:{action:{save:"Хадгалах",cancel:"Цуцлах",delete:"Устгах",add:"Мөр нэмэх"}},switch:{open:"Нээх",close:"Хаах"}}),ge=fe("ar_EG",{moneySymbol:"$",form:{lightFilter:{more:"المزيد",clear:"نظف",confirm:"تأكيد",itemUnit:"عناصر"}},tableForm:{search:"ابحث",reset:"إعادة تعيين",submit:"ارسال",collapsed:"مُقلص",expand:"مُوسع",inputPlaceholder:"الرجاء الإدخال",selectPlaceholder:"الرجاء الإختيار"},alert:{clear:"نظف",selected:"محدد",item:"عنصر"},pagination:{total:{range:" ",total:"من",item:"عناصر"}},tableToolBar:{leftPin:"ثبت على اليسار",rightPin:"ثبت على اليمين",noPin:"الغاء التثبيت",leftFixedTitle:"لصق على اليسار",rightFixedTitle:"لصق على اليمين",noFixedTitle:"إلغاء الإلصاق",reset:"إعادة تعيين",columnDisplay:"الأعمدة المعروضة",columnSetting:"الإعدادات",fullScreen:"وضع كامل الشاشة",exitFullScreen:"الخروج من وضع كامل الشاشة",reload:"تحديث",density:"الكثافة",densityDefault:"افتراضي",densityLarger:"أكبر",densityMiddle:"وسط",densitySmall:"مدمج"},stepsForm:{next:"التالي",prev:"السابق",submit:"أنهى"},loginForm:{submitText:"تسجيل الدخول"},editableTable:{action:{save:"أنقذ",cancel:"إلغاء الأمر",delete:"حذف",add:"إضافة صف من البيانات"}},switch:{open:"مفتوح",close:"غلق"}}),he=fe("zh_CN",{moneySymbol:"¥",deleteThisLine:"删除此项",copyThisLine:"复制此项",form:{lightFilter:{more:"更多筛选",clear:"清除",confirm:"确认",itemUnit:"项"}},tableForm:{search:"查询",reset:"重置",submit:"提交",collapsed:"展开",expand:"收起",inputPlaceholder:"请输入",selectPlaceholder:"请选择"},alert:{clear:"取消选择",selected:"已选择",item:"项"},pagination:{total:{range:"第",total:"条/总共",item:"条"}},tableToolBar:{leftPin:"固定在列首",rightPin:"固定在列尾",noPin:"不固定",leftFixedTitle:"固定在左侧",rightFixedTitle:"固定在右侧",noFixedTitle:"不固定",reset:"重置",columnDisplay:"列展示",columnSetting:"列设置",fullScreen:"全屏",exitFullScreen:"退出全屏",reload:"刷新",density:"密度",densityDefault:"正常",densityLarger:"宽松",densityMiddle:"中等",densitySmall:"紧凑"},stepsForm:{next:"下一步",prev:"上一步",submit:"提交"},loginForm:{submitText:"登录"},editableTable:{onlyOneLineEditor:"只能同时编辑一行",action:{save:"保存",cancel:"取消",delete:"删除",add:"添加一行数据"}},switch:{open:"打开",close:"关闭"}}),be=fe("en_US",{moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}}),ve=fe("en_GB",{moneySymbol:"£",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}}),ye=fe("vi_VN",{moneySymbol:"₫",form:{lightFilter:{more:"Nhiều hơn",clear:"Trong",confirm:"Xác nhận",itemUnit:"Mục"}},tableForm:{search:"Tìm kiếm",reset:"Làm lại",submit:"Gửi đi",collapsed:"Mở rộng",expand:"Thu gọn",inputPlaceholder:"nhập dữ liệu",selectPlaceholder:"Vui lòng chọn"},alert:{clear:"Xóa",selected:"đã chọn",item:"mục"},pagination:{total:{range:" ",total:"trên",item:"mặt hàng"}},tableToolBar:{leftPin:"Ghim trái",rightPin:"Ghim phải",noPin:"Bỏ ghim",leftFixedTitle:"Cố định trái",rightFixedTitle:"Cố định phải",noFixedTitle:"Chưa cố định",reset:"Làm lại",columnDisplay:"Cột hiển thị",columnSetting:"Cấu hình",fullScreen:"Chế độ toàn màn hình",exitFullScreen:"Thoát chế độ toàn màn hình",reload:"Làm mới",density:"Mật độ hiển thị",densityDefault:"Mặc định",densityLarger:"Mặc định",densityMiddle:"Trung bình",densitySmall:"Chật"},stepsForm:{next:"Sau",prev:"Trước",submit:"Kết thúc"},loginForm:{submitText:"Đăng nhập"},editableTable:{action:{save:"Cứu",cancel:"Hủy",delete:"Xóa",add:"thêm một hàng dữ liệu"}},switch:{open:"mở",close:"đóng"}}),xe=fe("it_IT",{moneySymbol:"€",form:{lightFilter:{more:"più",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalità schermo intero",exitFullScreen:"Esci da modalità schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}}),Se=fe("ja_JP",{moneySymbol:"¥",form:{lightFilter:{more:"更に",clear:"クリア",confirm:"確認",itemUnit:"アイテム"}},tableForm:{search:"検索",reset:"リセット",submit:"送信",collapsed:"拡大",expand:"折畳",inputPlaceholder:"入力してください",selectPlaceholder:"選択してください"},alert:{clear:"クリア",selected:"選択した",item:"アイテム"},pagination:{total:{range:"レコード",total:"/合計",item:" "}},tableToolBar:{leftPin:"左に固定",rightPin:"右に固定",noPin:"キャンセル",leftFixedTitle:"左に固定された項目",rightFixedTitle:"右に固定された項目",noFixedTitle:"固定されてない項目",reset:"リセット",columnDisplay:"表示列",columnSetting:"列表示設定",fullScreen:"フルスクリーン",exitFullScreen:"終了",reload:"更新",density:"行高",densityDefault:"デフォルト",densityLarger:"大",densityMiddle:"中",densitySmall:"小"},stepsForm:{next:"次へ",prev:"前へ",submit:"送信"},loginForm:{submitText:"ログイン"},editableTable:{action:{save:"保存",cancel:"キャンセル",delete:"削除",add:"追加"}},switch:{open:"開く",close:"閉じる"}}),Ce=fe("es_ES",{moneySymbol:"€",form:{lightFilter:{more:"Más",clear:"Limpiar",confirm:"Confirmar",itemUnit:"artículos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"artículos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuración",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"añadir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}}),Ae=fe("ca_ES",{moneySymbol:"€",form:{lightFilter:{more:"Més",clear:"Netejar",confirm:"Confirmar",itemUnit:"Elements"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col·lapsar",inputPlaceholder:"Introduïu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuració",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitjà",densitySmall:"Compacte"},stepsForm:{next:"Següent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Cancel·lar",delete:"Eliminar",add:"afegir una fila de dades"}},switch:{open:"obert",close:"tancat"}}),Te=fe("ru_RU",{moneySymbol:"₽",form:{lightFilter:{more:"Еще",clear:"Очистить",confirm:"ОК",itemUnit:"Позиции"}},tableForm:{search:"Найти",reset:"Сброс",submit:"Отправить",collapsed:"Развернуть",expand:"Свернуть",inputPlaceholder:"Введите значение",selectPlaceholder:"Выберите значение"},alert:{clear:"Очистить",selected:"Выбрано",item:"элементов"},pagination:{total:{range:" ",total:"из",item:"элементов"}},tableToolBar:{leftPin:"Закрепить слева",rightPin:"Закрепить справа",noPin:"Открепить",leftFixedTitle:"Закреплено слева",rightFixedTitle:"Закреплено справа",noFixedTitle:"Не закреплено",reset:"Сброс",columnDisplay:"Отображение столбца",columnSetting:"Настройки",fullScreen:"Полный экран",exitFullScreen:"Выйти из полноэкранного режима",reload:"Обновить",density:"Размер",densityDefault:"По умолчанию",densityLarger:"Большой",densityMiddle:"Средний",densitySmall:"Сжатый"},stepsForm:{next:"Следующий",prev:"Предыдущий",submit:"Завершить"},loginForm:{submitText:"Вход"},editableTable:{action:{save:"Сохранить",cancel:"Отменить",delete:"Удалить",add:"добавить ряд данных"}},switch:{open:"Открытый чемпионат мира по теннису",close:"По адресу:"}}),ke=fe("sr_RS",{moneySymbol:"RSD",form:{lightFilter:{more:"Više",clear:"Očisti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pronađi",reset:"Resetuj",submit:"Pošalji",collapsed:"Proširi",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"Očisti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zakači levo",rightPin:"Zakači desno",noPin:"Nije zakačeno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Podešavanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osveži",density:"Veličina",densityDefault:"Podrazumevana",densityLarger:"Veća",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sačuvaj",cancel:"Poništi",delete:"Obriši",add:"dodajte red podataka"}},switch:{open:"Отворите",close:"Затворите"}}),Pe=fe("ms_MY",{moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}}),Fe=fe("zh_TW",{moneySymbol:"NT$",deleteThisLine:"刪除此项",copyThisLine:"複製此项",form:{lightFilter:{more:"更多篩選",clear:"清除",confirm:"確認",itemUnit:"項"}},tableForm:{search:"查詢",reset:"重置",submit:"提交",collapsed:"展開",expand:"收起",inputPlaceholder:"請輸入",selectPlaceholder:"請選擇"},alert:{clear:"取消選擇",selected:"已選擇",item:"項"},pagination:{total:{range:"第",total:"條/總共",item:"條"}},tableToolBar:{leftPin:"固定到左邊",rightPin:"固定到右邊",noPin:"不固定",leftFixedTitle:"固定在左側",rightFixedTitle:"固定在右側",noFixedTitle:"不固定",reset:"重置",columnDisplay:"列展示",columnSetting:"列設置",fullScreen:"全屏",exitFullScreen:"退出全屏",reload:"刷新",density:"密度",densityDefault:"正常",densityLarger:"寬鬆",densityMiddle:"中等",densitySmall:"緊湊"},stepsForm:{next:"下一步",prev:"上一步",submit:"完成"},loginForm:{submitText:"登入"},editableTable:{onlyOneLineEditor:"只能同時編輯一行",action:{save:"保存",cancel:"取消",delete:"刪除",add:"新增一行資料"}},switch:{open:"打開",close:"關閉"}}),we=fe("zh_HK",{moneySymbol:"HK$",deleteThisLine:"刪除此項",copyThisLine:"複製此項",form:{lightFilter:{more:"更多篩選",clear:"清除",confirm:"確認",itemUnit:"項"}},tableForm:{search:"搜尋",reset:"重設",submit:"提交",collapsed:"展開",expand:"收起",inputPlaceholder:"請輸入",selectPlaceholder:"請選擇"},alert:{clear:"取消選取",selected:"已選取",item:"項"},pagination:{total:{range:"第",total:"項/總共",item:"項"}},tableToolBar:{leftPin:"固定到左邊",rightPin:"固定到右邊",noPin:"不固定",leftFixedTitle:"固定在左側",rightFixedTitle:"固定在右側",noFixedTitle:"不固定",reset:"重設",columnDisplay:"列顯示",columnSetting:"列設定",fullScreen:"全螢幕",exitFullScreen:"退出全螢幕",reload:"重新整理",density:"密度",densityDefault:"正常",densityLarger:"寬鬆",densityMiddle:"中等",densitySmall:"緊湊"},stepsForm:{next:"下一步",prev:"上一步",submit:"完成"},loginForm:{submitText:"登入"},editableTable:{onlyOneLineEditor:"只能同時編輯一行",action:{save:"保存",cancel:"取消",delete:"刪除",add:"新增一行資料"}},switch:{open:"打開",close:"關閉"}}),je=fe("fr_FR",{moneySymbol:"€",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"Réinitialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"Réduire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"Sélectionner une valeur"},alert:{clear:"Réinitialiser",selected:"Sélectionné",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"éléments"}},tableToolBar:{leftPin:"Épingler à gauche",rightPin:"Épingler à gauche",noPin:"Sans épingle",leftFixedTitle:"Fixer à gauche",rightFixedTitle:"Fixer à droite",noFixedTitle:"Non fixé",reset:"Réinitialiser",columnDisplay:"Affichage colonne",columnSetting:"Réglages",fullScreen:"Plein écran",exitFullScreen:"Quitter Plein écran",reload:"Rafraichir",density:"Densité",densityDefault:"Par défaut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Précédente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de données"}},switch:{open:"ouvert",close:"près"}}),Me=fe("pt_BR",{moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar à esquerda",rightPin:"Fixar à direita",noPin:"Desfixado",leftFixedTitle:"Fixado à esquerda",rightFixedTitle:"Fixado à direita",noFixedTitle:"Não fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configurações",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padrão",densityLarger:"Largo",densityMiddle:"Médio",densitySmall:"Compacto"},stepsForm:{next:"Próximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}}),Be=fe("ko_KR",{moneySymbol:"₩",form:{lightFilter:{more:"더보기",clear:"초기화",confirm:"확인",itemUnit:"건수"}},tableForm:{search:"조회",reset:"초기화",submit:"제출",collapsed:"확장",expand:"닫기",inputPlaceholder:"입력해 주세요",selectPlaceholder:"선택해 주세요"},alert:{clear:"취소",selected:"선택",item:"건"},pagination:{total:{range:" ",total:"/ 총",item:"건"}},tableToolBar:{leftPin:"왼쪽으로 핀",rightPin:"오른쪽으로 핀",noPin:"핀 제거",leftFixedTitle:"왼쪽으로 고정",rightFixedTitle:"오른쪽으로 고정",noFixedTitle:"비고정",reset:"초기화",columnDisplay:"컬럼 표시",columnSetting:"설정",fullScreen:"전체 화면",exitFullScreen:"전체 화면 취소",reload:"새로 고침",density:"여백",densityDefault:"기본",densityLarger:"많은 여백",densityMiddle:"중간 여백",densitySmall:"좁은 여백"},stepsForm:{next:"다음",prev:"이전",submit:"종료"},loginForm:{submitText:"로그인"},editableTable:{action:{save:"저장",cancel:"취소",delete:"삭제",add:"데이터 행 추가"}},switch:{open:"열",close:"가까 운"}}),Oe=fe("id_ID",{moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}}),ze=fe("de_DE",{moneySymbol:"€",form:{lightFilter:{more:"Mehr",clear:"Zurücksetzen",confirm:"Bestätigen",itemUnit:"Einträge"}},tableForm:{search:"Suchen",reset:"Zurücksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte auswählen"},alert:{clear:"Zurücksetzen",selected:"Ausgewählt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Einträgen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zurücksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Größer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zurück",submit:"Abschließen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"Löschen",add:"Hinzufügen einer Datenzeile"}},switch:{open:"offen",close:"schließen"}}),Ee=fe("fa_IR",{moneySymbol:"تومان",form:{lightFilter:{more:"بیشتر",clear:"پاک کردن",confirm:"تایید",itemUnit:"مورد"}},tableForm:{search:"جستجو",reset:"بازنشانی",submit:"تایید",collapsed:"نمایش بیشتر",expand:"نمایش کمتر",inputPlaceholder:"پیدا کنید",selectPlaceholder:"انتخاب کنید"},alert:{clear:"پاک سازی",selected:"انتخاب",item:"مورد"},pagination:{total:{range:" ",total:"از",item:"مورد"}},tableToolBar:{leftPin:"سنجاق به چپ",rightPin:"سنجاق به راست",noPin:"سنجاق نشده",leftFixedTitle:"ثابت شده در چپ",rightFixedTitle:"ثابت شده در راست",noFixedTitle:"شناور",reset:"بازنشانی",columnDisplay:"نمایش همه",columnSetting:"تنظیمات",fullScreen:"تمام صفحه",exitFullScreen:"خروج از حالت تمام صفحه",reload:"تازه سازی",density:"تراکم",densityDefault:"پیش فرض",densityLarger:"بزرگ",densityMiddle:"متوسط",densitySmall:"کوچک"},stepsForm:{next:"بعدی",prev:"قبلی",submit:"اتمام"},loginForm:{submitText:"ورود"},editableTable:{action:{save:"ذخیره",cancel:"لغو",delete:"حذف",add:"یک ردیف داده اضافه کنید"}},switch:{open:"باز",close:"نزدیک"}}),Ie=fe("tr_TR",{moneySymbol:"₺",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"Öğeler"}},tableForm:{search:"Filtrele",reset:"Sıfırla",submit:"Gönder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek için bir değer girin",selectPlaceholder:"Filtrelemek için bir değer seçin"},alert:{clear:"Temizle",selected:"Seçili",item:"Öğe"},pagination:{total:{range:" ",total:"Toplam",item:"Öğe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sağa sabitle",noPin:"Sabitlemeyi kaldır",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sağa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"Sıfırla",columnDisplay:"Kolon Görünümü",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan Çık",reload:"Yenile",density:"Kalınlık",densityDefault:"Varsayılan",densityLarger:"Büyük",densityMiddle:"Orta",densitySmall:"Küçük"},stepsForm:{next:"Sıradaki",prev:"Önceki",submit:"Gönder"},loginForm:{submitText:"Giriş Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazgeç",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"açık",close:"kapatmak"}}),Le=fe("pl_PL",{moneySymbol:"zł",form:{lightFilter:{more:"Więcej",clear:"Wyczyść",confirm:"Potwierdź",itemUnit:"Ilość"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierdź",collapsed:"Pokaż wiecej",expand:"Pokaż mniej",inputPlaceholder:"Proszę podać",selectPlaceholder:"Proszę wybrać"},alert:{clear:"Wyczyść",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpisów"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypięte do lewej",rightFixedTitle:"Przypięte do prawej",noFixedTitle:"Nieprzypięte",reset:"Reset",columnDisplay:"Wyświetlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pełen ekran",exitFullScreen:"Zamknij pełen ekran",reload:"Odśwież",density:"Odstęp",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zurück",submit:"Abschließen"},loginForm:{submitText:"Zaloguj się"},editableTable:{action:{save:"Zapisać",cancel:"Anuluj",delete:"Usunąć",add:"dodawanie wiersza danych"}},switch:{open:"otwierać",close:"zamykać"}}),Re=fe("hr_",{moneySymbol:"kn",form:{lightFilter:{more:"Više",clear:"Očisti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretraži",reset:"Poništi",submit:"Potvrdi",collapsed:"Raširi",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"Očisti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prikači lijevo",rightPin:"Prikači desno",noPin:"Bez prikačenja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Izađi iz punog zaslona",reload:"Ponovno učitaj",density:"Veličina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljedeći",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obriši",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}}),He=fe("th_TH",{moneySymbol:"฿",deleteThisLine:"ลบบรรทัดนี้",copyThisLine:"คัดลอกบรรทัดนี้",form:{lightFilter:{more:"มากกว่า",clear:"ชัดเจน",confirm:"ยืนยัน",itemUnit:"รายการ"}},tableForm:{search:"สอบถาม",reset:"รีเซ็ต",submit:"ส่ง",collapsed:"ขยาย",expand:"ทรุด",inputPlaceholder:"กรุณาป้อน",selectPlaceholder:"โปรดเลือก"},alert:{clear:"ชัดเจน",selected:"เลือกแล้ว",item:"รายการ"},pagination:{total:{range:" ",total:"ของ",item:"รายการ"}},tableToolBar:{leftPin:"ปักหมุดไปทางซ้าย",rightPin:"ปักหมุดไปทางขวา",noPin:"เลิกตรึงแล้ว",leftFixedTitle:"แก้ไขด้านซ้าย",rightFixedTitle:"แก้ไขด้านขวา",noFixedTitle:"ไม่คงที่",reset:"รีเซ็ต",columnDisplay:"การแสดงคอลัมน์",columnSetting:"การตั้งค่า",fullScreen:"เต็มจอ",exitFullScreen:"ออกจากโหมดเต็มหน้าจอ",reload:"รีเฟรช",density:"ความหนาแน่น",densityDefault:"ค่าเริ่มต้น",densityLarger:"ขนาดใหญ่ขึ้น",densityMiddle:"กลาง",densitySmall:"กะทัดรัด"},stepsForm:{next:"ถัดไป",prev:"ก่อนหน้า",submit:"เสร็จ"},loginForm:{submitText:"เข้าสู่ระบบ"},editableTable:{onlyOneLineEditor:"แก้ไขได้เพียงบรรทัดเดียวเท่านั้น",action:{save:"บันทึก",cancel:"ยกเลิก",delete:"ลบ",add:"เพิ่มแถวของข้อมูล"}},switch:{open:"เปิด",close:"ปิด"}}),Ne=fe("cs_cz",{moneySymbol:"Kč",deleteThisLine:"Smazat tento řádek",copyThisLine:"Kopírovat tento řádek",form:{lightFilter:{more:"Víc",clear:"Vymazat",confirm:"Potvrdit",itemUnit:"Položky"}},tableForm:{search:"Hledat",reset:"Resetovat",submit:"Odeslat",collapsed:"Zvětšit",expand:"Zmenšit",inputPlaceholder:"Zadejte prosím",selectPlaceholder:"Vyberte prosím"},alert:{clear:"Vymazat",selected:"Vybráno",item:"Položka"},pagination:{total:{range:" ",total:"z",item:"položek"}},tableToolBar:{leftPin:"Připnout doleva",rightPin:"Připnout doprava",noPin:"Odepnuto",leftFixedTitle:"Fixováno nalevo",rightFixedTitle:"Fixováno napravo",noFixedTitle:"Nefixováno",reset:"Resetovat",columnDisplay:"Zobrazení sloupců",columnSetting:"Nastavení",fullScreen:"Celá obrazovka",exitFullScreen:"Ukončit celou obrazovku",reload:"Obnovit",density:"Hustota",densityDefault:"Výchozí",densityLarger:"Větší",densityMiddle:"Střední",densitySmall:"Kompaktní"},stepsForm:{next:"Další",prev:"Předchozí",submit:"Dokončit"},loginForm:{submitText:"Přihlásit se"},editableTable:{onlyOneLineEditor:"Upravit lze pouze jeden řádek",action:{save:"Uložit",cancel:"Zrušit",delete:"Vymazat",add:"Přidat řádek"}},switch:{open:"Otevřít",close:"Zavřít"}}),De=fe("sk_SK",{moneySymbol:"€",deleteThisLine:"Odstrániť tento riadok",copyThisLine:"Skopírujte tento riadok",form:{lightFilter:{more:"Viac",clear:"Vyčistiť",confirm:"Potvrďte",itemUnit:"Položky"}},tableForm:{search:"Vyhladať",reset:"Resetovať",submit:"Odoslať",collapsed:"Rozbaliť",expand:"Zbaliť",inputPlaceholder:"Prosím, zadajte",selectPlaceholder:"Prosím, vyberte"},alert:{clear:"Vyčistiť",selected:"Vybraný",item:"Položka"},pagination:{total:{range:" ",total:"z",item:"položiek"}},tableToolBar:{leftPin:"Pripnúť vľavo",rightPin:"Pripnúť vpravo",noPin:"Odopnuté",leftFixedTitle:"Fixované na ľavo",rightFixedTitle:"Fixované na pravo",noFixedTitle:"Nefixované",reset:"Resetovať",columnDisplay:"Zobrazenie stĺpcov",columnSetting:"Nastavenia",fullScreen:"Celá obrazovka",exitFullScreen:"Ukončiť celú obrazovku",reload:"Obnoviť",density:"Hustota",densityDefault:"Predvolené",densityLarger:"Väčšie",densityMiddle:"Stredné",densitySmall:"Kompaktné"},stepsForm:{next:"Ďalšie",prev:"Predchádzajúce",submit:"Potvrdiť"},loginForm:{submitText:"Prihlásiť sa"},editableTable:{onlyOneLineEditor:"Upravovať možno iba jeden riadok",action:{save:"Uložiť",cancel:"Zrušiť",delete:"Odstrániť",add:"pridať riadok údajov"}},switch:{open:"otvoriť",close:"zavrieť"}}),_e=fe("he_IL",{moneySymbol:"₪",deleteThisLine:"מחק שורה זו",copyThisLine:"העתק שורה זו",form:{lightFilter:{more:"יותר",clear:"נקה",confirm:"אישור",itemUnit:"פריטים"}},tableForm:{search:"חיפוש",reset:"איפוס",submit:"שלח",collapsed:"הרחב",expand:"כווץ",inputPlaceholder:"אנא הכנס",selectPlaceholder:"אנא בחר"},alert:{clear:"נקה",selected:"נבחר",item:"פריט"},pagination:{total:{range:" ",total:"מתוך",item:"פריטים"}},tableToolBar:{leftPin:"הצמד לשמאל",rightPin:"הצמד לימין",noPin:"לא מצורף",leftFixedTitle:"מוצמד לשמאל",rightFixedTitle:"מוצמד לימין",noFixedTitle:"לא מוצמד",reset:"איפוס",columnDisplay:"תצוגת עמודות",columnSetting:"הגדרות",fullScreen:"מסך מלא",exitFullScreen:"צא ממסך מלא",reload:"רענן",density:"רזולוציה",densityDefault:"ברירת מחדל",densityLarger:"גדול",densityMiddle:"בינוני",densitySmall:"קטן"},stepsForm:{next:"הבא",prev:"קודם",submit:"סיום"},loginForm:{submitText:"כניסה"},editableTable:{onlyOneLineEditor:"ניתן לערוך רק שורה אחת",action:{save:"שמור",cancel:"ביטול",delete:"מחיקה",add:"הוסף שורת נתונים"}},switch:{open:"פתח",close:"סגור"}}),Xe=fe("uk_UA",{moneySymbol:"₴",deleteThisLine:"Видатили рядок",copyThisLine:"Скопіювати рядок",form:{lightFilter:{more:"Ще",clear:"Очистити",confirm:"Ок",itemUnit:"Позиції"}},tableForm:{search:"Пошук",reset:"Очистити",submit:"Відправити",collapsed:"Розгорнути",expand:"Згорнути",inputPlaceholder:"Введіть значення",selectPlaceholder:"Оберіть значення"},alert:{clear:"Очистити",selected:"Обрано",item:"елементів"},pagination:{total:{range:" ",total:"з",item:"елементів"}},tableToolBar:{leftPin:"Закріпити зліва",rightPin:"Закріпити справа",noPin:"Відкріпити",leftFixedTitle:"Закріплено зліва",rightFixedTitle:"Закріплено справа",noFixedTitle:"Не закріплено",reset:"Скинути",columnDisplay:"Відображення стовпців",columnSetting:"Налаштування",fullScreen:"Повноекранний режим",exitFullScreen:"Вийти з повноекранного режиму",reload:"Оновити",density:"Розмір",densityDefault:"За замовчуванням",densityLarger:"Великий",densityMiddle:"Середній",densitySmall:"Стислий"},stepsForm:{next:"Наступний",prev:"Попередній",submit:"Завершити"},loginForm:{submitText:"Вхіх"},editableTable:{onlyOneLineEditor:"Тільки один рядок може бути редагований одночасно",action:{save:"Зберегти",cancel:"Відмінити",delete:"Видалити",add:"додати рядок"}},switch:{open:"Відкрито",close:"Закрито"}}),Ve=fe("uz_UZ",{moneySymbol:"UZS",form:{lightFilter:{more:"Yana",clear:"Tozalash",confirm:"OK",itemUnit:"Pozitsiyalar"}},tableForm:{search:"Qidirish",reset:"Qayta tiklash",submit:"Yuborish",collapsed:"Yig‘ish",expand:"Kengaytirish",inputPlaceholder:"Qiymatni kiriting",selectPlaceholder:"Qiymatni tanlang"},alert:{clear:"Tozalash",selected:"Tanlangan",item:"elementlar"},pagination:{total:{range:" ",total:"dan",item:"elementlar"}},tableToolBar:{leftPin:"Chapga mahkamlash",rightPin:"O‘ngga mahkamlash",noPin:"Mahkamlashni olib tashlash",leftFixedTitle:"Chapga mahkamlangan",rightFixedTitle:"O‘ngga mahkamlangan",noFixedTitle:"Mahkamlashsiz",reset:"Qayta tiklash",columnDisplay:"Ustunni ko‘rsatish",columnSetting:"Sozlamalar",fullScreen:"To‘liq ekran",exitFullScreen:"To‘liq ekrandan chiqish",reload:"Yangilash",density:"O‘lcham",densityDefault:"Standart",densityLarger:"Katta",densityMiddle:"O‘rtacha",densitySmall:"Kichik"},stepsForm:{next:"Keyingi",prev:"Oldingi",submit:"Tugatish"},loginForm:{submitText:"Kirish"},editableTable:{action:{save:"Saqlash",cancel:"Bekor qilish",delete:"O‘chirish",add:"maʼlumotlar qatorini qo‘shish"}},switch:{open:"Ochish",close:"Yopish"}}),We=fe("nl_NL",{moneySymbol:"€",deleteThisLine:"Verwijder deze regel",copyThisLine:"Kopieer deze regel",form:{lightFilter:{more:"Meer filters",clear:"Wissen",confirm:"Bevestigen",itemUnit:"item"}},tableForm:{search:"Zoeken",reset:"Resetten",submit:"Indienen",collapsed:"Uitvouwen",expand:"Inklappen",inputPlaceholder:"Voer in",selectPlaceholder:"Selecteer"},alert:{clear:"Selectie annuleren",selected:"Geselecteerd",item:"item"},pagination:{total:{range:"Van",total:"items/totaal",item:"items"}},tableToolBar:{leftPin:"Vastzetten aan begin",rightPin:"Vastzetten aan einde",noPin:"Niet vastzetten",leftFixedTitle:"Vastzetten aan de linkerkant",rightFixedTitle:"Vastzetten aan de rechterkant",noFixedTitle:"Niet vastzetten",reset:"Resetten",columnDisplay:"Kolomweergave",columnSetting:"Kolominstellingen",fullScreen:"Volledig scherm",exitFullScreen:"Verlaat volledig scherm",reload:"Vernieuwen",density:"Dichtheid",densityDefault:"Normaal",densityLarger:"Ruim",densityMiddle:"Gemiddeld",densitySmall:"Compact"},stepsForm:{next:"Volgende stap",prev:"Vorige stap",submit:"Indienen"},loginForm:{submitText:"Inloggen"},editableTable:{onlyOneLineEditor:"Slechts één regel tegelijk bewerken",action:{save:"Opslaan",cancel:"Annuleren",delete:"Verwijderen",add:"Een regel toevoegen"}},switch:{open:"Openen",close:"Sluiten"}}),Ue=fe("ro_RO",{moneySymbol:"RON",deleteThisLine:"Șterge acest rând",copyThisLine:"Copiază acest rând",form:{lightFilter:{more:"Mai multe filtre",clear:"Curăță",confirm:"Confirmă",itemUnit:"elemente"}},tableForm:{search:"Caută",reset:"Resetează",submit:"Trimite",collapsed:"Extinde",expand:"Restrânge",inputPlaceholder:"Introduceți",selectPlaceholder:"Selectați"},alert:{clear:"Anulează selecția",selected:"Selectat",item:"elemente"},pagination:{total:{range:"De la",total:"elemente/total",item:"elemente"}},tableToolBar:{leftPin:"Fixează la început",rightPin:"Fixează la sfârșit",noPin:"Nu fixa",leftFixedTitle:"Fixează în stânga",rightFixedTitle:"Fixează în dreapta",noFixedTitle:"Nu fixa",reset:"Resetează",columnDisplay:"Afișare coloane",columnSetting:"Setări coloane",fullScreen:"Ecran complet",exitFullScreen:"Ieși din ecran complet",reload:"Reîncarcă",density:"Densitate",densityDefault:"Normal",densityLarger:"Larg",densityMiddle:"Mediu",densitySmall:"Compact"},stepsForm:{next:"Pasul următor",prev:"Pasul anterior",submit:"Trimite"},loginForm:{submitText:"Autentificare"},editableTable:{onlyOneLineEditor:"Se poate edita doar un rând simultan",action:{save:"Salvează",cancel:"Anulează",delete:"Șterge",add:"Adaugă un rând"}},switch:{open:"Deschide",close:"Închide"}}),$e=fe("sv_SE",{moneySymbol:"SEK",deleteThisLine:"Radera denna rad",copyThisLine:"Kopiera denna rad",form:{lightFilter:{more:"Fler filter",clear:"Rensa",confirm:"Bekräfta",itemUnit:"objekt"}},tableForm:{search:"Sök",reset:"Återställ",submit:"Skicka",collapsed:"Expandera",expand:"Fäll ihop",inputPlaceholder:"Vänligen ange",selectPlaceholder:"Vänligen välj"},alert:{clear:"Avbryt val",selected:"Vald",item:"objekt"},pagination:{total:{range:"Från",total:"objekt/totalt",item:"objekt"}},tableToolBar:{leftPin:"Fäst till vänster",rightPin:"Fäst till höger",noPin:"Inte fäst",leftFixedTitle:"Fäst till vänster",rightFixedTitle:"Fäst till höger",noFixedTitle:"Inte fäst",reset:"Återställ",columnDisplay:"Kolumnvisning",columnSetting:"Kolumninställningar",fullScreen:"Fullskärm",exitFullScreen:"Avsluta fullskärm",reload:"Ladda om",density:"Täthet",densityDefault:"Normal",densityLarger:"Lös",densityMiddle:"Medium",densitySmall:"Kompakt"},stepsForm:{next:"Nästa steg",prev:"Föregående steg",submit:"Skicka"},loginForm:{submitText:"Logga in"},editableTable:{onlyOneLineEditor:"Endast en rad kan redigeras åt gången",action:{save:"Spara",cancel:"Avbryt",delete:"Radera",add:"Lägg till en rad"}},switch:{open:"Öppna",close:"Stäng"}}),Ke={"mn-MN":me,"ar-EG":ge,"zh-CN":he,"en-US":be,"en-GB":ve,"vi-VN":ye,"it-IT":xe,"ja-JP":Se,"es-ES":Ce,"ca-ES":Ae,"ru-RU":Te,"sr-RS":ke,"ms-MY":Pe,"zh-TW":Fe,"zh-HK":we,"fr-FR":je,"pt-BR":Me,"ko-KR":Be,"id-ID":Oe,"de-DE":ze,"fa-IR":Ee,"tr-TR":Ie,"pl-PL":Le,"hr-HR":Re,"th-TH":He,"cs-CZ":Ne,"sk-SK":De,"he-IL":_e,"uk-UA":Xe,"uz-UZ":Ve,"nl-NL":We,"ro-RO":Ue,"sv-SE":$e},Ge=Object.keys(Ke),Ze=n(56287),qe=n.n(Ze),Qe=n(48381),Je=n(33820),Ye=n(46774),et=function(){for(var e={},t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];for(var r,i=n.length,l=0;l<i;l+=1)for(r in n[l])n[l].hasOwnProperty(r)&&("object"!==(0,Ye.A)(e[r])||"object"!==(0,Ye.A)(n[l][r])||void 0===e[r]||null===e[r]||Array.isArray(e[r])||Array.isArray(n[l][r])?e[r]=n[l][r]:e[r]=(0,a.A)((0,a.A)({},e[r]),n[l][r]));return e},tt=(n(55843),n(13274)),nt=n(68558),ot=["locale","getPrefixCls"],rt=["locale","theme"],it=function(e){var t={};if(Object.keys(e||{}).forEach(function(n){void 0!==e[n]&&(t[n]=e[n])}),!(Object.keys(t).length<1))return t},at=function(){var e,t;return void 0===nt||"TEST"!==(null===(e="production")?void 0:e.toUpperCase())&&"DEV"!==(null===(t="production")?void 0:t.toUpperCase())},lt=u().createContext({intl:(0,a.A)((0,a.A)({},he),{},{locale:"default"}),valueTypeMap:{},theme:Je.emptyTheme,hashed:!0,dark:!1,token:Je.defaultToken}),ct=(lt.Consumer,function(){var e=re().cache;return(0,d.useEffect)(function(){return function(){e.clear()}},[]),null}),st=function(e){var t,n=e.children,o=e.dark,s=e.valueTypeMap,u=e.autoClearCache,p=void 0!==u&&u,f=e.token,m=e.prefixCls,g=e.intl,h=(0,d.useContext)(c.Ay.ConfigContext),b=h.locale,v=h.getPrefixCls,y=(0,i.A)(h,ot),x=null===(t=Qe.JM.useToken)||void 0===t?void 0:t.call(Qe.JM),S=(0,d.useContext)(lt),C=m?".".concat(m):".".concat(v(),"-pro"),A="."+v(),T="".concat(C),k=(0,d.useMemo)(function(){return e=f||{},t=x.token||Je.defaultToken,c=(0,a.A)({},e),(0,a.A)((0,a.A)({bgLayout:"linear-gradient(".concat(t.colorBgContainer,", ").concat(t.colorBgLayout," 28%)"),colorTextAppListIcon:t.colorTextSecondary,appListIconHoverBgColor:null==c||null===(n=c.sider)||void 0===n?void 0:n.colorBgMenuItemSelected,colorBgAppListIconHover:(0,Qe.X9)(t.colorTextBase,.04),colorTextAppListIconHover:t.colorTextBase},c),{},{header:(0,a.A)({colorBgHeader:(0,Qe.X9)(t.colorBgElevated,.6),colorBgScrollHeader:(0,Qe.X9)(t.colorBgElevated,.8),colorHeaderTitle:t.colorText,colorBgMenuItemHover:(0,Qe.X9)(t.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorBgMenuElevated:"rgba(255, 255, 255, 0.6)"!==(null==c||null===(o=c.header)||void 0===o?void 0:o.colorBgHeader)?null===(r=c.header)||void 0===r?void 0:r.colorBgHeader:t.colorBgElevated,colorTextMenuSelected:(0,Qe.X9)(t.colorTextBase,.95),colorBgRightActionsItemHover:(0,Qe.X9)(t.colorTextBase,.03),colorTextRightActionsItem:t.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextMenuActive:t.colorText},c.header),sider:(0,a.A)({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:0,colorBgCollapsedButton:t.colorBgElevated,colorTextCollapsedButtonHover:t.colorTextSecondary,colorTextCollapsedButton:(0,Qe.X9)(t.colorTextBase,.25),colorMenuBackground:"transparent",colorMenuItemDivider:(0,Qe.X9)(t.colorTextBase,.06),colorBgMenuItemHover:(0,Qe.X9)(t.colorTextBase,.03),colorBgMenuItemSelected:(0,Qe.X9)(t.colorTextBase,.04),colorTextMenuItemHover:t.colorText,colorTextMenuSelected:(0,Qe.X9)(t.colorTextBase,.95),colorTextMenuActive:t.colorText,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextSubMenuSelected:(0,Qe.X9)(t.colorTextBase,.95)},c.sider),pageContainer:(0,a.A)({colorBgPageContainer:"transparent",paddingInlinePageContainerContent:(null===(i=c.pageContainer)||void 0===i?void 0:i.marginInlinePageContainerContent)||40,paddingBlockPageContainerContent:(null===(l=c.pageContainer)||void 0===l?void 0:l.marginBlockPageContainerContent)||32,colorBgPageContainerFixed:t.colorBgElevated},c.pageContainer)});var e,t,n,o,r,i,l,c},[f,x.token]),P=(0,d.useMemo)(function(){var e,t=null==b?void 0:b.locale,n=function(e){var t=(e||"zh-CN").toLocaleLowerCase();return Ge.find(function(e){return e.toLocaleLowerCase().includes(t)})}(t),r=null!=g?g:t&&"default"===(null===(e=S.intl)||void 0===e?void 0:e.locale)?Ke[n]:S.intl||Ke[n];return(0,a.A)((0,a.A)({},S),{},{dark:null!=o?o:S.dark,token:et(S.token,x.token,{proComponentsCls:C,antCls:A,themeId:x.theme.id,layout:k}),intl:r||he})},[null==b?void 0:b.locale,S,o,x.token,x.theme.id,C,A,k,g]),F=(0,a.A)((0,a.A)({},P.token||{}),{},{proComponentsCls:C}),w=(0,l.hV)(x.theme,[x.token,null!=F?F:{}],{salt:T,override:F}),j=(0,r.A)(w,2),M=j[0],B=j[1],O=(0,d.useMemo)(function(){return!1!==e.hashed&&!1!==S.hashed},[S.hashed,e.hashed]),z=(0,d.useMemo)(function(){return!1===e.hashed||!1===S.hashed||!1===at()?"":x.hashId?x.hashId:B},[B,S.hashed,e.hashed]);(0,d.useEffect)(function(){qe().locale((null==b?void 0:b.locale)||"zh-cn")},[null==b?void 0:b.locale]);var E=(0,d.useMemo)(function(){return(0,a.A)((0,a.A)({},y.theme),{},{hashId:z,hashed:O&&at()})},[y.theme,z,O,at()]),I=(0,d.useMemo)(function(){return(0,a.A)((0,a.A)({},P),{},{valueTypeMap:s||(null==P?void 0:P.valueTypeMap),token:M,theme:x.theme,hashed:O,hashId:z})},[P,s,M,x.theme,O,z]),L=(0,d.useMemo)(function(){return(0,tt.jsx)(c.Ay,(0,a.A)((0,a.A)({},y),{},{theme:E,children:(0,tt.jsx)(lt.Provider,{value:I,children:(0,tt.jsxs)(tt.Fragment,{children:[p&&(0,tt.jsx)(ct,{}),n]})})}))},[y,E,I,p,n]);return p?(0,tt.jsx)(de,{value:{provider:function(){return new Map}},children:L}):L},dt=function(e){var t=e.needDeps,n=e.dark,r=e.token,l=(0,d.useContext)(lt),u=(0,d.useContext)(c.Ay.ConfigContext),p=u.locale,f=u.theme,m=(0,i.A)(u,rt);if(t&&void 0!==l.hashId&&"children-needDeps"===Object.keys(e).sort().join("-"))return(0,tt.jsx)(tt.Fragment,{children:e.children});var g,h=(0,a.A)((0,a.A)({},m),{},{locale:p||s.A,theme:it((0,a.A)((0,a.A)({},f),{},{algorithm:(g=null!=n?n:l.dark,g&&!Array.isArray(null==f?void 0:f.algorithm)?[null==f?void 0:f.algorithm,Qe.JM.darkAlgorithm].filter(Boolean):g&&Array.isArray(null==f?void 0:f.algorithm)?[].concat((0,o.A)((null==f?void 0:f.algorithm)||[]),[Qe.JM.darkAlgorithm]).filter(Boolean):null==f?void 0:f.algorithm)}))});return(0,tt.jsx)(c.Ay,(0,a.A)((0,a.A)({},h),{},{children:(0,tt.jsx)(st,(0,a.A)((0,a.A)({},e),{},{token:r}))}))};lt.displayName="ProProvider";var ut=lt},69283:function(e,t,n){"use strict";n.d(t,{A:function(){return o}});var o=n(49582).A},74205:function(e,t,n){"use strict";var o=n(43815).default,r=n(35932).default;Object.defineProperty(t,"__esModule",{value:!0}),t.generate=function e(t,n,o){if(!o)return u.default.createElement(t.tag,(0,i.default)({key:n},f(t.attrs)),(t.children||[]).map(function(o,r){return e(o,"".concat(n,"-").concat(t.tag,"-").concat(r))}));return u.default.createElement(t.tag,(0,i.default)((0,i.default)({key:n},f(t.attrs)),o),(t.children||[]).map(function(o,r){return e(o,"".concat(n,"-").concat(t.tag,"-").concat(r))}))},t.getSecondaryColor=function(e){return(0,l.generate)(e)[0]},t.iconStyles=void 0,t.isIconDefinition=function(e){return"object"===(0,a.default)(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===(0,a.default)(e.icon)||"function"==typeof e.icon)},t.normalizeAttrs=f,t.normalizeTwoToneColors=function(e){if(!e)return[];return Array.isArray(e)?e:[e]},t.useInsertStyles=t.svgBaseProps=void 0,t.warning=function(e,t){(0,d.default)(e,"[@ant-design/icons] ".concat(t))};var i=r(n(47127)),a=r(n(9952)),l=n(36467),c=n(36376),s=n(3764),d=r(n(83205)),u=o(n(41594)),p=r(n(30722));function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,n){var o,r=e[n];if("class"===n)t.className=r,delete t.class;else delete t[n],t[(o=n,o.replace(/-(.)/g,function(e,t){return t.toUpperCase()}))]=r;return t},{})}t.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"};var m=t.iconStyles="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";t.useInsertStyles=function(e){var t=(0,u.useContext)(p.default),n=t.csp,o=t.prefixCls,r=t.layer,i=m;o&&(i=i.replace(/anticon/g,o)),r&&(i="@layer ".concat(r," {\n").concat(i,"\n}")),(0,u.useEffect)(function(){var t=e.current,o=(0,s.getShadowRoot)(t);(0,c.updateCSS)(i,"@ant-design-icons",{prepend:!r,csp:n,attachTo:o})},[])}},80004:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"}},82846:function(e,t,n){"use strict";var o=n(43815).default,r=n(35932).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n(32748)),a=o(n(41594)),l=r(n(93303)),c=r(n(11875)),s=function(e,t){return a.createElement(c.default,(0,i.default)({},e,{ref:t,icon:l.default}))},d=a.forwardRef(s);t.default=d},89977:function(e,t,n){"use strict";var o=n(35932).default,r=n(43815).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n(51257)),a=o(n(47127)),l=r(n(41594)),c=n(74205),s=["icon","className","onClick","style","primaryColor","secondaryColor"],d={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};var u=function(e){var t=e.icon,n=e.className,o=e.onClick,r=e.style,u=e.primaryColor,p=e.secondaryColor,f=(0,i.default)(e,s),m=l.useRef(),g=d;if(u&&(g={primaryColor:u,secondaryColor:p||(0,c.getSecondaryColor)(u)}),(0,c.useInsertStyles)(m),(0,c.warning)((0,c.isIconDefinition)(t),"icon should be icon definiton, but got ".concat(t)),!(0,c.isIconDefinition)(t))return null;var h=t;return h&&"function"==typeof h.icon&&(h=(0,a.default)((0,a.default)({},h),{},{icon:h.icon(g.primaryColor,g.secondaryColor)})),(0,c.generate)(h.icon,"svg-".concat(h.name),(0,a.default)((0,a.default)({className:n,onClick:o,style:r,"data-icon":h.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},f),{},{ref:m}))};u.displayName="IconReact",u.getTwoToneColors=function(){return(0,a.default)({},d)},u.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;d.primaryColor=t,d.secondaryColor=n||(0,c.getSecondaryColor)(t),d.calculated=!!n};t.default=u},93303:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"}}}]);