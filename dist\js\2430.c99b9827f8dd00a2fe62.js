"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[2430],{42430:function(e,l,a){a.r(l),a.d(l,{default:function(){return m}});var r={items_per_page:"/ стр.",jump_to:"Перейти",jump_to_confirm:"подтвердить",page:"Страница",prev_page:"Назад",next_page:"Вперед",prev_5:"Предыдущие 5",next_5:"Следующие 5",prev_3:"Предыдущие 3",next_3:"Следующие 3",page_size:"размер страницы"},t=a(70989),n=a(44340),o=(0,t.A)((0,t.A)({},n.I),{},{locale:"ru_RU",today:"Сегодня",now:"Сейчас",backToToday:"Текущая дата",ok:"ОК",clear:"Очистить",week:"Неделя",month:"Месяц",year:"Год",timeSelect:"Выбрать время",dateSelect:"Выбрать дату",monthSelect:"Выбрать месяц",yearSelect:"Выбрать год",decadeSelect:"Выбрать десятилетие",dateFormat:"D-M-YYYY",dateTimeFormat:"D-M-YYYY HH:mm:ss",previousMonth:"Предыдущий месяц (PageUp)",nextMonth:"Следующий месяц (PageDown)",previousYear:"Предыдущий год (Control + left)",nextYear:"Следующий год (Control + right)",previousDecade:"Предыдущее десятилетие",nextDecade:"Следущее десятилетие",previousCentury:"Предыдущий век",nextCentury:"Следующий век"});var i={placeholder:"Выберите время",rangePlaceholder:["Время начала","Время окончания"]};const c={lang:Object.assign({placeholder:"Выберите дату",yearPlaceholder:"Выберите год",quarterPlaceholder:"Выберите квартал",monthPlaceholder:"Выберите месяц",weekPlaceholder:"Выберите неделю",rangePlaceholder:["Начальная дата","Конечная дата"],rangeYearPlaceholder:["Начальный год","Год окончания"],rangeMonthPlaceholder:["Начальный месяц","Конечный месяц"],rangeWeekPlaceholder:["Начальная неделя","Конечная неделя"],shortWeekDays:["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],shortMonths:["Янв","Фев","Мар","Апр","Май","Июн","Июл","Авг","Сен","Окт","Ноя","Дек"]},o),timePickerLocale:Object.assign({},i)};const s="${label} не является типом ${type}";var m={locale:"ru",Pagination:r,DatePicker:c,TimePicker:i,Calendar:c,global:{placeholder:"Пожалуйста выберите"},Table:{filterTitle:"Фильтр",filterConfirm:"OK",filterReset:"Сбросить",filterEmptyText:"Без фильтров",filterCheckAll:"Выбрать все элементы",filterSearchPlaceholder:"Поиск в фильтрах",emptyText:"Нет данных",selectAll:"Выбрать всё",selectInvert:"Инвертировать выбор",selectNone:"Очистить все данные",selectionAll:"Выбрать все данные",sortTitle:"Сортировка",expand:"Развернуть строку",collapse:"Свернуть строку",triggerDesc:"Нажмите для сортировки по убыванию",triggerAsc:"Нажмите для сортировки по возрастанию",cancelSort:"Нажмите, чтобы отменить сортировку"},Tour:{Next:"Далее",Previous:"Назад",Finish:"Завершить"},Modal:{okText:"OK",cancelText:"Отмена",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Отмена"},Transfer:{titles:["",""],searchPlaceholder:"Поиск",itemUnit:"элем.",itemsUnit:"элем.",remove:"Удалить",selectAll:"Выбрать все данные",deselectAll:"Очистить все данные",selectCurrent:"Выбрать текущую страницу",selectInvert:"Инвертировать выбор",removeAll:"Удалить все данные",removeCurrent:"Удалить текущую страницу"},Upload:{uploading:"Загрузка...",removeFile:"Удалить файл",uploadError:"При загрузке произошла ошибка",previewFile:"Предпросмотр файла",downloadFile:"Загрузить файл"},Empty:{description:"Нет данных"},Icon:{icon:"иконка"},Text:{edit:"Редактировать",copy:"Копировать",copied:"Скопировано",expand:"Раскрыть",collapse:"Свернуть"},Form:{optional:"(необязательно)",defaultValidateMessages:{default:"Ошибка проверки поля ${label}",required:"Пожалуйста, введите ${label}",enum:"${label} должен быть одним из [${enum}]",whitespace:"${label} не может быть пустым",date:{format:"${label} не правильный формат даты",parse:"${label} не может быть преобразовано в дату",invalid:"${label} не является корректной датой"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label} должна быть ${len} символов",min:"${label} должна быть больше или равна ${min} символов",max:"${label} должна быть меньше или равна ${max} символов",range:"Длина ${label} должна быть между ${min}-${max} символами"},number:{len:"${label} должна быть равна ${len}",min:"${label} должна быть больше или равна ${min}",max:"${label} должна быть меньше или равна ${max}",range:"${label} должна быть между ${min}-${max}"},array:{len:"Количество элементов ${label} должно быть равно ${len}",min:"Количество элементов ${label} должно быть больше или равно ${min}",max:"Количество элементов ${label} должно быть меньше или равно ${max}",range:"Количество элементов ${label} должно быть между ${min} и ${max}"},pattern:{mismatch:"${label} не соответствует шаблону ${pattern}"}}},Image:{preview:"Предпросмотр"},QRCode:{expired:"QR-код устарел",refresh:"Обновить"},ColorPicker:{presetEmpty:"Пустой",transparent:"Прозрачный",singleColor:"Один цвет",gradientColor:"Градиент"}}}}]);