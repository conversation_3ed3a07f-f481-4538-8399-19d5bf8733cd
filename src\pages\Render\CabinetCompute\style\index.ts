import { createStyles } from '@svg/antd/es/theme/utils'

export default createStyles(({ css }: any) => {
  return {
    panelContainer: css`
      background-color: #fff;
      border-radius: 8px !important;
      position: relative;
      .swj-baseComponent-Containersbox-title
      {
        background-color: #fff !important;
      }
    `,
    content: css`
      .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell
      {
        background-color: #fff !important;
      }
    `,
    title: css`
      color: #282828;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
    `,
    tab: css`
      display: flex;
      flex-direction: row;
      gap: 10px;
      margin: 16px 0px;
      color: #282828;
      .tabItem
      {
        display: flex;
        width: 64px;
        height: 28px;
        padding: 0px 0px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
      }
      .selected
      {
        background: #F2F3F4;
        font-weight: 600;
      }
    `,
    table: css`
      .ant-table-thead .ant-table-selection-column .ant-checkbox-wrapper
      {
         display: none;
      }
      .ant-table-thead .ant-table-cell
      {
        background: #F2F3F4;
      }
      .ant-table-container
      {
        border: 2px solid #F2F3F4;
      }
    `,
    bottom: css`
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 16px;
      background-color: #fff;
      width: 95%;
      font-size: 16px;
    `,
    bottomRight: css`
      font-size: 16px;
      font-weight: 600;
    `
  }
})