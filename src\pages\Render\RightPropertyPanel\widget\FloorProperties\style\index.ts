
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      position: relative;
      overflow-y: scroll;
      text-align: center;
      display: flex;
      justify-content: space-between;
    `,
    rootItem: css`
      display: flex;
      justify-content: space-between;
      padding: 5px 12px;
      input {
        width:35px;
        margin:0;
        border:0px;
      }
      input::-webkit-outer-spin-button,  
      input::-webkit-inner-spin-button{  
        -webkit-appearance: none !important;  
        margin: 0;  
      }
      select {
        border:0;
      }
      .unit {
        font-size:10px;
        right:14px;
        line-height:15px;
      }
    `,
    clearWholeHouse: css`
      border-radius: 4px;
      background: #F4F5F5;
      border: 1px solid #F4F5F5;
      width: 90%;
      height: 24px;
      text-align: center;
      border: none;
      cursor: pointer;
      margin-bottom:15px;
    `
  }
});
