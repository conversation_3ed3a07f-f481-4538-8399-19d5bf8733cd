import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    shijiaoBarContainer: css`
      z-index: 99;
      display: flex;
      justify-content: center;
      backdrop-filter: blur(50px);
      width: 100%;
      max-width: 100%;
      overflow: hidden;
      
      .selectInfo {
        display: flex;
        gap: 8px;
        height: 64px;
        overflow-x: auto;
        overflow-y: hidden;
        scroll-behavior: smooth;
        padding: 0 4px;
        width: 100%;
        max-width: 100%;
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
          display: none;
        }
        
        /* Firefox 隐藏滚动条 */
        scrollbar-width: none;
        -ms-overflow-style: none;
        
        /* 确保内容可以横向滚动 */
        flex-wrap: nowrap;
        align-items: center;
        
        .shijiaoItem {
          width: 80px;
          height: 60px;
          position: relative;
          border: 2px solid #FFFFFF;
          border-radius: 4px;
          flex-shrink: 0; /* 防止项目被压缩 */
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          
          img {
            width: 80px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            display: block;
          }
          
          .title {
            position: absolute;
            bottom: 0px;
            left: 0;
            width: 100%;
            height: 20px;
            border-radius: 0 0 4px 4px;
            background: rgba(0, 0, 0, 0.40);
            backdrop-filter: blur(2px);
            padding: 0 11px;
            color: #fff;
            font-size: 12px;
            font-weight: 400;
            font-family: PingFang SC;
            font-style: normal;
            line-height: 166.7%;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        
        .shijiaoItem:hover {
          background-color: #ffffff1a;
          transform: scale(1.02);
          transition: all 0.3s ease;
        }
        
        .shijiaoItem:active {
          transform: scale(0.98);
        }
      }
    `,
  }
});