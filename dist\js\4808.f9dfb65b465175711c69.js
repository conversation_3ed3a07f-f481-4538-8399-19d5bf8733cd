"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4808],{54808:function(e,t,n){var r=n(98612),i=n(67869),a=n(39657),o=n(8206),l=n(79489),s=n(87441),u=n(84781),c=n(27347),h=n(69076),f=n(88934),d=n(778),y=n(21462);function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function g(e){return function(e){if(Array.isArray(e))return v(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){var n,r,i,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){a=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){a.label=l[1];break}if(6===l[0]&&a.label<i[1]){a.label=i[1],i=l;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(l);break}i[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var w=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),_(this,"_manager",void 0),_(this,"_scene3D",void 0),_(this,"UpdateScene3DWithMaterials",function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e=function(){var e,n,r,i,a;return D(this,function(o){switch(o.label){case 0:return e=this.container,c.nb.emit_M(f.U.SceneContentStateChanged,{state:0}),this.updateScene3D(t),c.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!0,title:"更新3D场景中..."}),n=[],e._rooms.forEach(function(e){e._cabinetStyleId&&(n.includes(e._cabinetStyleId)||n.push(e._cabinetStyleId))}),h.p.instance.clearCache(n),r=t?e._rooms.map(function(e){return d.N.instance.updateRoomFurnitureDesignMaterials(e,null,t)}):[],i=d.N.instance.updateWindowsFurnitureDesignMaterials(e._window_entities.filter(function(e){var t;return!(null==e||null===(t=e._win_figure_element)||void 0===t?void 0:t._solid_mesh3D)})),[4,Promise.allSettled(g(r).concat([i]))];case 1:return o.sent(),(a=c.nb.instance.scene3D).setLightMode(a.getLightMode()),a.entity_mesh_mode=a.entity_mesh_mode,c.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!1,title:""}),a.update(),c.nb.emit_M(f.U.updateAllMaterialScene3D,{state:1}),[2]}})},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){p(a,r,i,o,l,"next",e)}function l(e){p(a,r,i,o,l,"throw",e)}o(void 0)})}).call(n)}),this._manager=t}var t,n,r;return t=e,n=[{key:"initScene3D",value:function(e){this._scene3D=new s.G}},{key:"container",get:function(){return this._manager.layout_container}},{key:"scene3D",get:function(){return this._scene3D}},{key:"bindMainDiv",value:function(e){this.scene3D&&this.scene3D.bindMainDiv(e)}},{key:"onElementUpdate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(b(e,a.tQ))if((0,l.MP)([e.category,e.sub_category],["墙面","地面"])){var n=e._matched_material,r=e._solid_mesh3D;if(r&&b(r,i.eaF)&&(n.imageUrl&&h.p.updateMeshTextureWithImg(r,n.imageUrl,n.modelId),(0,l.MP)([e.sub_category,e.category],["地面"]))){var o=e.fill_color||"#aaa";r.material.color=new i.Q1f(o)}}else{var s=e._solid_mesh3D;if(s){var c=s.userData[u.HN.MaterialInfo];e.pictureViewImg&&c&&c.ContentUrl?(t.isWhite,e.updateRenderedTopViewImg()):e.shouldUseRenderedTopViewImg()&&e.updateRenderedTopViewImg();var f,d=this.scene3D;d&&(d.outlinePostProcessing.makeOutlineDirty(),(null===(f=d.selection_box)||void 0===f?void 0:f.userData[u.HN.EntityOfMesh])==e&&d.setSelectionBox(e))}}}},{key:"bindOnSelectFigure",value:function(e){var t;(null===(t=this.scene3D)||void 0===t?void 0:t.raycasteControls)&&(this.scene3D.raycasteControls.onSelectedFigure=e)}},{key:"bindOnSelectRoom",value:function(e){var t;(null===(t=this.scene3D)||void 0===t?void 0:t.raycasteControls)&&(this.scene3D.raycasteControls.onSelectedRoom=e)}},{key:"updateScene3D",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.scene3D){var n=this.container;if(t){this.scene3D.reset(),this.scene3D.cleanWalls(),this.scene3D.cleanWindows(),this.scene3D.cleanFurnitures(),this.scene3D.cleanRoomEntities(),this.scene3D.updateShadowTexture(),this.updateFillLights(!0);var r=!0,i=!1,a=void 0;try{for(var s,c=n._window_entities[Symbol.iterator]();!(r=(s=c.next()).done);r=!0){var h=s.value.updateMesh3D();h&&this.scene3D.windows_group.add(h)}}catch(e){i=!0,a=e}finally{try{r||null==c.return||c.return()}finally{if(i)throw a}}var f=!0,d=!1,v=void 0;try{for(var p,m=n._wall_entities[Symbol.iterator]();!(f=(p=m.next()).done);f=!0){var _=p.value.updateMesh3D();_&&this.scene3D.walls_group.add(_)}}catch(e){d=!0,v=e}finally{try{f||null==m.return||m.return()}finally{if(d)throw v}}var D=!0,w=!1,S=void 0;try{for(var M,I=n._room_entities[Symbol.iterator]();!(D=(M=I.next()).done);D=!0){var k=M.value;this.scene3D.rooms_group.add(k.updateMesh3D())}}catch(e){w=!0,S=e}finally{try{D||null==I.return||I.return()}finally{if(w)throw S}}var A=!0,x=!1,O=void 0;try{for(var E,C=n._furniture_entities[Symbol.iterator]();!(A=(E=C.next()).done);A=!0){var T=E.value;if(b(T,o.E)){var U=T;U.figure_element.haveMatchedGroupMaterial()&&!U.figure_element.isMaterialMarkAsInvisible()?this.scene3D.furniture_group.add(U.updateMesh3D()):U.combination_entitys.forEach(function(t){t.figure_element.isMaterialMarkAsInvisible()||e.scene3D.furniture_group.add(t.updateMesh3D())})}else{var F=T.updateMesh3D();F&&!T.figure_element.isMaterialMarkAsInvisible()&&this.scene3D.furniture_group.add(F)}}}catch(e){x=!0,O=e}finally{try{A||null==C.return||C.return()}finally{if(x)throw O}}if(n._rooms){var L=[];n._rooms.forEach(function(e){var t;(t=L).push.apply(t,g(e._furniture_list.filter(function(e){return(0,l.MP)([e.category],["主灯"])})))}),this.scene3D.addNightLights(L)}var N=this.scene3D.entity_mesh_mode;this.scene3D.entity_mesh_mode=N,this.scene3D.active_controls&&this.scene3D.active_controls.updateMeshesVisible()}else{var P=[this.scene3D.furniture_group,this.scene3D.walls_group,this.scene3D.windows_group],R=!0,j=!1,V=void 0;try{for(var W,G=P[Symbol.iterator]();!(R=(W=G.next()).done);R=!0){var H,Q=W.value,B=[],J=!0,$=!1,q=void 0;try{for(var z,K=Q.children[Symbol.iterator]();!(J=(z=K.next()).done);J=!0){var X=z.value,Y=X.userData[u.HN.EntityOfMesh];Y&&this.container.containsEntity(Y)||B.push(X)}}catch(e){$=!0,q=e}finally{try{J||null==K.return||K.return()}finally{if($)throw q}}B.forEach(function(e){return y.J.disposeObject(e)}),(H=Q).remove.apply(H,g(B))}}catch(e){j=!0,V=e}finally{try{R||null==G.return||G.return()}finally{if(j)throw V}}this.scene3D.updateShadowTexture();var Z=!0,ee=!1,te=void 0;try{for(var ne,re=n._window_entities[Symbol.iterator]();!(Z=(ne=re.next()).done);Z=!0){var ie=ne.value.updateMesh3D();ie&&this.scene3D.windows_group.add(ie)}}catch(e){ee=!0,te=e}finally{try{Z||null==re.return||re.return()}finally{if(ee)throw te}}var ae=!0,oe=!1,le=void 0;try{for(var se,ue=n._wall_entities[Symbol.iterator]();!(ae=(se=ue.next()).done);ae=!0){var ce=se.value.updateMesh3D();ce&&this.scene3D.walls_group.add(ce)}}catch(e){oe=!0,le=e}finally{try{ae||null==ue.return||ue.return()}finally{if(oe)throw le}}var he=!0,fe=!1,de=void 0;try{for(var ye,ve=n._room_entities[Symbol.iterator]();!(he=(ye=ve.next()).done);he=!0){var pe=ye.value;this.scene3D.walls_group.add(pe.updateMesh3D())}}catch(e){fe=!0,de=e}finally{try{he||null==ve.return||ve.return()}finally{if(fe)throw de}}var me=!0,_e=!1,be=void 0;try{for(var ge,De=n._furniture_entities[Symbol.iterator]();!(me=(ge=De.next()).done);me=!0){var we=ge.value.updateMesh3D();we&&this.scene3D.furniture_group.add(we)}}catch(e){_e=!0,be=e}finally{try{me||null==De.return||De.return()}finally{if(_e)throw be}}var Se=this.scene3D.entity_mesh_mode;this.scene3D.entity_mesh_mode=Se,this.scene3D.active_controls&&this.scene3D.active_controls.updateMeshesVisible()}}}},{key:"updateFillLights",value:function(){this.scene3D.cleanFillLights();var e=this.container.getFillLightEntities(),t=!0,n=!1,r=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var o=i.value;this.scene3D.aiLightsGroupOffline.add(o.update3D()),this.scene3D.aiLightsGroupTest.add(o.lightMesh)}}catch(e){n=!0,r=e}finally{try{t||null==a.return||a.return()}finally{if(n)throw r}}}}],n&&m(t.prototype,n),r&&m(t,r),e}(),S=n(39454);c.nb.RegisterApp(r.e.AppName,function(){return new r.e}),c.nb.RegisterApp(S.I.AppName,function(){return new S.I}),c.nb.NewApp(r.e.AppName),c.nb.createScene3dManager=function(){return new w(c.nb.instance)},globalThis&&(globalThis.LayoutAI_App=c.nb)}}]);