import { EventName } from "@/Apps/EventSystem";
import { BaseModeHandler } from "@/Apps/LayoutAI/Handlers/BaseModeHandler";
import { TRoomNameType } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { I_FigureGroup, TFigureGroup } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TFigureGroup";
import { TGroupTemplate } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TSeriesFigureGroupDB } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TSeriesFigureGroupDB";
import { I_MouseEvent, ZDistanceDimension, ZRect, makeDimensionsOfEdge } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TrainingManager } from "../TrainingManager";


export class FigureTemplateTestingHandler extends BaseModeHandler {

    _seriesId: string;


    _figure_group: TFigureGroup;
    _group_template: TGroupTemplate;


    _target_rect: ZRect;

    _last_target_pos: Vector3;

    _seed_figure_group_dict: { [key: string]: I_FigureGroup };

    _use_same_group_code : boolean = true;
    constructor(manager: TrainingManager) {
        super(manager, "FigureTemplateTesting");

        this._seriesId = "default";

        this._figure_group = null;

        this._target_rect = new ZRect(1, 1);

        this._seed_figure_group_dict = {};
    }

    get manager(): TrainingManager {
        return this._manager as TrainingManager;
    }
    makeFigureGroup() {

    }



    async updateDBOnRangeSizeChanged() {
        this._seed_figure_group_dict = TSeriesFigureGroupDB.GetSeedGroupDict(this._seriesId);
        for (let code in this._seed_figure_group_dict) {
            let db = TSeriesFigureGroupDB.GetGroupDB(code, this._seriesId);

            if (db) {
                db.generateBySizeRange();
            }
        }

        await TSeriesFigureGroupDB.SaveSeries(this._seriesId);

    }
    updateLeftLabelList() {

        if (this._seed_figure_group_dict) {
            let label_list = [
            ];
            let space_code_dict = TSeriesFigureGroupDB.instance.space_code_dict;
            for(let key in space_code_dict)
            {
                let group_codes = space_code_dict[key];
                label_list.push({
                    label : key,
                    name : key,
                    subList : group_codes.map((code)=>{return {
                        label : code,
                        name : code,
                        command_name : code
                    }})
                })

            }
            
            // label_list.sort((a, b) => (a.label as string).localeCompare(b.label));
            this.manager.EventSystem.emit(EventName.TrainingLabelsListHandle, label_list);
        }
        else {
            let label_list = [
                {
                    label: "转角沙发",
                    name: TRoomNameType.LivingRoom,
                    command_name: "Analyze:" + TRoomNameType.LivingRoom
                }
            ];
            this.manager.EventSystem.emit(EventName.TrainingLabelsListHandle, label_list);
        }
        let scope = this;
        this.manager.EventSystem.emit(EventName.AttributeHandle, {
            mode: 'init', // 当前模式，支持edit ,hide 
            title: '编辑', // 当前面板的标题
            properties: {
                auto_generation_0: {
                    name: '更新尺寸链',
                    widget: 'ButtonItem',
                    onClick: () => {
                        scope.updateDBbyAutoGeneration();
                    }
                },
                use_all_group_code : {
                    name : "开关-相同分区",
                    widget: 'ButtonItem',
                    onClick: () => {
                        this._use_same_group_code = !this._use_same_group_code;
                        window.confirm(this._use_same_group_code?"仅请求同种组合":"请求同分区组合");
                    }
                }
            },
        });

    }

    clearLeftLabelList() {
        this.manager.EventSystem.emit(EventName.TrainingLabelsListHandle, []);
    }

    updateDBbyAutoGeneration() {

        if(this._group_template?.group_code)
        {
            console.log(this._group_template.group_code, TSeriesFigureGroupDB.GetGroupDB(this._group_template.group_code));

        }

    }

    setFigureGroupWithCode(code: string) {
        if (this._seed_figure_group_dict && this._seed_figure_group_dict[code]) {
            let t_figure_group = this._seed_figure_group_dict[code];

            // this._figure_group = new TFigureGroup(t_figure_group);

            this._group_template = new TGroupTemplate().makeBySeedFigureGroup(t_figure_group);

            this._target_rect._w = this._group_template.current_s_group.group_rect.w;
            this._target_rect._h = this._group_template.current_s_group.group_rect.h;
            // this._target_rect.copy(this._group_template.current_s_group.group_rect);
            this._target_rect.updateRect();

            this.painter.p_center = { x: 0, y: 0, z: 0 };


            this.queryBySizeChanged();
            this.update();

        }
    }
    async onLoadSeriesDB() {
        // await TSerieslSizeRangeDB.LoadSeries(this._seriesId);
        // await TSeriesFigureGroupDB.LoadSeries(this._seriesId);


        this._seed_figure_group_dict = TSeriesFigureGroupDB.GetSeedGroupDict(this._seriesId);



        this.updateLeftLabelList();


        // this.updateDBbyAutoGeneration();

        this.manager.update();

    }
    enter(state?: number): void {

        this.onLoadSeriesDB();
        // this.updateLeftLabelList();

        if (!this._group_template) {
            this.makeFigureGroup();
        }
        // TSeriesFigureGroupDB.SaveSeries(this._seriesId);

    }

    leave(state?: number): void {
        this.manager.EventSystem.emit(EventName.AttributeHandle, {
            mode: 'hide', // 当前模式，支持edit ,hide 
        });
        this.clearLeftLabelList();
    }
    get painter() {
        return this.manager?.painter;
    }
    onmousedown(ev: I_MouseEvent): void {
        if (ev.buttons == 1) {
            let i_pos = { x: ev.posX, y: ev.posY, z: 0 };
            if (this._target_rect) {
                let dist = this._target_rect.vertices[1].pos.distanceTo(i_pos);

                if (dist < 50) {
                    this._last_target_pos = new Vector3().copy(i_pos);
                }
            }
        }
        super.onmousedown(ev);
    }

    onmousemove(ev: I_MouseEvent): void {

        if (this._last_target_pos) {
            let i_pos = { x: ev.posX, y: ev.posY, z: 0 };
            let dx = i_pos.x - this._last_target_pos.x;
            let dy = i_pos.y - this._last_target_pos.y;

            if (this._target_rect) {
                this._target_rect._w += dx;
                this._target_rect._h += dy;
                this._target_rect.updateRect();

                this.queryBySizeChanged();
                this.update();

            }

            this._last_target_pos.copy(i_pos);

            return;
        }
        super.onmousemove(ev);
    }


    onmouseup(ev: I_MouseEvent): void {
        this._last_target_pos = null;
        super.onmouseup(ev);
    }


    queryBySizeChanged() {
        if (this._target_rect) {
            // let size = new TSize(this._target_rect._w,this._target_rect._h,3600);


            // console.log(size);
            // console.log(this._group_template.group_space_category);
            // this._group_template.queryBySize(size);

            this._group_template._target_rect = this._target_rect;
            this._group_template.updateByTargetRect({ query_same_space_category: !this._use_same_group_code });

            // console.log(this._group_template);
        }
    }

    drawCanvas(): void {
        if (this._target_rect) {
            this.painter.strokeStyle = "#000";
            this.painter.strokePolygons([this._target_rect]);


            this.painter.drawPointCircle(this._target_rect.vertices[1].pos, 30, 3);

            let target_rect = this._target_rect;
            let dim = new ZDistanceDimension(target_rect.backEdge.v0.pos, target_rect.backEdge.v1.pos);
            dim.nor = target_rect.backEdge.nor;

            dim.offset_len = 400;

            let dim1 = new ZDistanceDimension(target_rect.edges[0].v0.pos, target_rect.edges[0].v1.pos);
            dim1.nor = target_rect.edges[0].nor;
            dim1.offset_len = 400;

            this.painter.drawDimension(dim);
            this.painter.drawDimension(dim1);
        }

        if (this._group_template) {

            this.painter.strokeStyle = "#000";

            if (this._target_rect) {
                // this._group_template.current_s_group.main_figure._rect._back_center.copy(this._target_rect._back_center);
                let group_rect = this._group_template.current_s_group?.group_rect;
                if (group_rect) {
                    let offset = this._target_rect.vertices[3].pos.clone().sub(group_rect.vertices[3].pos);
                    group_rect = this._group_template.current_s_group.group_rect;
                    let dim = new ZDistanceDimension(group_rect.backEdge.v0.pos, group_rect.backEdge.v1.pos);
                    dim.nor = group_rect.backEdge.nor;
                    dim.offset_len = 100;
                    let dim1 = new ZDistanceDimension(group_rect.edges[0].v0.pos, group_rect.edges[0].v1.pos);
                    dim1.nor = group_rect.edges[0].nor;
                    dim1.offset_len = 100;
                    this.painter.drawDimension(dim);
                    this.painter.drawDimension(dim1);
                }
            }

            // this._group_template.apply();

            let list = this._group_template.current_s_group?.figure_elements;
            list.sort((a, b) => a.z_level - b.z_level);
            for (let ele of list) {
                ele.drawFigure(this.manager.painter);
            }

            let group_rect = this._group_template.current_s_group?.group_rect;
            if (group_rect) {
                this.painter.strokeStyle = "#f00";

                this.painter.strokePolygons([group_rect]);

                let main_rect = this._group_template.current_s_group.main_figure.rect;
                this.painter.strokeStyle = "#000";

                [main_rect.leftEdge,main_rect.frontEdge].forEach((edge)=>{
                    let dim = makeDimensionsOfEdge(edge);
                    dim.offset_len = 50;
                    this.painter.drawDimension(dim);
                })
            }
        }


    }


    runCommand(cmd_name: string): void {
        if (this._seed_figure_group_dict && this._seed_figure_group_dict[cmd_name]) {
            this.setFigureGroupWithCode(cmd_name);
        }
    }


}