import { <PERSON>4, <PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON>, Vector4 } from "three";
import { TExtDrawingEntity } from "./TExtDrawingEntity";
import { TPainter } from "../../../Drawing/TPainter";
import { I_EntityDrawingState } from "../TBaseEntity";
import { I_SwjViewCameraData } from "../../../AICadData/SwjLayoutData";
import { Vec3toMeta, compareNames } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TLayoutEntityContainer } from "../TLayoutEntityContainter";
import { TRoomEntity } from "../TRoomEntity";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TRoomShape } from "../../TRoomShape";
import { TG<PERSON>hBasicConfigs } from "../../TLayoutGraph/TGraphBasicConfigs";
import { TSubSpaceAreaEntity } from "../TSubSpaceAreaEntity";
import { I_Room } from "../../IRoomInterface";
import { ZEdge } from "@layoutai/z_polygon";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { SofaViewCameraRule } from "./ViewCameraRules/SofaViewCameraRule";
import { DiningViewCameraRule } from "./ViewCameraRules/DiningViewCameraRule";
import { BedRoomViewCameraRule } from "./ViewCameraRules/BedRoomViewCameraRule";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { OutlinePostProcess } from "@/Apps/LayoutAI/Scene3D/builder/OutlinePostProcess";
import { OutlineMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { TBaseGroupEntity } from "../TBaseGroupEntity";
import { DeskRoomViewCameraRule } from "./ViewCameraRules/DeskRoomViewCameraRule";
import { KitchenViewCameraRule } from "./ViewCameraRules/KitchenViewCameraRule";
import { BathRoomViewCameraRule } from "./ViewCameraRules/BathRoomViewCameraRule";


export interface IViewCameraGenerateOptions {
    /**
     *  不生成聚焦模式
     *     --- 即只允许在房间里面, 靠视角裁剪相关数据
     */
    no_focus_mode?: boolean;

    /**
     *  0: 默认;  1、[公牛]模式  2、新视角规则      
     */
    methods?: number;

    /**
     *   默认视角高度
     */
    camera_zval?: number;


}
export class TViewCameraEntity extends TExtDrawingEntity {
    _room_entity: TRoomEntity;
    public _is_focus_mode: boolean;

    private _near: number;

    private _far: number;
    private _fov: number;
    _view_img: HTMLImageElement;

    _perspective_img: HTMLImageElement;

    public _view_center: Vector3;

    private _room_info: I_Room;

    private _description: string;

    public _pitch_angle : number = 0;
    public _hide_furniture_entities: TFurnitureEntity[];
    static _test_rect: ZRect = null;
    public _main_rect: ZRect = null;  //主家具矩形
    public _target: string[] = [];  //目标家具
    public _dir: string = '';  //方向
    public _hide_names: string[] = [];   // 主动隐藏家具
    public hideFurnitures: (view_camera: TViewCameraEntity, room_entity: TRoomEntity, options: {hide_wall?: boolean}) => void;
    constructor() {
        super();
        this.realType = "ViewCamera";
        this._room_entity = null;
        this._view_img = new Image();
        this._is_focus_mode = false;
        this._near = 300;
        this._far = 20000;
        this._fov = 65;
        this._perspective_img = new Image();
        this._view_center = null;
        this._description = "";
        this._hide_furniture_entities = [];
        this._main_rect = null;
        this.hideFurnitures = null;
        this._hide_names = [];
    }
    public get hide_furniture_entities(): TFurnitureEntity[] {
        return this._hide_furniture_entities;
    }

    public get is_focus_mode(): boolean {
        return this._is_focus_mode;
    }
    public set is_focus_mode(value: boolean) {
        this._is_focus_mode = value;
    }
    public get near(): number {
        return this._near;
    }
    public set near(value: number) {
        this._near = value;
    }

    public get far(): number {
        return this._far;
    }
    public set far(value: number) {
        this._far = value;
    }

    public get fov(): number {
        return this._fov;
    }
    public set fov(value: number) {
        this._fov = value;
    }

    public get view_center(): Vector3 {
        return this._view_center;
    }
    public set view_center(value: Vector3 | Vector3Like) {
        this._view_center = new Vector3().copy(value);
    }

    public get room_info() {
        if (this._room_entity?._room) {
            this._room_info = this._room_entity._room.exportRoomData();
            return this._room_info
        }
        else if (this._room_info) {
            return this._room_info;
        }
        else {
            return null;
        }
    }
    importData(data: I_SwjViewCameraData): void {

        this.uidN = data.uid ? ~~data.uid : this.uidN;
        this._uuid = data.uuid || this._uuid;
        this.view_center = data.view_center || this.view_center;
        this.near = data.near || this.near;
        this.far = data.far || this.far;
        this.is_focus_mode = data.is_focus_mode || this.is_focus_mode;
        this._room_info = data.room_info || this._room_info;
        this.name = data.name || this.name;
        this._description = data.description || this._description;
        this.update();
    }

    exportData(): I_SwjViewCameraData {
        let data: I_SwjViewCameraData = {} as any as I_SwjViewCameraData;
        data.uid = this.uidN;
        data.uuid = this._uuid;
        data.near = this.near;
        data.far = this.far;
        data.name = this.name;
        data.view_center = Vec3toMeta(this.view_center || new Vector3(0, 0, 0));
        data.pos_z = this.pos_z;
        data.is_focus_mode = this.is_focus_mode;
        data.room_info = this.room_info;
        data.description = this._description;
        data.target = this._target;
        data.dir = this._dir;
        return data;
    }
    dispose(): void {
        super.dispose();
        if (this._hide_furniture_entities) {
            delete this._hide_furniture_entities;
            this._hide_furniture_entities = [];
        }
        if (this._perspective_img) {
            this._perspective_img = null;
        }
    }
    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {
        if (options.is_selected) {
            painter.fillStyle = "#66b8ff";
            painter.fillPolygon(this._rect, 0.5);
        }
        else if (options.is_hovered) {
            painter.fillStyle = "#66b8ff";
            painter.fillPolygon(this._rect, 0.5);
        }
        if (options.is_draw_figure) {
            painter.strokeStyle = "#000";
            painter.strokePolygons([this._rect]);
        }
    }

    updateViewImg(painter: TPainter, width: number = 300, height: number = 300) {
        if (!this._room_entity) return;
        if (this._room_entity._room) {
            let canvasElement = document.createElement("canvas");
            let currentRoom = this._room_entity._room;

            if (!currentRoom) return;
            let previous_canvas = painter._canvas;
            let ts = painter.exportTransformData();

            painter.bindCanvas(canvasElement);
            painter.clean();
            canvasElement.width = width;
            canvasElement.height = height;
            painter.p_center = this._view_center || this.rect.rect_center;

            painter._p_sc = 0.05;
            currentRoom._painter = painter;
            painter.enter_drawpoly();


            if (currentRoom._room_entity && currentRoom) {
                currentRoom._room_entity.drawRoomWithWalls(painter);
            }
            else {
                let previous_painter = currentRoom._painter;
                currentRoom._painter = painter;
                currentRoom.drawRoomWithWalls(30);
                currentRoom.drawRoomWindows("Layout");
                currentRoom._painter = previous_painter;
            }


            let figure_elements = [...this._room_entity._room._furniture_list];
            figure_elements.sort((a, b) => a.default_drawing_order - b.default_drawing_order);
            for (let ele of figure_elements) {
                if (ele.sub_category.indexOf("地毯") >= 0) continue;
                if (ele._decoration_type) continue;

                if (ele.furnitureEntity) {
                    let elements = ele.furnitureEntity.disassembled_figure_elements;
                    elements.forEach(ele => ele.drawPreviewFigure(painter, false));
                }
                else {
                    ele.drawPreviewFigure(painter, false);
                }
            }
            painter.drawFigureRect(this.rect, "相机", []);
            painter.leave_drawpoly();


            this._view_img.src = canvasElement.toDataURL();

            painter.bindCanvas(previous_canvas);
            painter.importTransformData(ts, false);

        }
    }

    updatePerspectiveViewImg(painter: TPainter): Promise<void> {
        let scope = this;
        const s3d = LayoutAI_App.instance.scene3D as Scene3D;
        const outlinePostProcess = s3d.outlinePostProcessing as OutlinePostProcess;

        return new Promise((resolve) => {
            let current_canvas = s3d.renderer.domElement as HTMLCanvasElement;

            scope._perspective_img.src = null;
            scope._perspective_img.src = current_canvas.toDataURL();
            scope._perspective_img.onload = (img) => {
                // 恢复材质下的线框模式
                // outlinePostProcess.setDefaultEnabled(true);
                // outlinePostProcess.setEnabled(true);
                // outlinePostProcess.makeOutlineDirty();
                resolve();
            }
            // setTimeout(() => {
            //     resolve();
            // }, 100);
        });
    }

    static getViewCameraEntites(container: TLayoutEntityContainer) {
        let view_cameras = container._ext_drawing_entities.filter((entity) => entity.realType === "ViewCamera");
        return view_cameras;
    }
    static getViewCameraEntityByUuid(uuid: string, container: TLayoutEntityContainer) {
        let view_cameras = container._ext_drawing_entities.filter((entity) => entity.realType === "ViewCamera" && entity._uuid === uuid) as TViewCameraEntity[];
        return view_cameras[0] || null;
    }

    static updateViewCameraEntities(container: TLayoutEntityContainer, room_entities: TRoomEntity[] = null, options: IViewCameraGenerateOptions = {}) {
        let view_cameras = container._ext_drawing_entities.filter((entity) => entity.realType === "ViewCamera");
        view_cameras.forEach((entity) => container._ext_drawing_entities.splice(container._ext_drawing_entities.indexOf(entity), 1));

        view_cameras = [];
        room_entities = [...container._room_entities];

        room_entities.sort((a, b) => b._area - a._area);
        room_entities.forEach((room_entity: TRoomEntity) => {
            let camera_entities = TViewCameraEntity.updateViewCamerasInRoomEntity(room_entity, options);
            view_cameras.push(...camera_entities);
            room_entity._view_cameras = [...camera_entities];
        })

        container._ext_drawing_entities.push(...view_cameras);
    }

    static updateViewCamerasInRoomEntity(room_entity: TRoomEntity, options: IViewCameraGenerateOptions = {}) {


        let view_cameras: TViewCameraEntity[] = [];

        let target_names = ["衣柜", "餐边柜", "浴室柜", "马桶", "床", "淋浴房", "炉灶地柜", "水槽地柜"];

        let ignore_names = ["背景墙"];
        let main_rect = room_entity._main_rect;

        /**
         * 保持分区矩形大小不变，new_rect的back_center与figure_rect一致
         * @param figure_rect 
         * @param area_rect 
         * @param label 
         * @returns 
         */
        const _local_computeTargetRect = (figure_rect: ZRect, area_rect: ZRect, label: string) => {
            let new_rect = ZRect.fromPoints(area_rect.positions, figure_rect.nor);
            let pp = new_rect.project(figure_rect.back_center);
            let pos = new_rect.unproject({ x: 0, y: pp.y });
            new_rect.back_center = pos;
            new_rect.updateRect();
            new_rect.ex_prop.label = label;
            return new_rect;
        }

        /**
         * 公牛模式
         */
        if (options.methods == 1) {
            if (room_entity._room && room_entity._room._furniture_list.length == 0 || room_entity.roomname === "厨房" || room_entity.roomname === "卫生间") {
                view_cameras.push(...TViewCameraEntity.updateViewCameraOfRectInRoom(room_entity._main_rect, room_entity, room_entity.roomname));
                return view_cameras;
            }
            if (room_entity.roomname === "厨房") {
                let view_camera = new TViewCameraEntity();
                view_camera.rect._w = 500;
                view_camera.rect._h = 500;
                view_camera.rect.nor = main_rect.nor;
                view_camera.rect.rect_center = main_rect.rect_center;

                view_camera._room_entity = room_entity;

                view_camera.name = (room_entity.name || room_entity.roomname) + "(中心)";
                let edge = room_entity._room_poly.findLongestEdge(); // 其它空间看最长的墙
                let dir = edge.nor;
                let center = view_camera.rect.rect_center;
                view_camera.rect.nor = dir;
                view_camera.rect.rect_center = center;
                view_camera.bindEntity();
                view_cameras.push(view_camera);
            }
            if (room_entity.roomname === "客餐厅") {
                let areas = room_entity._sub_room_areas;

                if (areas[0]) // 客厅
                {
                    let area_rect = areas[0].rect;


                    let target_rects: ZRect[] = [];
                    let sofa_element = room_entity._room._furniture_list.find(furniture => compareNames([furniture.sub_category, furniture.category], ["沙发"]) && furniture.rect.w > 1300);
                    let tv_element = room_entity._room._furniture_list.find(furniture => compareNames([furniture.sub_category, furniture.category], ["电视"]));

                    if (sofa_element) {
                        target_rects.push(_local_computeTargetRect(sofa_element.rect, area_rect, "沙发"));
                    }
                    if (tv_element) {
                        target_rects.push(_local_computeTargetRect(tv_element.rect, area_rect, "电视墙"));

                    }

                    if (areas[0]._space_area_room) {
                        let windows = areas[0]._space_area_room.windows.filter((win) => compareNames(win.room_names, ["阳台"]));
                        windows.sort((a, b) => b.length - a.length);
                        let balcony_window_rect = windows[0]?.rect;
                        if (balcony_window_rect) {
                            balcony_window_rect = balcony_window_rect.clone();
                            balcony_window_rect._h = 20;
                            balcony_window_rect.rect_center = balcony_window_rect.rect_center;
                            if (balcony_window_rect.w > 1800) {
                                balcony_window_rect.ex_prop.label = "阳台";
                                target_rects.push(balcony_window_rect);
                            }
                            else {
                                target_rects.push(_local_computeTargetRect(balcony_window_rect, area_rect, "阳台"))
                            }
                        }
                    }

                    target_rects.forEach((rect) => {
                        let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(rect, room_entity, "客厅 " + (area_rect.ex_prop?.label || ""), { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                        view_cameras.push(...v_cameras);
                    })

                }

                if (areas[1]) // 餐厅
                {
                    let area_rect = areas[1].rect;
                    // 主要从客厅-看到餐厅即可
                    let pp = area_rect.project(areas[0].rect.rect_center);
                    if (Math.abs(pp.x) > Math.abs(pp.y)) {
                        pp.y = 0;
                    }
                    else {
                        pp.x = 0;
                    }
                    let t_pos = area_rect.unproject(pp);
                    let t_nor = t_pos.clone().sub(area_rect.rect_center).normalize();

                    let new_rect = ZRect.fromPoints(area_rect.positions, t_nor);
                    {
                        let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(new_rect, room_entity, "餐厅", { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                        view_cameras.push(...v_cameras);
                    }
                }

            }
            else if (room_entity.roomname === "卧室") {
                let main_rect = room_entity?._room?._ceilling_list[0]?.rect || room_entity._main_rect; // 默认使用吊顶

                main_rect = main_rect.clone();
                let bed_element = room_entity._room._furniture_list.find((ele) => ele.category && ele.category.endsWith("床"));
                if (bed_element) {
                    main_rect = ZRect.fromPoints(main_rect.positions, bed_element.rect.nor);
                }

                let enter_door = room_entity._room.windows.find((door) => door.type === "Door" && compareNames(door.room_names, ["客餐厅"]));
                if (enter_door?.rect) {
                    let pp = main_rect.project(enter_door.rect.rect_center);
                    if (pp.x < 0) {
                        main_rect._u_dv_flag = -main_rect.u_dv_flag;
                        main_rect.updateRect();
                    }
                }
                {
                    let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(main_rect, room_entity, room_entity.aliasName + "-正对床", { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                    view_cameras.push(...v_cameras);
                }
                {
                    let t_main_rect = ZRect.fromPoints(main_rect.positions, main_rect.dv.clone());
                    let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(t_main_rect, room_entity, room_entity.aliasName + "-侧对床", { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                    view_cameras.push(...v_cameras);
                }

            }
            else {
                if (room_entity._room && room_entity._room._furniture_list) {
                    let target_elements = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], target_names) && !compareNames([ele.category], ignore_names));


                    let room_poly = room_entity._room_poly;
                    target_elements.forEach((ele) => {

                        let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(ele.rect, room_entity, room_entity.roomname + "(" + ele.category + ")", { back_edge_only: true });
                        view_cameras.push(...v_cameras);
                    });
                }
            }

            if (options.camera_zval !== undefined) {
                view_cameras.forEach((camera) => {
                    camera.pos_z = options.camera_zval;
                    // camera._pitch_angle = Math.PI/18;
                });
            }

        } 
        /**
         * 新视角规则
         */
        else if(options.methods == 2) {
            if (room_entity.roomname === "客餐厅") {
                let areas = room_entity._sub_room_areas;
                let sofa_element = room_entity._room._furniture_list.find(furniture => furniture.category && (furniture.category.endsWith("沙发") || furniture.category.endsWith("沙发组合")));
                if (areas[0]) // 客厅
                {
                    let area_rect = areas[0].rect;
                    let target_rects: ZRect[] = [];
                  
                    let tv_element = room_entity._room._furniture_list.find(furniture => compareNames([furniture.sub_category, furniture.category], ["电视"]));
                    if (sofa_element) {
                        target_rects.push(_local_computeTargetRect(sofa_element.rect, area_rect, "沙发"));
                    }
                    if (tv_element) {
                        target_rects.push(_local_computeTargetRect(tv_element.rect, area_rect, "电视墙"));
                    }
                    if (areas[0]._space_area_room) {
                        let windows = areas[0]._space_area_room.windows.filter((win) => compareNames(win.room_names, ["阳台"]));
                        windows.sort((a, b) => b.length - a.length);
                        let balcony_window_rect = windows[0]?.rect;
                        if (balcony_window_rect) {
                            balcony_window_rect = balcony_window_rect.clone();
                            balcony_window_rect._h = 20;
                            balcony_window_rect.rect_center = balcony_window_rect.rect_center;
                            if (balcony_window_rect.w > 1800) {
                                balcony_window_rect.ex_prop.label = "阳台";
                                target_rects.push(balcony_window_rect);
                            }
                            else {
                                target_rects.push(_local_computeTargetRect(balcony_window_rect, area_rect, "阳台"))
                            }
                        }
                    }
                    target_rects.forEach((rect) => {
                        if(rect.ex_prop?.label === "沙发" && sofa_element){
                            let v_cameras = SofaViewCameraRule.generateViewCamera(sofa_element, room_entity, "客厅 " + (area_rect.ex_prop?.label || ""), { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true }, rect);
                            view_cameras.push(...v_cameras);
                        }
                    })
                } else 
                {
                    if(sofa_element)
                    {
                        let v_cameras = SofaViewCameraRule.generateViewCamera(sofa_element, room_entity, "客厅 " + (""), { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true }, null);
                        view_cameras.push(...v_cameras);
                    }
                }
                let dining_element = room_entity._room._furniture_list.find(furniture => compareNames([furniture.sub_category, furniture.category], ["餐桌"])); 
                if(dining_element)
                {
                    let v_cameras = DiningViewCameraRule.generateViewCamera(dining_element, room_entity, "餐厅 " + (""), { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                    view_cameras.push(...v_cameras);
                }

            }
            else if (room_entity.roomname === "卧室") {
                let main_rect = room_entity?._room?._ceilling_list[0]?.rect || room_entity._main_rect; // 默认使用吊顶

                main_rect = main_rect.clone();    
                let bed_element = null;            
                room_entity._room._furniture_list.forEach((ele) => {
                    if(ele.furnitureEntity instanceof TBaseGroupEntity)
                    {
                        ele.furnitureEntity.combination_entitys.forEach((entity: TFurnitureEntity)=>
                        {
                            if(entity.category && entity.category.endsWith("床"))
                            {
                                bed_element = entity.figure_element;
                            }
                        });
                    }
                    else if(ele.category && ele.category.endsWith("床"))
                    {
                        bed_element = ele;
                    }
                });
                let v_cameras = BedRoomViewCameraRule.generateViewCamera(bed_element, room_entity, room_entity.aliasName + "-正对床", { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                view_cameras.push(...v_cameras);
            }
            else if(room_entity.roomname === "书房")
            {
                let desk_element = null;            
                room_entity._room._furniture_list.forEach((ele) => {
                    if(ele.furnitureEntity instanceof TBaseGroupEntity)
                    {
                        ele.furnitureEntity.combination_entitys.forEach((entity: TFurnitureEntity)=>
                        {
                            if(entity.category && entity.category.endsWith("书桌"))
                            {
                                desk_element = entity.figure_element;
                            }
                        });
                    }
                    else if(ele.category && ele.category.endsWith("书桌"))
                    {
                        desk_element = ele;
                    }
                });
                let v_cameras = DeskRoomViewCameraRule.generateViewCamera(desk_element, room_entity, room_entity.aliasName + "-正对书桌", { back_edge_only: true, no_focus_mode: true, enableHideFurnitures: true });
                view_cameras.push(...v_cameras);
            }
            else if(room_entity.roomname === "厨房")
            {
                let v_cameras = KitchenViewCameraRule.generateViewCamera(room_entity, "厨房", {});
                view_cameras.push(...v_cameras);
            }
            else if(room_entity.roomname === "卫生间")
            {
                let v_cameras = BathRoomViewCameraRule.generateViewCamera(room_entity, "卫生间", {});
                view_cameras.push(...v_cameras);
            }
            else {
                if (room_entity._room && room_entity._room._furniture_list) {
                    let target_elements = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], target_names) && !compareNames([ele.category], ignore_names));
                    let room_poly = room_entity._room_poly;
                    target_elements.forEach((ele) => {

                        let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(ele.rect, room_entity, room_entity.roomname + "(" + ele.category + ")", { back_edge_only: true });
                        view_cameras.push(...v_cameras);
                    });
                }
            }
            if (options.camera_zval !== undefined) {
                view_cameras.forEach((camera) => {
                    camera.pos_z = options.camera_zval;
                });
            }
        }
        /**
         * 默认规则
         */
        else {
            for (let rule of TGraphBasicConfigs.SimpleAreaExtractRules) {
                if (rule.room_names && !compareNames(rule.room_names, [room_entity.roomname])) continue;
                let area_rect = TSubSpaceAreaEntity.extractSubAreaRectByRule(rule, room_entity._room);
                if (area_rect) {
                    let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(area_rect, room_entity, rule.area_name);
                    view_cameras.push(...v_cameras);
                }

            }
            if (room_entity._room && room_entity._room._furniture_list) {
                let target_elements = room_entity._room._furniture_list.filter((ele) => compareNames([ele.category], target_names) && !compareNames([ele.category], ignore_names));


                let room_poly = room_entity._room_poly;
                target_elements.forEach((ele) => {

                    let v_cameras = TViewCameraEntity.updateViewCameraOfRectInRoom(ele.rect, room_entity, room_entity.roomname + "(" + ele.category + ")", { back_edge_only: true });
                    view_cameras.push(...v_cameras);
                });
            }

        }




        return view_cameras;
    }

    static updateCameraByRect = (camera: PerspectiveCamera, rect: ZRect) => {

        camera.position.copy(rect.rect_center_3d);
        camera.up.set(0, 0, 1);
        let t_nor = rect.nor.clone();
        t_nor.applyAxisAngle(rect.dv, rect.rotation_x);
        let target = new Vector3().copy(rect.rect_center_3d).add(t_nor.multiplyScalar(10));
        camera.lookAt(target);

        camera.updateProjectionMatrix();
        camera.updateWorldMatrix(true, false);
        camera.updateMatrixWorld();
    }

    static cameraProjectPos = (mvpMatrix: Matrix4, pos: Vector3) => {
        let t_pos = new Vector4(pos.x, pos.y, pos.z, 1.);
        t_pos.applyMatrix4(mvpMatrix);

        t_pos.divideScalar(t_pos.z);

        return t_pos;
    }
    

    static updateCameraByEdge = (camera: PerspectiveCamera, main_rect: ZRect, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}) => {
        let view_cameras: TViewCameraEntity[] = [];
        let room_poly = room_entity._room_poly.clone();

        TRoomShape.optimizePoly(room_poly);

        const min_wall_length = 600;
        const min_hallway_length = 1300;
        let wall_edges = main_rect.edges.filter((edge) => edge.length > min_wall_length);

        if (options.back_edge_only) {
            wall_edges = [main_rect.backEdge];

        }
        let wins = room_entity._room.windows.filter((win) => win.length > 1300);

        if (wins.length > 0) {
            wins.sort((a, b) => b.length - a.length);
            wall_edges.sort((a, b) => Math.abs(a.projectEdge2d(wins[0].rect.rect_center).y) - Math.abs(b.projectEdge2d(wins[0].rect.rect_center).y))
        }
        wall_edges.forEach((edge) => {
            if (!edge) return;
            let int_data = room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: -5 }), edge.nor.clone().negate());
            if (!int_data || !int_data.point) return;

            let target_center = edge.center;
            let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
            if (dist_to_front_wall < min_hallway_length) return;


            let max_dist = dist_to_front_wall + min_hallway_length;
            if (options.no_focus_mode) {
                max_dist = dist_to_front_wall + 300;   // 聚焦模式下, 相机离墙300mm
            }

            let t_dist = Math.min(max_dist, edge.length);
            let t_rect = new ZRect(500, 500);
            t_rect.nor = edge.nor;
            t_rect.zval = 1400; // 默认高度还是高一些1400mm更合理
            let dist_step = 200;

            let iter = 20;
            while (iter--) {
                let pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -t_dist });

                t_rect.rect_center = pos;

                TViewCameraEntity.updateCameraByRect(camera, t_rect);

                let mvp_matrix = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);


                let s_p0 = TViewCameraEntity.cameraProjectPos(mvp_matrix, edge.v0.pos);

                let xx = Math.abs(s_p0.x);
                let yy = Math.abs(s_p0.y);

                let ml = Math.max(xx, yy);

                if (ml < 0.40 || t_dist > max_dist - 10) {
                    break;
                }

                t_dist += dist_step;
                if (t_dist > max_dist) t_dist = max_dist;
            }
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(t_rect);

            view_camera._room_entity = room_entity;
            let view_camera_name = prefix;
            if (!options.back_edge_only) {
                view_camera_name = prefix + ((edge._edge_id + 1) % main_rect.length || main_rect.length);
            }
            view_camera.name = view_camera_name;
            view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;

            if (!view_camera.is_focus_mode) {
                view_camera.near = 600;
            }
            if (options.enableHideFurnitures) {
                let view_rect = view_camera.rect.clone();
                view_rect.back_center = view_rect.rect_center;
                view_rect._h = Math.max(view_rect._h / 2, view_camera.near);
                view_rect.updateRect();

                let furniture_list = room_entity?._room?.furnitureList || [];

                furniture_list.forEach((ele) => {
                    let furnitureEntity = ele.furnitureEntity;
                    if (furnitureEntity && furnitureEntity.height > 2000 && compareNames([ele.sub_category, ele.category], ["窗帘"]) == 0) {
                        let has_int = furnitureEntity.rect.intersect_rect(view_rect);
                        if (has_int) {
                            view_camera._hide_furniture_entities.push(furnitureEntity);
                        }
                    }
                })
            }
            view_camera._view_center = main_rect.rect_center;
            view_camera._room_entity = room_entity;
            view_cameras.push(view_camera);
        })
        return view_cameras;
    }


    static updateViewCameraOfRectInRoom(main_rect: ZRect, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}) {


        // 用一个投影相机, 临时计算
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);

        let view_cameras = TViewCameraEntity.updateCameraByEdge(camera, main_rect, room_entity, prefix, options);

        return view_cameras;
    }

}