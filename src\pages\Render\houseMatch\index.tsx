import React, { useState, useEffect } from 'react';
import useStyles from './styles';
import { useTranslation } from 'react-i18next';
import { message, Skeleton } from '@svg/antd';
import { DreamHouseService, I_DreamSchemeInfo } from '@/Apps/LayoutAI/Services/DreamHouse/DreamHouseService';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { compareNames } from "@layoutai/z_polygon";
import { LayoutAI_App } from '@/Apps/LayoutAI_App';

interface I_TagData {
    name: string;
    checked: boolean;
    enabled?: boolean;
}
interface I_TagsRow {
    title: string;
    tags: I_TagData[];
}
interface I_DreamHouseResult extends I_DreamSchemeInfo {
    fit_score?: number;
}

const HouseMatch: React.FC = () => {
    const { styles } = useStyles();
    const { t } = useTranslation();
    const [TagsList, setTagsList] = useState<I_TagsRow[]>([
        { title: t("归属"), tags: [{ name: t("门店"), checked: false }, { name: t("企业"), checked: false }, { name: t("平台"), checked: true }] },
        { title: t("空间"), tags: [{ name: "客餐厅", checked: false }, { name: "厨房", checked: false }, { name: "卧室", checked: false }] },/*[i18n:ignore]*/
    ]);
    const [showProgress, setShowProgress] = useState<boolean>(false);
    const [dreamHouseResultList, setDreamHouseResultList] = useState<I_DreamHouseResult[]>([]);

    let c_type = "平台";
    let is_init_query_rooms = false;

    const onOpenDreamHouseScheme = (result: I_DreamHouseResult) => {
        const data = {
            origin: "layoutai.api",
            data: {
                schemeId: result.schemeId
            }
        };
        window.parent.postMessage(data, '*');
        console.log("postMessage", JSON.stringify(data));
    }

    const initQueryDreamRooms = async () => {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        if (c_type === "平台")  /*[i18n:ignore]*/
        {
            await manager.layout_graph_solver.queryDreamerRoomsFromServer(manager.layout_container._rooms, "dreamer", true, true);
        }
        else if (c_type == "企业")  /*[i18n:ignore]*/
        {
            await manager.layout_graph_solver.queryDreamerRoomsFromServer(manager.layout_container._rooms, "dreamer", true, false);
        }
        else if (c_type == "门店")  /*[i18n:ignore]*/
        {
            await manager.layout_graph_solver.queryDreamerRoomsFromServer(manager.layout_container._rooms, "dreamer_store", true, false);

        }
        is_init_query_rooms = true;
    }

    const updateQueryResults = async () => {
        if (LayoutAI_App.instance) {
            const manager = LayoutAI_App.instance as TAppManagerBase;

            setShowProgress(true);
            if (!is_init_query_rooms) {
                await initQueryDreamRooms();
            }
            const onSelectedRoomNameTag = (TagsList[1].tags.find((tag) => tag.checked));
            let rooms = [...manager.layout_container._rooms];
            if (onSelectedRoomNameTag) {
                rooms = rooms.filter((room) => compareNames([(room.name || room.roomname) + room._t_id], [onSelectedRoomNameTag.name]));

                if (rooms[0]) {
                    if (manager.layer_CadRoomNameLayer) {
                        manager.layer_CadRoomNameLayer._name_mode = 1;
                    }
                    if (manager.layer_CadRoomNameLayer) {
                        manager.layer_CadRoomNameLayer._name_mode = 0;
                    }
                }
            }


            const scheme_list = manager.layout_graph_solver.queryDreamerRoomsWithLayout(rooms);

            const result_list: I_DreamHouseResult[] = [];
            const query_data = async (scheme_id: string, score = 90) => {

                const info = await DreamHouseService.instance.getSchemeInfo(scheme_id);
                if (info) {
                    (info as I_DreamHouseResult).fit_score = score;
                    result_list.push(info);
                }
            }

            const query_list = scheme_list.map((scheme) => { return query_data(scheme.scheme_id, scheme.score) });

            await Promise.all(query_list);

            result_list.sort((a, b) => (b.fit_score || 0) - (a.fit_score || 0));

            setDreamHouseResultList(result_list);

            setShowProgress(false);
        }
    }

    const onTagClick = async (tag: I_TagData) => {
        if (tag.enabled === false) {
            message.info(t('暂未开放'));

            return;
        }
        if (tag.checked) return;
        // 清空
        // setDreamHouseResultList([]);

        tag.checked = !tag.checked;
        if (tag.checked) {
            for (const row of TagsList) {
                if (row.tags.includes(tag)) {
                    for (const t_tag of row.tags) t_tag.checked = t_tag === tag;
                }
            }
        }
        const c_type_name = (TagsList[0].tags.find((tag) => tag.checked))?.name || "";
        if (c_type_name && c_type_name !== c_type) {
            c_type = c_type_name;
            await initQueryDreamRooms();
        }

        setTagsList([...TagsList]);
        
        // 重置滚动位置到顶部
        const resultContainer = document.getElementById('dreamhouse_result_container');
        if (resultContainer) {
            resultContainer.scrollTop = 0;
        }
        
        updateQueryResults();
    }

    const updateRoomsLayout = () => {
        const manager = LayoutAI_App.instance as TAppManagerBase;
        const container = manager.layout_container;

        for (const funiture of container._furniture_entities) {
            funiture.is_selected = false;
        }

        container.updateRoomsFromEntities();
    }

    const updateLayoutImgAndTags = () => {
        if (LayoutAI_App.instance) {
            const manager = LayoutAI_App.instance as TAppManagerBase;
            if (manager.layout_container) {
                manager.layout_container.updateWholeBox();

                const row = TagsList[1];
                row.tags = [];
                const rooms = [...manager.layout_container._rooms];
                const room_order = ["入户花园", "阳台", "厨房", "卧室", "卫生间", "客餐厅"]; /*[i18n:ignore]*/
                rooms.sort((a, b) => {
                    return (room_order.indexOf(b.roomname) + (b.area / 1000)) - (room_order.indexOf(a.roomname) + (a.area / 1000));
                });
                rooms.forEach((room, index) => room._t_id = (index + 1));

                row.tags = rooms.map((room, index) => { return { name: (room.name || room.roomname) + room._t_id, checked: index == 0 } }) //Object.keys(roomname_dict)).map((roomname,index)=>{return {name:roomname,checked:index==0}})
                setTagsList([...TagsList]);
            }
        }
    }

    function SkeletonBox() {
        const skeletons = Array.from({ length: 9 }).map((_, index) => (
            <div className='Skeleton_content' key={`skeleton_${index}`}>
                <Skeleton.Button key={`skeleton_button_1_${index}`} active={true} />
                <Skeleton.Button key={`skeleton_button_2_${index}`} active={true} />
                <Skeleton.Button key={`skeleton_button_3_${index}`} active={true} />
            </div>

        ));

        return <div className={styles.houseMatch_skeleton}>{skeletons}</div>;
    }

    useEffect(() => {
        updateRoomsLayout();
        updateLayoutImgAndTags();
        updateQueryResults();
    }, []);

    return (
        <div className={styles.houseMatch}>
            <div className={styles.tagListBox}>
                {TagsList.map((row, index) => 
                    <div className='choose_tag_row' key={"tags_row_" + index}>
                        <div className='taglabel'>{row.title}</div>
                        <div className='tagbtns'>
                            {row.tags.map((tag, t_index) => (
                                <div
                                    key={"tag_ele_" + t_index}
                                    className={'tag' + (tag.checked ? " checked" : "")}
                                    onClick={() => onTagClick(tag)}
                                >
                                    {tag.name}
                                </div>))}
                        </div>
                    </div>
                )}
            </div>
            <div className={styles.result_container} id="dreamhouse_result_container">
                {showProgress &&
                    <div className={styles.resultprogress}>         
                        <SkeletonBox />
                    </div>}
                {dreamHouseResultList.length > 0 &&
                    <div className={styles.result_list}>
                        {dreamHouseResultList.map((result, index) => (
                            <div className='result_list_item' key={"dreamhouse_result_" + index} onClick={() => onOpenDreamHouseScheme(result)}>
                                <div className='hximg'>
                                    <div className={'img_element'}>
                                        <img src={result.layout2d} className='layout2d_img'></img>
                                    </div>
                                </div>
                                <img src={result.imagePath + "?x-oss-process=image/resize,w_600"} className='result_img'></img>
                                <div className='result_info'>
                                    <div className='result_title' title={result.schemeName}>{result.schemeName}</div>
                                    <div className={'result_tags' + (result.fit_score > 80 ? " goodFit" : "")}>
                                        {result.fit_score > 80 ? <div className='bestFit'>{ Math.min(Math.round(result.fit_score), 95) + "%" + t("匹配")}</div> :
                                            <div className='defaultFit'>{Math.min(Math.round(result.fit_score), 80) + "%" + t("匹配")}</div>
                                        }
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>}
                {dreamHouseResultList.length === 0 &&
                    <div className={styles.emptymatch}>
                        <div className='emptybox'>
                            <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt='' />
                            <span className="name">{t('暂无方案')}</span>
                        </div>
                    </div>
                }
            </div>
        </div>
    );
};

export default HouseMatch;