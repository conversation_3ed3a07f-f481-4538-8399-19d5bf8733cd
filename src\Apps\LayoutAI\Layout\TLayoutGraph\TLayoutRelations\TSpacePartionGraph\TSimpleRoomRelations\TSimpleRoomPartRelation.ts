import { I_SubSpacePoly, TRoom } from "../../../../TRoom";
import { TLayoutRelation } from "../../TLayoutRelation";
import { TFigureElement } from "../../../../TFigureElements/TFigureElement";
import { TGroupTemplate } from "../../../TGroupTemplate/TGroupTemplate";
import { TFeatureShape } from "../../../../TFeatureShape/TFeatureShape";
import { TLayoutOptimizer } from "../../TLayoutOptimizer/TLayoutOptimizer";
import { WPolygon } from "../../../../TFeatureShape/WPolygon";
import { TGraphBasicConfigs } from "../../../TGraphBasicConfigs";
import { TSimpleRoomPartionGraph } from "../TSimpleRoomPartitionGraph";
import { Vector3 } from "three";
import { TLayoutOptmizerOnWallRules } from "../../TLayoutOptimizer/TLayoutOptimizerOnWallRules";
import { compareNames, ZEdge, ZRect } from "@layoutai/z_polygon";
import { TPostProcessLayout } from "../../TLayoutOptimizer/TPostProcessLayout";
import { FigLayoutState, I_BackWallLayoutRule, I_SpaceLayoutRule } from "../../../TGraphConfigureInterface";
import { TPostLayoutWashingPart } from "../../TLayoutOptimizer/TPostLayoutWashingPart";


export const AttachedBackGroupRuleData = "back_wall_group_rule_data";

export class TSimpleRoomPartRelation extends TLayoutRelation
{
    protected _candidate_figure_list : {figure_elements?:TFigureElement[],
        group_templates?:TGroupTemplate[],
        sub_aeras ?: I_SubSpacePoly[],
        debug_data?:{_isFinetuned?:boolean, scheme_name?:string,_flow_score?:number, [key:string]:any}}[];

    protected _temp_group_templates : TGroupTemplate[];

    constructor()
    {
        super();
    }

    get graph() 
    {
        return this._graph as any as TSimpleRoomPartionGraph;
    }
    precompute(): void {
        this._candidate_figure_list = [];

        if(!this._room) return;

        if(!this._room.room_shape._feature_shape)
        {
            this._room.updateFeatures();

        }

        if(compareNames([this._room.roomname],["书房"]))
        {
            this._room.room_shape._feature_shape.update_W_Polygon(["Window"],false);
        }

        // if(this._room.room_shape._poly.edges.length != 4) return;


        let feature_shape = this._room.room_shape._feature_shape;

        let w_poly = feature_shape._w_poly;

        let layout_rules = TGraphBasicConfigs.SpaceLayoutRules[this._room.roomname];

        if(!layout_rules) return;


        if(w_poly.edges.length < 2) return;


        let main_rect = this._room.max_R_shape.getRect();

        let length = main_rect.w;
        let depth = main_rect.h;
        let pre_length = w_poly.edges[w_poly.edges.length-1].length;

        
        let candidate_rules : {rule:I_SpaceLayoutRule,diff:number}[] = [];
        for(let rule of layout_rules)
        {
            let suitable_size = rule.suitable_size;
            let l_diff = Math.abs(length - suitable_size.length);
            let d_diff = Math.abs(depth -  suitable_size.depth );

            let weight = 1;

            if(rule.suitable_names && this._room.name)
            {
                if(compareNames(rule.suitable_names,[this._room.name,this._room.roomname]))
                {
                    weight =0.2;
                }
            }

            if(suitable_size.pre_length !== undefined)
            {
                let p_diff =Math.abs(suitable_size.pre_length - pre_length);
            }
            {
                candidate_rules.push({rule:rule,diff:(l_diff+d_diff) * weight});
            }
        }
        candidate_rules.sort((a,b)=>a.diff - b.diff);
        this._candidate_figure_list = [];

        for(let candidate of candidate_rules)
        {
            let rule = candidate.rule;
            let  group_templates : TGroupTemplate[] = [];


            group_templates = TSimpleRoomPartRelation.applySpaceLayoutRule(rule,this._room,false);

            if(!group_templates) continue;

            TLayoutOptimizer.optimize_groups_in_shape(feature_shape,group_templates,this._room.roomname,3,true);

            this.postProcess(feature_shape,group_templates);


            TLayoutOptimizer.optimize_groups_in_shape(feature_shape,group_templates,this._room.roomname,3,true);

            // TPostProcessLayout.post_back_to_wall_rule_with_src_poly(feature_shape,group_templates,this._room.roomname);

            if(compareNames([this._room.roomname],["卫生间"]))
            {
                TPostLayoutWashingPart.instance.postInfillLayout(this._room,group_templates);
            }

            let t_group_templates : TGroupTemplate[] = [];

            let occlusion_templates = (this.graph.checkOcclusion(this._room,group_templates)) || [];

            for(let group_tempalte of group_templates)
            {
                if(occlusion_templates.includes(group_tempalte)) continue;
                if(  group_tempalte.updateByTargetRect())
                {
                    t_group_templates.push(group_tempalte);
                }
            }

            if(!this.graph.checkIntegrity(this._room,t_group_templates))
            {
                continue;
            }

            let room = this._room;


            this._candidate_figure_list.push({group_templates:t_group_templates,debug_data:{rule:rule,scheme_name:"逻辑-"+rule.layout_rule_name}});

        }

        this._attempt_num = this._candidate_figure_list.length;

        // console.log(this._candidate_figure_list,this._attempt_num);


    }

    static getTargetEdgeWithRuleData(init_rule:I_BackWallLayoutRule, room:TRoom)
    {
        let feature_shape = room.room_shape._feature_shape;

        let w_poly = feature_shape._w_poly;
        let main_rect = room.max_R_shape.getRect();
        if(init_rule.w_edge_id === undefined && init_rule.main_rect_edge_id === undefined) return null;

        let edge:ZEdge = null;

        if(init_rule.w_edge_id !== undefined)
        {
            let t_id =  (init_rule.w_edge_id + w_poly.edges.length) % w_poly.edges.length;
            edge =  w_poly.edges[ t_id];
        }
        else if(init_rule.main_rect_edge_id !== undefined)
        {
            let main_rect_edge_id = init_rule?.main_rect_edge_id;
            edge = WPolygon.getTargetEdgeAlignOnMainRect(w_poly,main_rect,main_rect_edge_id,room.roomname);
        }

        return edge;
    }

    static applySpaceLayoutRule(rule:I_SpaceLayoutRule,room:TRoom, optimize_on_wall : boolean = true, use_templates_in_room:boolean = false)
    {
        let feature_shape = room.room_shape._feature_shape;

        let w_poly = feature_shape._w_poly;

        let layout_rules = TGraphBasicConfigs.SpaceLayoutRules[room.roomname];

        if(!layout_rules) return;


        if(w_poly.edges.length < 2) return;


        let main_rect = room.max_R_shape.getRect();

        let length = main_rect.w;
        let depth = main_rect.h;
        let pre_length = w_poly.edges[w_poly.edges.length-1].length;
        let  is_valid= true;
        let group_templates : TGroupTemplate[] = [];
        for(let init_rule of rule.initial_rules)
        {
            if(init_rule.w_edge_id === undefined && init_rule.main_rect_edge_id === undefined) continue;

            let edge:ZEdge = null;

            let r_edge :ZEdge = null;
            if(init_rule.w_edge_id !== undefined)
            {
                let t_id =  (init_rule.w_edge_id + w_poly.edges.length) % w_poly.edges.length;
                edge =  w_poly.edges[ t_id];

            }
            else if(init_rule.main_rect_edge_id !== undefined)
            {
                let main_rect_edge_id = init_rule?.main_rect_edge_id;
                edge = WPolygon.getTargetEdgeAlignOnMainRect(w_poly,main_rect,main_rect_edge_id,room.roomname);
                r_edge =  main_rect.edges[ (init_rule.main_rect_edge_id + 4 - 1) % 4];

            }
   
            
            if(!edge) {
                is_valid = false;
                break;
            }

            if(init_rule.min_length)
            {
                if(edge.length < init_rule.min_length)
                {
                    is_valid = false;
                    break;
                }
            }
            if(init_rule.max_length)
            {
                if(edge.length > init_rule.max_length)
                {
                    is_valid = false;
                    break;
                }
            }

            if(init_rule.window_check)
            {
                let win = WPolygon.getWindowOnEdge(edge);
                if(init_rule.window_check == 1) // 必须有窗
                {
                    if(!win)
                    {
                        is_valid = false;
                        break;
                    }
                }
                else if(init_rule.window_check == -1) // 必须没窗
                {
                    if(win) {
                        is_valid = false;
                        break;
                    }

                }

            }

            if(!init_rule.group_array[0]) continue;


            let t_edge = edge;

            let t_len = 0;

            let t_group_templates:TGroupTemplate[] = [];
            let e_l = t_edge.length;
            for(let data of init_rule.group_array)
            {
                let config = TGraphBasicConfigs.MainGroupFigureConfigs[feature_shape._room.roomname][data.group_space_category];

                let group_template = new TGroupTemplate();
                group_template.group_space_category = data.group_space_category;
                if(!config) continue;  

                if(use_templates_in_room && room._group_template_list)
                {
                    for(let room_group_template of room._group_template_list)
                    {
                        if(room_group_template._target_rect._attached_elements[AttachedBackGroupRuleData] == data)
                        {
                            group_template = room_group_template;
                        }
                    }
                }
                let length = data.length;
                if(data.length_val && data.length_val.length > 0)
                {
                    length = eval(data.length_val);
                }

                let r_l = length;
                if(data.start_val && data.start_val.length > 0)
                {
                    t_len = eval(data.start_val);
                }
                
                let target_rect = new ZRect(length,data.depth);
                let ll = t_len;
                let rr = t_len + length;
                if(data.not_on_wall)
                {
                    let t_nor = edge.nor.clone().negate();
                    let dir_type = data.dir_type || 0;

                    dir_type = Math.PI / 180 * dir_type;
                    t_nor.applyAxisAngle({x:0,y:0,z:1} as Vector3,dir_type);

                    target_rect.nor = t_nor;

                    let wall_dir_dist = data.wall_dir_dist || 0;
                    let wall_nor_dist = data.wall_nor_dist || 0;

                    wall_dir_dist += edge.length / 2;

                    let pp = {x:wall_dir_dist,y:-wall_nor_dist};

                    let pos = edge.unprojectEdge2d(pp);

                    target_rect.rect_center = pos;
                }
                else{
                    target_rect.nor = edge.nor.clone().negate();
                
                    let pp = {x:t_len + length/2, y:0};
                    let pos = t_edge.unprojectEdge2d(pp);
                    target_rect.back_center = pos;
                    target_rect.updateRect();
                    t_group_templates.push(group_template);

                    t_len += length;
                }

                
                if(data.need_corner === true) // 这个分组 需要在角落
                {
                    target_rect.u_dv = edge.dv;
                    target_rect.updateRect();



                    if(ll <  Math.abs(edge.length - rr)) // 靠左侧
                    {
                        let prev_edge = edge.prev_edge;
                        if(!prev_edge)
                        {
                            is_valid = false;
                        }
                        else{
                            
                            if(Math.abs(prev_edge.dv.dot(edge.dv)) > 0.1) // 不是垂直
                            {
                                is_valid = false;
                            }
                            if(prev_edge.length < (data.neighbor_wall_min_length || 600)) // 小于最小期望墙长
                            {
                                is_valid = false;
                            }
    
                            if(is_valid)
                            {
                                target_rect.u_dv = edge.dv.clone().negate();

                                // 尽量不镜像的处理, 让其旋转90度
                                if(target_rect.u_dv_flag < 0) 
                                {
                                    let r_center = target_rect.rect_center;
                                    let nor = prev_edge.nor.clone().negate();
                                    target_rect.nor = nor;
                                    target_rect._u_dv_flag = 1;
                                    target_rect.rect_center = r_center;
                                    
                                }
                                
                                target_rect.updateRect();
                            }
                        }
   

                    }
                    else  // 靠右侧
                    {
                        let next_edge = edge.next_edge;
                        if(!next_edge)
                        {
                            is_valid = false;
                        }
                        else{
                            if(Math.abs(next_edge.dv.dot(edge.dv)) > 0.1) // 不是垂直
                            {
                                is_valid = false;
                            }
                            if(next_edge.length < (data.neighbor_wall_min_length || 600)) // 小于最小期望墙长
                            {
                                is_valid = false;
                            }
    
                            if(is_valid)
                            {
              

                                target_rect.u_dv = edge.dv.clone();
                                target_rect.updateRect();

                                 // 尽量不镜像的处理, 让其旋转90度
                                if(target_rect.u_dv_flag < 0) 
                                {
                                    let r_center = target_rect.rect_center;
                                    let nor = next_edge.nor.clone().negate();
                                    target_rect.nor = nor;
                                    target_rect._u_dv_flag = 1;
                                    target_rect.rect_center = r_center;
                                    
                                }
                            }
                        }
           
                    }
                }
  

                group_template._target_length = length;
                group_template._target_rect.copy(target_rect);
                group_template._target_rect._attached_elements[AttachedBackGroupRuleData] = data;
                group_template.updateByTargetRect();
                group_templates.push(group_template);



            }
            if(optimize_on_wall)
            {
                TLayoutOptmizerOnWallRules.optimize_groups_with_onwall_rules_onwall(t_edge,t_group_templates,room.roomname);


            }

        }
        if(!is_valid) return null;
        return group_templates;
    }
    postProcess(target_f_shape:TFeatureShape,group_templates:TGroupTemplate[])
    {
        
        let configs = TGraphBasicConfigs.MainGroupFigureConfigs[this._room.roomname];
        let post_process_templates : TGroupTemplate[] = [];
        let origin_group_templates : TGroupTemplate[] = [...group_templates];
        for(let key in configs)
        {
            let config = configs[key];
            if(config.layout_state == FigLayoutState.PostProcess || config.layout_state == FigLayoutState.PreProcess)
            {

             let bbox = target_f_shape._poly.computeBBox();
             let test_pos_list = [bbox.min, bbox.getCenter(new Vector3()), bbox.max];
             let t_rect_list : ZRect[] = [];
             for(let c_pos of test_pos_list)
             {
                let target_rect =  TLayoutOptimizer.getTargetRect(config.opt_rules[0],target_f_shape,group_templates,
                    c_pos);

                
                if(target_rect)
                {
                    let has_visited = false;
                    for(let a_rect of t_rect_list)
                    {
                        if(a_rect.back_center.distanceTo(target_rect.back_center) < 100)
                        {
                            has_visited = true;
                        }
                    }
                    if(has_visited)
                    {
                        continue;

                    }

                      let n_group_template = new TGroupTemplate();
                      n_group_template.group_space_category = key;
                      n_group_template.group_code = config.main_figure_names[0];
                      n_group_template._target_rect = target_rect.clone();

                      if(config?.group_length_levels?.max)
                      {
                        n_group_template._target_rect._w = config.group_length_levels.max;
                        n_group_template._target_rect.updateRect();
                      }
                      if(n_group_template.updateByTargetRect())
                      {
                          group_templates.push(n_group_template);
                          post_process_templates.push(n_group_template);
                      }

                    //   console.log(n_group_template.group_space_category,n_group_template);

                      t_rect_list.push(target_rect);
                }
             }

            }
        }



        TPostProcessLayout.post_process_group_by_opt_rule(this._room.room_shape._feature_shape,
            post_process_templates,origin_group_templates, this._room.roomname);

        

    }
    apply(): void {
        if(this._candidate_figure_list.length <= this._attempt_id) return;

        this.graph._group_template_list = [...this._candidate_figure_list[this._attempt_id].group_templates];
        this.graph._debug_data = this._candidate_figure_list[this._attempt_id].debug_data;
        if(this._candidate_figure_list[this._attempt_id].sub_aeras)
        {
            this.graph._sub_area_list = [...(this._candidate_figure_list[this._attempt_id]?.sub_aeras||[])];
        }
        else{
            this.graph._sub_area_list = null;
        }

    }
}
