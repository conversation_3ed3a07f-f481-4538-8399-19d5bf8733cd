import { FigureZValRangeType } from "../../IRoomInterface";
import { BackToBedRule, BackToSofaRule, BackToWallRule, BackToWallRule_1, BackToWallRule_Balcony, 
    BackToWallRule_Sofa, BackToWindowRule, CabinetBackToWallRule, CaininetGroupConfigs, DeskChairNeighborInfos, FaceAlignByMainRectRule, 
    FigLayoutState, HangingCabinetGroupConfigs, I_FigureGroupConfigs, LengthByBedRule, LengthBySofaRule, LengthByWindowRule, LengthScaleByWallRule, LengthScaleByWallRule_Bed, LengthScaleByWallRule_Cabinets, SideToWallRule, TargetDepthRule_Bed, TargetDepthRule_Sofa, TargetDepthRule_Wardrobe, I_DecorationRule, CornerHangingCabinetGroupConfigs, FlipDoorHangingCabinetGroupConfigs, OpenHangingCabinetGroupConfigs, SideToWallRule_2 } 
    from "../TGraphConfigureInterface";

export var MainGroupFigureConfigs :  { [key: string]: { [key: string]: I_FigureGroupConfigs } } = {
    "客餐厅": {
        // 客餐厅
        "沙发组合区": {
            main_figure_names: ["沙发", "多人沙发","直排沙发", "转角沙发", "双人沙发"],
            sub_figure_names: ["矩形茶几", "茶几","单人沙发", "休闲椅","脚踏","圆形茶几","凳子","矩形边几"],
            group_range: { front_dist: 3000, back_dist: 0, side_dist: 1500 },
            opt_rules: [
                BackToWallRule_Sofa,
                // LengthScaleByWallRule_Sofa,
                SideToWallRule,
                TargetDepthRule_Sofa,
            ],
            backwall_max_dist: 2000,
            group_length_levels: {
                min: 1400,
                max: 3600,
                step: 200
            },
            differ_weight: 0.6,
            place_order: 2,
            priority: 3,
            is_adaptive_space: true
        },
        "地毯区": {
            main_figure_names: ["地毯"],
            sub_figure_names: [],
            layout_state: FigLayoutState.PostProcess,
            opt_rules: [
                BackToSofaRule,
                LengthBySofaRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: `(Math.min(a_fd - 300, 3000))`
                    }
                }
            ],
            group_length_levels: {
                min: 200,
                max: 4000,
                step: 50
            },
            differ_weight: 0.1,
            place_order: 0, //
            is_placeholder: false,
        },
        "窗帘区": {
            main_figure_names: ["窗帘", "双开帘", "单开帘"],
            sub_figure_names: [],
            layout_state: FigLayoutState.PreProcess,
            opt_rules: [
                BackToWindowRule,
                LengthByWindowRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '' + 200
                    }
                }
            ],
            place_order: 4, // 
            placeholder_expand: {
                wall_side_extend: 500
            }
        },
        "沙发背景墙": {
            main_figure_names: ["沙发背景墙"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 200 },
            layout_state: FigLayoutState.PostProcess,
            is_onwall_element: true,
            opt_rules: [
                BackToSofaRule,
                LengthBySofaRule,
                LengthScaleByWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '50'
                    }
                }
            ],
            place_order: 3,  // 仅次于衣柜
            is_placeholder: false  // 然后并不占位
        },
        "书柜区": {
            main_figure_names: ["书柜"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 200 },
            is_onwall_element: true,
            opt_rules: [
                BackToWallRule,
            ],
            place_order: 4,  
        },
        "电视背景墙": {
            main_figure_names: ["电视背景墙"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 200 },
            layout_state: FigLayoutState.PostProcess,
            is_onwall_element: true,
            opt_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        target_group_category: "电视柜区",
                        adsorb_back_target_dist: '' + 10000, // 如果背靠区域小于300, 才会执行这个优化
                        target_back_dist_val: '' + 0    // 目标的贴墙距离为500
                    }
                },
                {
                    ruleType: "LengthByTarget",
                    params: {
                        target_group_category: "电视柜区",
                        adsorb_length_ratio: '' + 0.001,  // 强制放缩就行了
                        margin_length_val: '' + 0,
                        target_length_val: 'Math.min(t_w_len - margin_length, target_l)'          // 背靠墙长, 目标的长  
                    }
                },
                LengthScaleByWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '30'
                    }
                }
            ],
            place_order: 3,  // 仅次于衣柜
            is_placeholder: false  // 然后并不占位
        },
        "岛台区":
        {
            main_figure_names: ["岛台", "吧台", "地柜"],
            main_figure_conditions: { size_range: { min: { x: 2000, y: 500, z: 300 }, max: { x: 5000, y: 5000, z: 3500 } } }, // 深度不超过800
            sub_figure_names: ["吧椅", "餐椅", "书椅"],
            group_range: { front_dist: 700, back_dist: 700, side_dist: 600 }
        },
        "定制组合区": CaininetGroupConfigs,
        "定制组合区1": CaininetGroupConfigs,
        "定制组合区2": CaininetGroupConfigs,
        "定制组合区3": CaininetGroupConfigs,
        "定制组合区4": CaininetGroupConfigs,
        "定制组合区5": CaininetGroupConfigs,
        "定制组合区6": CaininetGroupConfigs,
        "餐桌区": {
            main_figure_names: ["餐桌", "方桌"],
            sub_figure_names: ["餐椅", "书椅"],
            group_range: { front_dist: 1000, back_dist: 1000, side_dist: 1000 },
            free_nor: true,
            opt_rules: [
                {
                    ruleType: "NearTarget",
                    params: {
                        target_group_category: "餐边柜区"
                    }
                }
            ],
            group_length_levels: {
                min: 1500,
                max: 3000,
                step: 300
            },
            differ_weight: 0.4,
            place_order: 1,
            priority: 2,
            is_placeholder: false
        },
        "餐边柜区": {
            main_figure_names: ["餐边柜", "酒柜"],
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                LengthScaleByWallRule_Cabinets
            ],
            group_length_levels: {
                min: 900,
                max: 3000,
                step: 100
            },
            differ_weight: 0.3,
            place_order: 2,
            priority: 1
        },
        "电视柜区": {
            main_figure_names: ["电视柜"],
            sub_figure_names: ["电视"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                min: 1500,
                max: 4500,
                step: 300
            },
            differ_weight: 0.6,
            place_order: 2,
            priority: 3
        },
        "挂墙影视区": {
            main_figure_names: ["电视", "投影幕布"],
            sub_figure_names: ["电视"]
        },
        "玄关柜区": {
            main_figure_names: ["玄关柜", "鞋柜"],
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                LengthScaleByWallRule
            ],
            group_length_levels: {
                min: 900,
                max: 3000,
                step: 100
            },
            differ_weight: 0.5,

            place_order: 2,
            priority: 4
        },
        "书桌区": {
            main_figure_names: ["直边书桌", "书桌"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 300, z: 500 }, max: { x: 2400, y: 1000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 400, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule
            ],
        },
        "浴室柜区": {
            main_figure_names: ["浴室柜", "洗衣机柜"],
            sub_figure_names: ["浴室镜", "装饰镜"],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '' + 0.5,
                        target_length_val: 'Math.min(2000, a_wl)'
                    }
                }
            ],
            group_length_levels: {
                values:[
                    800,
                    900,
                    950,
                    1000,
                    1100,
                    1200
                ]
            },
            differ_weight: 0.5,
            place_order: 2, //
        },
        
    },
    "客厅":{
        // 客餐厅
        "沙发组合区": {
            main_figure_names: ["沙发", "多人沙发","直排沙发", "转角沙发", "双人沙发"],
            sub_figure_names: ["矩形茶几", "茶几","单人沙发", "休闲椅","脚踏","圆形茶几","凳子","矩形边几"],
            group_range: { front_dist: 3000, back_dist: 0, side_dist: 1500 },
            opt_rules: [
                BackToWallRule_Sofa,
                // LengthScaleByWallRule_Sofa,
                SideToWallRule,
                TargetDepthRule_Sofa,
            ],
            backwall_max_dist: 2000,
            group_length_levels: {
                min: 1400,
                max: 3600,
                step: 200
            },
            differ_weight: 0.6,
            place_order: 2,
            priority: 3,
            is_adaptive_space: true
        },
        "沙发背景墙": {
            main_figure_names: ["沙发背景墙"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 200 },
            layout_state: FigLayoutState.PostProcess,
            is_onwall_element: true,
            opt_rules: [
                BackToSofaRule,
                LengthBySofaRule,
                LengthScaleByWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '50'
                    }
                }
            ],
            place_order: 3,  // 仅次于衣柜
            is_placeholder: false  // 然后并不占位
        },        
        "餐边柜区": {
            main_figure_names: ["餐边柜", "酒柜"],
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                LengthScaleByWallRule_Cabinets
            ],
            group_length_levels: {
                min: 900,
                max: 3000,
                step: 100
            },
            differ_weight: 0.3,
            place_order: 2,
            priority: 1
        },
        "电视柜区": {
            main_figure_names: ["电视柜"],
            sub_figure_names: ["电视"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                min: 1500,
                max: 4500,
                step: 300
            },
            differ_weight: 0.6,
            place_order: 2,
            priority: 3
        },        
        "浴室柜区": {
            main_figure_names: ["浴室柜", "洗衣机柜"],
            sub_figure_names: ["浴室镜", "装饰镜"],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '' + 0.5,
                        target_length_val: 'Math.min(2000, a_wl)'
                    }
                }
            ],
            group_length_levels: {
                values:[
                    800,
                    900,
                    950,
                    1000,
                    1100,
                    1200
                ]
            },
            differ_weight: 0.5,
            place_order: 2, //
        },
    },
    "餐厅":{
        "餐桌区": {
            main_figure_names: ["餐桌", "方桌"],
            sub_figure_names: ["餐椅", "书椅"],
            group_range: { front_dist: 1000, back_dist: 1000, side_dist: 1000 },
            free_nor: true,
            opt_rules: [
                {
                    ruleType: "NearTarget",
                    params: {
                        target_group_category: "餐边柜区"
                    }
                }
            ],
            group_length_levels: {
                min: 1500,
                max: 3000,
                step: 300
            },
            differ_weight: 0.4,
            place_order: 1,
            priority: 2,
            is_placeholder: false
        },
        "餐边柜区": {
            main_figure_names: ["餐边柜", "酒柜"],
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                LengthScaleByWallRule_Cabinets
            ],
            group_length_levels: {
                min: 900,
                max: 3000,
                step: 100
            },
            differ_weight: 0.3,
            place_order: 2,
            priority: 1
        },
    },
    "厨房": {
        "水槽地柜": {
            main_figure_names: ["水槽地柜"],
            main_figure_category : "地柜-水槽地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min: 600,
                max: 1000,
                step: 100
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 2.,
            place_order: 2,
            priority: 3,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "转角水槽地柜": {
            main_figure_names: ["转角水槽地柜"],
            main_figure_category : "地柜-转角水槽地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min: 800,
                max: 1000,
                step: 100
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 1.,
            place_order: 3.5,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "消毒地柜": {
            main_figure_names: ["消毒地柜"],
            main_figure_category :"地柜-消毒地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                min: 600,
                max: 600,
                step: 100
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor


        },
        "微波炉地柜": {
            main_figure_names: ["微波炉地柜"],
            main_figure_category :"地柜-微波炉地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                min: 600,
                max: 600,
                step: 100
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor


        },
        "炉灶地柜": {
            main_figure_names: ["炉灶地柜", "灶台柜"],
            main_figure_category :"地柜-炉灶地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    800,
                    900
                ]
            },
            differ_weight: 1,
            place_order: 2,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "转角炉灶地柜": {
            main_figure_names: ["转角炉灶地柜", "灶台柜"],
            main_figure_category :"地柜-转角炉灶地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    1000,
                    1100,
                    1200,
                    1300,
                ]
            },
            differ_weight: 1,
            place_order: 3.5,
            priority: 3,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "抽屉地柜": {
            main_figure_names: ["抽屉地柜"],
            main_figure_category :"地柜-抽屉地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 1200, y: 1000, z: 1500 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    400,
                    450,
                    500
                ]
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 3,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "工具拉篮地柜": {
            main_figure_names: ["拉篮地柜"],
            main_figure_category : "地柜-拉篮地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    300,
                    350
                ]
            },
            differ_weight: 1.,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "调味拉篮地柜": {
            main_figure_names: ["拉篮地柜"],
            main_figure_category :"地柜-拉篮地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    300
                ]
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "碗碟拉篮地柜": {
            main_figure_names: ["拉篮地柜"],
            main_figure_category : "地柜-拉篮地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    500,
                    600,
                    700,
                    800,
                    900
                ]
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "拉篮地柜": {
            main_figure_names: ["拉篮地柜"],
            main_figure_category : "地柜-拉篮地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 1500 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            opt_rules: [
                CabinetBackToWallRule
            ],
            group_length_levels: {
                values: [
                    300,
                    350,
                    400,
                    450,
                    500,
                    600,
                    700,
                    800,
                    900
                ]
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "冰箱": {
            main_figure_names: ["冰箱"],
            main_figure_category :"冰箱",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                values: [
                    830,
                    930,
                    1030
                ]
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.5,
            place_order: 3,
            priority: 4,
            zval_range_type: FigureZValRangeType.All

        },
        "米箱地柜": {
            main_figure_names: ["米箱地柜"],
            main_figure_category :"地柜-米箱柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 300, y: 560, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min: 300,
                max: 300,
                step: 100
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 5.,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "单门地柜": {
            main_figure_names: ["单门地柜", "层板地柜"],
            main_figure_category :"地柜-单门地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 600, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min: 350,
                max: 600,
                step: 50
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.01,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "双门地柜": {
            main_figure_names: ["双门地柜"],
            main_figure_category :"地柜-双门地柜",
            main_figure_conditions: { size_range: { min: { x: 600, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min: 600,
                max: 900,
                step: 100
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.02,
            place_order: 1,
            priority: 0,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "转角单门地柜":{
            main_figure_names: ["转角单门地柜"],
            main_figure_category : "地柜-转角单门地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                values: [
                    350,                        
                    400,
                    450                   
                ]
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 2,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "转角自由单门地柜":{
            main_figure_names: ["转角自由单门地柜"],
            main_figure_category : "地柜-转角自由单门地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                min:30,
                max:400,
                step:10
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 10,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "转角自由地柜":{
            main_figure_names: ["转角自由地柜"],
            main_figure_category : "地柜-转角自由地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:400,
                max:900,
                step:50
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "主操作区地柜":{
            main_figure_names: ["主操作区地柜"],
            main_figure_category : "地柜-主操作区地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 4000, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:50,
                max:3500,
                step:10
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "洗菜区地柜":{
            main_figure_names: ["洗菜区地柜"],
            main_figure_category : "地柜-洗菜区地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 4000, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:50,
                max:3500,
                step:10
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "基础阵列地柜":{
            main_figure_names: ["基础阵列地柜"],
            main_figure_category : "地柜-基础阵列地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 4000, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:30,
                max:3500,
                step:10
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "基础阵列吊柜":{
            main_figure_names: ["基础阵列吊柜"],
            main_figure_category : "吊柜-基础阵列吊柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 4000, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:300,
                max:3500,
                step:50
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnHalfWall
        },
        "自由收口地柜":{
            main_figure_names: ["自由收口地柜"],
            main_figure_category : "地柜-自由收口地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            group_length_levels: {
                min:30,
                max:400,
                step:10
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 0.1,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "转角地柜": {
            main_figure_names: ["转角地柜"],
            main_figure_category : "地柜-转角地柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 200, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: ["插座"],
            group_length_levels: {
                values: [
                    400,
                    450,
                    500,
                    600,
                    700,
                    800,
                    900
                ]
            },
            opt_rules: [
                CabinetBackToWallRule
            ],
            differ_weight: 2,
            place_order: 3.3,
            priority: 4,
            zval_range_type: FigureZValRangeType.OnFloor

        },
        "烟机吊柜": {
            main_figure_names: ["烟机吊柜"],
            main_figure_category :"吊柜-烟机吊柜(侧吸)",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            group_length_levels: {
                values: [
                    800,
                    850,
                    900,
                    950,
                    1000,
                    1050,
                    1100,
                    1150,
                    1200,

                ]
            },
            differ_weight: 0.5,
            place_order: 1,
            priority: 3,
            zval_range_type: FigureZValRangeType.OnHalfWall

        },
        "烟道包管": {
            main_figure_names: ["烟道包管"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                },
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 10000,
                        target_side_dist_val: '0'
                    }
                }
            ],
            place_order: 4,
            priority: 4,
            group_length_levels: {
                min: 300,
                max: 600,
                step: 100
            },
            zval_range_type: FigureZValRangeType.All

        },
        "地柜见光板": {
            main_figure_names: ["地柜见光板"],
            main_figure_category : "小板件-地柜见光板",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            place_order: 1,
            priority: 2,
            group_length_levels: {
                min: 18,
                max: 20,
                step: 1
            },
            differ_weight: 0.5,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "假门地柜": {
            main_figure_names: ["假门地柜"],
            main_figure_category :"地柜-假门柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            place_order: 1,
            priority: 2,
            group_length_levels: {
                min: 50,
                max: 600,
                step: 10
            },
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "地柜收口板": {
            main_figure_names: ["地柜收口板"],
            main_figure_category :"小板件-地柜收口板",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            place_order: 1,
            priority: 2,
            group_length_levels: {
                values: [
                    30,
                    50,
                    60,
                    70,
                    80,
                    90,
                    100,
                    110,
                    120
                ]
            },
            differ_weight: 0.05,
            zero_differ_weight : 10000,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "地柜转角封板": {
            main_figure_names: ["地柜转角封板"],
            main_figure_category :"小板件-地柜转角封板",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                CabinetBackToWallRule
            ],
            place_order: 2,
            priority: 4,
            group_length_levels: {
                values: [
                    30,
                    50,
                    70,
                    80,
                    90,
                    100,
                    110,
                    120
                ]
            },
            differ_weight: 0.5,
            zval_range_type: FigureZValRangeType.OnFloor
        },
        "吊柜收口板": {
            main_figure_names: ["吊柜收口板"],
            main_figure_category :"小板件-吊柜收口板",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            place_order: 1,
            priority: 4,
            group_length_levels: {
                values: [
                    30,
                    50,
                    70,
                    80,
                    90,
                    100,
                    110,
                    120
                ]
            },
            differ_weight: 0.5,
            zval_range_type: FigureZValRangeType.OnHalfWall
        },
        "吊柜见光板": {
            main_figure_names: ["吊柜见光板"],
            main_figure_category :"小板件-吊柜见光板",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            place_order: 1,
            priority: 4,
            group_length_levels: {
                values: [
                    18
                ]
            },
            differ_weight: 0.5,
            zval_range_type: FigureZValRangeType.OnHalfWall
        },
        "窗前吊柜":{
            main_figure_names: ["窗前吊柜"],
            main_figure_category :"吊柜-窗前吊柜",
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 2000, z: 3000 } } },
            group_range: { front_dist: 10, back_dist: 10, side_dist: 0 },
            sub_figure_names: [],
            backwall_max_dist: 1500,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 1000, // 如果背靠区域小于600, 才会执行这个优化
                        target_back_dist_val: '0'    // 目标的贴墙距离为0
                    }
                }
            ],
            place_order: 1,
            priority: 4,
            group_length_levels: {
                values: [
                    600,
                    1000,
                    1200
                ]
            },
            differ_weight: 0.5,
            zval_range_type: FigureZValRangeType.OnHalfWall
        },
        "翻门吊柜": FlipDoorHangingCabinetGroupConfigs,
        "转角吊柜": CornerHangingCabinetGroupConfigs,
        "开放吊柜": OpenHangingCabinetGroupConfigs,
        "吊柜": HangingCabinetGroupConfigs
    },
    "卫生间": {
        // 卫浴
        "浴室柜区": {
            main_figure_names: ["浴室柜", "洗衣机柜"],
            sub_figure_names: ["浴室镜", "装饰镜"],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '' + 0.7,
                        margin_length_val: '' + 100,
                        target_length_val: 'Math.min(1200, a_wl - margin_length)'
                    }
                },
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 200,
                        target_sidewall_dist_val: '' + 0
                    }
                }
            ],
            group_length_levels: {
                values:[
                    600,
                    700,
                    800,
                    900,
                    950,
                    1000,
                    1100,
                    1200
                ]
            },
            differ_weight: 0.5,
            priority : 1,
            place_order: 1, //
        },
        "浴缸区": {
            main_figure_names: ["浴缸"],
            sub_figure_names: ["毛巾架", "花洒"],
            group_range: { front_dist: 0, back_dist: 0, side_dist: 0 },
        },
        "钻石形淋浴房区": {
            main_figure_names: ["钻石形淋浴房"], // 花洒可能容易输入居中位置
            group_range: { front_dist: 400, back_dist: 200, side_dist: 1500 },
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 500,
                        target_sidewall_dist_val: '' + 0
                    }
                }
            ],
            check_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        check_back_target_dist: "(back_to_target_dist > 300 || back_to_target_dist < -0.5)",   // 不可以背靠窗,只能侧靠
                        target_group_category: "平开窗",
                        adsorb_back_target_dist: '' + 10000, //
                    }
                }
            ],
            group_length_levels: {
                min: 800,
                max: 900,
                step: 100
            },
            differ_weight: 0.5,
            place_order: 3, //
            priority : 4,
            is_placeholder : false

        },
        "一字形淋浴房区": {
            main_figure_names: ["一字形淋浴房"], // 花洒可能容易输入居中位置
            group_range: { front_dist: 400, back_dist: 200, side_dist: 1500 },
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 1000,
                        target_sidewall_dist_val: '' + 0
                    }
                }
            ],
            check_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        check_back_target_dist: "(back_to_target_dist > 300 || back_to_target_dist < -0.5)",   // 不可以背靠窗,只能侧靠
                        target_group_category: "平开窗",
                        adsorb_back_target_dist: '' + 10000, //
                    }
                }
            ],
            group_length_levels: {
                min: 1000,
                max: 2000,
                step: 100
            },
            differ_weight: 0.5,
            place_order: 3, //
            priority : 2
        },
        "花洒-淋浴区": {
            main_figure_names: ["花洒"],
            group_range: { front_dist: 0, back_dist: 0, side_dist: 0 },
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(700)'
                    }
                },
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 400,
                        target_sidewall_dist_val: '' + 100
                    }
                }
            ],
            group_length_levels: {
                min: 700,
                max: 700,
                step: 100
            },
            group_margin_value:200,
            zero_differ_weight:10000,
            differ_weight: 4,
            priority:4,
            place_order: 4, //
            is_placeholder: true
        },
        "马桶区": {
            main_figure_names: ["马桶"],
            sub_figure_names: [],
            group_length_levels: {
                min: 650,
                max: 750,
                step: 50
            },
            place_order: 3,
            priority : 1,
            differ_weight: 2,
            zero_differ_weight:1000,
            group_margin_value: 200,
            opt_rules: [
                CabinetBackToWallRule,
                // {
                //     ruleType: "SideToWall",
                //     params: {
                //         adsorb_sidewall_dist: '' + 300,
                //         target_sidewall_dist_val: '' + 50
                //     }
                // }
            ],
        },
        "马桶-毛巾架": {
            main_figure_names: ["毛巾架"],
            sub_figure_names: [],
            opt_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        target_group_category: "马桶区",
                        target_figure_category: "马桶",
                        target_alignment: "align-center",
                        adsorb_back_target_dist: '' + 10000,
                        target_back_dist_val: '' + 0
                    }
                },
                BackToWallRule,
                {
                    ruleType: "LengthByTarget",
                    params: {
                        target_group_category: "马桶区",
                        target_figure_category: "马桶",
                        adsorb_length_ratio: '' + 0.001,  // 强制放缩就行了
                        margin_length_val: '' + 0,
                        target_length_val: '(target_l + 100)'          // 背靠墙长, 目标的长  
                    }
                },
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(300)'
                    }
                },

            ],
            check_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        check_back_target_dist: "(back_to_target_dist > 300 || back_to_target_dist < -0.5)",   // 不可以贴墙
                        target_group_category: "平开窗",
                        adsorb_back_target_dist: '' + 10000, //
                    }
                }
            ],
            layout_state: FigLayoutState.PostProcess,
            group_length_levels: {
                min: 400,
                max: 1000,
                step: 50
            },
            differ_weight: 0.1,
            place_order: 0,
            is_placeholder: false,
            zval_range_type: FigureZValRangeType.OnHalfWall
        },
        "窗帘区": {
            main_figure_names: ["窗帘", "双开帘", "单开帘"],
            sub_figure_names: [],
            layout_state: FigLayoutState.PreProcess,
            opt_rules: [
                BackToWindowRule,
                LengthByWindowRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '' + 100
                    }
                }
            ],
            place_order: 3, // 
            placeholder_expand: {
                wall_side_extend: 500
            }
        },

    },
    "阳台": {
        // 阳台
        "阳台柜区": {
            main_figure_names: ["高柜", "洗衣机柜", "阳台柜", "边柜", "吊柜", "收纳柜"],
            main_figure_name_map: { "吊柜": "洗衣机柜", "高柜": "洗衣机柜", "边柜": "洗衣机柜", "阳台柜": "洗衣机柜" },
            main_figure_conditions: { size_range: { min: { x: 400, y: 200, z: 400 }, max: { x: 2000, y: 1500, z: 3500 } } },
            sub_figure_names: ["洗衣机柜"],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(Math.min(a_fd-300,690))'
                    }
                }
            ],
        },
        "阳台柜区2": {
            main_figure_names: ["高柜", "洗衣机柜"],
            main_figure_conditions: { size_range: { min: { x: 400, y: 400, z: 0 }, max: { x: 2000, y: 1500, z: 3500 } } },
            sub_figure_names: ["洗衣机柜"],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule
            ],
        },
        "阳台-休闲区": {
            main_figure_names: ["茶几", "边几"],
            sub_figure_names: ["休闲沙发", "休闲椅", "休闲凳", "餐椅", "边几", "茶几", "躺椅", "单人沙发"],
            group_range: { front_dist: 300, back_dist: 300, side_dist: 1000 },
            opt_rules: [
                BackToWallRule_Balcony,
                FaceAlignByMainRectRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(a_fd - 200)'
                    }
                }
            ],
        },
        "休闲沙发区": {
            main_figure_names: ["休闲沙发", "休闲椅", "休闲凳"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 1000 },
            opt_rules: [
                BackToWallRule_Balcony,
                FaceAlignByMainRectRule,
            ],
        },
        "盆栽区": {
            main_figure_names: ["落地盆栽"],
            main_figure_conditions: { size_range: { min: { x: 400, y: 400, z: 400 }, max: { x: 4000, y: 1500, z: 1500 } } },
            sub_figure_names: ["绿植", "落地盆栽"],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule
            ],
        },
        "书桌区": {
            main_figure_names: ["直边书桌", "书桌"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 300, z: 500 }, max: { x: 2400, y: 1000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 400, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule
            ],
        },
        "梳妆台区": {
            main_figure_names: ["梳妆台"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 1000, z: 2500 } } },
            group_range: { front_dist: 800, back_dist: 400, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳","凳子"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule_1,
                LengthScaleByWallRule
            ],
            group_length_levels: {
                min: 850,
                max: 1800,
                step: 300
            },
            priority: 2,
            differ_weight: 1,
            place_order: 2,  // 仅次于衣柜

        },

    },
    "卧室": {
        "卧室-衣柜区": {
            main_figure_names: ["衣柜"],
            main_figure_conditions: { size_range: { min: { x: 300, y: 250, z: 0 }, max: { x: 8000, y: 1500, z: 3500 } } },
            sub_figure_names: [],
            opt_rules: [
                BackToWallRule,
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 501, // 如果侧靠区域小于100, 才会执行这个优化
                        target_sidewall_dist_val: '' + 0    // 目标的贴墙距离为20
                    }
                },
                {

                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '(a_wl - r_l)>600?1.:0.75',  // 如果贴墙区域超过0.8
                        margin_length_val: '' + 20,
                        target_length_val: '(a_wl - margin_length)'          // 直接是计算出数值
                    }
                },
                {
                    ruleType: "SideToWall",
                    params: {
                        adsorb_sidewall_dist: '' + 501,
                        target_sidewall_dist_val: '' + 0
                    }
                },
                TargetDepthRule_Wardrobe
            ],
            placeholder_expand: {
                front_extend: 200,
                wall_side_extend: 200
            },
            group_length_levels: {
                min: 1200,
                max: 4000,
                step: 50
            },
            place_order: 4,  // 优化时,放置的优先级最高
            priority: 4
        },
        "卧床区": {
            main_figure_names: ["床"],
            sub_figure_names: ["床头柜", "地柜", "边柜", "边几", "床具", "休闲沙发"],
            group_range: { front_dist: 0, back_dist: 200, side_dist: 600 },
            sub_figure_conditions: {
                "default": {
                    size_range: { min: { x: 300, y: 300, z: 0 }, max: { x: 1000, y: 1200, z: 1000 } },
                    w_d_ratio: [0.5, 2.]
                },
                "休闲沙发": {
                    size_range: { min: { x: 300, y: 300, z: 0 }, max: { x: 2000, y: 2200, z: 2000 } },

                }
            },
            sub_figure_neighbor_infos: [
                {
                    sub_figure_names: ["床头柜", "地柜", "边柜", "边几", "床具"],
                    sub_figure_name_map: { "地柜": "床头柜", "床具": "床头柜", "儿童床头柜": "床头柜" },
                    neighbor_info: {
                        dir_type: 0,
                        allowed_overlap: true,
                        // let ml = this._parent_element?.params?.length || 0;
                        // let md = this._parent_element?.params?.depth || 0;
                        // let tl = this._target_element?.params?.length || 0;
                        // let td = this._target_element?.params?.depth || 0;
                        nor_offset_dist: '(-0.5*md+0.5*td + 10)',  // 背靠墙 平齐, 稍微留点白
                        dir_offset_dist: 'dir_side * (0.5*ml+0.5*tl + 10)',
                    }
                }
            ],
            opt_rules: [
                BackToWallRule_1,
                LengthScaleByWallRule_Bed,
                // {
                //     ruleType: "FaceAlignByMainRect",
                //     params: {
                //         adsorb_front_target_dist: '' + 2000,
                //         target_alignment: "align-center"
                //     }
                // },
            ],
            group_length_levels: {
                min: 1000,
                max: 3600,
                step: 200
            },
            differ_weight: 1,
            priority: 2,
            place_order: 1,
            is_adaptive_space: true
        },
        "卧室背景墙": {
            main_figure_names: ["卧室背景墙","背景墙"],
            sub_figure_names: ["墙画","墙饰","挂画"],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 200 },
            layout_state: FigLayoutState.PostProcess,
            is_onwall_element: true,
            opt_rules: [
                {
                    ruleType: "BackToTarget",
                    params: {
                        target_group_category: "卧床区",
                        target_figure_category: "床",
                        adsorb_back_target_dist: '' + 500, // 如果背靠区域小于300, 才会执行这个优化
                        target_back_dist_val: '' + 0    // 目标的贴墙距离为500
                    }
                },
                {
                    ruleType: "LengthByTarget",
                    params: {
                        target_group_category: "卧床区",
                        adsorb_length_ratio: '' + 0.001,
                        margin_length_val: '' + 0,
                        target_length_val: 'Math.min(t_w_len - margin_length, target_l)'          // 背靠墙长, 目标的长  
                    }
                },
                {
                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '' + 0.5,  // 如果贴墙区域超过0.8
                        margin_length_val: '' + 0,
                        target_length_val: '(a_wl - margin_length)'          // 直接是计算出数值
                    }
                },                    
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '50'
                    }
                }
            ],
            place_order: 3,  // 仅次于衣柜
            is_placeholder: false  // 然后并不占位
        },
        "电视柜区": {
            main_figure_names: ["电视柜"],  // 如果有电视柜, 那子元素就是电视; 如果没有，电视就是主元素
            sub_figure_names: ["电视", "边柜", "餐边柜"],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 0 },
            opt_rules: [
                BackToWallRule,
                {
                    ruleType: "LengthByWall",
                    params: {
                        adsorb_length_ratio: '' + 0.8,  // 如果贴墙区域超过0.8
                        margin_length_val: '' + 300,
                        target_length_val: '(Math.min(a_wl,f_wl) - margin_length)'          // 直接是计算出数值
                    }
                },
                {
                    ruleType: "FaceAlignByMainRect",
                    params: {
                        adsorb_front_target_dist: '' + 2000,
                        target_alignment: "align-center"
                    }
                },
                {
                    ruleType: "AlignFront",
                    params: {
                        adsorb_front_target_dist: '' + 1000,
                        target_alignment: "align-center"
                    }
                }
            ],
            group_length_levels: {
                min: 1050,
                max: 4000,
                step: 300
            },
            differ_weight: 0.3,
            place_order: 1,
            priority: 1
        },
        "挂墙电视区": {
            main_figure_names: ["电视"],  // 
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 0 },
            opt_rules: [
                BackToWallRule,
                {
                    ruleType: "FaceAlignByMainRect",
                    params: {
                        adsorb_front_target_dist: '' + 2000,
                        target_alignment: "align-center"
                    }
                },
                {
                    ruleType: "AlignFront",
                    params: {
                        adsorb_front_target_dist: '' + 1000,
                        target_alignment: "align-center"
                    }
                }
            ],
            group_length_levels: {
                min: 850,
                max: 1750,
                step: 300
            },
            differ_weight: 0.01,
            place_order: 1,
            priority: 1
        },
        "梳妆台区": {
            main_figure_names: ["梳妆台"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 0,z:0}, max: { x: 2400, y: 1000, z: 2500 } } },
            group_range: { front_dist: 400, back_dist: 400, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳","凳子"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule_1,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: `1000`
                    }
                }
            ],
            group_length_levels: {
                min: 900,
                max: 1500,
                step: 300
            },
            priority: 3,
            differ_weight: 3,
            place_order: 2,  // 仅次于衣柜

        },
        "书桌区": {
            main_figure_names: ["书桌","直边书桌",],
            main_figure_conditions: { size_range: { min: { x: 0, y: 0, z: 0 }, max: { x: 2400, y: 1800, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 800, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                {
                    ruleType: "BackToWall",
                    params: {
                        adsorb_back_target_dist: '' + 200, 
                        target_back_dist_val: '' + 20    // 目标的贴墙距离为20
                    }
                },
                // SideToWallRule_2,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: `1050`
                    }
                }
            ],
            group_length_levels: {
                min: 850,
                max: 1800,
                step: 300
            },
            priority: 2,
            place_order: 2,
            differ_weight:2
        },
        "榻榻米区": {
            main_figure_names: ["榻榻米"],
            main_figure_conditions: { size_range: { min: { x: 1000, y: 1200, z: 200 }, max: { x: 4000, y: 4000, z: 1200 } } }, // 
            sub_figure_names: [""],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule_Bed,
                TargetDepthRule_Bed
            ],
            place_order: 1,
            group_length_levels: {
                min: 1400,
                max: 3600,
                step: 200
            },
            priority: 3
        },
        "梳妆凳区": {
            main_figure_names: ["休闲凳", "梳妆凳"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 300, z: 500 }, max: { x: 2000, y: 1000, z: 1500 } } }, // 深度不超过800
            group_range: { front_dist: 400, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜"],
            place_order: 0

        },
        "卧室-边柜区": {
            main_figure_names: ["餐边柜", "边柜"],
            main_figure_conditions: { size_range: { min: { x: 1000, y: 200, z: 400 }, max: { x: 2000, y: 600, z: 1100 } } }, // 高度
            sub_figure_names: ["电视"],
            opt_rules: [
                BackToWallRule
            ],
            place_order: 0
        },
        "卧室-收纳柜区": {
            main_figure_names: ["收纳柜", "书柜", "边柜"],
            main_figure_conditions: { size_range: { min: { x: 1000, y: 200, z: 400 }, max: { x: 4000, y: 1000, z: 3500 } } }, // 高度
            sub_figure_names: [],
            opt_rules: [
                BackToWallRule
            ],
            place_order: 2
        },
        "卧室-书桌柜区": {
            main_figure_names: ["书桌柜"],
            main_figure_conditions: { size_range: { min: { x: 1000, y: 200, z: 400 }, max: { x: 4000, y: 1000, z: 3500 } } }, // 高度
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule
            ],
            group_length_levels: {
                min: 850,
                max: 1800,
                step: 300
            },
            priority: 2,
            place_order: 2
        },
        "卧室-地台区": {
            main_figure_names: ["地台"],
            main_figure_conditions: { size_range: { min: { x: 1500, y: 400, z: 300 }, max: { x: 4000, y: 1000, z: 600 } } },
            sub_figure_names: [],
            opt_rules: [
                BackToWallRule
            ],
            place_order: 2
        },
        "沙发组合区": {
            main_figure_names: ["沙发", "转角沙发"],
            sub_figure_names: ["绿植", "边几", "茶几"],
            group_range: { front_dist: 200, back_dist: -300, side_dist: 1000 },
            place_order: 2

        },
        "休闲沙发区": {
            main_figure_names: ["休闲沙发", "躺椅"],
            sub_figure_names: [],
            group_range: { front_dist: 200, back_dist: -300, side_dist: 1000 }
        },
        "屏风隔断区": {
            main_figure_names: ["屏风隔断"],
            sub_figure_names: [],
            opt_rules: [
                SideToWallRule
            ],
            place_order: 5, // 隔断的优先级是最高的
        },
        "窗帘区": {
            main_figure_names: ["窗帘", "双开帘", "单开帘"],
            sub_figure_names: [],
            layout_state: FigLayoutState.PreProcess,
            opt_rules: [
                BackToWindowRule,
                LengthByWindowRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '' + 100
                    }
                }
            ],
            place_order: 3, // 
            placeholder_expand: {
                wall_side_extend: 500
            }
        },
        "地毯区": {
            main_figure_names: ["地毯"],
            sub_figure_names: [],
            layout_state: FigLayoutState.PostProcess,
            opt_rules: [
                BackToBedRule,
                LengthByBedRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: `(Math.min(a_fd - 300, a_td+300, 2000))`
                    }
                }
            ],
            group_length_levels: {
                min: 200,
                max: 4000,
                step: 50
            },
            differ_weight: 0.1,
            place_order: 0, //
            is_placeholder: false,
        },
        "书柜区": {
            main_figure_names: ["高柜", "边柜", "吊柜", "收纳柜", "书柜"],
            main_figure_name_map: { "高柜": "书柜", "边柜": "书柜", "阳台柜": "书柜" },
            main_figure_conditions: { size_range: { min: { x: 400, y: 0, z: 0 }, max: { x: 10000, y: 1500, z: 3600 } } },
            sub_figure_names: ["书柜", "高柜"],
            opt_rules: [
                BackToWallRule,
                SideToWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '450'
                    }
                }
            ],
            group_length_levels: {
                min: 1000,
                max: 3600,
                step: 100
            },
            placeholder_expand: {
                wall_side_extend: 200
            },
            priority: 4,
            place_order: 3,
            differ_weight:3
        },
    },
    "书房": {
        "书桌区": {
            main_figure_names: ["直边书桌", "书桌", "办公桌"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 300, z: 500 }, max: { x: 3400, y: 1800, z: 2000 } } }, // 深度不超过800
            group_range: { front_dist: 400, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule_1,
                {
                    ruleType: "AlignFront",
                    params: {
                        target_alignment: "align-center",
                        adsorb_front_target_dist: '' + 2000
                    }
                }
            ],
            group_length_levels: {
                min: 850,
                max: 1800,
                step: 300
            },
            priority: 2,
            place_order: 2
        },
        "书桌区2": {
            main_figure_names: ["直边书桌", "书桌", "办公桌"],
            main_figure_conditions: { size_range: { min: { x: 0, y: 300, z: 500 }, max: { x: 3400, y: 1800, z: 2000 } } }, // 深度不超过800
            group_range: { front_dist: 400, back_dist: 200, side_dist: 0 },
            sub_figure_names: ["浴室镜", "书椅", "写字椅", "餐椅", "儿童椅", "梳妆凳", "休闲凳", "休闲椅", "椅凳"],
            sub_figure_neighbor_infos: DeskChairNeighborInfos,
            opt_rules: [
                BackToWallRule_1
            ],
            group_length_levels: {
                min: 850,
                max: 1800,
                step: 300
            },
            priority: 2,
            place_order: 2
        },
        "书柜区": {
            main_figure_names: ["高柜", "边柜", "吊柜", "收纳柜", "书柜"],
            main_figure_name_map: { "高柜": "书柜", "边柜": "书柜", "阳台柜": "书柜" },
            main_figure_conditions: { size_range: { min: { x: 400, y: 0, z: 0 }, max: { x: 10000, y: 1500, z: 3600 } } },
            sub_figure_names: ["书柜", "高柜"],
            opt_rules: [
                BackToWallRule,
                LengthScaleByWallRule,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(Math.min(a_fd-300,400))'
                    }
                }
            ],
            group_length_levels: {
                min: 1000,
                max: 3200,
                step: 100
            },
            priority: 2,
            place_order: 3
        },

        "沙发组合区": {
            main_figure_names: ["沙发", "多人沙发","直排沙发", "转角沙发", "床具", "双人沙发", "单人沙发"],
            sub_figure_names: ["茶几"],
            group_range: { front_dist: 1000, back_dist: 0, side_dist: 1000 },
            opt_rules: [
                BackToWallRule_1
            ],

        },
        "书房-休闲区": {
            main_figure_names: ["茶几", "边几"],
            sub_figure_names: ["休闲沙发", "休闲椅", "休闲凳", "餐椅", "边几", "茶几", "躺椅", "坐垫"],
            group_range: { front_dist: 200, back_dist: 200, side_dist: 1000 },
            opt_rules: [
                BackToWallRule_Balcony,
                {
                    ruleType: "DepthExpand",
                    params: {
                        target_depth_val: '(a_wd - 200)'
                    }
                }
            ],
        },

    },
    "入户花园": {
        "玄关柜区": {
            main_figure_names: ["玄关柜", "鞋柜"],
            sub_figure_names: [],
            opt_rules: [
                CabinetBackToWallRule,
                LengthScaleByWallRule
            ],
            group_length_levels: {
                min: 900,
                max: 3000,
                step: 100
            },
            differ_weight: 0.5,

            place_order: 2,
            priority: 4
        },
    }
}

