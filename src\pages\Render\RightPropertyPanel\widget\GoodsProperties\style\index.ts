// import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  // let ismobile = checkIsMobile();
  return {
    root: css`
      position: relative;
    `,
    rootItem: css`
      display: flex;
      justify-content: space-between;
      padding: 5px 16px 5px 12px; 
    `,
    topInfo:css`
      display: flex;
      /* justify-content: space-between; */
      padding: 5px 12px;
      user-select: text;
      position: relative;
      #good{
        width: 64px;
        height: 64px;
        border-radius: 4px;
        background: #F2F3F5;
        /* border: 1px solid #0000000F; */
        margin-right: 8px;
      }
      #lock {
        position: absolute;
        width: 24px;
        height: 30px;
        cursor: pointer;
        top: 25px;
        right: 15px;
      }
    `,
    category:css`
      color: #282828;
      font-size: 12px;
      line-height: 24px;
      font-weight: 600;
    `,
    size: css`
      color: #a2a2a5;
      font-size: 12px;  
      line-height: 20px;
    `,    
    sizeWarning: css`
      line-height: 20px;
      position: relative;
      display: inline-block;
      :after {
        content: "超出目标尺寸范围";
        visibility: hidden;
        width: 110px;
        background-color: yellow;
        color: red;
        text-align: center;
        border-radius: 6px;
        padding: 5px 5px 0px 0px;
        position: absolute;
        z-index: 1;
        font-size: 12px;  
        bottom: 100%;
        left: 50%;
        opacity: 0;
        transition: opacity 0.3s;
      }
      :hover::after {
        visibility: visible;
        opacity: 1;
      }
    `,
    changeLine: css`
      margin: 20px 0px 8px 0px;
      margin-left: 12px;
    `,
    goodListRoot: css`
      overflow-x: scroll;
      overflow-y: hidden;
    `,
    goodsList: css`
      display: flex;
      flex-wrap: nowrap;
      align-items: stretch;
      padding: 2px 0 0px 12px;
      position: relative;
    `,
    goodsItem: css`
      border-radius: 4px;
      transition: all .3s;
      outline: 1px solid #fff;
      position: relative;
      width: 100px;
      margin-right: 10px;
      img{
        width: 100px;
        height: 85px;
        background: #F2F3F5;
        transition: all .3s;
        padding: 2px;
        margin-bottom: 5px;
      }
      .repalceBtn {
        display:none;
        height:30px;
        line-height:30px;
        width:100%;
        position:absolute;
        bottom:0px;
        text-align:center;
        cursor:pointer;
        background:rgba(255,255,255,0.5);
        font-weight:700;
      }
    `,
    selected: css` 
      outline: 1px solid #147FFA;
    `,
    selectIcon: css`
      background-color: #147FFA;
      width: 16px;
      height: 16px;
      position: absolute;
      right: 0px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top-right-radius: 2px;
    `,
    sizeInfo: css`
      color: #5B5E60;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 1.67;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
    `,
    goodPopover: css`
      position: absolute;
      display: block;
      top: 0px;
      left: -325px;

      /* right: 0px; */
      width: 320px;
      height: 426px;
      background-color: #FFF;
      border-radius: 4px;
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);
      img{
        width: 100%;
        height: 320px;
        background: #EEEFF2;
      }
    `,
    goodPopoverInfo: css`
      height: 106px;
      padding: 16px;
      user-select: text;
    `,
    name: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 14px;
      line-height: 1.57;
      font-weight: 600;
    `,
    sizes: css`
      color: #5B5E60;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 1.67;
    `,
    line: css`
      width: 1px;
      height: auto;
      margin: 15px auto;
      background: #00000014;
     `,
    categoryTitle: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: semibold;
      font-size: 14px;
      line-height: 1.57;
      letter-spacing: 0px;
      text-align: left;
      font-weight: 600;
      padding: 0 0px 6px 12px;
    `,
    emptyInfo: css`
      text-align: center;
      margin-top: 120px;
      img{
        width: 60px;
        height: 60px;
        margin-bottom: 12px;
      }
      .desc{
        color: #A2A2A5;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0px;
        text-align: center;
      }
      button{
        margin-top: 12px;
      }
    `,
    item_locked:css`
        position: absolute;
        width: 100%;
        height: 100%;
        background: #33333388;
        z-index:101;
    `,
    findInfo: css`
      padding-right: 4px;
      position: relative;
      margin-bottom: 6px;
      align-items: center;
      margin-right: 10px;        
      .iconClose_Large
      {
          cursor: pointer;
          font-size: 12px !important;
          position: absolute;
          right: 10px;
          top: 10px;
      }
    `,
    replaceInfo: css`
      display: flex;
      height: 140px;
    `,
    container_input: css`
      border-radius: 30px;
      background: #F2F3F5;
      color: #000;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      width: 50%;
      height: 24px;
      border: none;
      margin: 5px 0 0 0px;
      padding-left: 30px;
      :focus {
        border-color: none; /* 取消聚焦边框 */
        box-shadow: none; /* 取消聚焦阴影效果 */
        outline: none; /* 取消聚焦时的外边框效果 */
      }
    `,
    Icon: css`
      position: absolute;
      top: 8px;
      left: 10px;
      cursor: pointer;
    `,
  }
});
