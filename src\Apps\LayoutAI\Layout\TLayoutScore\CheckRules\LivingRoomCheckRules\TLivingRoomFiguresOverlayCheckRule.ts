import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { OverlayType, TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions, TCheckRule } from "../TCheckRule";
import { TLivingRoomToolUtil } from "./TLivingRoomToolUtil";

export class TLivingRoomFiguresOverlayCheckRule extends TCheckRule
{
    private ignoreFigureNamesOverlayWithWall: string[] = ["玄关柜", "背景墙"];
    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any {
        let overlayFigures: TFigureElement[] = [];
        let overlayRatio: number = 0;
        for(let figure of main_figures)
        {
            let otherFigures: TFigureElement[] = TBaseRoomToolUtil.instance.getOtherFigures(figure, main_figures);
            for(let otherFigure of otherFigures)
            {
                if((TLivingRoomToolUtil.instance.diningTableFigureNames.includes(figure.sub_category) && otherFigure.sub_category == "餐椅") 
                    || (figure.sub_category == "餐椅" && TLivingRoomToolUtil.instance.diningTableFigureNames.includes(otherFigure.sub_category))
                    || (figure.sub_category == "餐椅" && otherFigure.sub_category == "餐椅"))
                {
                    continue;
                }
                if( (compareNames([figure.sub_category],["转角沙发"]) && compareNames([otherFigure.sub_category],["茶几","休闲椅","边几","脚踏"])))
                {
                    continue;
                }
                if( (compareNames([otherFigure.sub_category],["转角沙发"]) && compareNames([figure.sub_category],["茶几","休闲椅","边几","脚踏"])))
                {
                    continue;
                }
                let tempOverlayRatio: number = TBaseRoomToolUtil.instance.calOverlayRatioByFigures(figure, otherFigure, 0, false);
                if(tempOverlayRatio > 0.01)
                {
                    overlayFigures.push(figure);
                    overlayFigures.push(otherFigure);
                    overlayRatio += tempOverlayRatio;
                }
            }
        }
        // TODO 这里要对玄关柜这种故意嵌墙放出白名单
        let overLayWallTol: number = 5;
        let filterSingleFigures: TFigureElement[] = [];
        main_figures.forEach(figure => {
            if(!this.ignoreFigureNamesOverlayWithWall.includes(figure.sub_category))
            {
                filterSingleFigures.push(figure);
            }
        });
        overlayRatio = TBaseRoomToolUtil.instance.calOverRoomRation(room, filterSingleFigures, overLayWallTol, overlayRatio, overlayFigures);

        overlayFigures = Array.from(new Set(overlayFigures));

        if(overlayFigures.length)
        {
            return {score: OverlayType.k_overlay, fineTuningFigures: overlayFigures, indexValue: overlayRatio};
        }
        return {score: OverlayType.k_noOverlay};
    }
}
