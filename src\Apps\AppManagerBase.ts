import { Vector2 } from 'three';
import {
  CadBatchDrawingLayerType,
  CadDrawingLayerType,
  TDrawingLayer
} from './LayoutAI/Drawing/TDrawingLayer';
import { TPainter } from './LayoutAI/Drawing/TPainter';
import { T_Handlerbase } from './LayoutAI/Handlers/HandlerBase';
import { TSwjLayoutGraphSolver } from './LayoutAI/Layout/TAppSolvers/TSwjLayoutGraphSolver';
import { I_ExtRoom } from './LayoutAI/Layout/TRoom';
import { TLayoutEntityContainer } from './LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { I_MouseEvent } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_CursorState } from './LayoutAI_App';
import { Scene3D } from './LayoutAI/Scene3D/Scene3D';
import { I_SwjRoom, I_SwjXmlScheme } from './LayoutAI/AICadData/SwjLayoutData';
import { I_Room } from './LayoutAI/Layout/IRoomInterface';
import { I_ModelRoomSourceType } from './LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TModelRoomTransferGraph/TBasicRoomTransferRelation';
import { TCadCeilingLayer } from './LayoutAI/Drawing/TCadCeilingDrawingLayer';
import { TCadRoomLightingLayer } from './LayoutAI/Drawing/TCadLightingDrawingLayer';
import { TEzdxfDataDrawingLayer } from './LayoutAI/Drawing/TCadEzdxfDrawingLayer';
import { TCadFurnitureLayer } from './LayoutAI/Drawing/TCadFurnitureLayer';
import { TCadCabinetLayer } from './LayoutAI/Drawing/TCadCabinetLayer';
import { TCadOutLineLayer } from './LayoutAI/Drawing/TCadOutLineLayer';
import { TCadRoomNameLayer } from './LayoutAI/Drawing/TCadRoomNameLayer';
import { TCadRoomStrucureLayer } from './LayoutAI/Drawing/TCadRoomFrameLayer';
import { AIMatchingModeHandler } from './AI2Design/Handlers/AIMatchingModeHandler';
import { AI2DesignBasicModes } from './AI2Design/AI2DesignManager';
import { TAILayoutDrawingLayer } from './LayoutAI/Drawing/TAILayoutDrawingLayer';
import { TAIMatchingDrawingLayer } from './LayoutAI/Drawing/TAIMatchingDrawingLayer';
import { TCadFloorDrawingLayer } from './LayoutAI/Drawing/TCadFloorDrawingLayer';
import { TDrawingBatchLayer } from './LayoutAI/Drawing/TDrawingBatchLayer';
import { TExportCadDrawingLayer } from './LayoutAI/Drawing/TExportCadLayer';
import { TCadCopyImageLayer } from './LayoutAI/Drawing/TCadCopyImageLayer';
import { TCadSubRoomAreaDrawingLayer } from './LayoutAI/Drawing/TCadSubRoomAreaDrawingLayer';
import { _editHxId } from '@/config';
import { TCadRoomDecoratesLayer } from './LayoutAI/Drawing/TCadRoomDecoratesLayer';
import { AppSetting, PerformanceMode } from './LayoutAI/setting/AppSetting';
import { Scene3DManager } from './LayoutAI/Scene3D/Scene3DManager';
import { TDimensionOutterWallElementLayer } from './LayoutAI/Layout/TransformElements/TDimensionOutterWallElementLayer';
import { LayoutAI_Configs } from './LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs';
import { IndexedDBService } from './LayoutAI/Services/IndexedDB/IndexedDBService';

/**
 *   AppManager 通用基础, 有一些基本框架是相同的
 */
export class TAppManagerBase extends LayoutAI_App {
  body_div_container: HTMLDivElement;
  side_div_container: HTMLDivElement;
  side_house_panel_container: HTMLDivElement;
  background_pattern_canvas: HTMLCanvasElement;
  canvas: HTMLCanvasElement; // 主要的绘制对象

  painter: TPainter;

  _moving_canvas_enabled: boolean;
  handlers: { [key: string]: T_Handlerbase };
  _current_handler: T_Handlerbase;

  drawing_layers: { [key: string]: TDrawingLayer };

  _previous_mode: string;
  /**
   *  合批图层
   */
  _batch_drawing_layers: { [key: string]: TDrawingLayer };

  /**
   *  可全局注册的子handler
   */
  _ext_sub_handlers: { [key: string]: T_Handlerbase };

  protected _focus_div: HTMLDivElement | HTMLCanvasElement;
  protected _mouse_over_div: HTMLDivElement | HTMLCanvasElement;

  /**
   *  维护所有房型需要的关键实体对象
   */
  protected _layout_container: TLayoutEntityContainer;

  protected _layout_graph_solver: TSwjLayoutGraphSolver;

  protected _pointers: PointerEvent[];
  protected _pointerPositions: { [key: string]: Vector2 };

  static SidePanelDivId = 'side_pannel';
  constructor(app_name: string) {
    super(app_name);

    this.checkIsDebug();

    this._moving_canvas_enabled = true;

    this._layout_container = null;

    if (!TSwjLayoutGraphSolver.instance) {
      TSwjLayoutGraphSolver.instance = new TSwjLayoutGraphSolver();
    }
    this._layout_graph_solver = TSwjLayoutGraphSolver.instance;
  }
  public init(): void {
    super.init();
    this._layout_graph_solver.initGraphs();

    this.initElements();
    this.initHandlers();
  }

  get layout_graph_solver() {
    return this._layout_graph_solver;
  }
  get layout_container(): TLayoutEntityContainer {
    return this._layout_container;
  }
  public checkIsDebug() {
    if (localStorage) {
      let item = localStorage.getItem('LayoutAI_Debug');
      if (item) {
        this._debug_mode = ~~item;
      } else {
        this._debug_mode = 0;
      }
    }
  }

  public setDebugMode(t: number = 1) {
    this._debug_mode = t;
    if (localStorage) {
      localStorage.setItem('LayoutAI_Debug', '' + this._debug_mode);
    }
  }
  initElements(canvas: HTMLCanvasElement = null) {
    this.canvas = canvas || (document.getElementById('room_canvas') as HTMLCanvasElement);

    // 2D绘图的主对象
    if (this.canvas != null) {
      this.painter = new TPainter(this.canvas);
    }

    // ** 动态感知当前的焦点对象
    this._focus_div = null;
    this._mouse_over_div = null;

    if (this.scene3DManager) {
      this.scene3DManager.initScene3D(true);
    }
    AppSetting.instance.performanceMode = PerformanceMode.Middle;
    // console.log("init Elements", this.canvas, this.painter);

    this._layout_container.curEditHouseSchemeId = _editHxId;
  }

  initHandlers() {
    // 初始handlers
    this.handlers = {};

    // 初始化 扩展的子handler
    this._ext_sub_handlers = {};
  }

  get scene3DManager() {
    return super.scene3DManager as Scene3DManager;
  }
  /**
   *  注册鼠标、键盘事件
   */
  registerEvents() {
    let scope = this;

    let t_parent_contianer =
      (this.canvas.parentElement as HTMLDivElement) ||
      (document.getElementById('body_container') as HTMLDivElement);

    if (t_parent_contianer == this.body_div_container) return;
    this.body_div_container =
      (this.canvas.parentElement as HTMLDivElement) ||
      (document.getElementById('body_container') as HTMLDivElement);
    this.side_div_container = document.getElementById('pad_left_panel') as HTMLDivElement;
    this.side_house_panel_container = document.getElementById('pad_house_left_panel') as HTMLDivElement;
    this._pointers = [];
    this._pointerPositions = {};
    let _pointers = this._pointers;
    let _pointerPositions = this._pointerPositions;

    let _prev_scale_val = 1;
    const addPointer = (ev: any) => {
      _pointers.push(ev);
    };

    const removePointer = (ev: any) => {
      if (_pointerPositions[ev.pointerId]) {
        delete _pointerPositions[ev.pointerId];
      }

      for (let i = 0; i < _pointers.length; i++) {
        if (_pointers[i].pointerId == ev.pointerId) {
          _pointers.splice(i, 1);
          return;
        }
      }
    };
    function trackPointer(event: any) {
      let position = _pointerPositions[event.pointerId];

      if (position === undefined) {
        position = new Vector2();
        _pointerPositions[event.pointerId] = position;
      }

      position.set(event.pageX, event.pageY);
    }

    const onPointerDown = (ev: PointerEvent) => {
      if (ev.target == scope.canvas) {
        addPointer(ev);
      }
      if (_pointers.length == 1 && ev.target == scope.canvas) {
        scope.onmousedown(ev);
      } else {
      }
    };

    const onTouchStart = (ev: TouchEvent) => {
      if (ev.target == scope.canvas) {
        addPointer(ev.touches[0]);
      }
      if (_pointers.length == 1 && ev.target == scope.canvas) {
        const touch = ev.touches[0];
        const simulatedMouseEvent = new MouseEvent('mousedown', {
          bubbles: true,
          cancelable: true,
          view: window,
          detail: 1,
          screenX: touch.screenX,
          screenY: touch.screenY,
          clientX: touch.clientX,
          clientY: touch.clientY,
          ctrlKey: ev.ctrlKey,
          altKey: ev.altKey,
          shiftKey: ev.shiftKey,
          metaKey: ev.metaKey,
          button: 0,
          buttons: 1,
          relatedTarget: null
        });
        scope.onmousedown(simulatedMouseEvent);
      }
    };

    const onPointerMove = (ev: PointerEvent) => {
      trackPointer(ev);
      if (_pointers.length <= 1) {
        if(ev.target == scope.canvas)
        {
          scope.onmousemove(ev);
        }
      }
    };
    const onPointerUp = (ev: PointerEvent) => {
      if (_pointers.length <= 1) {
        if (
          (ev.target as Element).classList.contains('ant-image') ||
          (ev.target as Element).classList.contains('image') ||
          (ev.target as Element).classList.contains('ant-image-img')
        ) {
          scope.onmouseup(ev);
          return;
        }
        if (ev.target == scope.canvas) {
          scope.onmouseup(ev);
        }
      } else {
        // // Handle two-finger gesture end
        // if (_pointers.length === 2 && ev.target === scope.canvas) {
        //   // Reset pointers and force update
        //   scope._pointers.length = 0;
        //   scope._pointerPositions = {};
        //   scope.update();
        // }
      }
      removePointer(ev);
    };

    const onLeftPanelTouchMove = (ev: TouchEvent) => {
      const touch = ev.touches[0];
      const simulatedMouseEvent = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        view: window,
        detail: 1,
        screenX: touch.screenX,
        screenY: touch.screenY,
        clientX: touch.clientX,
        clientY: touch.clientY,
        ctrlKey: ev.ctrlKey,
        altKey: ev.altKey,
        shiftKey: ev.shiftKey,
        metaKey: ev.metaKey,
        button: 0,
        buttons: 1,
        relatedTarget: null
      });
      trackPointer(simulatedMouseEvent);
      if (_pointers.length <= 2) {
        scope.onmousemove(simulatedMouseEvent);
      }
    };

    const onLeftPanelTouchEnd = (ev: TouchEvent) => {
      const touch = ev.changedTouches[0];
      const simulatedMouseEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true,
        view: window,
        detail: 1,
        screenX: touch.screenX,
        screenY: touch.screenY,
        clientX: touch.clientX,
        clientY: touch.clientY,
        ctrlKey: ev.ctrlKey,
        altKey: ev.altKey,
        shiftKey: ev.shiftKey,
        metaKey: ev.metaKey,
        button: 0,
        buttons: 1,
        relatedTarget: null
      });
      if (_pointers.length <= 2) {
        if ((ev.target as Element).classList.contains('ant-image')) {
          scope.onmouseup(simulatedMouseEvent);
          return;
        }
        if (ev.target == scope.canvas) {
          scope.onmouseup(simulatedMouseEvent);
        }
      }
      removePointer(simulatedMouseEvent);
    };

    const onPointerOut = (ev: PointerEvent) => {
      removePointer(ev);
    };
    const onDbClick = (ev: any) => {
      scope._focus_div = scope.canvas;
      _pointers.length = 0;
      scope._pointerPositions = {};
      _pointerPositions = scope._pointerPositions;

      scope.ondbclick(ev);
    };
    this.canvas.onmousedown = ev => {
      scope._focus_div = scope.canvas;
      // scope.onmousedown(ev);
    };
    this.canvas.onmouseover = ev => {
      scope._focus_div = scope.canvas;
    };

    this.canvas.ontouchmove = ev => {
      scope._focus_div = scope.canvas;
    };

    this.canvas.onpointermove = ev => {};

    this.canvas.onmousemove = ev => {
      scope._focus_div = scope.canvas;
      scope._mouse_over_div = scope.canvas;
    };
    this.canvas.onmouseleave = ev => {
      scope._focus_div = null;
      scope._mouse_over_div = null;
    };

    this.body_div_container.onpointerdown = null;
    this.body_div_container.onpointermove = null;
    this.body_div_container.onpointerup = null;
    this.body_div_container.ontouchstart = null;

    this.body_div_container.addEventListener('pointerdown', onPointerDown);
    this.body_div_container.addEventListener('pointermove', onPointerMove);
    this.body_div_container.addEventListener('pointerup', onPointerUp);
    this.body_div_container.addEventListener('pointerout', onPointerOut);
    this.body_div_container.addEventListener('pointerleave', onPointerOut);

    // this.body_div_container.addEventListener('touchstart',onTouchStart);
    if (this.side_div_container) {
      this.side_div_container.ontouchmove = null;
      this.side_div_container.ontouchend = null;
      this.side_div_container.addEventListener('touchmove', onLeftPanelTouchMove);
      this.side_div_container.addEventListener('touchend', onLeftPanelTouchEnd);
    }

    // this.body_div_container.onmousemove = (ev) => {

    //     console.log("mouse move");

    //     scope.onmousemove(ev);

    // }
    // this.body_div_container.onmousedown = (ev) => {
    //     console.log("mouse down");

    //     scope.onmousedown(ev);
    // }

    // this.body_div_container.onmouseup = (ev) => {

    //     console.log("mouse up");

    //     scope.onmouseup(ev);
    // }

    this.canvas.oncontextmenu = ev => {
      scope.oncontextmenu(ev);
    };
    this.canvas.ondblclick = onDbClick;

    // this.body_div_container.ondblclick = (ev) => {
    //     scope.ondbclick(ev);
    // }
    this.body_div_container.ontouchmove = ev => {
      ev.preventDefault();
    };

    const wheel_event = (ev: any) => {
      if (ev.target === scope.canvas) {
        scope.onwheel(ev);
      }
    };
    // console.log(this.body_div_container);

    this.body_div_container.onwheel = null;

    // this.body_div_container.removeEventListener('wheel',wheel_event);
    this.body_div_container.addEventListener('wheel', wheel_event, { passive: true });

    document.body.onkeydown = ev => {
      scope.onkeydown(ev);
    };
    document.body.onkeyup = ev => {
      scope.onkeyup(ev);
    };

    // qt-like connect

    // this.connect(OperationManager.signalUndoable,{
    //     target:this,
    //     ui_name :"Undoable",
    //     callback: ()=>{
    //         console.log("undoable...");
    //     }
    // });

    // this.connect(OperationManager.signalRedoable,{
    //     target:this,
    //     ui_name :"Redoable",
    //     callback: ()=>{
    //         console.log("redoable...");

    //     }
    // });

    // this.updateBackgroundGrids();
  }

  resetPointers() {
    this._pointers.length = 0;
    this._pointerPositions = {};
  }
  bindCanvas(canvas: HTMLCanvasElement = null, sc_ratio: number = 1) {
    if (!canvas) return;
    let t_sc = 1;
    const is_mobile = this.isMoblie;
    if (is_mobile) t_sc = 1.0;
    if (canvas.parentElement) {
      // 如果有父亲节点
      let width = 0;
      let height = 0;

      width = canvas.parentElement.clientWidth * t_sc;
      height = canvas.parentElement.clientHeight * t_sc;

      let devicePixelRatio = window.devicePixelRatio > 2 ? 2 : window.devicePixelRatio;
      if (is_mobile) {
        devicePixelRatio = this.Configs.default_mobile_device_pixel_ratio;
      } else {
        // devicePixelRatio = window.devicePixelRatio;
        devicePixelRatio = this.Configs.default_pc_device_pixel_ratio;
      }

      canvas.width = width * devicePixelRatio * sc_ratio;
      canvas.height = height * devicePixelRatio * sc_ratio;

      canvas.style.width = width * sc_ratio + 'px';
      canvas.style.height = height * sc_ratio + 'px';
    }

    this.canvas = canvas;
    if (this.painter == null) {
      this.painter = new TPainter(this.canvas);
    } else {
      this.painter.bindCanvas(this.canvas);
    }
    let t_offset_sc = (t_sc - 1) / 2;
    canvas.style.left = `-${canvas.parentElement.clientWidth * t_offset_sc}px`;
    canvas.style.top = `-${canvas.parentElement.clientHeight * t_offset_sc}px`;

    this.registerEvents();
    this._focus_div = null;
    this._mouse_over_div = null;

    this.canvas.focus();

    super.bindCanvas(canvas);
  }

  public addHouseLeftPanelEvent() {
    this._addHouseLeftPanelEvent();
  }

  private _addHouseLeftPanelEvent() {
    this.side_house_panel_container = document.getElementById('pad_house_left_panel') as HTMLDivElement;
    if (this.side_house_panel_container) {
      this.side_house_panel_container.ontouchmove = null;
      this.side_house_panel_container.ontouchend = null;
      this.side_house_panel_container.addEventListener('touchmove', this._onLeftPanelTouchMove);
      this.side_house_panel_container.addEventListener('touchend', this._onLeftPanelTouchEnd);
    }
  }

  private _removeHouseLeftPanelEvent() {
    if (this.side_house_panel_container) {
      this.side_house_panel_container.removeEventListener('touchmove', this._onLeftPanelTouchMove);
      this.side_house_panel_container.removeEventListener('touchend', this._onLeftPanelTouchEnd);
    }
  }

  private trackPointer(event: any) {
    let x, y;
    x = event.pageX;
    y = event.pageY;

    this._pointerPositions[event.pointerId] = new Vector2(x, y);
  }

  private removePointer(event: any) {
    delete this._pointerPositions[event.pointerId];
  }

  private _onLeftPanelTouchMove = (ev: TouchEvent) => {
    const touch = ev.touches[0];
    const simulatedMouseEvent = new MouseEvent('mousemove', {
      bubbles: true,
      cancelable: true,
      view: window,
      detail: 1,
      screenX: touch.screenX,
      screenY: touch.screenY,
      clientX: touch.clientX,
      clientY: touch.clientY,
      ctrlKey: ev.ctrlKey,
      altKey: ev.altKey,
      shiftKey: ev.shiftKey,
      metaKey: ev.metaKey,
      button: 0,
      buttons: 1,
      relatedTarget: null
    });
    this.trackPointer(simulatedMouseEvent);
    if (this._pointers.length <= 2) {
      this.onmousemove(simulatedMouseEvent);
    }
  };

  private _onLeftPanelTouchEnd = (ev: TouchEvent) => {
    const touch = ev.changedTouches[0];
    const simulatedMouseEvent = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      view: window,
      detail: 1,
      screenX: touch.screenX,
      screenY: touch.screenY,
      clientX: touch.clientX,
      clientY: touch.clientY,
      ctrlKey: ev.ctrlKey,
      altKey: ev.altKey,
      shiftKey: ev.shiftKey,
      metaKey: ev.metaKey,
      button: 0,
      buttons: 1,
      relatedTarget: null
    });
    if (this._pointers.length <= 2) {
      let classList = (ev.target as Element).classList;
      if (classList.contains('ant-image') || classList.contains('image') || classList.contains('ant-image-img')) {
        this.onmouseup(simulatedMouseEvent);
        return;
      }
      if (ev.target == this.canvas) {
        this.onmouseup(simulatedMouseEvent);
      }
    }
    delete this._pointerPositions[touch.identifier];
  };

  get designModeHandler() {
    return this.handlers[AI2DesignBasicModes.DesignMode] as AIMatchingModeHandler;
  }

  get layer_CadRoomFrameLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadRoomStrucure] as TCadRoomStrucureLayer;
  }
  get layer_OutLineLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadOutLine] as TCadOutLineLayer;
  }
  get layer_CadRoomNameLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadRoomName] as TCadRoomNameLayer;
  }
  get layer_CadFurnitureLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadFurniture] as TCadFurnitureLayer;
  }
  get layer_CadCabinetLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadCabinet] as TCadCabinetLayer;
  }
  get layer_CadEzdxfLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadEzdxfDrawing] as TEzdxfDataDrawingLayer;
  }

  get layer_LightingLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadLighting] as TCadRoomLightingLayer;
  }

  get layer_DecorationLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadDecorates] as TCadRoomDecoratesLayer;
  }
  get layer_CadSubAreaLayer() {
    return this.drawing_layers[
      CadDrawingLayerType.CadSubRoomAreaDrawing
    ] as TCadSubRoomAreaDrawingLayer;
  }
  get layer_CeilingLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadCeiling] as TCadCeilingLayer;
  }
  get layer_AILayoutLayer() {
    return this.drawing_layers[CadDrawingLayerType.AILayoutDrawing] as TAILayoutDrawingLayer;
  }

  get layer_AIMatchingLayer() {
    return this.drawing_layers[CadDrawingLayerType.AIMatchingDrawing] as TAIMatchingDrawingLayer;
  }
  get layer_CadFloorLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadFloorDrawing] as TCadFloorDrawingLayer;
  }

  get layer_DefaultBatchLayer() {
    return this._batch_drawing_layers[
      CadBatchDrawingLayerType.AICadDefaultBatch
    ] as TDrawingBatchLayer;
  }

  get layer_ExtDrawingBatchLayer() {
    return this._batch_drawing_layers[
      CadBatchDrawingLayerType.ExtDrawingBatch
    ] as TDrawingBatchLayer;
  }

  get layer_ExportCadLayer() {
    return this.drawing_layers[CadDrawingLayerType.ExportCadDrawing] as TExportCadDrawingLayer;
  }

  get layer_CadCopyImageLayer() {
    return this.drawing_layers[CadDrawingLayerType.CadCopyImageDrawing] as TCadCopyImageLayer;
  }

  get layer_RulerLayer() {
    return this.drawing_layers[CadDrawingLayerType.RulerDrawing] as TDrawingLayer;
  }

  get layer_RemodelingLayer() {
    return this.drawing_layers[CadDrawingLayerType.Remodeling] as TDrawingLayer;
  }

  get layer_DimensionOutterWallLayer() {
    return this.drawing_layers[
      CadDrawingLayerType.CadDimensionOutterWallElement
    ] as TDimensionOutterWallElementLayer;
  }

  onLayerVisibilityChanged() {}

  get _container_offset() {
    const rect = this.painter._canvas.getBoundingClientRect();
    return { x: rect.left, y: rect.top };
  }
  /**
   * 转化鼠标为图层的世界坐标
   * @param ev
   * @returns
   */
  public turnIntoCanvasMouseEvent(ev: MouseEvent) {
    let c_offset = this._container_offset;

    let ox = ev.pageX - c_offset.x;
    let oy = ev.pageY - c_offset.y;

    // console.log(ev.clientX,ev.clientY);

    // 屏幕翻转暂时不用处理
    // if(LayoutAI_App.instance._is_landscape)
    // {

    //     ox = ev.pageY - c_offset.x;
    //     oy = window.innerWidth - ev.pageX - c_offset.y;

    // }

    let pos = this.painter.canvas2world({ x: ox, y: oy });

    let res: I_MouseEvent = {
      posX: pos.x,
      posY: pos.y,
      altKey: ev.altKey,
      ctrlKey: ev.ctrlKey,
      shiftKey: ev.shiftKey,
      button: ev.button,
      buttons: ev.buttons,
      type: ev.type,
      _isOverCanvas: ev.target == this.canvas,
      _ev: ev
    };
    return res;
  }

  containerToWorld(pos: Vector2) {
    return this.painter.canvas2world(pos);
  }

  wordToContainer(pos: Vector2) {}
  onmousedown(ev: MouseEvent) {
    // ev.preventDefault();

    if (this._current_handler) {
      let t_ev = this.turnIntoCanvasMouseEvent(ev);

      this._current_handler.onmousedown(t_ev);
    }
  }
  onmousemove(ev: MouseEvent) {
    // ev.preventDefault();

    if (this._current_handler) {
      let t_ev = this.turnIntoCanvasMouseEvent(ev); // 移动画布是这里实现的
      this._current_handler.onmousemove(t_ev);
    }
  }
  onmouseup(ev: MouseEvent) {
    // ev.preventDefault();
    if (this._current_handler) {
      let t_ev = this.turnIntoCanvasMouseEvent(ev);

      this._current_handler.onmouseup(t_ev);
    }
  }
  oncontextmenu(ev: MouseEvent) {
    ev.preventDefault();
  }
  onkeydown(ev: KeyboardEvent) {
    if (ev.ctrlKey && ev.altKey && ev.code === 'KeyD') {
      this.setDebugMode(this._debug_mode == 1 ? 0 : 1);
      confirm(this._debug_mode ? '开发测试模式' : '生产模式');
    }
    if (this._current_handler) {
      this._current_handler.onkeydown(ev);
    }
  }
  onkeyup(ev: any) {
    if (this._current_handler) {
      this._current_handler.onkeyup(ev);
    }
  }

  ondbclick(ev: MouseEvent) {
    if (this.layout_container) {
      // this.layout_container.focusOnRoom(null);
      this.update();
    }
    if (this._current_handler) {
      let t_ev = this.turnIntoCanvasMouseEvent(ev);

      this._current_handler.ondbclick(t_ev);
    }
  }

  onwheel(ev: WheelEvent) {
    if (this._current_handler) {
      if (this._focus_div == this.canvas || this._mouse_over_div == this.canvas) {
        this._current_handler.onwheel(ev);
      }
    }
    // this.updateBackgroundGrids();
  }
  onViewScale(sc: number = 1) {
    if (this.painter) {
      this.painter._p_sc *= sc;
      this.update();
    }
  }

  /**
   *  绘制函数
   */
  onDraw() {
    this.painter.clean();

    this.painter.enter_drawpoly();
    if (this._current_handler) {
      this._current_handler.drawCanvas();
    }

    this.painter.leave_drawpoly();
  }

  /**
   *   更新
   */
  update() {
    this.onDraw();
  }

  /**
   *  进入模式
   */

  public setMode(mode: string) {
    this._previous_mode = this._current_handler ? this._current_handler.name : '';

    if (this._current_handler) {
      this._current_handler.leave();
    }

    this._current_handler = this.handlers[mode] || null;

    if (this._current_handler) {
      this._current_handler.enter();
      
      // 如果进入HouseDesignMode，添加左侧面板的触摸事件
      if (mode === AI2DesignBasicModes.HouseDesignMode) {
        this._addHouseLeftPanelEvent();
      } else {
        // 如果离开HouseDesignMode，移除左侧面板的触摸事件
        if (this._previous_mode === AI2DesignBasicModes.HouseDesignMode) {
          this._removeHouseLeftPanelEvent();
        }
      }
    }
  }

  _runCommand(cmd_str: string) {
    // console.log(cmd_str);

    if (this.handlers[cmd_str] !== undefined) {
      this.setMode(cmd_str);
      return;
    }

    if (this._current_handler) {
      this._current_handler.runCommand(cmd_str);
    }
  }

  // 初始化属性面板，需要在实例化后调用
  initAttribute() {}

  setCanvasCursorState(state: LayoutAI_CursorState) {
    if (!this.canvas) return;
    if (state == LayoutAI_CursorState.Default) {
      this.canvas.className = 'canvas';
    } else if (state == LayoutAI_CursorState.Drawing) {
      this.canvas.className = 'canvas canvas_drawing';
    } else if (state == LayoutAI_CursorState.Moving) {
      this.canvas.className = 'canvas canvas_moving';
    } else if (state == LayoutAI_CursorState.Leftmove) {
      this.canvas.className = 'canvas canvas_leftmove';
    } else if (state == LayoutAI_CursorState.Rightmove) {
      this.canvas.className = 'canvas canvas_rightmove';
    } else if (state == LayoutAI_CursorState.Acrossmove) {
      this.canvas.className = 'canvas canvas_acrossmove';
    } else if (state == LayoutAI_CursorState.verticalmove) {
      this.canvas.className = 'canvas canvas_verticalmove';
    } else if (state == LayoutAI_CursorState.Text) {
      this.canvas.className = 'canvas canvas_text';
    } else if (state == LayoutAI_CursorState.Pointer) {
      this.canvas.className = 'canvas canvas_pointer';
    } else if (state == LayoutAI_CursorState.SplitWall) {
      this.canvas.className = 'canvas canvas_splitWall';
    }
  }

  getExSubHandler(name: string) {
    if (!this._ext_sub_handlers) return null;
    return this._ext_sub_handlers[name];
  }
  setExSubHandler(name: string, handler: T_Handlerbase) {
    if (!this._ext_sub_handlers) this._ext_sub_handlers = {};
    this._ext_sub_handlers[name] = handler;

    return handler;
  }

  /**
   *  保存xml_data到本地localstorage, 目前只在开发模式开放
   */
  async _save_to_local_XmlSchemeData() {
    if (LayoutAI_App.IsDebug) {

      if(IndexedDBService.isReady)
      {
        let data = {
           id : "layout_ai_scheme_xml_json",
           data : this.layout_container.toXmlSchemeData()
        }
        await IndexedDBService.instance.addData(data,IndexedDBService.DefaultTable);
      }
      else{
        if (localStorage) {
          let saved_data = this.layout_container.toXmlSchemeData();
          localStorage.setItem('layout_ai_scheme_xml_json', JSON.stringify(saved_data));
  
        }
      }

    }
  }

  async _load_local_XmlSchemeData() {
    if (LayoutAI_App.IsDebug) {
       
      if(IndexedDBService.isReady)
      {
          let item :{id:string,data?:I_SwjXmlScheme}=  await IndexedDBService.instance.getDataById('layout_ai_scheme_xml_json',IndexedDBService.DefaultTable);

          if(item?.data)
          {
            console.log(item.data);
            this.layout_container.fromXmlSchemeData(item.data);
            this.update();
            return true;
          }
      }
      else{
        
      }

    }
    return false;
  }

  /**
   *  保存当前布局到 本地localStorage, 用于相似迁移
   */
  _save_to_local_model_rooms() {
    let res_rooms = this.layout_container._rooms.map(room =>
      this.layout_container.saveTRoomToJson(room, null, false)
    );
    let json_text = JSON.stringify(res_rooms);
    if (localStorage) {
      // localStorage.setItem('layoutai_local_model_rooms', json_text);
    }
  }

  _load_local_model_rooms(load_once: boolean = true) {
    if (localStorage) {
      let json_text = localStorage.getItem('layoutai_local_model_rooms');
      if (json_text) {
        try {
          let res_rooms: { room_data: I_Room; swj_room_data: I_SwjRoom }[] = JSON.parse(json_text);
          let room_data_list = res_rooms.map(data => {
            let room_data: I_ExtRoom = {
              room: data.room_data,
              swj_room: data.swj_room_data,
              scheme_id: data.swj_room_data.scheme_id,
              room_id: '' + data.swj_room_data.uid,
              scheme_room_id: '本地' + data.swj_room_data.scheme_id
            };
            return room_data;
          });

          console.log('本地临时样板间列表', room_data_list);
          this.layout_graph_solver.addModelRooms(
            room_data_list,
            I_ModelRoomSourceType.LocalStorage
          );

          load_once && localStorage.removeItem('layoutai_local_model_rooms');
        } catch (error) {}
      }
    }
  }
}
