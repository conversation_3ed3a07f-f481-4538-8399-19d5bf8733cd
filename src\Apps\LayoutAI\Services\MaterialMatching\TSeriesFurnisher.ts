import { ApplyTo3DService } from "../Sunvega3D/ApplyTo3DService";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import SunvegaAPI from "@api/clouddesign";
import { Logger } from "@/Apps/LayoutAI/Utils/logger";
import { I_MaterialMatchingItem, toStringForMaterialMatchingItem } from "../../Layout/IMaterialInterface";
import { TFigureElement } from "../../Layout/TFigureElements/TFigureElement";
import { TRoom } from "../../Layout/TRoom";
import { TLayoutEntityContainer } from "../../Layout/TLayoutEntities/TLayoutEntityContainter";
import { TSeriesSample } from "../../Layout/TSeriesSample";
import { TMaterialMatcher } from "./TMaterialMatcher";
import { Scene3DEvents } from "../../Scene3D/Scene3DEvents";
import { Scene3DEventsProcessor } from "../../Scene3D/Scene3DEventsProcessor";
import { TLayoutFineTuningManagerToolUtil } from "../../Layout/TLayoutFineTuningOperation/TLayoutFineTuningManagerToolUtil";
import { TBaseGroupEntity } from "../../Layout/TLayoutEntities/TBaseGroupEntity";
import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import { compareNames } from "@layoutai/z_polygon";
import { SeriesMode, Series2LightMap } from "../AutoLighting/configs/SeriesLightConfig";
import { AutoLightingService } from "../AutoLighting/AutoLightService";
import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";
import { SceneLightMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { PERMISSIONS } from "@/config/permissions";
import { Permissions } from "../../setting/Permissions";
import { LightManager } from "../../Scene3D/LightManager";


/**
 *   套系匹配器
 */
export class TSeriesFurnisher {

    protected _current_rooms: TRoom[] = [];

    //需要布置的房间及其所使用的套系或样板间
    room2SeriesSampleMap: Map<TRoom, TSeriesSample>;


    //当前选中的图元
    _figure_element_selected: TFigureElement;

    _kitchen_use_default_material: boolean

    _enable_hard_furnish: boolean;
    _enable_3dpreview_material_furnish: boolean = true;
    _logger: Logger = null;

    _debug: boolean = false;

    protected static _instance: TSeriesFurnisher = null;

    _current_series: TSeriesSample = null;
    _selected_series: TSeriesSample = null;
    _is_use_template: boolean = true; // 是否自定义灯光模板

    constructor() {
        this.room2SeriesSampleMap = new Map();
        this._current_rooms = [];
        this._enable_hard_furnish = true;
        this._kitchen_use_default_material = true;

        this._logger = Logger.instance;

        this._enable_3dpreview_material_furnish = Permissions.instance.hasPermission(PERMISSIONS.SERIES.SHOW_WHITE_MODEL);
    }
    static get instance() {
        if (!TSeriesFurnisher._instance) {
            TSeriesFurnisher._instance = new TSeriesFurnisher();
        }
        return TSeriesFurnisher._instance;
    }

    public disable3dPreviewMaterialFurnish()
    {
        this._enable_3dpreview_material_furnish = false;
    }

    public enable3dPreviewMaterialFurnish()
    {
        this._enable_3dpreview_material_furnish = true;
    }

    get container(): TLayoutEntityContainer {
        if (LayoutAI_App.instance) {
            return (LayoutAI_App.instance as TAppManagerBase).layout_container;
        }
        return null;
    }

    get manager() {
        return LayoutAI_App.instance as TAppManagerBase;
    }

    /**
     *  swj_layout_scheme_layer 的房间列表
     */
    get room_list() {
        return this.container._rooms;
    }

    get current_rooms() {
        return this._current_rooms;
    }

    set current_rooms(rooms: TRoom[]) {
        this._current_rooms.length = 0;
        this._current_rooms.push(...rooms);
    }
    public clearCurrentRooms() {
        this._current_rooms = [];
    }

    update() {
        LayoutAI_App.instance.update();
    }

    public refreshRoom2SeriesSample(allRoomList: TRoom[]) {
        let newRoomSeriesMap: Map<TRoom, TSeriesSample> = new Map();
        this.room2SeriesSampleMap.forEach((value, key) => {
            let t_room = allRoomList.find((room: TRoom) => room.uid == key.uid);
            if (t_room) {
                newRoomSeriesMap.set(t_room, value);
                t_room._series_sample_info = value;
            }
        });
        this.room2SeriesSampleMap = newRoomSeriesMap;
    }

    public clearRoom2SeriesSample(filterTarget: TRoom, ignoreLocked: boolean = false) {
        let roomsToClear: TRoom[] = [];
        if (filterTarget == null) {
            for (let [roomItem, seriesSampleItem] of this.room2SeriesSampleMap.entries()) {
                roomsToClear.push(roomItem);
            }
        } else {
            roomsToClear = [filterTarget];
        }
        roomsToClear.forEach((room: TRoom) => {
            if ((ignoreLocked && room.locked)) {
                return;
            }
            room.clearMatchedMaterials();
            room.isSelectSeries = false;
            room.clearApplyScope();
            this.room2SeriesSampleMap.delete(room);
        });
    }

    private async setAutoLightConfigBySeriesDefined(series: TSeriesSample) {
        if (!series) return;
        this._selected_series = series;
        let URL = series.lightTemplateDataUrl
        if (URL) {
            fetch(URL)
                .then((data) => data.json())
                .then((data) => {
                    console.log("当前套系关联的灯光模板：", data);
                    // 自动补光
                    AutoLightingService.instance.setRulers(data);

                    // // 环境光
                    // LightManager.getDayAmbientLight().intensity = ambientData[0].intensity;
                    // LightManager.getDayAmbientLight().color.set(ambientData[0].color);
                    // // 阳光
                    // LightManager.sunlight.intensity = sunlightData[0].intensity;
                    // LightManager.sunlight.color.set(sunlightData[0].color);
                })
                .catch((error) => {
                    console.error('setAutoLightConfigBySeriesDefinedError:', error);
                    this.setAutoLightConfigBySeries(series);
                });
        } else {
            this.setAutoLightConfigBySeries(series);
        }
    }

    private setAutoLightConfigBySeries(series: TSeriesSample) {
        if (!series) return;
        // console.log("当前套系类型：", series);
        this._selected_series = series;
        let comment = series.ruleComment || SeriesMode.Medium;
        let seriesMode: SeriesMode = SeriesMode.Default;
        // let sceneLightMode =  LayoutAI_App.instance.scene3D.getLightMode();
        let lightMode: LightMode = LightMode.Night;
        // if (sceneLightMode === SceneLightMode.Day) {
        //     lightMode = LightMode.Day;
        // } else if (sceneLightMode === SceneLightMode.Night) {
        //     lightMode = LightMode.Night;
        // }
        // console.log("当前灯光类型：", sceneLightMode, lightMode);
        for (const modeValue of Object.values(SeriesMode)) {
            if (comment.includes(modeValue)) {
                seriesMode = modeValue;
                break;
            }
        }
        let config = Series2LightMap[lightMode][seriesMode];
        console.log("当前应用灯光：", seriesMode, config);
        AutoLightingService.instance.setRulers(config);
    }

    /**
     * 套系应用流程（套系选中后的处理流程， 这是套系应用流程在本工程内核层的起点）
     * (1) 用户点击选中套系后，UI层会通过事件发送的机制传递到TSeriesFurnisher，然后触发此方法
     * (2) 根据选中套系的scope，决定是否需要进行套系应用
     * 名词解释：
     * seriesSample == series == 风格套系 == 套系
     * scope: 应用范围（需要应用套系的一个或多个图元种类的集合），包括soft（软装）、cabinet（定制柜）、hard（硬装）、remaining（补全）
     * 补全：房间在应用了套系后，如果房间中还有未布置素材的范围和图元，则需要对这些范围和图元进行套系应用，也就是“补全”功能。
     * targetRooms: 需要应用套系的目标房间列表
     * options: 套系应用的选项，包括needsDiversifyMatched（是否需要多样化匹配）、updateTopViewBy3d（是否需要更新3D视图）、needAutoFinetune（是否需要自动微调）
     * 
     * @param scope  
     * @param series 
     * @param targetRooms 
     * @param options 
     */
    async onSeriesSampleSelected(
        scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean },
        series: TSeriesSample,
        targetRooms: TRoom[] = null,
        options: {
            needsDiversifyMatched?: boolean,
            updateTopViewBy3d?: boolean,
            needAutoFinetune?: boolean
        } = { needsDiversifyMatched: true, updateTopViewBy3d: true, needAutoFinetune: true }) {
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "应用套系中..." });

        if (this._is_use_template){
            await this.setAutoLightConfigBySeriesDefined(series);
        } else {
            this.setAutoLightConfigBySeries(series);
        }

        //取消被选中图元的选中状态
        LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
        //如果未指定目标房间列表，则使用当前选中的房间列表
        if (!targetRooms) targetRooms = this.current_rooms;
        if (targetRooms != null) {
            //用于记录需要应用套系的房间列表
            let needFurnishRooms: TRoom[] = [];
            //遍历目标房间列表，对每个房间进行套系应用，采用异步并行处理
            let allPromises = targetRooms.map(async (roomItem: TRoom) => {
                //如果房间被锁定，则此房间跳过套系应用流程
                if (roomItem.locked) return;
                //将需要应用套系的房间添加到需要应用套系的房间列表中
                needFurnishRooms.push(roomItem);
                //创建一个新的套系实例
                let newSeries = series?.kgId ? new TSeriesSample(series) : roomItem._series_sample_info;
                let promise_list = null;
                //如果scope.remaining为false，则此房间需要走常规的应用套系流程
                if (scope.remaining == false) {
                    //记录此房间的需要应用套系的范围和套系
                    roomItem.addApplyScope(scope, newSeries);
                    //设置当前应用范围
                    roomItem.setCurrentApplyScope(scope);
                    //清除当前应用范围中所属图元匹配到的素材
                    roomItem.clearMatchedMaterialsInCurrentScope();
                    //记录房间和套系的映射关系
                    this.room2SeriesSampleMap.set(roomItem, newSeries);
                    //尝试应用套系
                    promise_list = this.tryToFurnishRooms([roomItem]);
                    //等待所有套系应用完成
                    return Promise.allSettled(promise_list).then(() => {
                        //更新UI层：更新选中房间列表、房间套系的映射关系
                        this.emitSeriesSamplesWithOrdering();
                        // LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
                        roomItem.kgId = (series as any)["kgId"];
                    });
                } else {
                    //这是套系补全功能的流程
                    //获取房间已应用了套系的范围
                    let furnishedApplyScope = roomItem.furnishedApplyScope;
                    //根据已应用套系的范围，计算和设置当前应用范围
                    let applyScope = { soft: furnishedApplyScope && !furnishedApplyScope.soft, cabinet: furnishedApplyScope && !furnishedApplyScope.cabinet, hard: furnishedApplyScope && !furnishedApplyScope.hard, remaining: true };
                    //设置此房间的当前应用范围
                    roomItem.setCurrentApplyScope(applyScope);
                    //尝试对此空间应用套系，走补全的套系应用流程
                    promise_list = this.tryToFurnishRoomRemainings([roomItem], newSeries);
                    //记录此房间的应用范围和风格套系的映射关系
                    roomItem.addApplyScope(applyScope, newSeries);
                    //等待所有套系应用完成
                    return Promise.allSettled(promise_list);
                }

            });

            await Promise.allSettled(allPromises); // 素材匹配完成

         
            this._logger.uploadLogs();
            if (options.needsDiversifyMatched) {
                //对需要应用套系的房间的图元进行多样化匹配
                this.diversifyMatchedMatetrial(needFurnishRooms);
            }

            LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
            this.update();

            //如果3D场景有效，则通知更新3D场景
            if (LayoutAI_App.instance.scene3D) {
                if (options.updateTopViewBy3d) {
                    await Scene3DEventsProcessor.UpdateEntitiesNeedsTopViewImage();
                }
            }
            //应用套系后，通知UI层去根据套系生成报价数据
            LayoutAI_App.emit(EventName.quoteDataSeries, series);
            this._current_series = series; // 当前的套系

            //更新UI层，重新绘制canvas
            this.update();

            // //如果需要自动微调，则进行自动微调
            // if (options.needAutoFinetune) {
            //     await this.autoFineTuning();
            // }
        }
        //如果当前没有房间被选中，则通知UI层弹出提示，提示用户先选择空间
        if (this.current_rooms == null || this.current_rooms.length == 0) {
            LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "info", message: LayoutAI_App.t("请先选择空间") });
        }

        // 微调后需要调整组合类图元的尺寸
        let groupEntities: TBaseGroupEntity[] = [];
        this.current_rooms.forEach((room) => {
            room._furniture_list.forEach((figure) => {
                if (figure.furnitureEntity instanceof TBaseGroupEntity) {
                    groupEntities.push(figure.furnitureEntity as TBaseGroupEntity);
                }
            });
        });
        groupEntities.forEach((entity) => {
            entity.updateSize();
        });
        LayoutAI_App.emit(EventName.ApplySeriesSample, { opening: false, title: "" });
    }
    deleteSeriesSample() {
        this.current_rooms.forEach((item: TRoom) => {
            if (item.locked) return;
            this.room2SeriesSampleMap.delete(item);
            item.clearMatchedMaterials();
            item.clearApplyScope();
        });
        LayoutAI_App.emit_M(EventName.SelectingRoom, { current_rooms: this.current_rooms });
        LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
        this.manager.updateScene3D();
        this.update();
    }

    updateCurrentRooms() {
        // 这里从代码上看是在这里个实例中存一份当前房间的数据 应该不能直接拿到、layoutcontainer的_room本身
        // 否则上面在set current_rooms 的时候将layoutcontainer的_room 也修改了
        this._current_rooms = this.container._selected_room ? [this.container._selected_room] : this.container._rooms.concat();
    }
    emitSeriesSamplesWithOrdering(options: { clickOnRoom?: boolean } = {}) {
        LayoutAI_App.emit_M(EventName.SelectingRoom, { ...options, current_rooms: this.current_rooms });
        LayoutAI_App.emit(EventName.Room2SeriesSampleRoom, this.orderRoom2SeriesSample(this.room2SeriesSampleMap, this.current_rooms));
    }
    orderRoom2SeriesSample(map: Map<TRoom, TSeriesSample>, rooms: TRoom[]): [TRoom, TSeriesSample][] {
        let newArray: [TRoom, TSeriesSample][] = [];
        for (let i = rooms.length - 1; i >= 0; i--) {
            if (map.has(rooms[i])) {
                newArray.push([rooms[i], map.get(rooms[i])]);
            }
        }
        if (map != null) {
            map.forEach((value: TSeriesSample, key: TRoom, map: Map<TRoom, TSeriesSample>) => {
                let found = false;
                for (let j = 0; j < newArray.length; j++) {
                    if (key === newArray[j][0]) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    newArray.push([key, value]);
                }
            });
        }

        for (let room of this.room_list) {
            if (map.has(room)) {
                room._series_sample_info = map.get(room);
            }
            else {
                if (room._series_sample_info) delete room._series_sample_info;
            }
        }
        return newArray;
    }

    tryToFurnishRoomsFor3dPreview(targetRooms: TRoom[]): Promise<any>[] {
        if(!this._enable_3dpreview_material_furnish)
        {
            return [];
        }
        let default3dPreviewSeries = new TSeriesSample({
            kgId: 334306,
            seriesStyle: "白模套系"
        });

        let logger = this._logger;
        let promises: Promise<any>[] = [];
        for (let roomItem of targetRooms) {
            roomItem._furniture_list.forEach(ele => ele._room = roomItem);

            if (roomItem._furniture_list.length > 0) {
                promises.push(TMaterialMatcher.instance.furnishRoomFor3dPreview(roomItem, default3dPreviewSeries, logger));
            }
        }
        return promises;
    }

    /**
     * 尝试对指定的房间列表应用套系、匹配套系素材
     * @param targetRooms 需要应用套系的房间列表
     * @returns 
     */
    tryToFurnishRooms(targetRooms: TRoom[]): Promise<any>[] {
        let logger = this._logger;
        let promises: Promise<any>[] = [];
        for (let roomItem of targetRooms) {
            // 先将furniture 绑定room
            roomItem._furniture_list.forEach(ele => ele._room = roomItem);
            //更新房间已应用套系的范围
            roomItem.updateFurnishedApplyScope();
            //如果房间已应用套系，并且房间中有图元，则匹配套系素材
            if (this.room2SeriesSampleMap.has(roomItem) && (roomItem._furniture_list.length > 0)) {
                //所有匹配到的素材列表
                let allMatchedMaterials: I_MaterialMatchingItem[] = [];
                //从room2SeriesSampleMap（房间和套系的映射关系）中获取房间当前所对应的套系
                //然后进入房间匹配套系素材的流程
                promises.push(TMaterialMatcher.instance.furnishRoom(roomItem, this.room2SeriesSampleMap.get(roomItem), logger, allMatchedMaterials));
            }
        }
        return promises;
    }

    tryToFurnishRoomRemainings(targetRooms: TRoom[], sereis: TSeriesSample): Promise<any>[] {
        let logger = this._logger;
        let allSoftMaterials: I_MaterialMatchingItem[] = [];
        let promises: Promise<any>[] = [];
        for (let roomItem of targetRooms) {
            roomItem.updateRemainingFurnishedApplyScope();
            roomItem._furniture_list.forEach(ele => ele._room = roomItem);
            if (roomItem._furniture_list.length > 0) {
                promises.push(TMaterialMatcher.instance.furnishRoom(roomItem, sereis, logger, allSoftMaterials));
            }
        }
        return promises;
    }

    /**
     * 对需要应用套系的房间的图元进行多样化匹配，即同一类图元所匹配到的素材在不同的房间中尽可能不一样。
     * 根据多样化匹配规则的不一样，将图元分成三类：
     * 1. 床、背景墙、窗帘、地毯、挂画、墙饰、主灯、床尾凳、梳妆台、书桌、床具组合、书桌组合、榻榻米组合、沙发组合、岛台组合、餐桌椅组合
     * 2. 背景墙、电视背景墙、沙发背景墙
     * 3. 床头柜、床头吊灯
     * @param rooms 
     */
    diversifyMatchedMatetrial(rooms: TRoom[]) {
        let needUpdate: Boolean = false;
        let diversifyModelLocs = ["床", "背景墙", "窗帘", "地毯", "挂画", "墙饰", "主灯", "床尾凳", "梳妆台", "书桌", "床具组合", "书桌组合", "榻榻米组合", "沙发组合", "岛台组合", "餐桌椅组合"];
        diversifyModelLocs.forEach((modelLoc: string) => {
            let allModellocFigureElements: TFigureElement[] = [];
            for (let roomItem of rooms) {
                if (!roomItem._furniture_list) continue;
                roomItem._furniture_list.filter((ele: TFigureElement) => ele.modelLoc.endsWith(modelLoc)).forEach((ele: TFigureElement) => {
                    allModellocFigureElements.push(ele);
                });
                if(roomItem.decoration_elements)
                {
                    allModellocFigureElements.push(...roomItem.decoration_elements);
                }
            }
            let allModellocMatchedMaterials: I_MaterialMatchingItem[] = [];
            allModellocFigureElements.forEach((ele: TFigureElement) => {
                let matchedMaterial = ele._matched_material;
                if (matchedMaterial == null) return;
                if (allModellocMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId != null && matchedMaterial.modelId != null && item.modelId == matchedMaterial.modelId).length > 0) {
                    for (let i = 0; i < ele._candidate_materials.length; i++) {
                        let candidateMaterial = ele._candidate_materials[i];
                        if (!TFigureElement.checkIsMatchedSizeSuitable(ele, candidateMaterial)) {
                            continue;
                        }
                        if (allModellocMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId == candidateMaterial.modelId).length == 0) {
                            TMaterialMatcher.instance.replaceMaterial(ele, candidateMaterial, this.room_list, { post_process_in_room: true, needs_update_3d: true });
                            this._logger.info("Replace diversify matetrial for: " + ele.toString() + "\n     " + toStringForMaterialMatchingItem(matchedMaterial) + "\n  => " + toStringForMaterialMatchingItem(candidateMaterial));
                            needUpdate = true;
                            break;
                        }
                    }
                }
                allModellocMatchedMaterials.push(ele._matched_material);
            });
        });
        let diversifyBackgorupWallModelLocs = ["背景墙", "电视背景墙", "沙发背景墙"];
        diversifyBackgorupWallModelLocs.forEach((modelLoc: string) => {
            let allModellocFigureElements: TFigureElement[] = [];
            for (let roomItem of rooms) {
                if (!roomItem._furniture_list) continue;
                roomItem._furniture_list.filter((ele: TFigureElement) => ele.modelLoc.endsWith(modelLoc)).forEach((ele: TFigureElement) => {
                    allModellocFigureElements.push(ele);
                });
            }
            let allModellocMatchedMaterials: I_MaterialMatchingItem[] = [];
            allModellocFigureElements.forEach((ele: TFigureElement) => {
                let matchedMaterial = ele._matched_material;
                if (matchedMaterial == null) return;
                if (allModellocMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId != null && matchedMaterial.modelId != null && item.modelId == matchedMaterial.modelId).length > 0) {
                    let candidateMaterial: I_MaterialMatchingItem = null;
                    for (let i = 0; i < ele._candidate_materials.length; i++) {
                        candidateMaterial = ele._candidate_materials[i];
                        if (!TFigureElement.checkIsMatchedSizeSuitable(ele, candidateMaterial)
                            || allModellocMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId == candidateMaterial.modelId).length > 0) {
                            candidateMaterial = null;
                            continue;
                        }
                    }
                    if (candidateMaterial == null) {
                        for (let i = 0; i < ele._candidate_materials.length; i++) {
                            candidateMaterial = ele._candidate_materials[i];
                            if (allModellocMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId == candidateMaterial.modelId).length > 0) {
                                continue;
                            }
                        }
                    }
                    if (candidateMaterial != null) {
                        ele.replaceMatchedMaterial(candidateMaterial);
                        this._logger.info("Replace diversify matetrial for: " + ele.toString() + "\n     " + toStringForMaterialMatchingItem(matchedMaterial) + "\n  => " + toStringForMaterialMatchingItem(candidateMaterial));
                        needUpdate = true;
                    }
                }
                allModellocMatchedMaterials.push(ele._matched_material);
            });
        });
        let diversifyPairModelLocs = ["床头柜", "床头吊灯"];
        diversifyPairModelLocs.forEach((modelLoc) => {
            let allRoomsBedsideTableGroupList: TFigureElement[][] = [];
            for (let roomItem of rooms) {
                if (!roomItem._furniture_list) continue;
                let roomBedsideTableGroup: TFigureElement[] = [];
                roomItem._furniture_list.filter((ele: TFigureElement) => ele.modelLoc == modelLoc).forEach((ele: TFigureElement) => {
                    roomBedsideTableGroup.push(ele);
                });
                if (roomBedsideTableGroup.length > 0) {
                    allRoomsBedsideTableGroupList.push(roomBedsideTableGroup);
                }
            }
            let allBedsideTableMatchedMaterials: I_MaterialMatchingItem[] = [];
            allRoomsBedsideTableGroupList.forEach((group: TFigureElement[]) => {
                let bedsideTableFigure: TFigureElement = group[0];
                let matchedMaterial = bedsideTableFigure._matched_material;
                if (matchedMaterial == null) return;
                if (allBedsideTableMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId != null && matchedMaterial.modelId != null && item.modelId == matchedMaterial.modelId).length > 0) {
                    for (let i = 0; i < bedsideTableFigure._candidate_materials.length; i++) {
                        let candidateMaterial = bedsideTableFigure._candidate_materials[i];
                        if (!TFigureElement.checkIsMatchedSizeSuitable(bedsideTableFigure, candidateMaterial)) {
                            continue;
                        }
                        if (allBedsideTableMatchedMaterials.filter((item: I_MaterialMatchingItem) => item.modelId == candidateMaterial.modelId).length == 0) {
                            group.forEach(ele => ele.replaceMatchedMaterial(candidateMaterial));
                            this._logger.info("Replace diversify matetrial for: " + group[0].toString() + "\n     " + toStringForMaterialMatchingItem(matchedMaterial) + "\n  => " + toStringForMaterialMatchingItem(candidateMaterial));
                            needUpdate = true;
                            break;
                        }
                    }
                }
                allBedsideTableMatchedMaterials.push(bedsideTableFigure._matched_material);
            });
        });

        if (needUpdate) {
            this.update();
        }
    }

    checkUnfurnishedRooms(): boolean {
        let hasUnfurishedRoom = this.current_rooms.some((room: TRoom) => room.hasUnFurnishedApplyScope())
        if (hasUnfurishedRoom) {
            setTimeout(() =>
                LayoutAI_App.emit(EventName.PerformFurnishResult, { progress: "furnishremaining", message: "需要补全布置" }),
                10);
        }
        return !hasUnfurishedRoom;
    }

    async applyFurnishTo3D() {
        let cameraArray: Array<SunvegaAPI.BasicBiz.CameraOption> = [];

        for (let [roomItem, seriesSampleItem] of this.room2SeriesSampleMap.entries()) {
            for (let fe of roomItem._furniture_list) {
                if (fe.modelLoc === "相机" || fe.modelLoc === "人物") {
                    cameraArray.push({
                        position: { x: fe.rect.rect_center_3d.x, y: fe.rect.rect_center_3d.y, z: fe.rect.rect_center_3d.z },
                        rotation: { x: fe.rect.rotation_x, z: fe.rect.rotation_z },
                        fov: fe.rect.fov
                    } as SunvegaAPI.BasicBiz.CameraOption);
                }
            }
        }

        let followupActions: SunvegaAPI.BasicBiz.PostLayoutAction = {
            save3dScheme: false,
            createDreamer: false,
            submitRender: false,
            uploadRoyScene: false,
            renderOption: { cameras: cameraArray, lightTemplate: "AILightRealisticV2Plus" }
        };

        // 保存模型id到模型位的信息
        this.manager.layout_container._save_model_material_dict();

        // 应用到3D, 把代码整合到一起

        ApplyTo3DService.instance.applyTo3D(this.manager.layout_container._rooms, this.room2SeriesSampleMap, this.manager.layout_container._layout_scheme_id, this.manager.layout_container._layout_scheme_name, {
            _kitchen_use_default_material: this._kitchen_use_default_material,
            _enable_hard_furnish: this._enable_hard_furnish,
            xml_is_recreated: this.manager.layout_container.current_xml_is_regenerated
        }, followupActions, this._debug, this._logger);
    }

    private async autoFineTuning() {
        TLayoutFineTuningManagerToolUtil.instance.postMatchAutoFineTuning(this.room_list);
        this.update();
    }


    /**
     * @description 自动应用套系比如切换到风格模式，需要自动应用套系
     */
    public async autoApplySeries() {
        let target_room = [] as TRoom[];
        this.manager.layout_container._room_entities.forEach((room: TRoomEntity) => {
            let num = 0;
            if(!room._room?._series_sample_info?.ruleId)
            {
                return;
            }
            room.furniture_entities.forEach((furniture) => {
                if(compareNames([furniture.category],['收口板']))
                {
                    return;
                }
                if(furniture instanceof TBaseGroupEntity)
                {
                    furniture.combination_entitys.forEach(combination => {
                        if((combination.realType ==='SoftFurniture' || (combination.realType === 'Cabinet')) && (!combination.figure_element?.pictureViewImg || !combination.figure_element?.pictureViewImg.src))
                        {
                            num++;
                        }
                    });
                }
                else if((furniture.realType === 'SoftFurniture' || (furniture.realType === 'Cabinet')) && (!furniture.figure_element?.pictureViewImg || !furniture.figure_element?.pictureViewImg.src))
                {
                    num++;
                }
            });
            if(num > 1)
            {
                target_room.push(room._room);
            }
        });
        await TSeriesFurnisher.instance.onSeriesSampleSelected(
            {soft: true, cabinet: true, hard: true, remaining: false}, 
            null,
            target_room);
    }

}
