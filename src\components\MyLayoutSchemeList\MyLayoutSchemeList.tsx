import React, { useState, useEffect } from 'react';
import styles from './style/index.module.less';
import { PanelContainer } from '@svg/antd-cloud-design';
import { useStore } from '@/models';
import { observer } from 'mobx-react-lite';
import { useTranslation } from 'react-i18next';
import Content from './Content/schemeContent';
interface MySchemeListProps {
  schemeNameCb: (arg0: string) => void;
}

const MyLayoutSchemeList: React.FC<MySchemeListProps> = ({ schemeNameCb }) => {
  const { t } = useTranslation();
  const store = useStore();
  const [height, setHeight] = useState(650);
  const [audioHeight, setAudioHeight] = useState('80%');
  const [loading, setLoading] = useState(true); // 控制加载状态
  const [isIframeLoaded, setIsIframeLoaded] = useState(false)
  const [loadError, setLoadError] = useState(false); // 控制加载失败状态

  // const onClickOpenLayoutScheme = (layoutSchemeData: any) => {
  //   LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, layoutSchemeData);
  //   store.homeStore.setShowMySchemeList(false);
  //   LayoutAI_App.emit(EventName.OpenHouseSearching, false);
  // }

  useEffect(() => {
    if (store.homeStore.showMySchemeList) {
      setIsIframeLoaded(false)
      setLoading(true);
      setLoadError(false);
    }
  }, [store.homeStore.showMySchemeList]);

  useEffect(() => {
    if (window.innerWidth < 1025) {
      setHeight(550);
      setAudioHeight('75%');
    }
  }, [window.innerWidth]);

  
  // 监听iframe
  // useEffect(() => {
  //   const handleMessage = (event: MessageEvent) => {
  
  //     // 根据消息类型处理逻辑
  //     switch (event.data.type) {
  //       case 'open':

  //         const d = event.data.data
  //         // const schemeData: LayoutSchemeData = {
  //         //   area: d.area,
  //         //   contentUrl: d.contentUrl,
  //         //   coverImage: d.coverImage,
  //         //   createDate: d.createDate,
  //         //   createUser: d.createUser,
  //         //   id: d.id,
  //         //   isDelete: d.isDelete,
  //         //   layoutSchemeName: d.layoutSchemeName,
  //         //   svjSchemeId: d.svjSchemeId,
  //         //   tenantId: d.tenantId,
  //         //   updateDate: d.updateDate,
  //         //   updateUser: d.updateUser,
  //         //   dreamerScheme: null
  //         // }

  //         onClickOpenLayoutScheme(d);
  //         store.homeStore.setShowMySchemeList(false);
  //         store.homeStore.setShowWelcomePage(false);
  //         store.homeStore.setShowEnterPage({show: false, source: ''});
  //         break;

  //       case "iframeStatus":
  //         console.log(event.data.status)
  //         if(event.data.status === 'loaded'){
  //           setLoading(false)
  //         }
  //         break;
  //       default:
  //         console.warn('未知消息类型:', event.data.type);
  //     }
  //   };
  
  //   // 添加消息监听器
  //   window.addEventListener('message', handleMessage);
  
  //   // 清理消息监听器
  //   return () => {
  //     window.removeEventListener('message', handleMessage);
  //   };
  // }, []);


  return (
    <>
      {store.homeStore.showMySchemeList && (
        <div className={styles.mySchemeList}>
          <PanelContainer
            className={styles.panel}
            title={t('我的方案')}
            center={true}
            width={979}
            height={700}
            onClose={() => {
              store.homeStore.setShowMySchemeList(false);
            }}
            draggable={true}
          >
            {/* <SchemeContent height={audioHeight}></SchemeContent> */}

            <div className={styles.schemeContainer}>

              {/* <Spin
                size="large"
                style={{ display: loading ? 'block' : 'none' }}
                className={styles.loading}
              ></Spin>

              <iframe
                src={`${workDomainMap}/scheme/i`}
                style={{ width: '100%', height: '100%', border: 'none', marginBottom: '20px' }}
                title="方案预览"
                loading='eager'
                onLoad={() => {
                  setIsIframeLoaded(true)
                  console.log('iframe Loaded');
                }}
              ></iframe> */}

              <Content></Content>
            </div>
            
          </PanelContainer>
        </div>
      )} 
    </>
  );
};

export default observer(MyLayoutSchemeList);
