import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import SchemeList from "@/components/LeftPanel/components/SchemeList/schemeList";
import SeriesList from "../seriesList/seriesList";
import FigureLabelList from '../materialList/Menu/figureLabelList';
import AttributeEdit from "../AttributeEdit/attributeEdit";
import IconFont from "@/components/IconFont/iconFont";
import Replace from "@/pages/Mobile/Replace/replace";
import { useStore } from "@/models";
import SearchMaterial from "../searchMaterial/searchMaterial";
/**
 * @description 主页
 */
export enum LayoutPopEvents {
    setIsVisible = "setIsVisible",
    showPopup = "showPopup"
}
interface I_Btn {
    icon: string;
    label: string;
    onClick?: () => void;
}
const LayoutPopup: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const [isVisible, setIsVisible] = useState<boolean>(true);
    const [popupType, setShowPopupType] = useState<"Layout" | "Matching" | "view" | "material" | "attribute" | "replace" | "searchMaterial" | string>("");
    const [isOpen, setIsOpen] = useState(false); // 状态管理，控制展开和收起
    const store = useStore();
    const object_id = "LayoutPopUp";
    useEffect(() => {
        LayoutAI_App.on(LayoutPopEvents.showPopup, (type: string) => {
            if(!type)
            {
                setIsOpen(false);
                return;
            }
            setIsOpen(true);
            setShowPopupType(type)
        });

    }, []);


    const retract = () => {
        setIsOpen(false); // 收起
        if(store.homeStore.viewMode === '3D_FirstPerson' && store.homeStore.selectEntity?.type === 'Furniture') /*[i18n:ignore]*/
        {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.cleanSelect,null);
        }
    };

    const open = () => {
        setIsOpen(true); // 展开
    };
    const getTitleByPopupType = (type: string) => {
        switch (type) {
            case "Layout":
                return t("布局");
            case "Matching":
                return t("");
            case "view":
                return t("视角");
            case "material":
                return t("素材");
            case "attribute":
                return t("属性");
            case "replace":
                return t("替换素材");
            case "searchMaterial":
                return t("空间素材");
            default:
                return "";
        }
    };
    return (
        <div 
            className={styles.root} 
            style={{ zIndex: 10,
            maxHeight: store.homeStore.IsLandscape && (isOpen ? '800px' : '0px'),
            maxWidth: !store.homeStore.IsLandscape && (isOpen ? '376px' : '0px'),
            transition: 'all 0.3s ease' 
            }}>
            {
            (popupType != "view") &&
            <div className={styles.topTitle}>
                <div>
                    {getTitleByPopupType(popupType)} 
                </div>
                <div>
                    <IconFont style={{color: '#959598'}} type="icon-icon" onClick={retract}/>
                </div>  
            </div>
            }
            <div className={styles.listContainer} style={{ display: (popupType === "Layout" ? 'block' : 'none') }}> <SchemeList width={400} showSchemeName={false} isLightMobile={true}></SchemeList> </div>
            <div className={styles.listContainer} style={{ display: (popupType === "Matching" ? 'block' : 'none') }}> <SeriesList ></SeriesList> </div>
            <div className={styles.listContainer} style={{ display: (popupType === "material" ? 'block' : 'none') }}> <FigureLabelList ></FigureLabelList> </div>
            <div className={styles.listContainer} style={{ display: (popupType === "attribute" ? 'block' : 'none') }}> <AttributeEdit></AttributeEdit> </div>
            <div className={styles.listContainer} style={{ display: (popupType === "replace" ? 'block' : 'none') }}> <Replace selectedFigureElement={(store.homeStore.selectEntity as any)?.figure_element}></Replace> </div>
            <div className={styles.listContainer} style={{ display: (popupType === "searchMaterial" ? 'block' : 'none') }}> <SearchMaterial></SearchMaterial> </div>
        </div>
    )

};

export default observer(LayoutPopup);
