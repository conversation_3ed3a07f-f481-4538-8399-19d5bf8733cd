import React, { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from "mobx-react-lite";
import useStyles from './styles';
import { getMyCase } from '@/services/home';
import { useStore } from '@/models';
import { Input, Dropdown, Space } from '@svg/antd'
import type { MenuProps } from '@svg/antd'
import Icon from '@/components/Icon/icon';

const { Search } = Input

type timeRangeType = '全部' | '近一个月' | '近三个月' | '近半年'
type timeOrderType = '修改时间' | '创建时间'

interface ParamsType {
    orderBy: string;
    pageIndex: number;
    pageSize: number;
    keyword?: string;
    updateTimeRange?: number;
    updateTimeRange3?: number;
    updateTimeRange6?: number;
}

const MyCase: React.FC<{getHxId: (hxId: string) => void}> = ({getHxId}) => {
    const { styles } = useStyles();
    const isInitialMount = useRef(true);
    const listRef = useRef<HTMLDivElement>(null);
    const store = useStore();
    const [params, setParams] = useState<ParamsType>({
        orderBy: "update_date desc",
        pageIndex: 1,
        pageSize: 25,
    }); // 查询参数
    const [myCaseList, setMyCaseList] = useState<any[]>([]); // 我的方案列表数据
    const [timeOrder, setTimeOrder] = useState<timeOrderType>('修改时间'); // 时间排序
    const [timeRange, setTimeRange] = useState<timeRangeType>('全部'); // 时间范围
    const [keyword, setKeyword] = useState<string>(''); // 搜索关键词
    const [loading, setLoading] = useState(false); // 加载状态
    const [hasMore, setHasMore] = useState(true); // 是否还有更多数据

    const timeOrderMenu: MenuProps['items'] = [
        {
            key: 'update_date desc',
            label: (
                <div className='timeOrderItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeOrder('修改时间');
                }}>
                    <span className='timeOrderItemText'>修改时间</span>
                </div>
            ),
        },
        {
            key: 'create_date desc',
            label: (
                <div className='timeOrderItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeOrder('创建时间');
                }}>
                    <span className='timeOrderItemText'>创建时间</span>
                </div>
            ),
        },
    ]
    
    const timeRangeMenu: MenuProps['items'] = [
        {
            key: 'updateTimeRangeAll',
            label: (
                <div className='timeRangeItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeRange('全部');
                }}>
                    <span className='timeRangeItemText'>全部</span>
                </div>
            ),
        },
        {
            key: 'updateTimeRange',
            label: (
                <div className='timeRangeItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeRange('近一个月');
                }}>
                    <span className='timeRangeItemText'>近一个月</span>
                </div>
            ),
        },
        {
            key: 'updateTimeRange3',
            label: (
                <div className='timeRangeItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeRange('近三个月');
                }}>
                    <span className='timeRangeItemText'>近三个月</span>
                </div>
            ),
        },
        {
            key: 'updateTimeRange6',
            label: (
                <div className='timeRangeItem' onClick={(e) => {
                    e.stopPropagation(); // 阻止冒泡
                    setTimeRange('近半年');
                }}>
                    <span className='timeRangeItemText'>近半年</span>
                </div>
            ),
        },
    ]

    const getMyCaseList = async (isLoadMore = false) => {
        if (loading) return;
        
        setLoading(true);
        const res = await getMyCase(params).catch((e: unknown): null => {
            console.log(e);
            if (!isLoadMore) {
                setMyCaseList([]);
            }
            setLoading(false);
            return null;
        });
        
        if(res?.success) {
            const newData = res.result.result || [];
            if (isLoadMore) {
                // 加载更多时，追加数据
                setMyCaseList(prev => [...prev, ...newData]);
            } else {
                // 首次加载或筛选时，替换数据
                setMyCaseList(newData);
            }
            
            // 判断是否还有更多数据
            setHasMore(newData.length === params.pageSize);
        } else {
            if (!isLoadMore) {
                setMyCaseList([]);
            }
            setHasMore(false);
        }
        
        setLoading(false);
    }

    const onSearch = (value: string) => {
        setKeyword(value);
    }

    // 滚动到底部加载更多
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
        const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
        
        // 当滚动到距离底部100px时开始加载
        if (scrollHeight - scrollTop - clientHeight < 100 && !loading && hasMore) {
            // 直接调用加载更多，而不是修改params
            const newParams = { ...params, pageIndex: params.pageIndex + 1 };
            setParams(newParams);
        }
    }, [loading, hasMore, params]);

    // 只在组件挂载时请求一次数据
    useEffect(() => {
        getMyCaseList();
    }, []); // 空依赖数组，只在挂载时执行

    // 监听params变化，触发请求
    useEffect(() => {
        if (isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }
        // 判断是否为加载更多
        const isLoadMore = params.pageIndex > 1;
        getMyCaseList(isLoadMore);
    }, [params]);

    // 处理筛选条件变化
    useEffect(() => {
        if(isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }
        
        // 重置分页状态
        const newParams: ParamsType = {
            orderBy: "update_date desc",
            pageIndex: 1,
            pageSize: 25,
        };
        
        // 先删除
        delete newParams.updateTimeRange;
        delete newParams.updateTimeRange3;
        delete newParams.updateTimeRange6;
        delete newParams.keyword;
        
        if(timeRange === '近一个月') {
            newParams.updateTimeRange = 1;
        } else if(timeRange === '近三个月') {
            newParams.updateTimeRange3 = 1;
        } else if(timeRange === '近半年') {
            newParams.updateTimeRange6 = 1;
        }

        if(keyword) {
            newParams.keyword = keyword;
        }

        if(timeOrder === '修改时间') {
            newParams.orderBy = 'update_date desc';
        } else if(timeOrder === '创建时间') {
            newParams.orderBy = 'create_date desc';
        }

        setParams(newParams);
        setHasMore(true);
    }, [keyword, timeRange, timeOrder]);

    useEffect(() => {
        console.log(myCaseList);
    }, [myCaseList]);

    return <div className={styles.myCaseRoot} onClick={(e) => {
            e.stopPropagation(); // 阻止冒泡
        }}>
        <div className={styles.header}>
            <div className='title'>
                <span className='title_text'>我的方案</span>
            </div>
            <div className='close' onClick={() => {
                store.homeStore.setShowMySchemeList(false);
            }}>
                <Icon iconClass="icon-close1" style={{fontSize: '20px', color: '#282828'}}/>
            </div>
        </div>
        <div className={styles.content}>
            <div className={styles.search}>
                <Search
                    placeholder="搜索全部方案"
                    allowClear
                    enterButton="搜索"
                    onSearch={onSearch}
                    className='search_input'
                />
                <div className='searchChoice'>
                    <div className='searchChoiceItem'>
                        <Dropdown menu={{ items: timeOrderMenu }} trigger={['click']}>
                            <a onClick={(e) => e.preventDefault()}>
                                <Space>
                                    {timeOrder}
                                    <Icon iconClass="icon-line_down" style={{fontSize: '12px', color: '#595959'}}/>
                                </Space>
                            </a>
                        </Dropdown>
                    </div>
                    <div className='searchChoiceItem'>
                        <Dropdown menu={{ items: timeRangeMenu }} trigger={['click']}>
                            <a onClick={(e) => e.preventDefault()}>
                                <Space>
                                    {timeRange}
                                    <Icon iconClass="icon-line_down" style={{fontSize: '12px', color: '#595959'}}/>
                                </Space>
                            </a>
                        </Dropdown>
                    </div>
                </div>
            </div>
            <div className={styles.list} onScroll={handleScroll} ref={listRef}>
                {myCaseList.map((item, index) => {
                    return <div className='listItem' key={`${item.id}-${index}`} onClick={() => {
                        console.log(item.id);
                        getHxId(item.id);
                        store.homeStore.setShowMySchemeList(false);
                    }}>
                        <div className='listItemImg'>
                            <img src={item.coverImage} alt="" />
                        </div>
                        <div className='itemInfo'>
                            <div className='itemame'>{item.layoutSchemeName}</div>
                        </div>
                    </div>
                })}
                {!hasMore && myCaseList.length > 0 && (
                    <div className='no-more'>
                        <div className='no-more-text'>没有更多数据了</div>
                    </div>
                )}
            </div>
        </div>
    </div>;
};

export default observer(MyCase);
