import React, { useEffect, useState } from 'react';
import useStyles from './style';
import { observer } from 'mobx-react-lite';
import type { MenuProps } from '@svg/antd'
import { Dropdown, Space } from '@svg/antd'
import Icon from "@/components/Icon/icon";
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { useStore } from '@/models';
import { useTranslation } from 'react-i18next'
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { EventName } from '@/Apps/EventSystem';

const SelectRoom: React.FC = () => {
    const { styles } = useStyles();
    const store = useStore();
    const { t } = useTranslation();
    const [selectedRoom, setSelectedRoom] = useState<TRoomEntity | null>(null);
    const [menuItems, setMenuItems] = useState<MenuProps['items']>([]);
    const [btnList, setBtnList] = useState<TRoomEntity[]>([]);
    const [isWholeHouse, setIsWholeHouse] = useState<boolean>(true); // 全屋展示时，不因为点击某个房间导致selectedRoom变化而改变按钮的激活状态

    // 处理房间选择逻辑
    const handleRoomSelect = (room: TRoomEntity | null) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, room);
        store.homeStore.setSelectedRoom(room); // 设置选中的房间
        setSelectedRoom(room); // 更新本地状态
        setIsWholeHouse(room === null); // 更新全屋状态
    };

    // 监听房间列表变化
    useEffect(() => {
        setBtnList(store.homeStore.roomEntities);
    }, [store.homeStore.roomEntities]);

    // 监听房间选择事件
    useEffect(() => {
        LayoutAI_App.on(EventName.selectRoom, (event: TRoomEntity) => {
            if (event) {
                store.homeStore.setSelectedRoom(event);
                setSelectedRoom(event);
                setIsWholeHouse(false);
            } else {
                store.homeStore.setSelectedRoom(null);
                setSelectedRoom(null);
                setIsWholeHouse(true);
            }
        });
    }, []);

    // 构建菜单项
    useEffect(() => {
        const items: MenuProps['items'] = [
            {
                key: 'all',
                label: t('全屋'),
                onClick: () => {
                    handleRoomSelect(null);
                }
            }
        ];

        // 添加房间选项
        btnList.forEach((room: TRoomEntity) => {
            items.push({
                key: room.uidN,
                label: t(room.aliasName),
                onClick: () => {
                    handleRoomSelect(room);
                }
            });
        });

        setMenuItems(items);
    }, [btnList, t]);

    // 同步选中状态
    useEffect(() => {
        if (store.homeStore.selectedRoom !== selectedRoom) {
            setSelectedRoom(store.homeStore.selectedRoom);
            setIsWholeHouse(store.homeStore.selectedRoom === null);
        }
    }, [store.homeStore.selectedRoom]);

    const getDisplayText = () => {
        if (selectedRoom && !isWholeHouse) {
            return t(selectedRoom.aliasName);
        }
        return t('全屋');
    };

    return (
        <div className={styles.root}>
            <Dropdown 
                menu={{ items: menuItems }}
                placement="bottomLeft"
                trigger={['click']}
            >
                <a onClick={(e) => e.preventDefault()}>
                    <Space>
                        {getDisplayText()}
                        <Icon iconClass="icon-line_down" style={{ fontSize: '16px', color: '#BCBEC2' }} />
                    </Space>
                </a>
            </Dropdown>
        </div>
    )
}

export default observer(SelectRoom);
