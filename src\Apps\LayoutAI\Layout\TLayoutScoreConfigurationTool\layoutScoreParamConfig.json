{"bathRoomParamConfig": {"name": "卫生间参数配置", "data": {"storageGroup": {"name": "收纳容量", "data": {"storageRatioRule": {"name": "收纳容量范围得分(以柜体长度计算收纳容量)", "data": {"withoutBathRoomStorageCabinetScore": {"name": "卫生间无柜体得分", "data": {"value": -5}}, "bathRoomStorageCabinetLen": {"name": "卫生间柜体长度得分", "data": {"paramItems": [{"minParam": 0, "maxParam": 300, "minEqual": true, "maxEqual": true, "score": -10}, {"minParam": 300, "maxParam": 600, "minEqual": false, "maxEqual": true, "score": 5}, {"minParam": 600, "maxParam": 800, "minEqual": false, "maxEqual": true, "score": 10}, {"minParam": 800, "maxParam": 1000, "minEqual": false, "maxEqual": true, "score": 15}, {"minParam": 1000, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 18}]}}}}}}, "spaceUsageGroup": {"name": "空间利用率", "data": {"furnitureUsageRatioRule": {"name": "家具利用率", "data": {"paramItems": [{"minParam": 0.4, "maxParam": 0.7, "minEqual": true, "maxEqual": true, "score": 10}, {"minParam": 0.2, "maxParam": 0.4, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 0.1, "maxParam": 0.2, "minEqual": true, "maxEqual": false, "score": 0}, {"minParam": 0.7, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": null, "maxParam": 0.1, "minEqual": false, "maxEqual": false, "score": -10}]}}, "functionNumRule": {"name": "功能区丰富度", "data": {"minRoomArea": {"name": "区分功能区数量的最小面积", "data": {"value": 2}}, "normalMinFuncNum": {"name": "大于最小面积的最小功能区数量", "data": {"value": 1}}, "funcScore": {"name": "最小面积下满足功能区的数量得分", "data": {"value": 5}}, "baseFuncNum": {"name": "正常面积下需要具备的多少基础功能数量", "data": {"value": 3}}, "bathRoomFunctionAbnormalScore": {"name": "功能丰富度不足扣分数值", "data": {"value": -50}}}}}}, "flowGroup": {"name": "动线布局", "data": {"dryAndWetFlowRule": {"name": "动线流畅度", "data": {"dryAndWetMixScore": {"name": "干湿区混合的得分", "data": {"value": 0}}, "dryAndWetSplitScore": {"name": "干湿区分离的得分", "data": {"value": 0}}, "dryAndWetSamePathScore": {"name": "干湿区同路径下的得分", "data": {"value": 5}}, "dryAndWetLayonOneWallScore": {"name": "图元都在同一堵墙上的得分", "data": {"value": 15}}, "dryAndWetLayonNearWallScore": {"name": "图元分布在相邻两条墙上的得分", "data": {"value": 10}}, "dryAndWetLayonMultiWallScore": {"name": "图元分布在多堵墙的得分", "data": {"value": 0}}, "washHandNearDoorScore": {"name": "浴室柜相较于马桶离门近得分", "data": {"value": 10}}, "dryAndWetReverseScore": {"name": "湿区比干区更靠门得分", "data": {"value": -10}}}}, "DryAndWetLayoutRule": {"name": "干湿规划", "data": {"withoutDryAndWetSeparationScore": {"name": "无干湿分离得分", "data": {"value": -10}}, "dryAndWetSeparationScore": {"name": "干湿分离得分", "data": {"value": 15}}, "dryAndWetSeparationWithWallScore": {"name": "独立空间干湿分离得分", "data": {"value": 15}}}}, "flowMinDistanRule": {"name": "主过道宽度", "data": {"value": 650}}}}, "basicGroup": {"name": "家具布局", "data": {"toiletAreaSizeRule": {"name": "马桶区大小", "data": {"toiletAreaMinLen": {"name": "马桶区长度最小不准允出长度", "data": {"value": 650}}, "toiletOneSideMinLen": {"name": "马桶区单侧长度最小不准允出长度", "data": {"value": 60}}, "toiletAreaSumParam": {"name": "马桶区长度参数累计得分", "data": {"paramItems": [{"minParam": 800, "maxParam": 1000, "minEqual": true, "maxEqual": false, "score": 2}, {"minParam": 1000, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 2}]}}, "toiletOneSideSumParam": {"name": "马桶区单侧参数累计得分", "data": {"paramItems": [{"minParam": 200, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 2}]}}}}, "toiletFaceDoorRule": {"name": "马桶对门", "data": {"toiletFaceDoorScore": {"name": "开放空间下马桶对门得分", "data": {"value": -5}}, "toiletWithoutFaceDoorScore": {"name": "开放空间下马桶不对门得分", "data": {"value": 0}}, "toiletIndependentSpaceScore": {"name": "马桶处于独立空间下得分", "data": {"value": 5}}}}, "toileFronAreaLenRule": {"name": "马桶前侧活动距离", "data": {"paramItems": [{"minParam": 900, "maxParam": 1350, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 600, "maxParam": 900, "minEqual": true, "maxEqual": false, "score": 5}, {"minParam": 450, "maxParam": 600, "minEqual": true, "maxEqual": false, "score": 0}, {"minParam": 1350, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 0}, {"minParam": 0, "maxParam": 450, "minEqual": true, "maxEqual": false, "score": -100}]}}, "toiletBowlSizeRule": {"name": "马桶尺寸", "data": {"paramItems": [{"minParam": null, "maxParam": 300, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 300, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 0}]}}, "bathtubAreaLenRule": {"name": "浴缸与其他障碍物检查规则", "data": {"bathtubOneSideLayonScore": {"name": "浴缸一侧靠墙，其他三侧与其他障碍物的最小距离", "data": {"paramItems": [{"minParam": null, "maxParam": 50, "minEqual": false, "maxEqual": false, "score": -3}, {"minParam": 100, "maxParam": 150, "minEqual": true, "maxEqual": true, "score": 3}, {"minParam": 150, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 5}]}}, "bathtubThreeSideLayonScore": {"name": "浴缸三侧靠墙，其他一侧与其他障碍物的最小距离", "data": {"paramItems": [{"minParam": 600, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 10}, {"minParam": 450, "maxParam": 600, "minEqual": true, "maxEqual": true, "score": 5}, {"minParam": null, "maxParam": 300, "minEqual": false, "maxEqual": false, "score": -5}]}}}}, "washHandMinSizeRule": {"name": "浴室柜最小尺寸检查", "data": {"paramItems": [{"minParam": 0, "maxParam": 600, "minEqual": false, "maxEqual": false, "score": -5}]}}, "occlusionWindowRule": {"name": "家具挡窗", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 0.2, "maxParam": 0.5, "minEqual": false, "maxEqual": false, "score": -20}, {"minParam": 0.02, "maxParam": 0.2, "minEqual": false, "maxEqual": false, "score": -5}]}}, "washHandFrontAreRule": {"name": "浴室柜前方距离检查", "data": {"paramItems": [{"minParam": 1200, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 10}, {"minParam": 900, "maxParam": 1200, "minEqual": true, "maxEqual": true, "score": 5}, {"minParam": 0, "maxParam": 450, "minEqual": true, "maxEqual": false, "score": -100}]}}, "showerSizeRule": {"name": "淋浴尺寸检查", "data": {"showerSizeScore": {"name": "淋浴区尺寸得分", "data": {"paramItems": [{"minParam": 1100, "maxParam": 1500, "minEqual": false, "maxEqual": false, "score": 10}, {"minParam": 900, "maxParam": 1100, "minEqual": true, "maxEqual": true, "score": 5}]}}, "abnormalMinSizeLen": {"name": "淋浴区不准允出的最小尺寸", "data": {"value": 800}}}}, "IShowerRoomOpenDoorRule": {"name": "一字形淋浴房开口方向检查", "data": {"IShowerRoomMinDistance": {"name": "开口离障碍物距离", "data": {"value": 300}}, "IShowerRoomScore": {"name": "开口有障碍物得分", "data": {"value": -5}}}}}}}}, "livingRoomParamConfig": {"name": "客餐厅参数配置", "data": {"storageGroup": {"name": "收纳容量", "data": {"storageRatioRule": {"name": "收纳容量范围得分", "data": {"paramItems": [{"minParam": 0.1, "maxParam": 0.2, "minEqual": true, "maxEqual": false, "score": 20}, {"minParam": 0, "maxParam": 0.09, "minEqual": false, "maxEqual": false, "score": 0}, {"minParam": 0.2, "maxParam": 0.3, "minEqual": true, "maxEqual": false, "score": 5}]}}}}, "spaceUsageGroup": {"name": "空间利用率", "data": {"diningSpaceUsageRatioRule": {"name": "餐厅区收纳容量", "data": {"paramItems": [{"minParam": 0.31, "maxParam": 1, "minEqual": false, "maxEqual": false, "score": 5}, {"minParam": 0, "maxParam": 0.03, "minEqual": false, "maxEqual": false, "score": 0}, {"minParam": 0.04, "maxParam": 0.3, "minEqual": true, "maxEqual": true, "score": 10}]}}}}, "splitSpaceGroup": {"name": "分区合理性", "data": {"livingAndDiningSpacePositionRule": {"name": "客厅餐厅位置关系", "data": {"diningNearKitchenScore": {"name": "餐厅接近厨房得分", "data": {"value": 10}}, "livingNearKitchenScore": {"name": "客厅接近厨房得分", "data": {"value": -10}}, "livingNearBalconyScore": {"name": "客厅接近阳台得分", "data": {"value": 10}}, "livingNearEntranceScore": {"name": "客厅接近入户门得分", "data": {"value": -10}}}}, "livingRoomSplitSpaceWholenessRule": {"name": "分区完整性", "data": {"livingRoomTwoSpaceScore": {"name": "两个分区得分", "data": {"value": 10}}, "livingRoomLessThanTwoSpaceScore": {"name": "少于两个分区得分", "data": {"value": -20}}}}, "livingRoomSpaceMinAreaRule": {"name": "区域最小尺寸面积检查（单位㎡）", "data": {"livingSpaceMinArea": {"name": "客厅最小尺寸面积", "data": {"value": 7}}, "diningSpaceMinArea": {"name": "餐厅最小尺寸面积", "data": {"value": 4.5}}, "lessLivingOrDiningSpaceAreaScore": {"name": "面积不满足的得分", "data": {"value": -10}}}}}}, "flowGroup": {"name": "动线布局", "data": {"livingRoomFlowMainFluencyRule": {"name": "主动线流畅度", "data": {"livingRoomMainFlowMinDistance": {"name": "主动线过道最最小距离", "data": {"value": 200}}, "blockLivingRoomMainFlowScore": {"name": "动线被遮挡得分", "data": {"value": -100}}}}, "livingRoomSofaAndTVSubFlowLineRule": {"name": "沙发到电视柜的动线检查", "data": {"sofaAndTVFlowMinDistance": {"name": "沙发到电视柜的过道最小距离", "data": {"value": 500}}, "blockSofaAndTVFlowScore": {"name": "动线被遮挡得分", "data": {"value": -100}}}}, "livingRoomSofaAndBalconySubFlowLineRule": {"name": "沙发到阳台的动线检查", "data": {"sofaAndBalconyFlowMinDistance": {"name": "沙发到阳台的过道最小距离", "data": {"value": 350}}, "blockSofaAndBalconyFlowScore": {"name": "动线被遮挡得分", "data": {"value": -100}}}}, "diningTableToCabinetSubFlowLineRule": {"name": "餐桌到餐边柜的动线检查", "data": {"diningTableToCabinetFlowMinDistance": {"name": "餐桌到餐边柜的过道最小距离", "data": {"value": 300}}, "blockDiningTableToCabinetFlowScore": {"name": "动线被遮挡得分", "data": {"value": -100}}}}}}, "basicGroup": {"name": "家具布局", "data": {"livingEntranceCabinetRule": {"name": "玄关柜检查", "data": {"hasEntranceCabinerScore": {"name": "有玄关柜得分", "data": {"value": 10}}, "withoutEntranceCabinetScore": {"name": "无玄关柜得分", "data": {"value": -10}}}}, "diningFunctionRule": {"name": "餐厅功能区检查", "data": {"hasDiningTableScore": {"name": "有餐桌得分", "data": {"value": 10}}, "withoutDiningTableScore": {"name": "无餐桌得分", "data": {"value": -20}}}}, "diningTabelCenterRule": {"name": "餐桌中心点检查", "data": {"diningTableCenterOffsetCenterLen": {"name": "餐桌中心点偏离距离", "data": {"value": 500}}, "diningTableCenterWithoutOffsetScore": {"name": "餐桌中心点无偏离得分", "data": {"value": 10}}, "diningTableCenterOffsetScore": {"name": "餐桌中心点偏离得分", "data": {"value": -10}}}}, "livingFunctionRule": {"name": "客厅功能区检查", "data": {"livingWithoutSofaScore": {"name": "无沙发得分", "data": {"value": -20}}, "hasTVOrBookCabinerScore": {"name": "有电视柜或者书柜得分", "data": {"value": 20}}, "livingAddBaseScore": {"name": "客厅区多其他家具的新增得分", "data": {"value": 5}}, "livingMaxAddBaseScore": {"name": "新增得分上限", "data": {"value": 30}}}}, "sofaCenterRule": {"name": "沙发中心点检查", "data": {"sofaCenterOffsetCenterLen": {"name": "沙发中心点偏离距离", "data": {"value": 500}}, "sofaCenterWithoutOffsetScore": {"name": "沙发中心点无偏离得分", "data": {"value": 0}}, "sofaCenterOffsetScore": {"name": "沙发中心点偏离得分", "data": {"value": -10}}}}, "sofaFaceEntranceDoorRule": {"name": "沙发对入户门", "data": {"sofaFaceEntranceDoorScore": {"name": "沙发对门扣除分值", "data": {"value": -20}}}}, "sofaBackFlowRule": {"name": "沙发不靠墙后背离障碍物距离", "data": {"sofaBackDistance": {"name": "沙发不靠墙后背离障碍物过道距离", "data": {"value": 450}}, "sofaBackLessDistScore": {"name": "沙发不靠墙后背离障碍物过道距离小于设定值得分", "data": {"value": -20}}}}, "livingAndDiningFlowRule": {"name": "客厅与餐厅区过道检查", "data": {"livingAndDiningFlowDistance": {"name": "客厅与餐厅区的过道距离", "data": {"value": 450}}, "livingAndDiningLessFlowDistScore": {"name": "过道距离小于客厅与餐厅区过道距离得分", "data": {"value": -30}}}}, "diningCabinetFrontDistanceRule": {"name": "餐边柜前距离检查", "data": {"diningCabinetFrontDistance": {"name": "餐边柜前距离", "data": {"value": 300}}, "diningCabinetFrontDistScore": {"name": "餐边柜前距离小于设定值得分", "data": {"value": -20}}}}}}}}, "kitchenRoomParamConfig": {"name": "厨房参数配置", "data": {"storageGroup": {"name": "收纳容量", "data": {"storageRatioRule": {"name": "收纳容量范围得分", "data": {"paramItems": [{"minParam": 0.2, "maxParam": 0.25, "minEqual": true, "maxEqual": true, "score": 15}, {"minParam": 0.15, "maxParam": 0.2, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 0.25, "maxParam": 0.3, "minEqual": false, "maxEqual": false, "score": 10}, {"minParam": 0.08, "maxParam": 0.15, "minEqual": true, "maxEqual": false, "score": 3}, {"minParam": 0.3, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 0}, {"minParam": null, "maxParam": 0.08, "minEqual": false, "maxEqual": false, "score": 0}]}}}}, "flowGroup": {"name": "动线布局", "data": {"kitchenFlowLineRule": {"name": "厨房过道检查", "data": {"kitchenFlowMinDistance": {"name": "最短过道长度", "data": {"value": 900}}, "kitchenFlowMaxDistance": {"name": "合理过道下最大的过道距离", "data": {"value": 1300}}, "fineKitchenFlowScore": {"name": "合理过道值下的得分", "data": {"value": 20}}, "otherKitchenFlowScore": {"name": "其余的过道值下的得分", "data": {"value": -10}}}}, "kitchenWorkingTriangleRule": {"name": "三角动线平均长度检查(mm)", "data": {"paramItems": [{"minParam": 1000, "maxParam": 2000, "minEqual": true, "maxEqual": false, "score": 20}, {"minParam": 900, "maxParam": 1000, "minEqual": true, "maxEqual": false, "score": 15}, {"minParam": 2000, "maxParam": 2100, "minEqual": true, "maxEqual": false, "score": 15}, {"minParam": 800, "maxParam": 900, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 2100, "maxParam": 2400, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 510, "maxParam": 800, "minEqual": true, "maxEqual": false, "score": 5}, {"minParam": 2400, "maxParam": 3990, "minEqual": true, "maxEqual": false, "score": 5}, {"minParam": null, "maxParam": 500, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 4000, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 500, "maxParam": 510, "minEqual": true, "maxEqual": false, "score": 0}, {"minParam": 3990, "maxParam": 4000, "minEqual": true, "maxEqual": true, "score": 0}]}}, "kitchenWorkingTriangleFlowRule": {"name": "动线流畅性", "data": {"flowWithoutIntersectScore": {"name": "动线不交叉得分", "data": {"value": 20}}, "lessFlowWithoutIntersectScore": {"name": "少量动线交叉得分", "data": {"value": 10}}, "flowIntersectScore": {"name": "动线不流畅得分", "data": {"value": 0}}}}}}, "flatPlaneGroup": {"name": "操作台面", "data": {"kitchenWorkingPlaneRule": {"name": "操作台面检查", "data": {"paramItems": [{"minParam": 1.2, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 15}, {"minParam": null, "maxParam": 1.2, "minEqual": false, "maxEqual": false, "score": -15}]}}, "kitchenCutPlaneRule": {"name": "黄金备菜区长度检查", "data": {"paramItems": [{"minParam": 600, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": null, "maxParam": 600, "minEqual": false, "maxEqual": false, "score": 0}]}}, "kitchenPutPlaneRule": {"name": "出餐区长度检查", "data": {"paramItems": [{"minParam": null, "maxParam": 300, "minEqual": false, "maxEqual": false, "score": 0}, {"minParam": 300, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 5}]}}}}, "basicGroup": {"name": "家具布局", "data": {"distanceWithFlueRule": {"name": "炉灶与烟道的距离", "data": {"paramItems": [{"minParam": 200, "maxParam": 1000, "minEqual": true, "maxEqual": true, "score": 20}, {"minParam": null, "maxParam": 200, "minEqual": false, "maxEqual": false, "score": -20}, {"minParam": 1000, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -20}]}}, "distanceWithCookingRule": {"name": "炉灶与冰箱的距离", "data": {"paramItems": [{"minParam": null, "maxParam": 200, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 200, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 10}]}}, "cookingWithWallRule": {"name": "炉灶到墙的距离", "data": {"paramItems": [{"minParam": null, "maxParam": 200, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 200, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 10}]}}, "kitchenFlueCookingBeamRule": {"name": "排烟管道绕梁检查", "data": {"kitchenFlueCookingWithoutOverBeamScore": {"name": "不绕梁得分", "data": {"value": 0}}, "kitchenFlueCookingOverBeamScore": {"name": "绕梁得分", "data": {"value": -10}}}}, "rinseOnWidnowRule": {"name": "水槽靠窗检查", "data": {"rinseNearWindowScore": {"name": "水槽靠窗得分", "data": {"value": 20}}, "rinseNotNearWindowScore": {"name": "水槽不靠窗得分", "data": {"value": 5}}}}, "fridgeWithRinseDistanceRule": {"name": "水槽与冰箱的距离检查", "data": {"paramItems": [{"minParam": null, "maxParam": 200, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 200, "maxParam": null, "minEqual": true, "maxEqual": false, "score": 10}]}}, "kitchenFridgeRule": {"name": "冰箱检查", "data": {"hasFridgeScore": {"name": "具有冰箱得分", "data": {"value": 10}}, "withoutFridgeScore": {"name": "无沙发得分", "data": {"value": 0}}}}, "figureShapeRule": {"name": "形状检查", "data": {"IShapeScore": {"name": "一字形形状得分", "data": {"value": 2}}, "IIShapeScore": {"name": "二字形形状得分", "data": {"value": 3}}, "LShapeScore": {"name": "L形形状得分", "data": {"value": 4}}, "UShapeScore": {"name": "U形形状得分", "data": {"value": 5}}}}, "slidingDoorOcclusionRule": {"name": "布局挡门（推拉门）", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 0.2, "maxParam": 0.5, "minEqual": false, "maxEqual": true, "score": -30}, {"minParam": 0.01, "maxParam": 0.2, "minEqual": false, "maxEqual": true, "score": -10}, {"minParam": null, "maxParam": 0.01, "minEqual": false, "maxEqual": true, "score": 0}]}}, "kitchenWindowOcclusionRule": {"name": "布局挡窗", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 0.2, "maxParam": 0.5, "minEqual": false, "maxEqual": true, "score": -30}, {"minParam": 0.01, "maxParam": 0.2, "minEqual": false, "maxEqual": true, "score": -10}, {"minParam": null, "maxParam": 0.01, "minEqual": false, "maxEqual": true, "score": 0}]}}}}}}, "bedRoomParamConfig": {"name": "卧室参数配置", "data": {"storageGroup": {"name": "收纳容量", "data": {"storageRatioRule": {"name": "卧室收纳占比得分", "data": {"bedRoomArea": {"name": "卧室面积", "data": {"value": 10}}, "lessThanAreaStorageScore": {"name": "小于卧室面积的收纳容量占比得分", "data": {"paramItems": [{"minParam": 0.15, "maxParam": 0.25, "minEqual": true, "maxEqual": true, "score": 20}, {"minParam": 0.1, "maxParam": 0.15, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": null, "maxParam": 0.1, "minEqual": false, "maxEqual": false, "score": 3}, {"minParam": 0.25, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 3}]}}, "moreThanAreaStorageScore": {"name": "大于卧室面积的收纳容量占比得分", "data": {"paramItems": [{"minParam": 0.1, "maxParam": 0.2, "minEqual": true, "maxEqual": true, "score": 20}, {"minParam": 0.4, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 3}, {"minParam": 0, "maxParam": 0.05, "minEqual": false, "maxEqual": false, "score": 0}, {"minParam": 0.05, "maxParam": 0.1, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 0.2, "maxParam": 0.4, "minEqual": false, "maxEqual": true, "score": 10}]}}}}}}, "spaceUsageGroup": {"name": "空间利用率", "data": {"bedRoomUseageRule": {"name": "家具利用率", "data": {"paramItems": [{"minParam": 0.4, "maxParam": 0.5, "minEqual": true, "maxEqual": true, "score": 15}, {"minParam": 0.2, "maxParam": 0.4, "minEqual": true, "maxEqual": false, "score": 10}, {"minParam": 0.5, "maxParam": 0.65, "minEqual": true, "maxEqual": true, "score": 10}, {"minParam": null, "maxParam": 0.2, "minEqual": false, "maxEqual": false, "score": 5}, {"minParam": 0.65, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 5}]}}, "bedRoomRichnessRule": {"name": "家具丰富度", "data": {"bedRoomMustHasFigureNum": {"name": "必备的图元数量", "data": {"value": 3}}, "bedRoomMoreFigureScore": {"name": "有多余的图元的得分", "data": {"value": 15}}, "bedRoomNormalFiguteScore": {"name": "没有缺少指定图元的得分", "data": {"value": 6}}, "bedRoomLessFigureScore": {"name": "缺少图元的得分", "data": {"value": 3}}}}}}, "basicGroup": {"name": "家具布局", "data": {"doorSightFaceDoorRule": {"name": "床头对门", "data": {"paramItems": [{"minParam": 599, "maxParam": null, "minEqual": true, "maxEqual": false, "score": -25}, {"minParam": 200, "maxParam": 599, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": 100, "maxParam": 200, "minEqual": false, "maxEqual": true, "score": 5}, {"minParam": 1, "maxParam": 100, "minEqual": false, "maxEqual": true, "score": 10}, {"minParam": null, "maxParam": 1, "minEqual": false, "maxEqual": true, "score": 25}]}}, "doorOcclusionRule": {"name": "布局挡门（平开门）", "data": {"strongDoorOcclusionRule": {"name": "强挡门", "data": {"paramItems": [{"minParam": 50, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": null, "maxParam": 50, "minEqual": false, "maxEqual": false, "score": 0}]}}, "weakDoorOcclusionRule": {"name": "弱挡门", "data": {"paramItems": [{"minParam": 50, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": null, "maxParam": 50, "minEqual": false, "maxEqual": false, "score": 0}]}}}}, "slidingDoorOcclusionRule": {"name": "布局挡门（推拉门）", "data": {"slidingDoorExtendLen": {"name": "推拉门预留距离", "data": {"value": 450}}, "slidingDoorScore": {"name": "挡推拉门范围判断", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 0.2, "maxParam": 0.5, "minEqual": false, "maxEqual": true, "score": -30}, {"minParam": 0.05, "maxParam": 0.2, "minEqual": false, "maxEqual": true, "score": -10}, {"minParam": null, "maxParam": 0.05, "minEqual": false, "maxEqual": true, "score": 0}]}}}}, "bedBackOnWallRule": {"name": "床头靠墙", "data": {"paramItems": [{"minParam": 0.8, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 10}, {"minParam": null, "maxParam": 0.8, "minEqual": false, "maxEqual": false, "score": 0}]}}, "bedCenterToWallRule": {"name": "床居于空间中心位置", "data": {"bedInCenterScore": {"name": "位于中心得分", "data": {"value": 15}}, "bedNotInCenterScore": {"name": "不位于中心得分", "data": {"value": 0}}}}, "bedSideToWallRule": {"name": "床长边不靠墙范围得分", "data": {"paramItems": [{"minParam": null, "maxParam": 0.8, "minEqual": false, "maxEqual": true, "score": 5}, {"minParam": 0.8, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 0}]}}, "bedCornerRule": {"name": "床在角落", "data": {"bedInCornerScore": {"name": "床在角落得分", "data": {"value": 3}}, "bedNotInCornerScore": {"name": "床不在角落得分", "data": {"value": 0}}}}, "bedSideToWindowRule": {"name": "床侧对窗", "data": {"bedSideLayOnWallLen": {"name": "定义床侧到墙的距离", "data": {"value": 10000}}, "bedSideLayonWindonScore": {"name": "床侧对窗占比得分", "data": {"paramItems": [{"minParam": null, "maxParam": 0, "minEqual": false, "maxEqual": true, "score": 0}, {"minParam": 0, "maxParam": 1.5, "minEqual": false, "maxEqual": true, "score": 10}, {"minParam": 1.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 7}]}}}}, "bedRoomWindowOcclusionRule": {"name": "布局挡窗", "data": {"bedRoomWindowHightExtendLen": {"name": "窗户高度延伸距离", "data": {"value": 100}}, "bedRoomWindowOcclusionScore": {"name": "挡窗占比得分", "data": {"paramItems": [{"minParam": 800, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 300, "maxParam": 800, "minEqual": false, "maxEqual": true, "score": -30}, {"minParam": 150, "maxParam": 300, "minEqual": false, "maxEqual": true, "score": -10}, {"minParam": null, "maxParam": 150, "minEqual": false, "maxEqual": true, "score": 0}]}}}}, "wardrobeToWallRule": {"name": "衣柜背靠墙", "data": {"wardrobeLayOnWallScore": {"name": "衣柜背靠墙得分", "data": {"value": 10}}, "wardrobeNoLayOnWallScore": {"name": "衣柜不背靠墙得分", "data": {"value": -20}}}}, "bedBackOnWindowlRule": {"name": "床头在窗下", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": -100}, {"minParam": 0.1, "maxParam": 0.5, "minEqual": false, "maxEqual": false, "score": -10}, {"minParam": null, "maxParam": 0.1, "minEqual": false, "maxEqual": true, "score": 0}]}}, "bedFaceWindowlRule": {"name": "床正对窗", "data": {"bedBackLayOnWallLen": {"name": "床背靠墙的容差距离", "data": {"value": 10000}}, "bedFaceWindowScore": {"name": "床正对窗范围得分", "data": {"paramItems": [{"minParam": 0.5, "maxParam": null, "minEqual": false, "maxEqual": false, "score": 5}, {"minParam": null, "maxParam": 0.5, "minEqual": false, "maxEqual": false, "score": 0}]}}}}}}}}}