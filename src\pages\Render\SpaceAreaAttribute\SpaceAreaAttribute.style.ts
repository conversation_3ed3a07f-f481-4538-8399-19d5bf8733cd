import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    // color: ${token.colorPrimary};
    return {
        container: css`
            width: 100%;
            background-color: #fff;
            border-radius: 4px;
            padding: 0;
            position: relative;
        `,
        titleBar: css`
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: #333;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        `,
        header: css`
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        `,
        title: css`
            font-size: 16px;
            font-weight: 500;
            color: #333;
        `,
        closeIcon: css`
            cursor: pointer;
            color: #999;
            font-size: 16px;

            &:hover {
                color: #666;
            }
        `,
        formItem: css`
            margin-bottom: 16px;
            padding: 0 16px;
            &:first-of-type {
                margin-top: 16px;
            }
        `,
        label: css`
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        `,
        colorRow: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 36px;
            padding: 0;
            
            span {
                margin-bottom: 0;
                flex: 1;
            }
        `,
        select: css`
            width: 100%;
            height: 36px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            background-color: #fff;
            position: relative;

            &.ant-select-focused .ant-select-selector,
            .ant-select-selector:hover,
            .ant-select-selector:focus {
                border-color: #40a9ff !important;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
            }

            .ant-select-selector {
                border: none !important;
                box-shadow: none !important;
                height: 100% !important;
                padding: 0 11px !important;
            }
        `,
        inputNumber: css`
            width: 100%;
            height: 36px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 0 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        `,
        slider: css`
            width: 100%;
            padding: 8px 0;

            .ant-slider-track {
                background-color: #1890ff;
            }
        `,
        colorPicker: css`
            display: flex;
            align-items: center;
            justify-content: flex-end;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            height: 36px;
            padding: 0 12px;
        `,
        colorBlock: css`
            width: 36px;
            height: 24px;
            border-radius: 2px;
            background-color: #ffa500;
            cursor: pointer;
            border: 1px solid #e8e8e8;
            
            &:hover {
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
        `,
        footer: css`
            margin-top: 0;
            margin-bottom: 16px;
            padding: 0 16px;
        `,
        footerButton: css`
            width: 100%;
            height: 36px;
            background-color: #f5f5f5;
            border: none;
            border-radius: 4px;
            color: #333;
            cursor: pointer;

            &:hover {
                background-color: #e8e8e8;
            }
        `,
        unitText: css`
            color: #999;
            margin-left: 4px;
        `
    }
});

