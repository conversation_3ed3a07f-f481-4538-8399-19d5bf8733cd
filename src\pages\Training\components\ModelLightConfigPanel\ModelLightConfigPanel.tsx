import React, { useEffect, useState, useRef } from 'react';
import {
    Tabs,
    Form,
    Button,
    Space,
    Input,
    Card,
    Slider,
    Typography,
    Table,
    Upload,
    Modal,
    Popconfirm, message, Tooltip, Pagination, InputRef
} from '@svg/antd';
import { ColorPicker } from '@svg/antd-basic';
import useCommonStyles from '../../common_style/index';
import { useTranslation } from 'react-i18next';
import {
    SortAscendingOutlined, DownloadOutlined,
    DeleteOutlined, PictureOutlined, UploadOutlined, SearchOutlined,
    UnorderedListOutlined, SortDescendingOutlined
} from '@ant-design/icons';
import { LightTemplateService } from '../../../../Apps/LayoutAI/Services/Basic/LightTemplateService';
import { observer } from 'mobx-react-lite';
import { lightTemplateStore } from '../../../../Apps/LayoutAI/Services/Basic/LightConfigService';
import { Uploader } from "@svg/oss-upload"
import { getSign } from '@/services/user';
import {
    LightConfigGroupProps,
    Template,
    TemplateManagementProps,
    ConfigData
} from './ModelLightConfigType';
import { uploadFileToOss } from '@/Apps/LayoutAI/Utils/file_utils';

const createUploader = async (file: any) => {
    const type = 12
    const res = await getSign(type, file.name)
    const {
        accessKeyId, expireAt, readDomain, policy,
        securityToken, signature, keyPrefix, vendor, contentType, ossHost
    } = res
    return {
        uploader: new Uploader({
            contentType,
            policy,
            signature,
            accessKeyId,
            server: ossHost.replace('-internal', ''),
            expireAt,
            securityToken,
            path: keyPrefix,
            vendor: vendor as any
        } as any),
        readDomain,
        ossHost
    }
}
const uploadFile = async (
    file: any,
) => {
    try {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            message.error('请上传JPG/PNG格式的图片');
            return;
        }
        const { uploader, readDomain, ossHost } = await createUploader(file)
        const data = await uploader.upload(file)
        const imageUrl = `${readDomain || ossHost.replace('-internal', '')}/${data.key}`
        return imageUrl
    } catch (error) {
        throw error
    }
}

const LightConfigGroup: React.FC<LightConfigGroupProps & { category: "dayLight" | "nightLight" }> = ({ groupName, groupData, category
}) => {
    const groupIndex = category === "dayLight"
        ? lightTemplateStore.dayLightConfigs.findIndex(g => g.groupName === groupName)
        : lightTemplateStore.nightLightConfigs.findIndex(g => g.groupName === groupName);

    const [colorPicker, setColorPicker] = useState({
        visible: false,
        index: -1,
    });

    const [brightness, setBrightness] = useState(0);
    const handleBrightnessChange = (lightIndex: number, value: number) => {
        if (groupIndex === -1) return;
        setBrightness(value);
        lightTemplateStore.updateLightBrightness(category, groupIndex, lightIndex, value);
    };

    const handleColorChange = (lightIndex: number, color: string) => {
        if (groupIndex === -1) return;
        lightTemplateStore.updateLightColor(category, groupIndex, lightIndex, color);
    };

    const handleOpenColorPicker = (index: number) => {
        setColorPicker({ visible: true, index });
    };

    const PickColor: React.FC = () => {
        const onColorChanged = (value: string) => {
            handleColorChange(colorPicker.index, value);
        };
        return <ColorPicker disableColorPanel onColorChanged={onColorChanged} />;
    };

    return (
        <>
            <Card
                title={groupName}
                style={{
                    marginBottom: 16,
                    backgroundColor: '#ffffff',
                    maxWidth: 800,
                    margin: '0 auto 16px auto',
                    border: '1px solid #d9d9d9',
                    borderRadius: '8px',
                    boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                }}
                styles={{
                    header: {
                        backgroundColor: '#ffffff',
                        fontWeight: 'bold',
                        borderBottom: '1px solid #d9d9d9',
                        borderTopLeftRadius: '8px',
                        borderTopRightRadius: '8px'
                    }
                }}
            >
                {groupData.map((light, lightIndex) => (
                    <div
                        key={light.lightName}
                        style={{
                            padding: '16px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '16px',
                            width: '100%',
                            height: '100%',
                            position: 'relative' // 确保拾色器相对于此元素定位
                        }}
                    >
                        <div style={{ width: '30%' }}>
                            {/* 灯光名称 - 固定长宽的圆角矩形框 */}
                            <div
                                style={{
                                    width: '200px',
                                    height: '60px',
                                    border: '1px solid #000',
                                    borderRadius: '8px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '14px',
                                    flexShrink: 0
                                }}
                            >
                                {light.lightName}
                            </div>
                        </div>
                        {/* 竖直分隔线 */}
                        <div style={{
                            width: '1px',
                            height: '80px',
                            backgroundColor: '#e8e8e8',
                            flexShrink: 0
                        }} />
                        <div style={{
                            width: '70%',
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: '16px',
                            alignItems: 'center'
                        }}>

                            <Typography.Text style={{ display: 'inline-block', marginBottom: '6px', width: '30px' }}>亮度</Typography.Text>
                            {/* 亮度控制区域 */}
                            <div style={{ flex: 1, minWidth: 0 }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <Slider
                                        min={0}
                                        max={100}
                                        value={light.brightness}
                                        onChange={(value: any) => handleBrightnessChange(lightIndex, value)}
                                        style={{ flex: 1, minWidth: 0 }}
                                    />
                                    <Input
                                        type="number"
                                        min={0}
                                        max={100}
                                        value={light.brightness}
                                        onChange={(e) => handleBrightnessChange(lightIndex, Number(e.target.value))}
                                        style={{ width: '80px' }}
                                        suffix="%"
                                    />
                                </div>
                            </div>

                            <Typography.Text style={{ display: 'inline-block', marginBottom: '6px', width: '30px' }}>颜色</Typography.Text>
                            {/* 颜色控制区域 */}
                            <div style={{ flex: 1, minWidth: 0 }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    {/* 颜色矩阵 - 点击触发取色器 */}
                                    <div
                                        style={{
                                            width: '40px',
                                            height: '40px',
                                            backgroundColor: light.color,
                                            borderRadius: '4px',
                                            border: '1px solid #d9d9d9',
                                            cursor: 'pointer'
                                        }}
                                        onClick={() => handleOpenColorPicker(lightIndex)}
                                    />
                                    <Input
                                        value={light.color}
                                        onChange={(e) => handleColorChange(lightIndex, e.target.value)}
                                        placeholder="输入颜色值"
                                        style={{ flex: 1, minWidth: 0 }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </Card >

            <Modal
                open={colorPicker.visible}
                onCancel={() => {
                    setColorPicker(prev => ({
                        ...prev,
                        visible: false
                    }));
                }}
                footer={null}
            >
                <PickColor />
            </Modal>
        </>
    );
};
const TemplateManagement: React.FC<TemplateManagementProps> = ({ templates: initialTemplates }: TemplateManagementProps) => {
    // 状态管理
    const [templates, setTemplates] = useState<Template[]>(initialTemplates);
    const [searchText, setSearchText] = useState<string>('');
    const [editingKey, setEditingKey] = useState<string>('');
    const uploadInputRef = useRef<HTMLInputElement>(null);
    const inputRef = useRef<InputRef>(null);
    type SortType = 'category' | 'createTimeAsc' | 'createTimeDesc';
    const [sortType, setSortType] = useState<SortType>('createTimeDesc');
    // 初始化：将初始模板同步到Store
    useEffect(() => {
        lightTemplateStore.initTemplateList(initialTemplates);
    }, [initialTemplates]);
    useEffect(() => {
        setTemplates(initialTemplates);
    }, [initialTemplates]);
    const handleSearch = async (value: string) => {
        setSearchText(value);

        // 调用listLightTemplate接口进行搜索
        const searchResult = await LightTemplateService.listLightTemplate({
            templateName: value  // 将搜索文本作为模板名称参数传递
        });

        // 更新模板列表
        if (searchResult) {
            const processedTemplates = (searchResult.result || []).map((template: Template) => ({
                ...template,
                templateType: template.templateType === 1 ? '日光' : '夜光'
            }));
            setTemplates(processedTemplates);
            lightTemplateStore.initTemplateList(processedTemplates); // 同步到Store
        }
    };

    const handleSortToggle = () => {
        const newSortType: SortType =
            sortType === 'category' ? 'createTimeAsc' :
                sortType === 'createTimeAsc' ? 'createTimeDesc' : 'category';
        setSortType(newSortType);

        const sortedData = [...templates].sort((a, b) => {
            if (newSortType === 'category') {
                const typeA = typeof a.templateType === 'string'
                    ? (a.templateType === '日光' ? 1 : 0)
                    : a.templateType;
                const typeB = typeof b.templateType === 'string'
                    ? (b.templateType === '日光' ? 1 : 0)
                    : b.templateType;
                return typeB - typeA;
            }
            else if (newSortType === 'createTimeAsc') {
                return new Date(a.createDate).getTime() - new Date(b.createDate).getTime();
            }
            else {
                return new Date(b.createDate).getTime() - new Date(a.createDate).getTime();
            }
        });
        setTemplates(sortedData);
    };

    const handleCoverChange = async (file: File, id: string) => {
        try {
            const newCoverUrl = await uploadFile(file);
            setTemplates(templates.map(template =>
                template.id === id
                    ? { ...template, templateImage: newCoverUrl }
                    : template
            ));
            lightTemplateStore.updateTemplateImage(id, newCoverUrl);
            const result = await LightTemplateService.editLightTemplate(
                {
                    id: id,
                    templateImage: newCoverUrl,
                }
            );

            if (!result) {
                // 更新成功后刷新列表
                setTemplates(templates.map(template =>
                    template.id === id ? { ...template, templateImage: newCoverUrl } : template
                ));
                lightTemplateStore.updateTemplateImage(id, newCoverUrl);
                message.success('封面更新成功');
            }
        } catch (error) {
            message.error('封面更新失败');
        }
    };

    const isEditing = (record: Template) => record.id === editingKey;

    const edit = (record: Template) => {
        setEditingKey(record.id);
    };
    const save = async (key: string) => {
        if (!inputRef.current) return;

        const newName = inputRef.current.input?.value?.trim() || '';
        if (!newName) {
            message.error('模板名称不能为空');
            return;
        }

        try {
            const result = await LightTemplateService.editLightTemplate(
                {
                    id: key,
                    templateName: newName
                }
            );

            if (!result) {
                setTemplates(templates.map(template =>
                    template.id === key ? { ...template, templateName: newName } : template
                ));
                setEditingKey('');
                message.success('名称修改成功');
            } else {
                message.error('名称修改失败');
            }
        } catch (error) {
            console.error('修改名称接口调用失败:', error);
            message.error('名称修改失败');
        }
    };

    const handleTemplateDownload = async (id: string) => {
        try {
            const url = await LightTemplateService.getLightTemplate(id);
            const jsonUrl = url.dataUrl;
            const response = await fetch(jsonUrl);
            if (!response.ok) {
                throw new Error(`下载失败: ${response.statusText}`);
            }
            const blob = await response.blob();
            const a = document.createElement('a');
            const objectUrl = URL.createObjectURL(blob);
            a.href = objectUrl;
            a.download = 'LightConfig.json';
            a.click();
            URL.revokeObjectURL(objectUrl);
            message.info(`正在下载模板 ${id}`);
        } catch (error) {
            message.error(`下载模板 ${id} 失败`);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            const deleteResult = await LightTemplateService.delete([id]);
            if (deleteResult) {
                const updatedTemplates = templates.filter(template => template.id !== id);
                setTemplates(updatedTemplates);
                lightTemplateStore.initTemplateList(updatedTemplates);
                message.success('模板删除成功');
            } else {
                message.error('模板删除失败：接口返回异常');
            }
        } catch (error) {
            message.error('模板删除失败，请重试');
        }
    };

    // 表格列定义
    const columns = [
        {
            title: '序号',
            key: 'index',
            width: '6%',
            render: (_: any, __: Template, index: number) => <span>{index + 1}</span>
        },
        {
            title: '模板封面',
            dataIndex: 'templateImage',
            key: 'templateImage',
            width: '12%',
            render: (text: string, record: Template) => (
                <div style={{ position: 'relative', display: 'inline-block' }}>
                    <img
                        src={text}
                        alt={`${record.templateName}的封面`}
                        style={{ width: 90, height: 90, objectFit: 'cover', borderRadius: 4 }}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            borderRadius: 4,
                            opacity: 0,
                            transition: 'opacity 0.3s',
                            cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.opacity = '1'}
                        onMouseLeave={(e) => e.currentTarget.style.opacity = '0'}
                        onClick={() => uploadInputRef.current?.click()}
                    >

                        <Upload
                            name="avatar"
                            listType="picture-card"
                            showUploadList={false}
                            beforeUpload={(file) => handleCoverChange(file, record.id)}
                        >
                            {lightTemplateStore.cachedImage ? (
                                <div style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 4,
                                    overflow: 'hidden',
                                }}>
                                    <img
                                        src={lightTemplateStore.cachedImage}
                                        alt="模板封面"
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            objectFit: 'cover',
                                            display: 'block'
                                        }}
                                    />
                                </div>
                            ) : (
                                <div style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 4,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'column',
                                    cursor: 'pointer',
                                }}>
                                    <Button
                                        icon={<PictureOutlined />}
                                        size="small"
                                        type="text"
                                        style={{ color: 'white' }}
                                    >
                                        替换
                                    </Button>
                                </div>
                            )}
                        </Upload>
                    </div>
                </div>
            )
        },
        {
            title: '模板名称',
            dataIndex: 'templateName',
            key: 'templateName',
            width: '20%',
            render: (text: string, record: Template) => {
                const editable = isEditing(record);
                return editable ? (
                    <Input
                        ref={inputRef}  // 使用ref关联输入框
                        id={`name-input-${record.id}`}
                        defaultValue={text}
                        autoFocus
                        onPressEnter={() => save(record.id)}
                        style={{ width: '100%' }}
                        onClick={(e) => e.stopPropagation()}
                        onBlur={() => save(record.id)}
                    />
                ) : (
                    <Typography.Text
                        underline={editable ? true : false}
                        onClick={() => edit(record)}
                        style={{
                            cursor: 'pointer',
                        }}
                    >
                        {text}
                    </Typography.Text>
                );
            }
        },
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: '15%'
        },
        {
            title: '创建时间',
            dataIndex: 'createDate',
            key: 'createDate',
            width: '15%'
        },
        {
            title: '分类',
            dataIndex: 'templateType',
            key: 'templateType',
            width: '12%'
        },
        {
            title: '操作',
            key: 'action',
            width: '20%',
            render: (_: any, record: Template) => (
                <Space size="middle">
                    <Button
                        icon={<DownloadOutlined />}
                        type="text"
                        style={{ color: '#1890ff' }}
                        onClick={() => handleTemplateDownload(record.id)}
                    >
                        下载
                    </Button>
                    <Popconfirm
                        title="确定要删除这个模板吗？"
                        onConfirm={() => handleDelete(record.id)}
                        okText="是"
                        cancelText="否"
                    >
                        <Button
                            icon={<DeleteOutlined />}
                            type="text"
                            style={{ color: '#1890ff' }}
                        >
                            删除
                        </Button>
                    </Popconfirm>
                </Space>
            )
        }
    ];

    const filteredTemplates = templates.filter(template =>
        template.templateName.includes(searchText)
    );

    return (
        <div style={{ padding: '20px' }}>
            <div style={{
                backgroundColor: '#f5f5f5',
                padding: '16px',
                borderRadius: '4px',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'flex-end',
                alignItems: 'center'
            }}>
                <Space size="middle">
                    <Button
                        icon={<SearchOutlined />}
                        size="middle"
                        onClick={() => handleSearch(searchText)}
                    />
                    <Input
                        placeholder="搜索模板"
                        allowClear
                        style={{ width: 300 }}
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                        onPressEnter={() => handleSearch(searchText)}
                        // 添加清除事件处理
                        onClear={() => {
                            setSearchText(''); // 清空搜索文本
                            setTemplates(initialTemplates); // 恢复初始模板列表
                            lightTemplateStore.initTemplateList(initialTemplates); // 同步到Store
                        }}
                    />
                    <Tooltip title={
                        sortType === 'category' ? '当前按分类排序' :
                            sortType === 'createTimeAsc' ? '当前按创建时间正序排序' : '当前按创建时间倒序排序'
                    }>
                        <Button
                            icon={
                                sortType === 'category' ? <UnorderedListOutlined /> :
                                    sortType === 'createTimeAsc' ? <SortAscendingOutlined /> :
                                        <SortDescendingOutlined />
                            }
                            onClick={handleSortToggle}
                            size="middle"
                        />
                    </Tooltip>
                </Space>
            </div>

            <Table<Template>
                columns={columns}
                dataSource={filteredTemplates}
                pagination={false}
                bordered
                rowKey="key"
            />
        </div>
    );
};
const ModelLightConfigPanel: React.FC<{ defaultKey?: string }> = observer((props: { defaultKey?: string }) => {
    const [configData, setConfigData] = useState<ConfigData | null>(null);
    const common_styles = useCommonStyles().styles;
    const { t } = useTranslation();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [templates, setTemplates] = useState<Template[]>([]);
    const [pageIndex, setPageIndex] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(50);
    const [totalCount, setTotalCount] = useState<number>(0);
    useEffect(() => {
        loadConfigData();
    }, []);

    const initListLightTemplate = async () => {
        try {
            const initialTemplates = await LightTemplateService.listLightTemplate({});
            const processedTemplates = (initialTemplates.result || []).map((template: Template) => ({
                ...template,
                templateType: template.templateType === 1 ? '日光' : '夜光'
            }));
            setTemplates(processedTemplates || []);
            setTotalCount(initialTemplates.recordCount);
            console.log('初始化模板数据:', initialTemplates);
        } catch (error) {
            console.error('获取模板数据失败:', error);
        }
    };
    const loadConfigData = () => {
        const allConfigDataObj: ConfigData = {
            dayLightParamConfig: { data: {} },
            nightLightParamConfig: { data: {} },
            modelLightParamConfig: { data: {} }
        };

        setConfigData(allConfigDataObj);
        initListLightTemplate();
    };

    // 处理弹窗显示
    const handleSaveAsTemplate = () => {
        setIsModalVisible(true);
    };

    const toJSON = () => {
        const category = lightTemplateStore.templateInfo.category;
        const templateData = lightTemplateStore.formatLightConfigsByCategory(category as 'dayLight' | 'nightLight')
        const blob = new File([templateData], 'wireFrameImageJson.json', { type: 'application/json' }); // 创建 File 对象
        return blob;
    }
    const handleDownload = () => {
        const url = URL.createObjectURL(toJSON());
        const a = document.createElement('a');
        a.href = url;
        a.download = 'LightConfig.json';
        a.click();
    };

    const beforeUpload = async (
        file: File,
    ) => {
        try {
            const res = await uploadFile(file);
            lightTemplateStore.setCachedImage(res);
            lightTemplateStore.setTemplateImage(res);
            form.setFieldValue('cover', res);
        } catch (error) {
            // handleRequestError(error, '上传')
        } finally {
        }
    }

    // 处理保存模板逻辑
    const handleSave = async () => {
        const templateType = lightTemplateStore.templateInfo.category === 'dayLight'
            ? 1
            : 2;
        const res = await uploadFileToOss(toJSON());
        lightTemplateStore.setDataUrl(res);
        const templateImage = lightTemplateStore.templateImage;
        const templateName = form.getFieldValue('name');

        LightTemplateService.insertLightTemplate({
            dataUrl: lightTemplateStore.dataUrl,
            templateImage: templateImage,
            templateName: templateName,
            templateType: templateType,
        });

        setIsModalVisible(false);
        form.setFieldsValue({ name: '' });
        lightTemplateStore.setCachedImage(null);
    };
    const handleLoad = () => {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';

        fileInput.onchange = async (e) => {
            const target = e.target as HTMLInputElement;
            const file = target.files?.[0];

            if (!file) {
                message.warning('未选择文件');
                return;
            }

            if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                message.error('请上传 JSON 格式的文件');
                return;
            }

            try {
                const content = await new Promise<string>((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result as string);
                    reader.onerror = reject;
                    reader.readAsText(file);
                });

                // 解析JSON内容
                const jsonData = JSON.parse(content);

                // 验证是否为数组
                if (!Array.isArray(jsonData)) {
                    message.error('JSON格式错误');
                    return;
                }

                // 转换为LightConfigGroupProps格式
                const convertedConfig = lightTemplateStore.convertToLightConfigGroup(jsonData);
                console.log('转换后的配置:', convertedConfig);

                // 更新到对应的配置中
                if (lightTemplateStore.templateInfo.category === 'dayLight') {
                    console.log('类型',lightTemplateStore.templateInfo.category)
                    lightTemplateStore.setDayLightConfigs(convertedConfig);
                    message.success('日光配置导入成功');
                } else {
                    console.log('类型',lightTemplateStore.templateInfo.category)    
                    lightTemplateStore.setNightLightConfigs(convertedConfig);
                    message.success('夜景配置导入成功');
                }

            } catch (error) {
                message.error(`加载失败: ${error instanceof Error ? error.message : '解析错误'}`);
            }
        };

        // 触发文件选择对话框
        fileInput.click();
    };
    const handlePageChange = async (page: number, pageSize: number) => {
        setPageIndex(page);
        setPageSize(pageSize);
        const result = await LightTemplateService.listLightTemplate({
            pageIndex: page,
            pageSize
        });
        if (result) {
            const processedTemplates = result.result.map((template: Template) => ({
                ...template,
                templateType: template.templateType === 1 ? '日光' : '夜光'
            }));
            setTemplates([...processedTemplates]);
            lightTemplateStore.initTemplateList([...processedTemplates]);
        }
    };

    if (!configData) {
        return <div>{t("加载中")}...</div>;
    }

    return (
        <div style={{
            position: 'absolute',
            left: "50%",
            top: 0,
            width: '100%',
            height: '100%',
            backgroundColor: '#fff',
            padding: '20px',
            zIndex: 1000,
            maxWidth: 1000,
            transform: "translate(-50%, 0px)"
        }}>
            <div style={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 0,
                bottom: 50,
            }}>
                <Tabs
                    className={common_styles.score_tabs}
                    defaultActiveKey="dayLight"
                    items={[
                        {
                            key: 'dayLight',
                            label: '日光灯光模板配置',
                            children: lightTemplateStore.dayLightConfigs.map((configGroup) => (
                                <LightConfigGroup
                                    key={configGroup.groupName}
                                    groupName={configGroup.groupName}
                                    groupData={configGroup.groupData}
                                    category="dayLight"
                                />
                            ))
                        },
                        {
                            key: 'nightLight',
                            label: '夜间灯光模板配置',
                            children: lightTemplateStore.nightLightConfigs.map((configGroup) => (
                                <LightConfigGroup
                                    key={configGroup.groupName}
                                    groupName={configGroup.groupName}
                                    groupData={configGroup.groupData}
                                    category="nightLight"
                                />
                            ))
                        },
                        {
                            key: 'modelLight',
                            label: '灯光模板管理',
                            children: <TemplateManagement templates={templates} />
                        },
                    ]}
                    style={{
                        backgroundColor: '#ffffff'
                    }}
                    onChange={(key) => { lightTemplateStore.setCategory(key); }}
                />
            </div>
            {(lightTemplateStore.templateInfo.category === 'dayLight' || lightTemplateStore.templateInfo.category === 'nightLight')
                && <div style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '100%',
                    padding: '10px',
                    backgroundColor: '#fff',
                    borderTop: '1px solid #e8e8e8',
                    textAlign: 'center',
                    zIndex: 1001
                }}>
                    <Space size="middle">
                        <Button type="primary" onClick={handleSaveAsTemplate} style={{ width: 150 }}>
                            保存当前配置为模板
                        </Button>
                        <Button type="primary" onClick={handleDownload} style={{ width: 120 }}>
                            下载配置
                        </Button>
                        <Button type="primary" onClick={() => { lightTemplateStore.resetLightConfigs(lightTemplateStore.templateInfo.category) }} style={{ width: 120 }}>
                            恢复默认配置
                        </Button>
                        <Button type="primary" onClick={handleLoad} style={{ width: 120 }}>
                            加载灯光配置
                        </Button>
                    </Space>
                </div>}

            {(lightTemplateStore.templateInfo.category === 'modelLight')
                && <div style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '100%',
                    padding: '10px',
                    backgroundColor: '#fff',
                    borderTop: '1px solid #e8e8e8',
                    textAlign: 'center',
                    zIndex: 1001,
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center'
                }}
                >
                    <Pagination
                        current={pageIndex}
                        pageSize={pageSize}
                        total={totalCount}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageChange}
                        showSizeChanger
                    />
                </div>}

            <Modal
                title={
                    <div style={{ textAlign: 'center', width: '100%' }}>
                        <Typography.Title level={4} style={{ margin: 0 }}>
                            保存为新灯光模板
                        </Typography.Title>
                        <div style={{ height: '1px', backgroundColor: '#000', marginTop: 8 }} />
                    </div>
                }
                open={isModalVisible}
                onCancel={() => {
                    setIsModalVisible(false);
                }}
                footer={null}
                width={700}
                destroyOnClose={true}
            >
                <div style={{ display: 'flex', gap: 20 }}>
                    {/* 左侧：封面上传 */}
                    <div style={{ width: '30%', marginTop: 0 }}>
                        <Typography.Text type="danger" style={{ display: 'inline-block' }}>* </Typography.Text>
                        <Typography.Text style={{ display: 'inline-block' }}>模板封面</Typography.Text>

                        <Upload
                            name="avatar"
                            listType="picture-card"
                            className="avatar-uploader"
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                        >
                            {lightTemplateStore.cachedImage ? (
                                <div style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 4,
                                    overflow: 'hidden',
                                }}>
                                    <img
                                        src={lightTemplateStore.cachedImage}
                                        alt="模板封面"
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            objectFit: 'cover', // 保持比例并覆盖整个容器
                                            display: 'block' // 消除图片底部可能的间隙
                                        }}
                                    />
                                </div>
                            ) : (
                                <div style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 4,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'column',
                                    cursor: 'pointer',
                                }}>
                                    <UploadOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                                    <p style={{ marginTop: 8 }}>点击上传</p>
                                    <p style={{ fontSize: 12, color: '#666' }}>支持JPG/PNG格式</p>
                                </div>
                            )}
                        </Upload>
                    </div>

                    {/* 右侧：模板信息 */}
                    <div style={{ width: '70%' }}>
                        <Form
                            form={form}
                            layout="vertical"
                        >
                            <Form.Item
                                name="name"
                                label="模板名称"
                                rules={[
                                    { required: true, message: '请输入模板名称' },
                                    { max: 50, message: '名称不能超过50个字符' }
                                ]}
                            >
                                <Input placeholder="请输入模板名称" />
                            </Form.Item>

                            <Form.Item
                                name="category"
                                label="模板分类"
                            >
                                <div style={{ display: 'flex', gap: 8 }}>
                                    <>
                                        {/* 日光按钮 */}
                                        <Button
                                            style={{
                                                backgroundColor: lightTemplateStore.templateInfo.category === 'dayLight' ||
                                                    lightTemplateStore.templateInfo.category === 'modelLight'
                                                    ? '#1890ff' : '#ffffff',
                                                color:
                                                    lightTemplateStore.templateInfo.category === 'dayLight' ||
                                                        lightTemplateStore.templateInfo.category === 'modelLight'
                                                        ? '#ffffff' : '#000000',
                                                border: '1px solid #d9d9d9',
                                                borderRadius: 4,
                                                padding: '8px 16px',
                                                cursor: 'pointer',
                                                marginRight: 8
                                            }}
                                        >
                                            日光
                                        </Button>

                                        <Button
                                            style={{
                                                backgroundColor: lightTemplateStore.templateInfo.category === 'nightLight' ? '#1890ff' : '#ffffff',
                                                color: lightTemplateStore.templateInfo.category === 'nightLight' ? '#ffffff' : '#000000',
                                                border: '1px solid #d9d9d9',
                                                borderRadius: 4,
                                                padding: '8px 16px',
                                                cursor: 'pointer'
                                            }}
                                        >
                                            夜光
                                        </Button>
                                    </>
                                </div>
                            </Form.Item>
                        </Form>
                    </div>
                </div>

                {/* 弹窗底部按钮 */}
                <div style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginTop: 20,
                    gap: 10
                }}>
                    <Button
                        onClick={() => setIsModalVisible(false)}
                        style={{ width: 100 }}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        style={{ width: 100 }}
                        onClick={handleSave}
                    >
                        保存
                    </Button>
                </div>
            </Modal>
        </div>
    );
});

export default ModelLightConfigPanel;