import { <PERSON>, Vector3 } from "three";

import { <PERSON><PERSON><PERSON>, ZPolygon, ZRect } from "@layoutai/z_polygon";

import { WPolygon } from "../../Layout/TFeatureShape/WPolygon";
import { TRoomShape } from "../../Layout/TRoomShape";
import { ILayoutFigure } from "./ILayoutFigure";
import { ILayoutRoom } from "./ILayoutRoom";
import { ILayoutStyle } from "./ILayoutStyle";
import { ILayoutSubService } from "./ILayoutSubService";
import { LayoutCategoryConfig, LayoutSizeConfig } from "./LayoutConfig";
import { WardrobeDirType } from "./WardrobeDirType";


/**
* @description 卧室布局子服务
* <AUTHOR>
* @date 2025-07-28
* @lastEditTime 2025-07-28 09:41:36
* @lastEditors xuld
*/
export class BedRoomSubService implements ILayoutSubService {
    /**
    * @description 获取 p 在直线AB上的投影点
    * @param pointP 点P
    * @param pointA 点A
    * @param pointB 点B
    * @return 投影点
    */
    public getProjectedPoint(pointP: Vector3, pointA: Vector3, pointB: Vector3): Vector3 {
        const AB = new Vector3().subVectors(pointB, pointA);
        const dir = AB.clone().normalize();

        const AP = new Vector3().subVectors(pointP, pointA);
        const dot = AP.dot(dir);
        const projectedPoint = new Vector3().copy(dir).multiplyScalar(dot).add(pointA);
        return projectedPoint;
    }

    /**
    * @description 获取点P到边AB的距离
    * @param pointP 点P
    * @param edge 边
    * @return 距离
    */
    public getDistanceToEdge(pointP: Vector3, edge: ZEdge): number {
        let projPoint = this.getProjectedPoint(pointP, edge.start_pos, edge.end_pos);
        return projPoint.distanceTo(pointP);
    }

    /**
    * @description 获取门禁区列表，门口禁止放家具
    * @param room 房间
    * @return 门禁区列表
    */
    public getDoorRestrictedArea(room: ILayoutRoom): ZRect[] {
        let doorAreas: ZRect[] = [];

        let w_poly = room.featurePoly;
        for (let edge of w_poly.edges) {
            let door = WPolygon.getWindowOnEdge(edge);
            if (door && door.type === "Door") {
                let w = Math.max(door.length, LayoutSizeConfig.DOOR_SIZE);
                let h = Math.max(door.width, LayoutSizeConfig.DOOR_SIZE);
                let areaRect = new ZRect(w, h);

                // 位置
                let doorPos = new Vector3(door.posX, door.posY, 0);
                if (door.center) {
                    doorPos = door.center.clone();
                }

                // 法向
                let nor = new Vector3(0, 0, 1);
                if (door._nor_data) {
                    nor.copy(door._nor_data);
                }
                else if (door.nor) {
                    nor.copy(door.nor);
                }

                // 投影点
                let projPoint = this.getProjectedPoint(doorPos, edge.start_pos, edge.end_pos);
                areaRect.nor = nor;
                areaRect.back_center.copy(projPoint).sub(nor.clone().setZ(0).multiplyScalar(areaRect.h));
                areaRect.back_center.setZ(0);
                areaRect.updateRect();
                doorAreas.push(areaRect);
            }
        }

        return doorAreas;
    }

    /**
    * @description 获取动线边列表
    * @param room 房间
    * @return 动线边列表
    */
    public getActEdges(room: ILayoutRoom): ZEdge[] {
        let w_poly = room.featurePoly;
        let mainRect = TRoomShape.computeMaxRectBySplitShape(w_poly);

        let doorEdges: ZEdge[] = [];
        for (let edge of w_poly.edges) {
            let door = WPolygon.getWindowOnEdge(edge);
            if (door && door.type === "Door") {
                doorEdges.push(edge);
            }
        }

        // 没有门时，每个房间的边都可以作为动线
        if (doorEdges.length === 0) {
            return mainRect.edges;
        }

        let edgeSet: Set<ZEdge> = new Set();
        for (let doorEdge of doorEdges) {
            let mid = doorEdge.center;
            // 计算动线，离门垂直距离最近的两条边
            let minDistance1 = Infinity;
            let minDistance2 = Infinity;
            let minEdge1 = null;
            let minEdge2 = null;
            for (let edge of mainRect.edges) {
                let projPoint = this.getProjectedPoint(mid, edge.start_pos, edge.end_pos);
                let distance = projPoint.distanceTo(mid);
                if (distance < minDistance1) {
                    minDistance2 = minDistance1;
                    minEdge2 = minEdge1;
                    minDistance1 = distance;
                    minEdge1 = edge;
                } else if (distance < minDistance2) {
                    minDistance2 = distance;
                    minEdge2 = edge;
                }
            }
            if (minEdge1) {
                edgeSet.add(minEdge1);
            }
            if (minEdge2) {
                edgeSet.add(minEdge2);
            }
        }

        return Array.from(edgeSet);
    }

    /**
    * @description 获取过道区列表
    * @param room 房间
    * @return 过道区列表
    */
    public getClearanceArea(room: ILayoutRoom): ZRect[] {
        let clearanceAreas: ZRect[] = [];
        let actEdges = this.getActEdges(room);
        for (let edge of actEdges) {
            let areaRect = new ZRect(edge.length, LayoutSizeConfig.CLEARANCE_WIDTH);
            areaRect.nor = edge.nor.clone().multiplyScalar(-1);
            areaRect.back_center.copy(edge.center.clone());
            areaRect.updateRect();
            clearanceAreas.push(areaRect);
        }
        return clearanceAreas;
    }

    /**
    * @description 获取多边形中的主矩形
    * @param poly 多边形
    * @return 主矩形
    */
    public getMainRect(poly: ZPolygon): ZRect {
        let mRect = TRoomShape.computeMaxRectBySplitShape(poly);
        let polys = WPolygon.splitPolyIntoRects(poly, "SplitRoom");
        for (let poly of polys) {
            if (poly.edges.length != 4) {
                continue;
            }
            let rect = ZRect.computeMainRect(poly);
            if (rect.min_hh > mRect.min_hh) {
                mRect = rect;
            }
        }
        return mRect;
    }

    /**
    * @description 获取布置区列表
    * @param room 房间
    * @return 布置区列表
    */
    public getLayoutAreas(room: ILayoutRoom): ZRect[] {
        let layoutAreas: ZRect[] = [];

        let doorAreas = this.getDoorRestrictedArea(room);
        let clearanceAreas = this.getClearanceArea(room);
        for (let clearanceArea of clearanceAreas) {
            let poly = room.roomPoly.clone();
            let subPolys = [...doorAreas, clearanceArea];
            let functionArea = poly.substract_polygons(subPolys);
            if (functionArea.length === 0) {
                continue;
            }

            // 从布尔结果中获取最大的矩形
            let mainRect = this.getMainRect(functionArea[0]);
            for (let i = 1; i < functionArea.length; ++i) {
                let rect = this.getMainRect(functionArea[i]);
                if (rect.min_hh > mainRect.min_hh) {
                    mainRect = rect;
                }
            }

            layoutAreas.push(mainRect);
        }
        return layoutAreas;
    }


    /**
    * @description 判断边是否在多边形边上，即边与多边形边重合（部分重合也算）
    * @param edge 边
    * @param poly 房间多边形
    * @return 是否在多边形边上
    */
    public isEdgeOnPoly(edge: ZEdge, poly: ZPolygon): boolean {
        for (let polyEdge of poly.edges) {
            let distanceStart = this.getDistanceToEdge(edge.start_pos, polyEdge);
            let distanceEnd = this.getDistanceToEdge(edge.end_pos, polyEdge);
            if (distanceStart < 1 && distanceEnd < 1) {
                return true;
            }
        }
        return false;
    }

    /**
    * @description 获取床头墙边列表
    * @param room 房间
    * @param funcArea 功能区
    * @return 床头墙边列表
    */
    public getBedHeadEdges(room: ILayoutRoom, funcArea: ZRect): ZEdge[] {
        let bedHeadEdges: ZEdge[] = [];
        let roomPoly = room.roomPoly;
        for (let edge of funcArea.edges) {
            if (this.isEdgeOnPoly(edge, roomPoly)) {
                bedHeadEdges.push(edge);
            }
        }
        return bedHeadEdges;
    }

    /**
    * @description 获取床朝向
    * @param room 房间
    * @param layoutArea 布置区
    * @return 床朝向列表
    */
    public getBedDir(room: ILayoutRoom, layoutArea: ZRect): { bedRay: Ray, bedEdge: ZEdge }[] {
        const bedHeadEdges = this.getBedHeadEdges(room, layoutArea);
        const dirs: { bedRay: Ray, bedEdge: ZEdge }[] = [];
        for (let edge of bedHeadEdges) {
            const bedRay = new Ray(edge.center, edge.nor.clone().multiplyScalar(-1).normalize());
            dirs.push({
                bedRay: bedRay,
                bedEdge: edge
            });
        }
        return dirs;
    }

    /**
    * @description 获取衣柜朝向(7种)
    * 相对于床计算：方+位
    * 平行床（4种）：左、右、左前、右前
    * 垂直床（3种）：前、左后、右后
    * @param bedInfo 床朝向射线
    * @param layoutArea 布置区
    * @return 衣柜朝向列表（7种）
    */
    public getWardrobeDir(bedInfo: { bedRay: Ray, bedEdge: ZEdge }): { ray: Ray, type: WardrobeDirType }[] {
        const bedRay = bedInfo.bedRay;
        const bedEdge = bedInfo.bedEdge;
        // 平行 - 左
        const sideOffset = bedRay.direction.clone().normalize().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_LENGTH / 2);
        const sideOrigin = bedRay.origin.clone().add(sideOffset);

        // 平行 - 左 (床方向旋转-90度)
        const leftDir = bedRay.direction.clone().applyAxisAngle(new Vector3(0, 0, 1), -Math.PI / 2);
        const leftOrigin = sideOrigin.clone().add(leftDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * -0.5));
        const leftRay = new Ray(leftOrigin, leftDir.clone());

        // 平行 - 右 (床方向旋转90度)
        const rightDir = bedRay.direction.clone().normalize().applyAxisAngle(new Vector3(0, 0, 1), Math.PI / 2);
        const rightOrigin = sideOrigin.clone().add(rightDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * -0.5));
        const rightRay = new Ray(rightOrigin, rightDir.clone());

        // 平行 - 左前
        const frontOffset = bedRay.direction.clone().normalize().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_LENGTH * 1.2);
        const frontOrigin = bedRay.origin.clone().add(frontOffset);

        const leftFrontOrigin = frontOrigin.clone().add(leftDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * -0.25));
        const leftFrontRay = new Ray(leftFrontOrigin, leftDir.clone());

        // 平行 - 右前
        const rightFrontOrigin = frontOrigin.clone().add(rightDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * -0.25));
        const rightFrontRay = new Ray(rightFrontOrigin, rightDir.clone());

        // 垂直 - 前
        const frontDir = new Ray(bedRay.origin, bedRay.direction.clone().multiplyScalar(-1));
        const frontRay = new Ray(frontOrigin, frontDir.direction);

        // 垂直 - 左后
        const leftBackOrigin = bedRay.origin.clone().add(leftDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * 0.55));
        const leftBackRay = new Ray(leftBackOrigin, bedRay.direction.clone());

        // 垂直 - 右后
        const rightBackOrigin = bedRay.origin.clone().add(rightDir.clone().multiplyScalar(LayoutSizeConfig.DEFAULT_BED_DEPTH * 0.55));
        const rightBackRay = new Ray(rightBackOrigin, bedRay.direction.clone());

        return [
            { ray: leftRay, type: WardrobeDirType.PARALLEL_LEFT },
            { ray: rightRay, type: WardrobeDirType.PARALLEL_RIGHT },
            { ray: leftFrontRay, type: WardrobeDirType.PARALLEL_LEFT_FRONT },
            { ray: rightFrontRay, type: WardrobeDirType.PARALLEL_RIGHT_FRONT },
            { ray: frontRay, type: WardrobeDirType.VERTICAL_FRONT },
            { ray: leftBackRay, type: WardrobeDirType.VERTICAL_LEFT_BACK },
            { ray: rightBackRay, type: WardrobeDirType.VERTICAL_RIGHT_BACK },
        ];
    }

    /**
    * @description 获取衣柜和床区域
    * @param layoutArea 布置区
    * @param bedRay 床朝向
    * @param wardrobeDirInfo 衣柜朝向
    * @return 衣柜和床区域
    */
    public getWardrobeAndBedArea(layoutArea: ZRect, bedInfo: { bedRay: Ray, bedEdge: ZEdge }, wardrobeDirInfo: { ray: Ray, type: WardrobeDirType }): { wardrobeArea: ZRect, bedArea: ZRect } | undefined {
        let wardrobeEdges: ZEdge = null;
        for (let edge of layoutArea.edges) {
            // 方向相反
            if (edge.nor.dot(wardrobeDirInfo.ray.direction) < -0.99) {
                wardrobeEdges = edge;
            }
        }

        if (!wardrobeEdges) {
            return;
        }

        const bedRay = bedInfo.bedRay;
        const bedEdge = bedInfo.bedEdge;

        let wardrobeArea: ZRect | undefined;
        let bedArea: ZRect | undefined;
        if (wardrobeDirInfo.type === WardrobeDirType.PARALLEL_LEFT ||
            wardrobeDirInfo.type === WardrobeDirType.PARALLEL_RIGHT) {
            // 衣柜
            wardrobeArea = new ZRect(wardrobeEdges.length, LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
            wardrobeArea.nor = wardrobeDirInfo.ray.direction.clone();
            wardrobeArea.back_center.copy(wardrobeEdges.center.clone());
            wardrobeArea.updateRect();

            // 床
            bedArea = new ZRect(bedEdge.length - LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH, wardrobeEdges.length);
            bedArea.nor = bedRay.direction.clone();
            const bedPos = bedEdge.center.clone().add(wardrobeArea.nor.clone().normalize().setZ(0).multiplyScalar(LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH * 0.5));
            bedArea.back_center.copy(bedPos);
            bedArea.updateRect();
        } else if (wardrobeDirInfo.type === WardrobeDirType.PARALLEL_LEFT_FRONT ||
            wardrobeDirInfo.type === WardrobeDirType.PARALLEL_RIGHT_FRONT) {
            // 衣柜
            const wardrobeLength = wardrobeEdges.length - LayoutSizeConfig.DEFAULT_BED_LENGTH;
            if (wardrobeLength > 0) {
                const offset = LayoutSizeConfig.DEFAULT_BED_LENGTH * 0.5;
                wardrobeArea = new ZRect(wardrobeLength, LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
                wardrobeArea.nor = wardrobeDirInfo.ray.direction.clone();
                const backPos = wardrobeEdges.center.clone().add(bedRay.direction.clone().normalize().setZ(0).multiplyScalar(offset));
                wardrobeArea.back_center.copy(backPos);
                wardrobeArea.updateRect();
            }

            // 床
            bedArea = new ZRect(LayoutSizeConfig.DEFAULT_BED_DEPTH, LayoutSizeConfig.DEFAULT_BED_LENGTH);
            bedArea.nor = bedRay.direction.clone();
            const offset = (bedEdge.length - LayoutSizeConfig.DEFAULT_BED_DEPTH) * 0.5;
            const bedPos = bedEdge.center.clone().sub(wardrobeDirInfo.ray.direction.clone().normalize().setZ(0).multiplyScalar(offset));
            bedArea.back_center.copy(bedPos);
            bedArea.updateRect();
        } else if (wardrobeDirInfo.type === WardrobeDirType.VERTICAL_FRONT) {
            // 衣柜
            wardrobeArea = new ZRect(wardrobeEdges.length, LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
            wardrobeArea.nor = wardrobeDirInfo.ray.direction.clone();
            wardrobeArea.back_center.copy(wardrobeEdges.center.clone());
            wardrobeArea.updateRect();

            // 床
            bedArea = new ZRect(bedEdge.length, bedEdge.next_edge.length - LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
            bedArea.nor = bedRay.direction.clone();
            const bedPos = bedEdge.center.clone();
            bedArea.back_center.copy(bedPos);
            bedArea.updateRect();
        } else if (wardrobeDirInfo.type === WardrobeDirType.VERTICAL_LEFT_BACK) {
            // 衣柜
            const leftDir = bedRay.direction.clone().applyAxisAngle(new Vector3(0, 0, 1), -Math.PI / 2);
            const wardrobeLength = wardrobeEdges.length - LayoutSizeConfig.DEFAULT_BED_DEPTH;
            if (wardrobeLength > 0) {
                wardrobeArea = new ZRect(wardrobeLength, LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
                wardrobeArea.nor = wardrobeDirInfo.ray.direction.clone();
                const offset = LayoutSizeConfig.DEFAULT_BED_DEPTH * 0.5;
                const backPos = wardrobeEdges.center.clone().add(leftDir.normalize().setZ(0).multiplyScalar(offset));
                wardrobeArea.back_center.copy(backPos);
                wardrobeArea.updateRect();
            }

            // 床
            bedArea = new ZRect(bedEdge.length - wardrobeLength, bedEdge.next_edge.length);
            bedArea.nor = bedRay.direction.clone();
            const offset = wardrobeLength * 0.5;
            const bedPos = bedEdge.center.clone().sub(leftDir.clone().normalize().setZ(0).multiplyScalar(offset));
            bedArea.back_center.copy(bedPos);
            bedArea.updateRect();
        }
        else if (wardrobeDirInfo.type === WardrobeDirType.VERTICAL_RIGHT_BACK) {
            // 衣柜
            const wardrobeLength = wardrobeEdges.length - LayoutSizeConfig.DEFAULT_BED_DEPTH;
            const rightDir = bedRay.direction.clone().normalize().applyAxisAngle(new Vector3(0, 0, 1), Math.PI / 2);
            if (wardrobeLength > 0) {
                wardrobeArea = new ZRect(wardrobeLength, LayoutSizeConfig.DEFAULT_WARDROBE_DEPTH);
                wardrobeArea.nor = wardrobeDirInfo.ray.direction.clone();
                const offset = LayoutSizeConfig.DEFAULT_BED_DEPTH * 0.5;
                const backPos = wardrobeEdges.center.clone().add(rightDir.normalize().setZ(0).multiplyScalar(offset));
                wardrobeArea.back_center.copy(backPos);
                wardrobeArea.updateRect();
            }

            // 床
            bedArea = new ZRect(bedEdge.length - wardrobeLength, bedEdge.next_edge.length);
            bedArea.nor = bedRay.direction.clone();
            const offset = wardrobeLength * 0.5;
            const bedPos = bedEdge.center.clone().sub(rightDir.clone().normalize().setZ(0).multiplyScalar(offset));
            bedArea.back_center.copy(bedPos);
            bedArea.updateRect();
        }
        return {
            wardrobeArea,
            bedArea
        };
    }

    /**
    * @description 获取卧室布局样式列表
    * @param room 房间
    * @return 布局样式列表
    */
    public getLayoutStyles(room: ILayoutRoom): ILayoutStyle[] {
        let layoutStyles: ILayoutStyle[] = [];

        const layoutAreas = this.getLayoutAreas(room);
        for (let i = 0; i < layoutAreas.length; ++i) {
            const bedDirList: { bedRay: Ray, bedEdge: ZEdge }[] = this.getBedDir(room, layoutAreas[i]);
            for (let j = 0; j < bedDirList.length; ++j) {
                const wardrobeDirList: { ray: Ray, type: WardrobeDirType }[] = this.getWardrobeDir(bedDirList[j]);
                for (let k = 0; k < wardrobeDirList.length; ++k) {
                    let areaInfo = this.getWardrobeAndBedArea(layoutAreas[i], bedDirList[j], wardrobeDirList[k]);
                    if (areaInfo) {
                        let figures: ILayoutFigure[] = [];
                        if (areaInfo.wardrobeArea) {
                            figures.push({
                                category: LayoutCategoryConfig.CATEGORY_WARDROBE,
                                rect: areaInfo.wardrobeArea,
                            });
                        }
                        if (areaInfo.bedArea) {
                            figures.push({
                                category: LayoutCategoryConfig.CATEGORY_BED,
                                rect: areaInfo.bedArea,
                            });
                        }
                        if (figures.length > 0) {
                            let style: ILayoutStyle = {
                                figures: figures,
                            };
                            layoutStyles.push(style);
                        }
                    }
                }
            }
        }
        return layoutStyles;
    }
}