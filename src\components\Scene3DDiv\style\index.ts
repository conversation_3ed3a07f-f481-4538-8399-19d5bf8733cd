import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    mainWidget: css`
      position:absolute;
      width:calc( 100%);
      left: 0px;
      height:100%;
      z-index:10;
      background:#333;

    `,
    mobileWidget: css`
      /* position:absolute;
      left: 0px;
      top: 0px; */
      width:100%;
      height:100%;
      z-index:3;
    `,
    mobileWidgetAiDraw: css`
      position:absolute;
      left: 0px;
      top: 0px;
      width:100%;
      height:calc(100%);
      z-index:3;
    `,
    lightMobileWidget: css`
      position:absolute;
      left: 0px;
      top: 40px;
      width:100%;
      height:calc( 100%  );
      z-index:3;
    `,
    editorMobileWidget: css`
      position:absolute;
      left: 0px;
      top: 0px;
      width:100%;
      height: 100%;
      z-index:3;
    `,
    closeBtn: css`
      position:absolute;
      right:5px;
      top:5px;
      width:20px;
      height:20px;
      font-size:20px;
      color:#aaa;
      cursor:pointer;
      z-index:11;
    `,
    doubleMainWidget: css`
      position:absolute;
      width:100%;
      left:0;
      height:100%;
      z-index:5001;
      background:#333;
      .PartI {
        position:absolute;
        width:50%;
        height:100%;
        left:0;
        top:0;
      }
      .imgContainer {
        width:100%;
        height:100%;
        display:flex;
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中，如果也需要水平居中的话 */
      }

      .PartII {
        position:absolute;
        width:50%;
        height:100%;
        left:50%;
        top:0;
      }
      .MidBorder {
        position:absolute;
        width:2%;
        left:50%;
        top:0;
        height:100%;
        cursor:w-resize;
        z-index:5;
        .borderLine {
            width:0px;
            height:100%;
            float:right;
            border-right:1px solid #77777777;
            // display:none;
        }
        &:hover .borderLine {
          display:block;
        }

      }
    `,
    viewerContainer: css`
      /* position: absolute; */
      position:relative;
      width:100%;
      height:100%;
    `,
    viewerDrawContainer: css`
      position:absolute;
      width:100px;
      height:100px;
    `,
    maskBackground: css`
      position:absolute;
      width:100%;
      height:100%;
      background:#fafafa;
    `,
    iconbtn_container: css`
      position:absolute;
      width:100%;
      top:0;
      z-index:10;
    `,
    iconbtn: css`
      float:left;
      // margin-top:5px;
      width:60px;
      margin-left:5px;
      text-align:center;
      cursor:pointer;
      color :#333;
      height:25px;
      line-height:25px;
      .sub_list {
        display:none;

        .sub_btn {
          width:60px;
          line-height:25px;
          height:25px;
          :hover {
            background:#aaa;

          }
        }
      }
      :hover {
        background:rgba(129, 129, 129, 0.2);

        .sub_list {
          display:block;
          background:#88888888;
        }
      }

    `,
    scene3D: css`
      width: 100%;
      height: 100%;    
      box-shadow: 0 2px 12px rgba(0, 0, 0, .2);  
      canvas {
        position:absolute;
        left:0;
        top:0;
      }  
    `,
    aiDrawscene3D: css`
      width: 800px !important;
      height: 600px !important;  
      box-shadow: 0 2px 12px rgba(0, 0, 0, .2);  
      canvas {
        position:absolute;
        left:0;
        top:0;
      }  
    `,
    plane3DLayer: css`
      width:100%;
      height:0;
      padding-bottom:100%;

      .ViewImg {
        width:100%;
        text-align:center;
        position:absolute;
     }
    `,
    leftHandle3Droot: css`
      width:200px;
      height:200px;
      border-radius:50px;
      position:absolute;
      left:0;
      bottom:50px;
      z-index:20;
      .handleCircle {
         left:70px;
         top:70px;
      }
      .btn
      {
        width:60px;
        height:60px;
        position:absolute;
        // border:1px solid;
        font-size:50px;
        line-height:60px;
        text-align:center;
        color:#00000066;
        &:hover {
          color:#ffffff;
        }
      }
      .upBtn {

          left:70px;
          top:5px;

      }
      .downBtn {

        left:70px;
        bottom:5px;

      }
      .leftBtn {

        left:5px;
        top:70px;

      }
      .rightBtn {

        right:5px;
        top:70px;

      }
      .Joystick {
        position: absolute;
        width: 120px;
        height: 120px;
      }
      .JoystickBg
      {
        position: absolute;
        background-image:url(data:image/png;base64,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);background-size:100% 100%;width:120px;height:120px;pointer-events:none
      }
      .JoystickRotate {
        position: absolute;
        width: 120px;
        height: 120px;
        pointer-events: none;
      }
      .JoystickArrow {
        background-image: url(data:image/png;base64,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);
        background-size: 100% 100%;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        pointer-events: none;
      }
      .JoystickProcess {
        background-image: url(data:image/png;base64,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);
        background-size: 100% 100%;
        opacity: 0;
        transition: opacity 1s;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        pointer-events: none;
    }
    .JoystickHolder {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAYAAAA5ZDbSAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAOKSURBVHhe7dxbThRREIdxnlyBD8ZX9gJPbABfWYJshUQQL4gmKkI0xjsuDfx/pk5iJtMyZC40h++XVCTOTJ+aqvTp6/SaJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSpLvn8vLywcXFxVbicWIvcZb4mjiv4G/+j9d4zxafqY9rjNKg+2nUduIo0Zr4KUEjTxLv8553BH/X//Ea72nN57PbLKsWq5uWhjxM7CZ+JL4kaNrbxOvEUZr1Iv8+TxxOxPN6jabyXj7DZ1kGy2KZD2sYrVqacy8N2El8T3xOsFa+rqbRwKeJ/YonA9Fe572H9VmazbJYJsveYawaVquQoq8njhNMrUy1rxLPEq2p05o5S7RmsyyWybIZg7HWa3gtUwq9mfiW+Jh4k2D6nbexk9EazbIZg7EYc7PS0DKkwOxE/Ux8SLCGMRUvsrGTwbIZg7EYk7G3Kx0tEoVN/Eowbb5MLHqtHYq2NjMmY5ODTV6kFHQjwdqz6ua2mGwyuWxUeppHCskOFds/psjW3GlNWEW0JpMLObnjNY86FGIPlp0ctoOrXnMno63J5EJOxx5CzSEF5DiXwxT2ZJe9QzVrkAO5kBO57VS6uo4UjjNUnFE6qRMQB4lpBb+JOKic2B6To2e8ritF41QhZ5OYom9yuzsU5ERu5LhbaWsWWTu4cMCawSlDziqNYWqeDHIiN3L8Qc6Vvq6Sgj1KcNKf88JjXHtbkBs5kuujSl9XSbG4wnNW27kxrr0t9itHrkIdVfr6nxSMi/W/E1y+Y291WmHHFORIrr/Jvb6GhqRQ3InB4cfYp+cWbZom5636GhqSInELDXdZME2PeXpuQY7kSs6P62toSIr09x6qW7D9bfHvdnivvoaGpEinCU4gcC12WkHHGORKzqf1NTQkRWJbxrHlbdjBakGu5Py1voaGpEjnmfK48/FWNbhyPq+voSEUyQZ3LEVyiu5ZiuROVs9SJA+TepYieaKjZymSpyp7lunOiw29S7G8XNizFMsL/j3LWuEtO71L0bzprmcpmrfN9i6F88b3nmUt8acrvUsR+fEZj1IY24/PyMkfny1CCunPR3uXgvoD8N5R2ARrD1Mk28Fl73ixbMZgLMZkbJu7TCmwD2HpXQrtY5R6V4dQPgitd2mAjzK8C9IsH0Z6V6RBPk5YkiRJkiRJkiRJkiRJkiRJkiRJkiRJkiRJ0gzW1v4AqpFHfTYtCGwAAAAASUVORK5CYII=);
      width: 120px;
      height: 120px;
      pointer-events: none;
      background-size: 100% 100%;
    }

    `,
    rightHandle3Droot: css`
      width:150px;
      height:150px;
      border-radius:50px;
      position:absolute;
      right:15px;
      bottom:25px;
      z-index:20;
      .btn
      {
        width:50px;
        height:50px;
        position:absolute;
        // border:1px solid;
        font-size:30px;
        line-height:50px;
        text-align:center;
        color:#00000066;
        &:hover {
          color:#ffffff;
        }
      }
    `,
    miniMapContainer: css`
      position: absolute;
      top: 40px;
      right: 20px;
      width: 600px;
      height: 600px;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 100;
    `,
    miniMap: css`
      width: 100%;
      height: calc(100% - 40px);  // 减去按钮的高度
    `,
    miniMapButton: css`
      z-index: 1000;
    `,
    atlasPicRoot: css`
      position: absolute;
      top: 200px;
      left: 20px;
      width: 300px;
      height: 300px;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 100;
      transition: all 0.3s ease;
    `,
    atlasPic: css`
      top: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 40px);  // 减去按钮的高度
      object-fit: cover;
    `,
    atlasLoading: css`
      top: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 40px);  // 减去按钮的高度
      display: flex;
      justify-content: center;
      align-items: center;
    `,
    buttonContainer: {
      display: 'flex',
      justifyContent: 'flex-start',
      gap: '8px',  // 按钮之间的间距
      padding: '8px',  // 给按钮容器添加内边距
      position: 'absolute',  // 将定位添加到容器上
      bottom: 0,  // 固定在底部
      left: 0,  // 从左侧开始
      right: 0,  // 延伸到右侧
      background: 'rgba(255, 255, 255, 0.9)',  // 可选：添加背景色
      borderTop: '1px solid #ddd',  // 可选：添加上边框
    },
    appSceneShowRoot: css`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      z-index: 9999;
      transition: all 0.3s ease;
    `,
    closeButton: css`
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 2000;
    `,
    sceneScaleButton: css`
      position: absolute;
      top: 10px;
      right: 60px;
      z-index: 2000;
    `,
    sceneModeBtns: css`
    position: absolute;
      width: 180px;
      right: 12px;
      top: 12px;
      z-index: 11;
      .ant-segmented {
        /* background-color: #EAEBEA; */
        /* color: #282828 !important; */
        @media screen and (max-width: 450px) {
          height: 28px;
        }
      }
      .ant-segmented-item-label {
        @media screen and (max-width: 450px) {
          height: 28px;
          line-height: 28px !important;
          font-size: 12px !important;
        }
      }
    `
  };
});
