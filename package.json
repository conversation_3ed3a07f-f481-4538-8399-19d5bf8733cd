{"name": "ai-design-plugin", "version": "1.0.138-250804190624-1002ebdd", "i18n": {"project": "LayoutAI", "namespaceCode": "layout_ai_key"}, "main": "index.js", "license": "MIT", "private": true, "scripts": {"i18n:test": "node ./i18nUpdate.mjs --env=test", "i18n:hws": "node ./i18nUpdate.mjs --env=hws", "i18n:pre": "node ./i18nUpdate.mjs --env=pre", "i18n:prod": "node ./i18nUpdate.mjs --env=prod", "i18n-run-py": "python ./i18nUpdate.py", "dev": "emp dev", "hws": "cross-env ENV=hws emp dev", "pre": "cross-env ENV=pre emp dev", "serve": "npm run dev", "dev:debug": "npm run hws:debug", "hws:debug": "cross-env ENV=hws concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "pre:debug": "cross-env ENV=pre concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "prod:debug": "cross-env ENV=prod concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "hws:local3d": "cross-env ENV=hws LOCAL3D=true concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "pre:local3d": "cross-env ENV=pre LOCAL3D=true concurrently \"cross-env DEBUG=1 npm run dev\" \"npm run dev\"", "build-hws-version": "npm run version-update && emp build && npm run deploy", "build-pre-version": "npm run version-update && emp build && npm run deploy", "build-prod-version": "npm run version-update && emp build && npm run deploy", "deploy": "node deploy.js", "build": "npm run version-update && emp build", "build:ts": "emp build --ts", "build:analyze": "emp build --analyze", "version-update": "node version-update.js", "fbuild": "sh build-and-commit.sh", "test:setup": "playwright test -c tests/playwright.config.ts --project=setup", "test": "playwright test --config=tests/playwright.config.ts", "test:headed": "playwright test --config=tests/playwright.config.ts --headed", "test:ui": "playwright test --config=tests/playwright.config.ts --ui", "test:debug": "playwright test --config=tests/playwright.config.ts --debug", "test:update-snapshots": "playwright test --config=tests/playwright.config.ts  --headed --update-snapshots", "test:example": "playwright test --config=tests/example/playwright.example.config.ts", "test:example:headed": "playwright test --config=tests/example/playwright.example.config.ts --headed --timeout=60000", "test:example:ui": "playwright test --config=tests/example/playwright.example.config.ts --ui", "test:example:debug": "playwright test --config=tests/example/playwright.example.config.ts --debug", "test:example:api": "playwright test --config=tests/example/playwright.example.config.ts tests/example/api.spec.ts", "test:example:pom": "playwright test --config=tests/example/playwright.example.config.ts tests/example/searchpage.pom.spec.ts", "test:example:standalone": "playwright test --config=tests/example/playwright.standalone.config.ts  --headed", "test:example:check-browser": "playwright test --config=tests/example/playwright.debug.config.ts"}, "devDependencies": {"@babel/core": "7.23.0", "@babel/helper-plugin-utils": "7.22.5", "@babel/runtime": "7.23.0", "@babel/types": "7.23.0", "@efox/emp": "2.7.1", "@efox/emp-compile-swc": "^1.3.3", "@efox/emp-tsconfig": "1.1.4", "@efox/eslint-config-react-prittier-ts": "1.2.6", "@eslint/js": "^9.18.0", "@playwright/test": "^1.51.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20.14.12", "@types/react": "18.2.55", "@types/react-dom": "18.2.19", "@types/react-router-dom": "5.3.3", "@types/spark-md5": "^3.0.5", "@types/three": "^0.171.0", "browserslist": "4.0.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "eslint": "^9.18.0", "react-app-polyfill": "2.0.0", "ts-loader": "^9.5.1", "typescript": "5.3.3", "typescript-eslint": "^8.20.0", "webpack": "^5.93.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "^2.6.28", "@api/clouddesign": "1.15.48", "@clouddesign/design-designmaterial": "^1.7.8", "@clouddesign/design-framework": "1.0.1", "@clouddesign/royscene_threejs_extend": "0.0.142", "@sd/plugin_svgsocketclient": "3.0.12-0", "@sd/roybase": "^3.0.38-0", "@sd/roysvgapi": "^3.0.24-0", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-react": "^0.4.6", "@svg/antd": "2.6.3", "@svg/antd-basic": "^5.6.8", "@svg/antd-cloud-design": "4.17.0", "@svg/deploy": "^2.1.0", "@svg/lang": "^2.7.1", "@svg/oss-upload": "^2.0.3", "@svg/request": "^0.4.0", "@svg/sensors": "^2.5.1", "@svg/sso-plus": "^1.0.68", "@types/css-modules": "^1.0.5", "axios": "0.27.2", "axios-cookiejar-support": "^5.0.5", "base64-js": "^1.5.1", "classnames": "^2.5.1", "default-passive-events": "^2.0.0", "express": "^4.21.2", "fflate": "^0.8.2", "form-render": "^2.4.5", "http": "0.0.1-security", "i18next": "21.9.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "i18next-icu": "^2.3.0", "intl-messageformat": "^10.7.14", "js-base64": "3.6.0", "js-cookie": "^3.0.5", "lil-gui": "^0.20.0", "lodash": "^4.17.21", "mobx": "6.6.1", "mobx-react": "7.5.2", "mobx-react-lite": "^4.0.7", "node-polyfill-webpack-plugin": "2.0.1", "pptxgenjs": "^3.12.0", "qs": "^6.12.3", "react": "18.2.0", "react-device-detect": "^2.0.1", "react-dom": "18.2.0", "react-draggable": "^4.4.6", "react-i18next": "11.18.4", "react-if": "^4.1.6", "react-router-dom": "6.3.0", "react-transition-group": "^4.4.5", "recharts": "2.9.2", "spark-md5": "^3.0.2", "three": "^0.171.0", "ws": "^8.18.0", "buffer": "^6.0.3", "@layoutai/z_polygon": "^0.0.2", "@layoutai/basic_data": "^1.0.2", "@layoutai/model3d_api": "^1.0.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "last 1 version", "> 1%", "IE 9"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 version", "> 1%", "IE 9"]}, "engines": {"node": ">=16.0.0"}}