import { GUI } from "lil-gui";
import { Color } from "three";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { LightMode } from "@/pages/SdkFrame/MsgCenter/IMsgType";
import { AutoLightingRulerType, LightingRuler } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightingRuler";
import { IFrameTestConfig } from "./IFrameTestConfig";
import { SeriesMode,Series2LightMap } from "@/Apps/LayoutAI/Services/AutoLighting/configs/SeriesLightConfig";
import { SwitchConfig } from "@/Apps/LayoutAI/Scene3D/NodeName";
import { SceneLightMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import {  saveJsonAs } from "@layoutai/z_polygon";
import { message } from '@svg/antd';
import { openFileInput } from "@/Apps/LayoutAI/Utils/file_utils";


export class AutoLightingTest {
    private _gui: GUI;
    private _autoLightingFolder: GUI;

    private _autoLightConfigs: LightingRuler[] = [];
    private _switchMap: { [key: string]: boolean } = {};
    private _MAX_BRIGHTNESS: number = IFrameTestConfig.maxBrightness;

    private _lightMode: LightMode;

    constructor(gui: GUI) {
        this._gui = gui;
        // 要与RoomBasicTest中默认值一致
        this._autoLightConfigs = Series2LightMap[LightMode.Night][this.selectedSeriesType];
    }

    public get config() {
        return {
            autoLightConfigs: this._autoLightConfigs,
            switchMap: this._switchMap,
        }
    }

    public get lightMode() {
        return this._lightMode;        
    }

    public set lightMode(value: LightMode) {
        this._lightMode = value;
    }

    public get selectedSeriesType() {
        const seriesFurnisher = TSeriesFurnisher.instance;
        let series = seriesFurnisher._selected_series;
        return (series?.ruleComment as SeriesMode) || SeriesMode.Default;
    }

    public get selectedSeriesKgId() {
        const seriesFurnisher = TSeriesFurnisher.instance;
        let series = seriesFurnisher._selected_series;
        return series?.kgId || "";
    }

    private addAutoConfigCtr(ruler: LightingRuler) {
        let lightingType: AutoLightingRulerType = ruler.typeId;
        if (!ruler) {
            console.error(`没有找到${lightingType}的配置`);
            return;
        }
        let lightName = ruler.name;
        this._autoLightingFolder.add({ isSwitch: false }, 'isSwitch')
            .name(lightName)
            .onChange((value: boolean) => {
                this._switchMap[`${lightingType}_${lightName}`] = value;
            });

        let tableLightConfig = this._autoLightConfigs.find((rule) => rule.typeId === lightingType && rule.name === lightName);
        this._autoLightingFolder.add({ brightness: tableLightConfig.lighting.intensity }, 'brightness', 0, this._MAX_BRIGHTNESS, 1)
            .name('亮度')
            .onChange((value: number) => {
                tableLightConfig.lighting.intensity = value;
            });

        let tableLightColor = new Color(tableLightConfig.lighting.color);
        this._autoLightingFolder.addColor({ color: [tableLightColor.r, tableLightColor.g, tableLightColor.b] }, 'color')
            .name('颜色')
            .onChange((value: number[]) => {
                if (tableLightConfig) {
                    tableLightColor.setRGB(value[0], value[1], value[2]);
                    tableLightConfig.lighting.color = tableLightColor.getHex();
                }
            });
    }

    public addAutoLightingCtr(folder: GUI, lightMode?: LightMode) {
        this._autoLightingFolder = folder
        this._autoLightingFolder.add({ showDebug: LayoutAI_App.instance.scene3D.aiLightsGroupTest.visible }, 'showDebug')
            .name('显示灯光组')
            .onChange((value: boolean) => {
                SwitchConfig.showNightMode = value;
                let mode = LayoutAI_App.instance.scene3D.isNightMode() ? SceneLightMode.Night : SceneLightMode.Day;
                LayoutAI_App.instance.scene3D.setLightMode(mode);
                LayoutAI_App.instance.scene3D.setLightGroupVisible(value, value, false);
            });
        this._autoLightingFolder.add({ isAllSwitch: false }, 'isAllSwitch')
            .name("全选")
            .onChange((value: boolean) => {
                this._autoLightConfigs.forEach((config) => {
                    this._switchMap[`${config.typeId}_${config.name}`] = value;
                });
                this._autoLightingFolder.controllers.forEach(controller => {
                    if (controller.property === 'isSwitch') {
                        controller.setValue(value);
                        controller.updateDisplay();
                    }
                });
            });

        this._autoLightConfigs.forEach((config) => {
            if (lightMode) {
                // 灯光日夜间切换
                const typeId = config.typeId;
                const isCommonType = typeId < 100;
                const isDayType = typeId > 100 && typeId < 200;
                const isNightType = typeId > 200;
                if ((lightMode === LightMode.Day && (isCommonType || isDayType)) ||
                    (lightMode === LightMode.Night && (isCommonType || isNightType))) {
                    this.addAutoConfigCtr(config);
                }
            } else {
                // 导入配置
                this.addAutoConfigCtr(config);
            }
        })

        this._autoLightingFolder.add({ export: () => {
            try {
                const filename = `AutoLightingConfig_${this.lightMode}_${this.selectedSeriesKgId}.json`;
                saveJsonAs(this._autoLightConfigs, filename);
                message.success('导出灯光配置成功');
            } catch (error) {
                message.error('导出灯光配置失败');
                console.error('导出灯光配置失败', error);
            }
        }}, 'export').name('导出灯光配置');

        this._autoLightingFolder.add({ import: async () => {
            try {
                const fileResult = await openFileInput('.json', 'Text');
                if (fileResult && fileResult.content) {
                    let jsonContent: string = fileResult.content;
                    const data = JSON.parse(jsonContent);
                    this._autoLightConfigs = data;
                    this.clearFolder(this._autoLightingFolder);
                    this.addAutoLightingCtr(this._autoLightingFolder);
                    message.success('导入灯光配置成功');
                }
            } catch (error) {
                message.error('导入灯光配置失败');
                console.error('导入灯光配置失败', error);
            }
        }}, 'import').name('导入灯光配置');
    }

    public changeAutoLightingCtr(lightMode: LightMode) {
        this.lightMode = lightMode;
        console.log("当前套系类型：", this.selectedSeriesType);
        this._autoLightConfigs = Series2LightMap[this.lightMode][this.selectedSeriesType];
        this.clearFolder(this._autoLightingFolder);
        this._switchMap = {};
        this.addAutoLightingCtr(this._autoLightingFolder, this.lightMode);
    }

    private clearFolder(folder: GUI) {
        folder.folders.forEach(subFolder => {
            this.clearFolder(subFolder);
            subFolder.destroy();
        });
        while (folder.controllers.length > 0) {
            folder.controllers[0].destroy();
        }
    }
}