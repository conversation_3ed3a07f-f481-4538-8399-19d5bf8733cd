import { I_DecorationRule } from "../TGraphConfigureInterface";


export var DefaultElectricityRules : I_DecorationRule[]  =[
    {
        decoration_name : "床头插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            tags:["五孔插座","七孔插座","开双五孔"],
            _is_decoration : true,
            fill_color:"#ff0",
            min_z : 750,
            max_z : 850,
            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["床头柜"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '50', 
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '750' // 离地750
    },
    {
        decoration_name : "电视插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            fill_color:"#ff0",
            min_z : 1100,
            max_z : 1200,
            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["电视柜","电视"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '4',
        array_angle_func : '180',
        array_pos_x_func : '-150 + j * 100',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '1100' // 离地750
    },
    {
        decoration_name : "书桌插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            min_z : 1100,
            max_z : 1200,
            fill_color:"#ff0",
            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["书桌"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '2',
        array_angle_func : '180',
        array_pos_x_func : '-t_l/2 + 200 + j * 100',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '1100' // 离地750
    },
    {
        decoration_name : "梳妆台插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            min_z : 1100,
            max_z : 1200,
            fill_color:"#ff0",
            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["梳妆台"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '-t_l/2 + 100 + j * 100',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '1100' // 离地750
    },
    {
        decoration_name : "厨房台面插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            min_z : 1100,
            max_z : 1200,
            fill_color:"#ff0",

            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["炉灶地柜","水槽地柜"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '4',
        array_angle_func : '180',
        array_pos_x_func : '-150 + 100 * j',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '1100' // 离地750
    },
    {
        decoration_name : "星盆下插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            min_z : 350,
            max_z : 450,
            fill_color:"#ff0",

            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["水槽地柜"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '0',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '350' // 离地
    },
    {
        decoration_name : "沙发旁插座",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "强电插座",
            public_category :"强电插座",
            sub_category : "五孔插座",
            _is_decoration : true,
            fill_color:"#ff0",
            min_z : 550,
            max_z : 650,
            params : {
                length : 100,
                depth : 40,
                height : 100
            }
        },
        target_figure_categories : ["直排沙发"],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '2',
        array_angle_func : '180',
        array_pos_x_func : '(t_l/2+200) * (j>0.5?1:-1)',
        array_pos_y_func : '-t_d/2-m_d/2+15',
        adjust_pos_z_func : '550' // 离地
    },
    {
        decoration_name : "进门开关",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "开关",
            public_category :"双联开关",
            sub_category : "双联开关",
            _is_decoration : true,
            fill_color:"#f0f",
            min_z : 1400,
            max_z : 1500,
            params : {
                length : 100,
                depth : 15,
                height : 100
            }
        },
        target_window_realtypes:["Door","SingleDoor","DoubleDoor","SlidingDoor"],
        target_figure_categories:[],
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '(t_l/2+100)',
        array_pos_y_func : '-t_d/2+m_d/2',
        adjust_pos_z_func : '1400' // 离地
    },
    {
        decoration_name : "床头开关",
        decortation_type :"Electricity",
        decoration_figure :  {
            category : "开关",
            public_category :"单联开关",
            sub_category : "单联开关",
            _is_decoration : true,
            fill_color:"#f0f",
            min_z : 750,
            max_z : 850,
            params : {
                length : 100,
                depth : 15,
                height : 100
            }
        },
        target_figure_categories:["床头柜"],
        target_parent_categories:["床"],
        target_parent_distance:1000,
        target_alignment_on_parent:"Right", 
        ruleType : "Array",
        array_x_len : 100,
        array_y_len : 0,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '-50',
        array_pos_y_func : '-t_d/2+m_d/2',
        adjust_pos_z_func : '1400' // 离地
    },
]