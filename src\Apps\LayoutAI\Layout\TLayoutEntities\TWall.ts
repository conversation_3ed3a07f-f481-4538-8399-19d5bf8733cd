import { Mesh, Vector3 } from "three";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { I_SwjLineEdge, I_SwjWall } from "../../AICadData/SwjLayoutData";
import { TPainter } from "../../Drawing/TPainter";
import { MeshName, SwitchConfig, UserDataKey } from "../../Scene3D/NodeName";
import { Vec3toMeta } from "@layoutai/z_polygon";
import { ZEdge } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ZVertex } from "@layoutai/z_polygon";
import { IRoomEntityRealType, IRoomEntityType, IType2UITypeDict, KeyEntity } from "../IRoomInterface";
import { WallGeometryBuilder } from "../../Scene3D/builder/WallGeometryBuilder";
import { I_EntityDrawingState, TBaseEntity } from "./TBaseEntity";
import { MaterialManager, SceneMaterialMode } from "../../Scene3D/MaterialManager";
import { checkIsMobile } from "@/config";

export interface I_WallNeighbor {
    /**
     *  1:表示end, -1:表示start
     */
    connect_endpoint?: number;
    neighbor_connect_endpoint?: number;
    uidN?: number;
    wall: TWall;
}
/**
 *  基础墙
 */
export class TWall extends TBaseEntity implements I_SwjWall {
    boundary: I_SwjLineEdge[];


    _win_rects: ZRect[];

    _wall_type: string;

    _wall_neighbors: I_WallNeighbor[];

    /**
     *  中线墙表示
     */
    _mid_line_edge: ZEdge;



    _vertical_neighbor_walls: TWall[] = [];
    _collinear_neighbor_walls: TWall[] = [];
    _generated_neighbor_walls: TWall[] = [];
    _draw_neighbor_walls: TWall[] = [];   //用来新增绘制的墙

    _delete_walls: TWall[] = [];  // 用来删除的墙
    new_wall: ZRect;

    intersect_rect: ZRect;
    more_intersection: boolean;  // 三个墙有交叉点

    static readonly EntityName: IRoomEntityType = "Wall";
    static AttachedWall = "AttachedWall";
    constructor(data: I_SwjWall) {
        super();
        this.type = "Wall";
        this.title = LayoutAI_App.t("墙体信息");
        this._mid_line_edge = null;
        this._wall_neighbors = [];
        this._win_rects = [];
        this.new_wall = null;
        this.intersect_rect = null;
        this.more_intersection = false;
        this._priority_for_selection = 2.5;
        this.importData(data);
    }

    get thickness() {
        return this._rect.h;
    }

    set thickness(hh: number) {
        this._rect._h = hh;

        if (this && this._win_rects) {
            for (let rect of this._win_rects) {
                if (rect.ex_prop['label'] == "baywindow") continue;
                rect._h = hh;
                rect.updateRect();
            }
        }
        this._rect.updateRect();
    }
    getRealTypeOptions(): { label: string; value: string; }[] {
        let values: IRoomEntityRealType[] = [];
        values = ["Wall", "InnerWall", "OutterWall", "LoadBearingWall"];
        let ans: { label: string, value: string }[] = [];

        for (let val of values) {
            ans.push({ label: LayoutAI_App.t(IType2UITypeDict[val]) || LayoutAI_App.t(val), value: IType2UITypeDict[val] || val });
        }
        return ans;
    }
    static getOrMakeEntityOfCadRect(rect: ZRect): TWall {
        let twall: TWall = rect._attached_elements[KeyEntity] || null;
        if (!twall) {
            twall = new TWall({ boundary: [] });
            twall._rect = rect;

            rect._attached_elements[KeyEntity] = twall;

        }
        twall.update();
        twall.updateDataByRect();
        return twall;
    }
    static RegisterGenerators() {
        TBaseEntity.RegisterPolyEntityGenerator(this.EntityType, this.getOrMakeEntityOfCadRect)
    }
    static makeWallByRect(rect: ZRect) {
        let start_pos = rect.leftEdge.center;
        let end_pos = rect.rightEdge.center;
        let wall = new TWall({
            start_x: start_pos.x,
            start_y: start_pos.y,
            end_x: end_pos.x,
            end_y: end_pos.y,
            thickness: rect.h,
            boundary: null
        });

        return wall;
    }

    static extendWall(wall: TWall, intersect_pos: Vector3, thickness: number = 120) {
        let pp = wall.rect.project(intersect_pos);
        if (pp.x > 0 && (intersect_pos.distanceTo(wall.end_v3) < thickness)) {

            wall.rect.side_extend_to_point(intersect_pos, wall.start_v3);
        }
        if (pp.x < 0 && (intersect_pos.distanceTo(wall.start_v3) < thickness)) {
            wall.rect.side_extend_to_point(intersect_pos, wall.end_v3);
        }
    }

    initWindowRects() {
        this._win_rects = [];
    }
    addAndBindWinRect(win_rect: ZRect) {
        if (!win_rect) return;
        if (Math.abs(win_rect.nor.dot(this.rect.nor)) < 0.99) return;

        if (this.rect.containsPoint(win_rect.back_center, 10)) {
            this._win_rects.push(win_rect);
        }
    }

    importData(data: I_SwjWall) {
        this.uidN = (~~('' + data.uid)) || TBaseEntity.EntityUidNCounter;

        let start_pos = new Vector3(data.start_x || 0, data.start_y || 0, 0);
        let end_pos = new Vector3(data.end_x || 0, data.end_y || 0, 0);

        this._height = data.height || 2800;

        this.boundary = [];
        if (data.boundary && data.boundary.length > 0) {
            let points: Vector3[] = [];
            for (let t_data of data.boundary) {
                this.boundary.push({

                    start: { x: t_data.start.x, y: t_data.start.y, z: 0 },
                    end: { x: t_data.end.x, y: t_data.end.y, z: 0 }
                });
                points.push(new Vector3(t_data.start.x, t_data.start.y, 0));
                points.push(new Vector3(t_data.end.x, t_data.end.y, 0));
            }
            let poly = new ZPolygon();
            poly.initByVertices(points);
            // WPolygon.optmizePoly(poly);

            poly.computeZNor();
            this._rect.copy(ZRect.computeMainRect(poly));


            if (this._rect.w < this._rect.h) {
                this._rect.swapWidthAndHeight();
            }

            if (this._rect.orientation_z_nor.z < 0) {
                this._rect.u_dv = this.rect.dv.negate();
                this._rect.updateRect();
            }

            this._rect._w = Math.ceil(this._rect.w);
            this._rect._h = Math.round(this._rect.h);
            this._rect.updateRect();
        }
        else {

            let t_v = (end_pos.clone().sub(start_pos));
            let length = t_v.length();

            let center = (end_pos.clone().add(start_pos).multiplyScalar(0.5));

            let thickness = data.thickness || data.thick || 240;
            let normal = t_v.clone().normalize().cross(new Vector3(0, 0, 1));

            this._rect.nor = normal;
            this._rect._w = length;
            this._rect._h = thickness;
            this._rect.rect_center = center;

            this._rect.updateRect();
        }

        this.initMidLineEdge();
        this.bindEntity();


    }
    exportData(): I_SwjWall {
        this.updateDataByRect();
        return {
            uid: this.uidN,
            height: this._height,
            thickness: this.thickness,
            start_x: this.start_x,
            start_y: this.start_y,
            end_x: this.end_x,
            end_y: this.end_y,
            boundary: this.boundary
        }
    }

    initMidLineEdge() {
        if (!this._mid_line_edge) {
            this._mid_line_edge = new ZEdge({ pos: this.start_v3 }, { pos: this.end_v3 });
            this._mid_line_edge._nor.copy(this._rect.nor);
        }
        else {
            this._mid_line_edge.v0.pos.copy(this.start_v3);
            this._mid_line_edge.v1.pos.copy(this.end_v3);
            this._mid_line_edge._nor.copy(this.rect.nor);
        }
    }

    updateMesh3D(mode: SceneMaterialMode = "WhiteModel") {
        let geometry = WallGeometryBuilder.build(this);
        if (this._mesh3d) {
            (this._mesh3d as Mesh).geometry = geometry;
        } else {
            this._mesh3d = new Mesh(geometry, MaterialManager.wall_material);
            this._mesh3d.receiveShadow = SwitchConfig.shadowSwitch;
            this._mesh3d.name = MeshName.Wall;
            this._mesh3d.userData[UserDataKey.SkipOutline] = true;
        }

        this._mesh3d.position.copy(this.rect.rect_center);
        this._mesh3d.rotation.set(0, 0, this.rotate_z);

        this._mesh3d.userData[UserDataKey.EntityOfMesh] = this;
        // GeometryBuilder.buildObjectEdgesLine(this._mesh3d);
        return this._mesh3d;
    }

    initWallNeighbors() {
        this._wall_neighbors = [];
    }
    /**
     * 添加邻居墙
     * @param wall 
     */
    checkAddWallNeighbor(wall: TWall) {
        if (!this._mid_line_edge) {
            this.initMidLineEdge();
        }
        if (!wall._mid_line_edge) {
            wall.initMidLineEdge();
        }

        let has_wall = wall._wall_neighbors.find((val) => val.wall === wall);
        if (has_wall) return;


        let vlist0 = [this._mid_line_edge.v0, this._mid_line_edge.v1];
        let vlist1 = [wall._mid_line_edge.v0, wall._mid_line_edge.v1];

        let min_dist = Math.max(this.thickness, wall.thickness);
        let pair: { v0: ZVertex, v1: ZVertex } = null;
        for (let v0 of vlist0) {
            for (let v1 of vlist1) {
                let dist = v0.pos.distanceTo(v1.pos);
                if (dist < min_dist) {
                    min_dist = dist;
                    pair = { v0: v0, v1: v1 };
                }
            }
        }

        if (pair) {
            let id = vlist0.indexOf(pair.v0);
            let n_id = vlist1.indexOf(pair.v1);
            this._wall_neighbors.push({
                connect_endpoint: id == 0 ? -1 : 1,
                neighbor_connect_endpoint: n_id == 0 ? -1 : 1,
                uidN: wall.uidN,
                wall: wall
            })
        }


    }

    checkNeighbor(wall: TWall): "Colinear" | "Verticle" {
        if (!this._mid_line_edge) {
            this.initMidLineEdge();
        }
        if (!wall._mid_line_edge) {
            wall.initMidLineEdge();
        }

        if (wall.rect.checkSameNormal(this.rect.nor)) // 水平
        {
            let p0 = this.rect.project(wall.rect.leftEdge.center);
            let p1 = this.rect.project(wall.rect.rightEdge.center);

            if (Math.abs(p0.y) > (this.rect._h + wall._rect.h) / 2) return null;

            if (p0.x > p1.x) {
                let tmp = p0; p0 = p1; p1 = tmp;
            }

            if (this.rect.w / 2 + 1. - Math.min(Math.abs(p0.x), Math.abs(p1.x)) > 0) {
                return "Colinear";
            }
            else {
                return null;
            }
        }
        else if (wall.rect.checkSameNormal(this.rect.dv)) // 垂直
        {
            let p0 = this.rect.project(wall.rect.leftEdge.center);
            let p1 = this.rect.project(wall.rect.rightEdge.center);

            if (Math.abs(p0.x) > (this.rect.w / 2) + (wall.rect._h / 2) + 1) return null;

            if (this.rect.h / 2 + 1. - Math.min(Math.abs(p0.y), Math.abs(p1.y)) > 0) {
                return "Verticle";
            }
            else {
                return null;
            }
        }

    }

    initNeighborsWalls(all_walls: TWall[]) {
        if (this.recordInWallWindowsData) {
            this.recordInWallWindowsData();
        }

        this.recordRectData(); // 记录初始数据

        this._vertical_neighbor_walls = [];
        this._collinear_neighbor_walls = [];
        this._generated_neighbor_walls = [];
        this._draw_neighbor_walls = [];

        let may_updated_walls: TWall[] = [];
        for (let wall of all_walls) {
            if (wall === this) continue;

            let neighbor_type = this.checkNeighbor(wall);
            if (neighbor_type === "Verticle") {
                this._vertical_neighbor_walls.push(wall);
            }
            else if (neighbor_type === "Colinear") {
                this._collinear_neighbor_walls.push(wall);
            }

        }

        may_updated_walls.push(...this._vertical_neighbor_walls, ...this._collinear_neighbor_walls);
        may_updated_walls.forEach(wall => {
            wall.recordInWallWindowsData();
            wall.recordRectData();
        })
    }

    cleanNeighborWalls() {
        this._collinear_neighbor_walls && delete this._collinear_neighbor_walls;
        this._generated_neighbor_walls && delete this._generated_neighbor_walls;
        this._vertical_neighbor_walls && delete this._vertical_neighbor_walls;
        this._draw_neighbor_walls && delete this._draw_neighbor_walls;
    }
    updateNeighborWallsAfterMoving() {
        let wall = this;
        let t_rect = wall.rect;
        let record_t_rect = wall.getRecordRect();
        let draw_wall_count = 0;
        this._draw_neighbor_walls = this._draw_neighbor_walls || [];
        for (let v_wall of this._vertical_neighbor_walls) {
            // 如果也是三堵墙有交叉点，不添加
            if (v_wall.more_intersection) continue;
            let record_v_rect = v_wall.getRecordRect();

            let r_pp0 = record_t_rect.project(record_v_rect.leftEdge.center);
            let r_pp1 = record_t_rect.project(record_v_rect.rightEdge.center);
            let r_pp = Math.abs(r_pp0.y) < Math.abs(r_pp1.y) ? r_pp0 : r_pp1;
            let fix_pp = Math.abs(r_pp0.y) < Math.abs(r_pp1.y) ? r_pp1 : r_pp0;
            let pp = t_rect.project(record_v_rect.rect_center);
            let pos = t_rect.unproject({ x: pp.x, y: r_pp.y });

            let fix_pos = record_t_rect.unproject({ x: fix_pp.x, y: fix_pp.y });
            let new_v_rect = ZRect.SideExtendToPoint(record_v_rect, pos, fix_pos);

            // v_wall.rect.copy(new_v_rect);
            v_wall.new_wall = new_v_rect.clone();
            v_wall.update();

            v_wall.updateInWallWindows();

            if (!this._draw_neighbor_walls[draw_wall_count]) {
                this._draw_neighbor_walls.push(TWall.makeWallByRect(new_v_rect));
            }
            else {
                this._draw_neighbor_walls[draw_wall_count].rect.copy(new_v_rect);
            }
            this._draw_neighbor_walls[draw_wall_count].rect._attached_elements[TWall.AttachedWall] = v_wall;
            draw_wall_count++;
        }
        this._draw_neighbor_walls.length = draw_wall_count;


        this._generated_neighbor_walls = this._generated_neighbor_walls || [];
        let generated_thickness = 120;

        let new_wall_count = 0;


        for (let h_wall of this._collinear_neighbor_walls) {
            let h_rect = h_wall.rect;
            let pp = t_rect.project(h_rect.rect_center);
            // 共线和垂直的墙，可能垂直的墙优先添加，不给共线墙添加
            if (Math.abs(pp.x) < t_rect._w / 2 + h_rect.w / 2 - 1) {
                continue;
            }
            pp.x = (t_rect._w / 2) * (pp.x > 0 ? 1 : -1);

            let pos0 = t_rect.unproject({ x: pp.x, y: pp.y });
            let pos1 = t_rect.unproject({ x: pp.x, y: 0 });
            let ww = (pos0.clone().sub(pos1)).length();
            let new_rect = new ZRect(ww, h_wall.rect._h);
            new_rect.nor = t_rect.dv;
            new_rect.rect_center = (pos0.clone().add(pos1)).multiplyScalar(0.5);
            if (new_rect._w > new_rect._h) {
                if (!this._generated_neighbor_walls[new_wall_count]) {
                    this._generated_neighbor_walls.push(TWall.makeWallByRect(new_rect));
                }
                else {
                    this._generated_neighbor_walls[new_wall_count].rect.copy(new_rect);
                }
                new_wall_count++;
            }
        }
        this._generated_neighbor_walls.length = new_wall_count;
    }

    getIntersectionPos(wall: TWall, expand: number = 0.1) {
        for (let vertical of this._vertical_neighbor_walls) {
            let mid_line1 = vertical._mid_line_edge._deep_clone();
            mid_line1.expandLength(1);
            mid_line1._nor.copy(vertical.rect.nor);
            let mid_line2 = wall._mid_line_edge._deep_clone();
            mid_line2.expandLength(1);
            mid_line2._nor.copy(wall.rect.nor);
            let mid_intersection = mid_line1.getIntersection(mid_line2);
            if (mid_intersection) {
                return mid_intersection;
            }
        }
    }
    /**
     * 新增墙后回到共线处，_w 小于this.wall_rect._h 垂直的墙，需要清理
     */
    updateNewWall(all_walls: TWall[] = null) {
        if (!all_walls) return;

        // 1、更新邻居墙，共线墙拖出来后，共线墙需要extend一半_h的距离
        for (let g_wall of this._generated_neighbor_walls) {
            for (let c_wall of this._collinear_neighbor_walls) {
                if (c_wall) {
                    let pp = c_wall.rect.project(g_wall.rect.rect_center);

                    let expand_pos = c_wall.rect.unproject({ x: pp.x + (g_wall.rect._h / 2) * (pp.x > 0 ? 1 : -1), y: 0 });
                    TWall.extendWall(c_wall, expand_pos, c_wall.rect._h);

                    let t_wall = this;
                    let _pp = t_wall.rect.project(g_wall.rect.rect_center);

                    let _expand_pos = t_wall.rect.unproject({ x: _pp.x + (g_wall.rect._h / 2) * (_pp.x > 0 ? 1 : -1), y: 0 });
                    TWall.extendWall(t_wall, _expand_pos, c_wall.rect._h);
                }
            }
        }

        for (let wall of all_walls) {
            if (wall === this) continue;
            if (wall.rect.checkSameNormal(this.rect.nor)) // 水平
            {
                let p0 = wall.rect.project(this.rect.leftEdge.center);
                let p1 = wall.rect.project(this.rect.rightEdge.center);

                if (Math.abs(p0.y) > (this.rect._h + wall._rect.h) / 2) continue;
                if ((p0.x < wall.rect.w / 2 && p0.x > -wall.rect.w / 2) && (p1.x < wall.rect.w / 2 && p1.x > -wall.rect.w / 2)) {
                    this._delete_walls.push(this);
                }
            }
        }
        if (this._vertical_neighbor_walls && this._vertical_neighbor_walls.length > 0) {
            for (let i = this._vertical_neighbor_walls.length - 1; i >= 0; i--) {
                let wall = this._vertical_neighbor_walls[i];
                if (!wall.new_wall) continue;
                wall.rect.copy(wall.new_wall);
                if (wall.new_wall._w < this.rect.h) {
                    // 回到共线处之后，得清除垂直的墙
                    wall.new_wall = null;
                    this._vertical_neighbor_walls.splice(i, 1);
                }
            }
        }
        if (this._generated_neighbor_walls && this._generated_neighbor_walls.length > 0) {
            for (let wall of this._generated_neighbor_walls) {
                if (wall._rect._w < this.rect.h) {
                    this._generated_neighbor_walls.splice(this._generated_neighbor_walls.indexOf(wall), 1);
                }
            }
        }

        // // 两堵墙重叠之后，需要重新计算两堵墙的交叉点
        // for(let wall of all_walls)
        // {
        //     if(wall === this) continue;
        //     let neighbor_type = this.checkNeighbor(wall);
        //     if(neighbor_type === "Colinear"){
        //         let intersect_rect = wall.rect.intersect_rect(this.rect);
        //         if(intersect_rect)
        //         {
        //             let expand_pos = intersect_rect.rect_center;
        //             TWall.extendWall(wall, expand_pos, intersect_rect._w);
        //             TWall.extendWall(this, expand_pos, intersect_rect._w);
        //         }
        //     }
        // }
    }

    update(): void {
        super.update();
        this.initMidLineEdge();
    }

    recordInWallWindowsData() {
        for (let rect of this._win_rects) {
            let pos = this.rect.project(rect.rect_center);
            rect._attached_elements["InWallPos2d"] = pos;

            rect._attached_elements["InWallNorFlag"] = rect.nor.dot(this.rect.nor);
        }
    }


    updateInWallWindows() {
        if (!this._win_rects) return;
        for (let rect of this._win_rects) {

            let pp = this.rect.project(rect.rect_center); //rect._attached_elements["InWallPos2d"];

            if (!pp) {
                continue;
                // this.recordInWallWindowsData();
            }

            let pos = null;
            if (rect.ex_prop['label'] !== "baywindow") {
                rect._h = this.thickness;
                pos = this.rect.unproject({ x: pp.x, y: 0 });
            } else {
                pos = this.rect.unproject({ x: pp.x, y: rect._h / 2 - this.thickness / 2 });
            }

            let flag = rect._attached_elements["InWallNorFlag"] || 1;

            rect.nor = this.rect.nor.clone().multiplyScalar(flag);
            rect.rect_center = pos;
        }

    }

    updateWinDoorLength() {
        // 门窗大小跟随修改大小
        if (this._vertical_neighbor_walls) {
            for (let wall of this._vertical_neighbor_walls) {
                for (let rect of wall._win_rects) {
                    if (this.rect.checkIntersection(rect)) {
                        rect._w = wall.rect.w > rect.w ? rect.w : wall.rect.w;
                        rect.rect_center = wall.rect.rect_center.clone();
                        rect.updateRect();
                    }
                }
            }
        }

    }

    // 短墙合并
    mergeWall(all_walls: TWall[]) {
        let _collinear_neighbor_walls = [];
        for (let wall of all_walls) {
            if (wall === this) continue;
            if (wall.rect.checkSameNormal(this.rect.nor)) // 水平
            {
                let p0 = wall.rect.project(this.rect.leftEdge.center);
                let p1 = wall.rect.project(this.rect.rightEdge.center);

                if (Math.abs(p0.y) > (this.rect._h + wall._rect.h) / 2) continue;

                // if(p0.x > p1.x) {
                //     let tmp = p0; p0 = p1; p1 = tmp;
                // }   
                if ((p0.x < wall.rect.w / 2 && p0.x > -wall.rect.w / 2) && (p1.x < wall.rect.w / 2 && p1.x > -wall.rect.w / 2)) {
                    return true;
                }
            }
        }


    }

    updateDataByRect() {
        this.boundary = [];
        for (let edge of this._rect.edges) {
            this.boundary.push({
                start: Vec3toMeta(edge.v0.pos),
                end: Vec3toMeta(edge.v1.pos)
            })
        }
        super.updateDataByRect();
    }

    initProperties(): void {
        super.initProperties();
        let isMobile = checkIsMobile();
        this._ui_properties["length"] = {
            name: LayoutAI_App.t("长度"),
            widget: "LabelItem",
            type: isMobile ? "string" : "number",
            min: isMobile ? 0 : 100,
            max: 20000,
            disabled: isMobile,
            defaultValue:Math.round(this.length),
            props: {
                type: isMobile ? "input" : "number",
                suffix: "mm"
            }
        }
        this._ui_properties["thickness"] = {
            name: LayoutAI_App.t("厚度"),
            widget: "LabelItem",
            type: isMobile ? "string" : "number",
            min: isMobile ? 0 : 100,
            max: 20000,
            disabled: isMobile,
            defaultValue: '' + Math.round(this.thickness),
            props: {
                type: isMobile ? "input" : "number",
                suffix: "mm"
            }
        }

        this._ui_props_keys = ["ui_type", "ui_realType", "length", "thickness"];
        if (LayoutAI_App.IsDebug) {
            this._ui_props_keys.unshift("uidN");
        }
        this._bindPropertiesOnChange();

    }


    drawEntity(painter: TPainter, options?: I_EntityDrawingState): void {

        let is_draw_figure = options.is_draw_figure;
        // options.is_draw_figure = false;
        // super.drawEntity(painter,options);
        if (this.intersect_rect) {
            painter.fillStyle = "red";
            painter.fillPolygon(this.intersect_rect, 1.);
            painter.strokePolygons([this.intersect_rect]);
        }


        if (options.is_draw_figure) {
            painter.strokeStyle = "#282828";
            if (this.is_selected) {
                painter.fillStyle = "#3D9EFF";
                painter.fillPolygon(this._rect, 1.);
                painter.strokePolygons([this.rect]);
                for (let rect of this._win_rects) {
                    let entity = TBaseEntity.getEntityOfRect(rect);
                    if (entity) {
                        entity.drawEntity(painter, options);
                    }
                }


            }
            else if (this.is_hovered) {
                painter.fillStyle = "#B8E2FF";
                painter.strokeStyle = "#B8E2FF";

                painter.fillPolygon(this._rect, 1.);
                painter.strokePolygons([this.rect]);


            }
            else {
                painter.fillStyle = (this.realType === "InnerWall" || this.realType === "Wall" || !this.realType) ? "#5C5C5C" : "#282828";
                painter.strokeStyle = "#282828";


                // painter.fillStyle = this.realType === "InnerWall"?"#A2A2A5":"#282828";
                // painter.strokeStyle =  this.realType === "InnerWall"?"#282828":"#282828";

                painter.fillPolygon(this._rect, 1.);
                // painter.strokePolygons([this.rect]);



            }



        }
    }
    set is_selected(t: boolean) {
        this._is_selected = t;

        if (this._win_rects) {
            for (let win of this._win_rects) {
                let entity = TBaseEntity.getEntityOfRect(win);
                if (entity) {
                    entity.is_selected = t;
                }
            }
        }

    }

    get is_selected() {
        return this._is_selected;
    }


    adjustRectByNeighbors() {
        let end_wall_has_same_dir_wall = false;
        let end_wall_corner_wall: I_WallNeighbor = null;
        let start_wall_has_same_dir_wall = false;
        let start_wall_corner_wall: I_WallNeighbor = null;
        for (let neighbor of this._wall_neighbors) {
            let n_wall = neighbor.wall;

            if (this.rect.checkSameDirection(n_wall.rect.dv, true)) {
                if (neighbor.connect_endpoint == 1) {
                    end_wall_has_same_dir_wall = true;
                }
                else {
                    start_wall_has_same_dir_wall = true;
                }
            }
            else {
                if (neighbor.connect_endpoint == 1) {
                    end_wall_corner_wall = neighbor;
                }
                else {
                    start_wall_corner_wall = neighbor;
                }
            }
        }
        // 当有转角墙 同时 没有同直线墙时，才会做延申
        let rect = this.rect;
        let ll = -rect.w / 2;
        let rr = rect.w / 2;
        if (end_wall_corner_wall && !end_wall_has_same_dir_wall) {
            let t_wall_rect = end_wall_corner_wall.wall.rect;
            let t_wall_edge = end_wall_corner_wall.neighbor_connect_endpoint == 1 ? t_wall_rect.rightEdge : t_wall_rect.leftEdge

            let r0 = rect.project(t_wall_edge.v0.pos).x;
            let r1 = rect.project(t_wall_edge.v1.pos).x;
            rr = Math.max(r0, r1, rr);

        }

        if (start_wall_corner_wall && !start_wall_has_same_dir_wall) {
            let t_wall_rect = start_wall_corner_wall.wall.rect;
            let t_wall_edge = start_wall_corner_wall.neighbor_connect_endpoint == 1 ? t_wall_rect.rightEdge : t_wall_rect.leftEdge

            let l0 = rect.project(t_wall_edge.v0.pos).x;
            let l1 = rect.project(t_wall_edge.v1.pos).x;
            ll = Math.min(l0, l1, ll);
        }

        let ww = rr - ll;
        let t_x = (rr + ll) / 2;
        let t_pos = rect.unproject({ x: t_x, y: 0 });
        rect._w = ww;
        rect.rect_center = t_pos;




    }

}


/**
 *  多边形墙, 以后再补
 */
export class TPolygonWall extends TWall {

}