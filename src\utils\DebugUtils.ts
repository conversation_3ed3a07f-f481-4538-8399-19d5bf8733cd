import GUI from "lil-gui";
import * as THREE from "three";
import { Box3, BoxGeometry, DirectionalLight, Group, Light, Material, Mesh, MeshBasicMaterial, MeshStandardMaterial, Object3D, PerspectiveCamera, Scene, ToneMapping, Vector3, Vector4, WebG<PERSON>enderer } from "three";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { Model3dApi } from "@/Apps/LayoutAI/Api/Model3dApi";
import { I_DesignMaterialInfo, isGroupDesignMaterialInfo } from "@/Apps/LayoutAI/Layout/IMaterialInterface";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { GeometryBuilder } from "@/Apps/LayoutAI/Scene3D/builder/GeometryBuilder";
import { OutlinePostProcess } from "@/Apps/LayoutAI/Scene3D/builder/OutlinePostProcess";
import { LightManager } from "@/Apps/LayoutAI/Scene3D/LightManager";
import { UserDataKey } from "@/Apps/LayoutAI/Scene3D/NodeName";
import { FigureTopViewer } from "@/Apps/LayoutAI/Scene3D/process/FigureTopViewer";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { MaterialService } from "@/Apps/LayoutAI/Services/MaterialMatching/MaterialService";
import { RenderFlag } from "@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType";
import { ServerRenderService } from "@/Apps/LayoutAI/Services/ServerRender/ServerRenderService";
import { RequestDataCenter } from "@/Apps/LayoutAI/Utils/compressV2_utils";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IFrameTest } from "@/test/IFrameTest";
import { CeilingConfigTest } from "@/test/CeilingConfigTest";

import { DebugTopView } from "./DebugTopView";
import { TMaterialMatcher } from "@/Apps/LayoutAI/Services/MaterialMatching/TMaterialMatcher";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { MatchingPostProcesser } from "@/Apps/LayoutAI/Services/MaterialMatching/MatchingPostProcesser";
import { PanoramaGenerator } from "@/Apps/LayoutAI/Scene3D/PanoramaGenerator";
import { AutoLayoutTest } from "@/test/AutoLayoutTest";


/**
* @description 调试工具栏
* <AUTHOR>
* @date 2025-02-13
* @lastEditTime 2025-02-13 18:26:29
* @lastEditors xuld
*/
export class DebugUtils {
    private static _gui: GUI;
    private static _debugObj: TFigureElement;
    private static _debounceTimer: NodeJS.Timeout;
    private static _debounceDelay = 2 * 1000;

    public static IsShowRenderInfo: boolean = false;
    private static _showClickObj: boolean = false;

    public static showRenderInfo(renderer: WebGLRenderer, fps: number) {

        if (!DebugUtils.IsShowRenderInfo) {
            let info_div = document.getElementById('scene3d-render-info');
            if (info_div) {
                document.body.removeChild(info_div);
            }
            return;
        }
        // 获取或创建信息显示 div
        let info_div = document.getElementById('scene3d-render-info');
        if (!info_div) {
            info_div = document.createElement('div');
            info_div.id = 'scene3d-render-info';
            info_div.style.position = 'fixed';
            info_div.style.left = '10px';
            info_div.style.bottom = '10px';
            info_div.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            info_div.style.color = 'white';
            info_div.style.padding = '10px';
            info_div.style.borderRadius = '5px';
            info_div.style.fontFamily = 'monospace';
            info_div.style.fontSize = '12px';
            info_div.style.zIndex = '10001';
            document.body.appendChild(info_div);
        }

        if (!renderer) return;
        // 获取渲染信息
        const info = renderer.info.render;

        // 更新显示内容
        info_div.innerHTML = `
            FPS: ${fps}<br>
            Triangles: ${info.triangles.toLocaleString()}<br>
            Lines: ${info.lines.toLocaleString()}<br>
            Points: ${info.points.toLocaleString()}<br>
            Calls: ${info.calls.toLocaleString()}<br>
            Frame: ${info.frame.toLocaleString()}
        `;
    }

    public static showObjectInfo(obj: Object3D) {
        (globalThis as any).debugObj = obj;
        if (this._gui) {
            this._gui.destroy();
            this._gui = null;
        }

        if (!obj) {
            return;
        }

        this._gui = new GUI();
        this._gui.title("节点信息");

        // 递归添加所有子节点到 GUI
        function addToGui(object: Object3D, folder: GUI) {
            // 为当前对象添加 visible 控制
            folder.add(object, 'visible')
                // 显示 id 和 name，可以通过id用 getObjectById 获取到object
                .name(`(${object.id}) ${object.name || "-"}`)
                .onChange(() => {
                    // 当 visible 改变时触发重新渲染
                    object.traverseVisible((child) => {
                        child.visible = object.visible;
                    });
                });

            // 如果有子节点，创建子文件夹并递归添加
            if (object.children.length > 0) {
                const childFolder = folder.addFolder(object.name || '--');
                object.children.forEach(child => {
                    addToGui(child, childFolder);
                });
            }
        }

        addToGui(obj, this._gui);

        this._gui.open();
    }

    public static showClickObj(enable: boolean) {
        this._showClickObj = enable;

        if (enable) {
            LayoutAI_App.on_M(EventName.FigureElementSelected, "DebugUtils", (fe: TFigureElement): void => {
                this.showObjectInfo(fe?._solid_mesh3D);
                if (fe) {
                    this._debugObj = fe;
                }
            });
        } else {
            if (this._gui) {
                this._gui.destroy();
                this._gui = null;
            }
            LayoutAI_App.off_M(EventName.FigureElementSelected, "DebugUtils");
        }
    }

    public static getObjectById(id: number) {
        return this._debugObj?._solid_mesh3D?.getObjectById(id);
    }

    public static showLightInfo(lightArray: Light[]) {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("灯光信息");

        lightArray.forEach((light, index) => {
            // 为每个灯光创建一个文件夹
            const lightFolder = this._gui.addFolder(`灯光 ${index + 1} (${light.type})`);

            // 添加颜色控制
            lightFolder.addColor(light, 'color')
                .name("颜色")
                .onChange(() => {
                    light.color.convertSRGBToLinear();
                    this.updateMaterials(light);
                });

            // 添加强度控制
            lightFolder.add(light, 'intensity', 0, 10)
                .name("强度")
                .onChange(() => {
                    this.updateMaterials(light);
                });

            // 如果是平行光，添加位置和朝向控制
            if (light instanceof DirectionalLight) {
                const posFolder = lightFolder.addFolder("位置");
                posFolder.add(light.position, 'x', -10000, 10000).name('X');
                posFolder.add(light.position, 'y', -10000, 10000).name('Y');
                posFolder.add(light.position, 'z', -10000, 10000).name('Z');

                // 添加目标点控制
                const targetFolder = lightFolder.addFolder("朝向目标点");
                const target = light.target;
                targetFolder.add(target.position, 'x', -10000, 10000)
                    .name('X')
                    .onChange(() => light.target.updateMatrixWorld());
                targetFolder.add(target.position, 'y', -10000, 10000)
                    .name('Y')
                    .onChange(() => light.target.updateMatrixWorld());
                targetFolder.add(target.position, 'z', -10000, 10000)
                    .name('Z')
                    .onChange(() => light.target.updateMatrixWorld());
            }

            lightFolder.open();
        });
    }


    // 更新材质的函数
    private static updateMaterials(light: Light) {
        if (light.parent) {
            light.parent.traverse((obj) => {
                if (obj instanceof Mesh && obj.material) {
                    obj.material.needsUpdate = true;
                }
            });
        }
    }

    public static showRenderToneInfo(scene: Scene, renderer: WebGLRenderer) {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("渲染色调信息");

        // 添加色调映射控制
        const toneMapping = {
            'NoToneMapping': 0,
            'LinearToneMapping': 1,
            'ReinhardToneMapping': 2,
            'CineonToneMapping': 3,
            'ACESFilmicToneMapping': 4,
            'CustomToneMapping': 5,
            'AgXToneMapping': 6,
            'NeutralToneMapping': 7
        };

        this._gui.add(renderer, 'toneMapping', toneMapping)
            .name('色调映射')
            .onChange(() => {
                renderer.toneMapping = Number(renderer.toneMapping) as ToneMapping;
                // 更新所有材质
                scene.traverse((obj: Object3D) => {
                    if (obj instanceof Mesh && obj.material) {
                        obj.material.needsUpdate = true;
                    }
                });
            });

        this._gui.add(renderer, 'toneMappingExposure', 0, 5)
            .name('曝光度')
            .onChange(() => {
                // 更新所有材质
                scene.traverse((obj: Object3D) => {
                    if (obj instanceof Mesh && obj.material) {
                        obj.material.needsUpdate = true;
                    }
                });
            });
    }

    public static showMeshBorder(isShow: boolean) {
        // 遍历场景中所有Mesh，如果有边框，则显示边框
        let rootNode = (LayoutAI_App.instance.scene3D as Scene3D).rootNode;
        GeometryBuilder.isShowEdges = isShow;
        rootNode.traverse((obj: Object3D) => {
            if (obj instanceof Mesh) {
                let l_mesh = obj.userData[UserDataKey.BorderLine];
                if (l_mesh) {
                    l_mesh.visible = isShow;
                }
            }
        });
    }

    public static clearMaterialTexture() {
        let rootNode = LayoutAI_App.instance.scene3D.rootNode;
        rootNode.traverse((obj: Object3D) => {
            if (obj instanceof Mesh) {
                let material = obj.material as Material;
                if (material) {
                    if ((material as any).color) {
                        (material as any).color.set(0xffffff);
                    }
                    if ((material as any).map) {
                        (material as any).map = null;
                    }
                    material.needsUpdate = true;
                }
            }
        });
    }
    public static hideDebugPanel() {
        if (this._gui) {
            this._gui.destroy();
            delete this._gui;
            this._gui = null;
        }
    }

    public static switchDebugPanel() {
        if (this._gui) {
            this.hideDebugPanel();
        }
        else {
            this.showDebugPanel();
        }
    }

    public static showModelDebug() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("模型调试面板");

        let info = {
            mid: "",
            x: 0,
            y: 0,
            z: 0,
            isWhite: false
        }

        this._gui.add(info, 'mid').name('模型ID');
        this._gui.add(info, 'x').name('X');
        this._gui.add(info, 'y').name('Y');
        this._gui.add(info, 'z').name('Z');
        this._gui.add(info, 'isWhite').name('是否白膜');
        this._gui.add({
            addModelOnPos: () => {
                this.addModelOnPos(info.mid, info.x, info.y, info.z, info.isWhite);
            }
        }, 'addModelOnPos').name('添加模型');

        this._gui.add({
            closePanel: () => {
                this.hideDebugPanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static showDebugPanel() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("白膜调试面板");

        const s3d = LayoutAI_App.instance.scene3D;
        const outlinePostProcess = s3d.outlinePostProcessing as OutlinePostProcess;
        const enabled = outlinePostProcess.isEnabled;
        this._gui.add({ showBorder: enabled }, 'showBorder')
            .name('显示边框')
            .onChange((value: boolean) => {
                if (value) {
                    outlinePostProcess.makeOutlineDirty();
                }
                outlinePostProcess.setDefaultEnabled(value);
                outlinePostProcess.setEnabled(value);
            });

        this._gui.add({ enableDiagonalSampling: outlinePostProcess.getEnableDiagonalSampling() }, 'enableDiagonalSampling')
            .name('启用对角线采样')
            .onChange((value: boolean) => {
                outlinePostProcess.setEnableDiagonalSampling(value);
            });

        this._gui.add({ lowIDConfig: outlinePostProcess.getLowIDConfig() }, 'lowIDConfig', 0, 1, 0.001)
            .name('边缘配置')
            .onChange((value: number) => {
                outlinePostProcess.setLowIDConfig(value);
            });

        this._gui.add({ lowNormalConfig: outlinePostProcess.getLowNormalConfig() }, 'lowNormalConfig', 0, 1, 0.001)
            .name('法线配置')
            .onChange((value: number) => {
                outlinePostProcess.setLowNormalConfig(value);
            });

        this._gui.add({ intensityConfig: outlinePostProcess.getIntensityConfig() }, 'intensityConfig', 0, 1, 0.001)
            .name('强度配置')
            .onChange((value: number) => {
                outlinePostProcess.setIntensityConfig(value);
            });

        let outlineColor: Vector4 = outlinePostProcess.getOutlineColor();
        this._gui.addColor({ outlineColor: [outlineColor.x, outlineColor.y, outlineColor.z] }, 'outlineColor')
            .name('边框颜色')
            .onChange((value: number[]) => {
                outlinePostProcess.setOutlineColor(value[0], value[1], value[2]);
            });

        this._gui.add({ ambientIntensity: LightManager.getDayAmbientLight().intensity }, 'ambientIntensity', 0, 10, 0.01)
            .name('环境光强度')
            .onChange((value: number) => {
                LightManager.getDayAmbientLight().intensity = value;
            });

        this._gui.addColor({ ambientColor: [LightManager.getDayAmbientLight().color.r, LightManager.getDayAmbientLight().color.b, LightManager.getDayAmbientLight().color.b] }, 'ambientColor')
            .name('环境光颜色')
            .onChange((value: number[]) => {
                LightManager.getDayAmbientLight().color.setRGB(value[0], value[1], value[2]);
            });

        this._gui.add({ sunlightIntensity: LightManager.sunlight.intensity }, 'sunlightIntensity', 0, 10, 0.01)
            .name('阳光强度')
            .onChange((value: number) => {
                LightManager.sunlight.intensity = value;
            });

        this._gui.addColor({ sunlightColor: [LightManager.sunlight.color.r, LightManager.sunlight.color.g, LightManager.sunlight.color.b] }, 'sunlightColor')
            .name('阳光颜色')
            .onChange((value: number[]) => {
                LightManager.sunlight.color.setRGB(value[0], value[1], value[2]);
            });

        this._gui.add({ sublightIntensity: LightManager.subSunLight.intensity }, 'sublightIntensity', 0, 10, 0.01)
            .name('辅光强度')
            .onChange((value: number) => {
                LightManager.subSunLight.intensity = value;
            });
        this._gui.addColor({ whiteModelColor: [1, 1, 1] }, 'whiteModelColor')
            .name('白膜颜色')
            .onChange((value: number[]) => {
                let r = value[0];
                let g = value[1];
                let b = value[2];
                let rootNode = LayoutAI_App.instance.scene3D.rootNode as Object3D;
                rootNode.traverse(object => {
                    if ((object as Mesh).isMesh) {
                        let mesh = object as Mesh;
                        if (mesh.userData[UserDataKey.key_white_material]) {
                            let material = mesh.userData[UserDataKey.key_white_material] as MeshStandardMaterial;
                            material.color.setRGB(r, g, b);
                        }
                    }
                });
            });

        const controls = LayoutAI_App.instance.scene3D.active_controls as any;
        this._gui.add({ fov: controls.camera.fov }, 'fov')
            .name('FOV')
            .onChange((value: number) => {
                const camera = controls.camera as PerspectiveCamera;
                camera.fov = value;
                camera.updateProjectionMatrix();
            });

        this._gui.add({
            exportTopView: () => {
                let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
                let figure = container._furniture_entities[0]?._figure_element;
                if (figure) {
                    let img = this.getTopViewImage();
                    img.style.width = Math.max(Math.round(figure.rect.w / 4), 1) + "px";
                    img.style.height = Math.max(Math.round(figure.rect.h / 4), 1) + "px";
                    img.src = FigureTopViewer.instance.renderFigureTopView(figure, true);
                }
            }
        }, 'exportTopView').name('图元');

        this._gui.add({
            closePanel: () => {
                let img = document.getElementById('figure-top-view') as HTMLImageElement;
                if (img) {
                    document.body.removeChild(img);
                }
                this.hideDebugPanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static getTopViewImage() {
        let img = document.getElementById('figure-top-view') as HTMLImageElement;
        if (!img) {
            img = document.createElement("img");
            img.id = 'figure-top-view';
            img.style.position = "fixed";
            img.style.top = "50px";
            img.style.left = "50px";
            img.style.zIndex = "1000";
            img.style.width = "400px";
            img.style.height = "400px";
            document.body.appendChild(img);
        }
        return img;
    }

    public static showMatchingConfig() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("素材匹配");

        this._gui.add({ autoAddDecorations: TMaterialMatcher.instance.enableAddDecorations }, 'autoAddDecorations')
            .name('自动添加饰品')
            .onChange((value: boolean) => {
                TMaterialMatcher.instance.enableAddDecorations = value;
            });

        this._gui.add({ autoResizeProductMaterial: TMaterialMatcher.instance.autoResizeProductMaterial }, 'autoResizeProductMaterial')
        .name('自动拉伸成品素材')
        .onChange((value: boolean) => {
            TMaterialMatcher.instance.autoResizeProductMaterial = value;
        });

        this._gui.add({ disable3dPreviewMaterialFurnish: TSeriesFurnisher.instance._enable_3dpreview_material_furnish == false }, 'disable3dPreviewMaterialFurnish')
            .name('禁用白膜套系')
            .onChange((value: boolean) => {
                if (value) {
                    TSeriesFurnisher.instance.disable3dPreviewMaterialFurnish();
                } else {
                    TSeriesFurnisher.instance.enable3dPreviewMaterialFurnish();
                }
            });

        this._gui.add({ hideSimpleMesh: !TFigureElement.SimpleMesh3DVisible }, 'hideSimpleMesh')
            .name('隐藏图元简易Box')
            .onChange((value: boolean) => {
                TFigureElement.SimpleMesh3DVisible = !value;
            });

        this._gui.add({ forceSaveCabinetStyle: MatchingPostProcesser.forceSaveCabinetStyle }, 'forceSaveCabinetStyle')
            .name('强制保存风格刷数据')
            .onChange((value: boolean) => {
                MatchingPostProcesser.forceSaveCabinetStyle = value;
            });

        this._gui.add({
            closePanel: () => {
                this.hideDebugPanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static showCabinetDebugPanel() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("定制柜调试面板");

        this._gui.add({ isGlbCabinet: Model3dApi.isGlbCabinet }, 'isGlbCabinet')
            .name('glb柜体')
            .onChange((value: boolean) => {
                Model3dApi.isGlbCabinet = value;
            });

        this._gui.add({ isPreSaveCabinet: Model3dApi.isPreSaveCabinet }, 'isPreSaveCabinet')
            .name('预存柜体')
            .onChange((value: boolean) => {
                Model3dApi.isPreSaveCabinet = value;
            });

        this._gui.add({
            closePanel: () => {
                this.hideDebugPanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static showRenderDebugPanel() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("渲染调试面板");

        this._gui.add({
            exportRoyScene: () => {
                ServerRenderService.exportSchemeRoyScene();
            }
        }, 'exportRoyScene').name('导出RS');

        this._gui.add({
            renderType: ServerRenderService.getRenderFlag()
        }, 'renderType', {
            '普通': RenderFlag.Normal,
            '全景': RenderFlag.Panorama
        }).name('渲染类型').onChange((value: number) => {
            console.log('renderType', value);
            ServerRenderService.setRenderFlag(value as RenderFlag);
        });

        this._gui.add({
            lightRule: () => {
                ServerRenderService.applyLightRulerByIndex(0);
            }
        }, 'lightRule').name('布置灯光');

        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const entity = container.getFillLightEntities()[0];
        let initIntensity = entity?.light.intensity || 0;
        let initMaxIntensity = entity?.maxLightIntensity || 100;
        this._gui.add({
            intensity: initIntensity,
        }, 'intensity', 0, initMaxIntensity, 1).name('灯光亮度').onChange((value: number) => {
            let entities = container.getFillLightEntities();
            for (let i = 0; i < entities.length; i++) {
                let entity = entities[i];
                entity.light.intensity = value;
                entity.update3D();
            }
        });

        this._gui.add({
            offlineRender: async () => {
                let res = await ServerRenderService.commitOfflineRender();
                if (res.success) {
                    let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
                    let schemeId = layout_container._layout_scheme_id;
                    let queueId = res.queueId;
                    let url = await ServerRenderService.getOfflineRenderUrl(schemeId, queueId);
                    console.log("url", url);
                }
            }
        }, 'offlineRender').name('离线渲染');

        this._gui.add({
            closePanel: () => {
                this.hideDebugPanel();
            }
        }, 'closePanel').name('关闭面板');
    }

    public static showEdgeDebugPanel() {
        if (!this._gui) {
            this._gui = new GUI();
        }

        this._gui.title("调试面板");

        this._gui.add({ showBorder: GeometryBuilder.isShowEdges }, 'showBorder')
            .name('显示边框')
            .onChange((value: boolean) => {
                DebugUtils.showMeshBorder(value);
            });

        let modeList = ["材质", "漫画"];
        this._gui.add({ mode: "材质" }, 'mode', modeList)
            .name('渲染模式')
            .onChange((value: string) => {
                // 可以在这里添加模式切换的回调
                let index = modeList.indexOf(value);
                let rootNode = LayoutAI_App.instance.scene3D.rootNode;
                switch (index) {
                    case 0:
                        // 材质
                        rootNode.traverse((mesh: Object3D) => {
                            if (mesh instanceof Mesh) {
                                if (mesh.userData[UserDataKey.key_standard_material]) {
                                    mesh.material = mesh.userData[UserDataKey.key_standard_material];
                                }
                            }
                        });
                        break;
                    case 1:
                        // 漫画
                        rootNode.traverse((mesh: Object3D) => {
                            if (mesh instanceof Mesh) {
                                if (!mesh.userData[UserDataKey.key_standard_material]) {
                                    mesh.userData[UserDataKey.key_standard_material] = mesh.material;
                                }
                                mesh.material = GeometryBuilder.meshEdgesMaterial;
                            }
                        });
                        break;
                    default:
                        break;
                }
            });

        this._gui.add({ angle: GeometryBuilder.thresholdAngle }, 'angle', 1, 90, 1)
            .name('边框角度')
            .onChange((value: number) => {
                if (this._debounceTimer) {
                    clearTimeout(this._debounceTimer);
                }
                this._debounceTimer = setTimeout(() => {
                    this.updateThresholdAngle(value);
                    this._debounceTimer = null;
                }, this._debounceDelay);
            });
    }


    private static updateThresholdAngle(value: number) {
        console.log("updateThresholdAngle", value);
        GeometryBuilder.thresholdAngle = value;
        let root = LayoutAI_App.instance.scene3D.rootNode;
        root.traverse((mesh: Object3D) => {
            if (mesh instanceof Mesh) {
                let pre = mesh.userData[UserDataKey.BorderLine];
                if (pre) {
                    GeometryBuilder.buildMeshEdgesLine(mesh);
                }
            }
        });
    }

    public static async loadModel(mid: string): Promise<Group | null> {
        let groupNode = null;
        let results = await MaterialService.getDesignMaterialInfoByIds([mid]);
        if (results.length > 0) {
            let dvo = results[0];
            if (isGroupDesignMaterialInfo(dvo)) {
                console.log("组合素材", mid);
                // 组合素材
                groupNode = await Model3dApi.MakeMesh3DWithGroupDesignMaterialInfo(dvo);
            }
            else {
                // 单体素材
                console.log("单体素材", mid);
                groupNode = await Model3dApi.MakeMesh3DWithDesignMaterialInfo(dvo);
            }
            if (groupNode) {
                groupNode.userData[UserDataKey.MaterialId] = dvo.MaterialId;
                groupNode.userData[UserDataKey.MaterialInfo] = dvo;
                let scene = (LayoutAI_App.instance.scene3D as Scene3D).scene;
                scene.add(groupNode);
            }
            else {
                console.error("load model failed", mid);
            }
        }
        else {
            console.error("load dvo failed", mid);
        }
        return groupNode;
    }

    // 通过box来显示坐标轴
    public static createDebugBoxes() {
        const scene = (LayoutAI_App.instance.scene3D as Scene3D).scene;
        const boxGeometry = new BoxGeometry(100, 100, 100);

        const materialO = new MeshBasicMaterial({ color: 0x000000 });
        const boxO = new Mesh(boxGeometry, materialO);
        boxO.position.set(0, 0, 0);
        scene.add(boxO);

        // X轴上的box
        const materialX = new MeshBasicMaterial({ color: 0xff0000 });
        const boxX = new Mesh(boxGeometry, materialX);
        boxX.position.set(1000, 0, 0);
        scene.add(boxX);

        // Y轴上的box 
        const materialY = new MeshBasicMaterial({ color: 0x00ff00 });
        const boxY = new Mesh(boxGeometry, materialY);
        boxY.position.set(0, 1000, 0);
        scene.add(boxY);

        // Z轴上的box
        const materialZ = new MeshBasicMaterial({ color: 0x0000ff });
        const boxZ = new Mesh(boxGeometry, materialZ);
        boxZ.position.set(0, 0, 1000);
        scene.add(boxZ);
    }

    public static async getDvo(mid: string) {
        let results = await MaterialService.getDesignMaterialInfoByIds([mid]);
        if (results.length > 0) {
            console.log("dvo", results);
            return results[0];
        }
        return null;
    }


    public static async getModel(mid: string, isWhite: boolean): Promise<Group | null> {
        let dvo: I_DesignMaterialInfo = await this.getDvo(mid);
        return Model3dApi.MakeMesh3DWithDesignMaterialInfo(dvo, { isWhite: isWhite });
    }

    public static async addModelOnPos(mid: string, x: number = 0, y: number = 0, z: number = 0, isWhite: boolean = false): Promise<Group | null> {
        let groupNode = await this.getModel(mid, isWhite);
        if (!groupNode) {
            console.error("groupNode is null", mid);
            return;
        }
        let scene = (LayoutAI_App.instance.scene3D as Scene3D).scene;
        groupNode.position.set(x, y, z);
        scene.add(groupNode);
    }

    public static showTopViewScene() {
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let figure = container._furniture_entities[0]?._figure_element;
        if (!figure) {
            console.error("figure is null");
            return;
        }
        let img = this.getTopViewImage();
        let width = Math.max(Math.round(figure.rect.w / 4), 1);
        let height = Math.max(Math.round(figure.rect.h / 4), 1);
        img.style.width = width + "px";
        img.style.height = height + "px";
        img.src = DebugTopView.instance.showTopViewScene(figure, true);
    }

    public static showIFrameTestPanel() {
        IFrameTest.init();
    }

    public static showCeilingConfig() {
        CeilingConfigTest.init();
    }

    public static getSceneModelByMid(mid: string) {
        let scene = (LayoutAI_App.instance.scene3D as Scene3D)
        return scene.furniture_group.children.find(e => e.userData?.EntityOfMesh?.figure_element?._solid_mesh3D?.userData.MaterialId == mid)
    }

    public static makeObjProxy(obj: any, propName: string): any {
        return new Proxy(obj, {
            get(target: any, prop: any) {
                if (prop === propName) {
                    console.log('Getting property:', prop, 'value:', target[prop]);
                }
                return target[prop];
            },
            set(target: any, prop: any, value: any) {
                if (prop === propName) {
                    console.log('Setting property:', prop, 'to value:', value);
                }
                target[prop] = value;
                return true;
            }
        });
    }
    // 获取group的尺寸
    public static getGroupDimensionsRecursive(group: Group) {
        const box = new Box3();

        // 递归遍历所有子对象（包括嵌套 Group 中的 Mesh）
        group.traverse(child => {
            if (child instanceof Mesh && child.geometry) {
                box.expandByObject(child);
            }
        });

        const size = new Vector3();
        box.getSize(size);

        return {
            length: size.x,   // 长（X 轴方向）
            width: size.y,  // 高（Y 轴方向）
            height: size.z    // 宽（Z 轴方向）
        };
    }

    public static async getRoySceneDataById(schemeId: string, rsId: string, v: number) {
        let res = await RequestDataCenter.getRoySceneDataByCompressV2(
            schemeId,
            rsId,
            v
        );
        return res;
    }

    public static async getPanoImage() {
        try {
            const scene = (LayoutAI_App.instance.scene3D as Scene3D).scene;
            const camera = (LayoutAI_App.instance.scene3D as Scene3D).camera as PerspectiveCamera;

            // 检查场景和相机
            if (!scene) {
                console.error('Scene is null');
                return;
            }
            if (!camera) {
                console.error('Camera is null');
                return;
            }

            // 生成全景图
            const panoramaGenerator = new PanoramaGenerator({
                scene,
                camera,
                resolution: 1024,
            });

            await panoramaGenerator.saveAsJPG();
        } catch (error) {
            console.error('Error generating panorama:', error);
        }
    }

    public static showAutoLayoutTestPanel() {
        AutoLayoutTest.showPanel();
    }
}
(globalThis as any).DebugUtils = DebugUtils;