"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3751],{73751:function(n,e,t){t.d(e,{$:function(){return hn},A:function(){return vn}});var r=t(13274),i=t(69802),o=t(8268);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function u(){var n=a(["\n      position:absolute;\n      left:0;\n      bottom:40px;\n      height:200px;\n      width:100%;\n    "]);return u=function(){return n},n}function c(){var n=a(["\n        position:absolute;\n        left:0;\n        top:0;\n        height:100%;\n        width:100%;\n        background:#ffffff;\n        overflow:scroll;\n    "]);return c=function(){return n},n}var l=(0,o.rU)(function(n){n.token;var e=n.css;return{root:e(u()),listContainer:e(c())}}),s=t(15696),f=t(41594),d=t(27347),p=t(45599),h=t(61307),v=t(88934),m=t(23825),y=t(9003),b=t(37660),g=t(13915),x=t(21236),w="apply_category_button_container-BCGCp",S="apply_category_button-zhD2_",j="checked-lf4zi",_="apply_all_button-t5KdL";function I(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function O(){var n=I(["\n      background: #FFF;\n      height: 100%;\n      z-index: 999;\n      padding: 0 0 0 16px;\n      /* overflow: hidden; */\n      position: fixed;\n      top: 48px;\n      left: 0;\n    "]);return O=function(){return n},n}function C(){var n=I(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    "]);return C=function(){return n},n}function k(){var n=I(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 16px 0px;\n    "]);return k=function(){return n},n}function N(){var n=I(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      min-width: 70%;\n      height: 32px;\n      border: none;\n      margin: 16px 0 0 0px;\n      padding-left: 30px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return N=function(){return n},n}function A(){var n=I(["\n      position: absolute;\n      top: 113px;\n      left: 7px;\n    "]);return A=function(){return n},n}function P(){var n=I(["\n      position: absolute;\n      top: 97px;\n      right: 38%;\n      cursor: pointer;\n    "]);return P=function(){return n},n}function z(){var n=I(["\n      width: 24%;\n      margin: 16px 8px 0px 0;\n      display: flex;\n      justify-items: baseline;\n      align-items: center;\n      a {\n        color: #ffffff;\n        padding: 5px;\n        line-height: 23px;\n        height: 34px;\n      }\n      a:hover {\n        color: #3D9EFF;\n        border-radius: 10px;\n        background: #BFD8FF14;\n        transition: all .3s;\n      }\n    "]);return z=function(){return n},n}function E(){var n=I(["\n      display: flex;\n      justify-content: space-between;\n      padding-right: 28px;\n      margin-bottom: 16px;\n    "]);return E=function(){return n},n}function F(){var n=I(["\n      position:absolute;\n      width:100%;\n      height:100%;\n    "]);return F=function(){return n},n}function D(){var n=I(["\n      overflow-y: hidden;\n      height: 100%;\n      width:100%;\n      display:flex;\n      overflow-x: auto;\n      :hover {\n      }\n    "]);return D=function(){return n},n}function T(){var n=I(["\n      float:left;\n    "]);return T=function(){return n},n}function V(){var n=I(["\n      width: 270px;\n      height: 180px;\n      box-sizing: border-box;\n      position: relative;\n      margin:10px;\n    "]);return V=function(){return n},n}function L(){var n=I(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin: 2px 0 4px 0;\n    "]);return L=function(){return n},n}function M(){var n=I(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 80%;\n    "]);return M=function(){return n},n}function R(){var n=I(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n    "]);return R=function(){return n},n}function U(){var n=I(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n    "]);return U=function(){return n},n}function B(){var n=I(["\n      overflow-y: hidden !important;\n    "]);return B=function(){return n},n}function $(){var n=I(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n      canvas:hover {\n        background:rgba(127,127,255,0.5);\n      }\n    "]);return $=function(){return n},n}function K(){var n=I(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return K=function(){return n},n}function W(){var n=I(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return W=function(){return n},n}function q(){var n=I(["\n      border-radius: 4px;\n      height: 100%;\n      overflow: hidden;\n      img {\n        transition: all .5s;\n        width: 100%;\n      }\n\n      :hover{\n        outline: 2px solid #147FFA;\n      }\n    "]);return q=function(){return n},n}var Z=(0,o.rU)(function(n){var e=n.css;return{container:e(O()),titleContainer:e(C()),title:e(k()),container_input:e(N()),Icon:e(A()),IconDelete:e(P()),selectInfo:e(z()),findInfo:e(E()),bottomPanel:e(F()),container_listInfo:e(D()),container_box:e(T()),container_data:e(V()),textInfo:e(L()),container_title:e(M()),container_desc:e(R()),seriesStyle:e(U()),noScroll:e(B()),side_list:e($()),line:e(K()),emptyInfo:e(W()),Popover_hoverInfo:e(q())}});function G(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function H(n,e,t,r,i,o,a){try{var u=n[o](a),c=u.value}catch(n){return void t(n)}u.done?e(c):Promise.resolve(c).then(r,i)}function J(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){H(o,r,i,a,u,"next",n)}function u(n){H(o,r,i,a,u,"throw",n)}a(void 0)})}}function Q(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function X(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Q(n,e,t[e])})}return n}function Y(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function nn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,u=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){u=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(u)throw i}}return o}}(n,e)||tn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function en(n){return function(n){if(Array.isArray(n))return G(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||tn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tn(n,e){if(n){if("string"==typeof n)return G(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?G(n,e):void 0}}function rn(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=u(0),a.throw=u(1),a.return=u(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(u){return function(c){return function(u){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,r=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==u[0]&&2!==u[0])){o=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){o.label=u[1];break}if(6===u[0]&&o.label<i[1]){o.label=i[1],i=u;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(u);break}i[2]&&o.ops.pop(),o.trys.pop();continue}u=e.call(n,o)}catch(n){u=[6,n],r=0}finally{t=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,c])}}}var on=(0,s.observer)(function(){var n,e,t=(0,i.B)().t,o=(0,y.P)(),a=Z().styles,u=(0,f.useContext)(b.DesignContext),c=u.waitingToFurnishRemaining,l=(u.setWaitingToFurnishRemaining,x.A,nn((0,f.useState)(!1),2)),s=(l[0],l[1]),p=nn((0,f.useState)(!1),2),I=p[0],O=p[1],C=nn((0,f.useState)(0),2),k=C[0],N=C[1],A=nn((0,f.useState)(!1),2),P=A[0],z=A[1],E=(0,f.useRef)(null),F=((0,f.useRef)(null),(0,f.useRef)(null),(0,f.useRef)(null),nn((0,f.useState)([]),2)),D=F[0],T=F[1],V=nn((0,f.useState)([]),2),L=(V[0],V[1]),M=nn((0,f.useState)([]),2),R=(M[0],M[1]),U=nn((0,f.useState)(""),2),B=(U[0],U[1],nn((0,f.useState)(!1),2)),$=(B[0],B[1],nn((0,f.useState)(["全案风格"]),2)),K=$[0],W=$[1],q=nn((0,f.useState)((0,m.fZ)()?260:360),2),G=(q[0],q[1],nn((0,f.useState)((null===(n=o.userStore.userInfo)||void 0===n?void 0:n.isFactory)?"2":"1"),2)),H=(G[0],G[1],nn((0,f.useState)(!1),2)),tn=(H[0],H[1],nn((0,f.useState)({}),2)),on=tn[0],an=tn[1],un=nn((0,f.useState)({}),2),cn=un[0],ln=un[1],sn=nn((0,f.useState)(c),2),fn=sn[0],dn=sn[1],pn=nn((0,f.useState)({orderBy:"sort asc",ruleType:1,pageSize:10,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),hn=pn[0],vn=pn[1];(0,f.useEffect)(function(){J(function(){var n,e,t,r;return rn(this,function(i){switch(i.label){case 0:return O(!0),z(!0),n=hn,[4,(0,h.Ic)(n)];case 1:return e=i.sent(),t=null==e?void 0:e.result,"风格套系"===o.designStore.segmentedValue&&t&&t.forEach(function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map(function(n){return{imgPath:n}})}),z(!1),O(!1),(null==e?void 0:e.result)?(r=Array.isArray(D)&&D.length>0&&hn.pageIndex>1?en(D).concat(en((null==e?void 0:e.result)||[])):(null==e?void 0:e.result)||[],T(r)):T([]),s(!1),N(null==e?void 0:e.recordCount),[2]}})})()},[hn]),(0,f.useEffect)(function(){var n=function(n){n.ctrlKey&&"q"===n.key.toLowerCase()&&W("全案风格"===K[0]?["风格套系","样板间"]:["全案风格"])};return window.addEventListener("keydown",n),function(){window.removeEventListener("keydown",n)}},[K]),(0,f.useEffect)(function(){s(!0),J(function(){var n,e,t;return rn(this,function(r){switch(r.label){case 0:return[4,(0,g.kV)()];case 1:return(e=r.sent())?(t=null===(n=Object)||void 0===n?void 0:n.keys(e).map(function(n,e){return{id:e+1,screenName:n}}),R(t),[2]):[2]}})})(),J(function(){var n,e;return rn(this,function(t){switch(t.label){case 0:return[4,(0,h.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map(function(n){return{value:n.key,screenName:n.label}}),L(e),[2]):[2]}})})()},[]),(0,f.useEffect)(function(){dn(c)},[c]);var mn=function(n,e,t,r){o.schemeStatusStore.layoutSchemeSaved=!1,o.schemeStatusStore.pendingOpenSchemeIn3D=!1,d.nb.DispatchEvent(d.n0.SeriesSampleSelected,{series:n,scope:{soft:e,hard:t,cabinet:r,remaining:!1}}),o.homeStore.selectData&&o.homeStore.selectData.rooms&&yn(o.homeStore.selectData.rooms)},yn=function(n){var e=cn;e={};var t=!0,r=!1,i=void 0;try{for(var o,a=n[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var u=o.value;if(u._scope_series_map)for(var c in u._scope_series_map){var l=u._scope_series_map[c];l&&l.ruleId&&(e[l.ruleId]||(e[l.ruleId]={}),e[l.ruleId][c]=!0)}}}catch(n){r=!0,i=n}finally{try{t||null==a.return||a.return()}finally{if(r)throw i}}ln(e)},bn=function(n,e){return cn[n.ruleId]&&cn[n.ruleId][e]};return(0,f.useEffect)(function(){d.nb.on_M(v.U.SelectingRoom,"SeriesCandidateList",function(n){yn(n.current_rooms||[])})},[]),(0,r.jsx)("div",{className:a.bottomPanel,children:(0,r.jsx)("div",{className:"".concat(a.container_listInfo," ").concat(I?a.noScroll:""),onScroll:function(n){return J(function(){var e;return rn(this,function(t){return e=n.target,Math.ceil(e.scrollTop)+e.clientHeight+5>=e.scrollHeight&&k>(null==D?void 0:D.length)&&!P&&vn(Y(X({},hn),{pageIndex:hn.pageIndex+1})),[2]})})()},ref:E,children:D&&D.length>0?(0,r.jsx)(r.Fragment,{children:null==D||null===(e=D.map)||void 0===e?void 0:e.call(D,function(n,e){return(0,r.jsx)("div",{id:"series_box"+e,className:a.container_box,children:(0,r.jsxs)("div",{className:a.container_data,onMouseEnter:function(){return an(function(n){return Y(X({},n),Q({},e,!0))})},onMouseLeave:function(){return an(function(n){return Y(X({},n),Q({},e,!1))})},children:[(0,r.jsxs)("div",{className:a.Popover_hoverInfo,children:[(0,r.jsx)("img",{src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(on[e]||(0,m.fZ)())&&!fn&&(0,r.jsx)("button",{onClick:function(){return mn(n,!0,!0,!0)},className:_,children:t("全部应用")}),(on[e]||(0,m.fZ)())&&fn&&(0,r.jsx)("button",{onClick:function(){return e=n,o.schemeStatusStore.layoutSchemeSaved=!1,o.schemeStatusStore.pendingOpenSchemeIn3D=!1,void d.nb.DispatchEvent(d.n0.SeriesSampleSelected,{series:e,scope:{remaining:!0}});var e},className:_,children:t("以此补全")})]}),!fn&&(0,r.jsxs)("div",{className:w,children:[(0,r.jsx)("div",{onClick:function(){return mn(n,!1,!1,!0)},className:S+" "+(bn(n,"cabinet")?j:""),children:t("定制")}),(0,r.jsx)("div",{onClick:function(){return mn(n,!0,!1,!1)},className:S+" "+(bn(n,"soft")?j:""),children:t("软装")}),(0,r.jsx)("div",{onClick:function(){return mn(n,!1,!0,!1)},className:S+" "+(bn(n,"hard")?j:""),children:t("硬装")})]}),(0,r.jsxs)("div",{className:a.textInfo,children:[(0,r.jsx)("div",{className:a.container_title,title:n.seedSchemeName||n.ruleName,children:n.seedSchemeName||n.ruleName}),(0,r.jsxs)("div",{className:a.container_desc,children:[(0,r.jsx)("span",{className:a.seriesStyle,children:t(n.seriesStyle)}),(0,r.jsxs)("span",{style:{display:"".concat("风格套系"===o.designStore.segmentedValue?"none":"inline-block")},children:[" | ",n.schemeArea,"㎡"]})]})]})]},e)},"record_"+e)})}):(0,r.jsx)("div",{className:a.emptyInfo,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("div",{className:"desc",children:t("暂无数据")})]})})})})});function an(){var n,e,t=(n=["\n            position:absolute;\n            height:100%;\n            display:flex;\n            overflow-x: auto;\n            overflow-y: hidden;\n        \n            .container_box {\n                float:left;\n                width:160px;\n                height:160px;\n                display: flex;\n                margin:20px;\n                border-radius:5px;\n                box-shadow: 0 2px 12px rgba(0, 0, 0, .2);\n                font-size:16px;\n                color:#777;\n                cursor:pointer;\n                box-sizing: border-box;\n                justify-content:center;\n                align-items:center;\n                flex-wrap:wrap;\n                &:hover{\n                    background:rgba(127,127,127,0.05);\n                    color:#333;\n                }\n\n                img {\n                    width:80%;\n\n                }\n\n            }\n        "],e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}})));return an=function(){return t},t}var un=(0,o.rU)(function(n){return{root:(0,n.css)(an())}}),cn=t(79750);function ln(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function sn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,u=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){u=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(u)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ln(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ln(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var fn=function(){var n=un().styles,e=d.nb.t,t=sn((0,f.useState)([]),2),i=t[0],o=t[1];return(0,f.useEffect)(function(){var n=d.nb.instance.layout_container._ext_drawing_entities.filter(function(n){return"ViewCamera"===n.realType});o(n)},[]),(0,r.jsxs)("div",{className:n.root,children:[(0,r.jsx)("div",{className:"container_box",onClick:function(){return function(){var n=d.nb.instance.layout_container;cn.q.updateViewCameraEntities(n);var e=n._ext_drawing_entities.filter(function(n){return"ViewCamera"===n.realType});e.forEach(function(e){"ViewCamera"===e.realType&&e.updateViewImg(n.painter,300,300)}),o(e)}()},children:(0,r.jsx)("div",{children:e("生成视角")})}),i.map(function(n,e){return(0,r.jsxs)("div",{className:"container_box",onClick:function(){return function(n){var e=d.nb.instance.scene3D;e.active_controls&&e.active_controls.bindViewEntity(n)}(n)},children:[(0,r.jsx)("img",{src:n._view_img.src}),e+1," : ",n.name]},"view_box_"+e)})]})};function dn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function pn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,u=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){u=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(u)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return dn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return dn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var hn=function(n){return n.setIsVisible="setIsVisible",n.showPopup="showPopup",n}({}),vn=(0,s.observer)(function(){(0,i.B)().t;var n=l().styles,e=pn((0,f.useState)(!0),2),t=(e[0],e[1],pn((0,f.useState)(""),2)),o=t[0],a=t[1];(0,f.useEffect)(function(){d.nb.on("showPopup",function(n){a(n)})},[]);window.innerWidth;return(0,r.jsxs)("div",{className:n.root,style:{zIndex:""==o?-2:10},children:[(0,r.jsx)("div",{className:n.listContainer,style:{zIndex:1},children:" "}),(0,r.jsxs)("div",{className:n.listContainer,style:{zIndex:"Layout"===o?2:-10},children:[" ",(0,r.jsx)(p.A,{width:400,showSchemeName:!1,isLightMobile:!0})," "]}),(0,r.jsxs)("div",{className:n.listContainer,style:{zIndex:"Matching"===o?2:-10},children:[" ",(0,r.jsx)(on,{})," "]}),(0,r.jsxs)("div",{className:n.listContainer,style:{zIndex:"CameraViews"===o?2:-10},children:[" ",(0,r.jsx)(fn,{})," "]})]})})}}]);