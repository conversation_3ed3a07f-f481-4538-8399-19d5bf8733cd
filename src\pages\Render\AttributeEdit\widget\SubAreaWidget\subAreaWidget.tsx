import { useState } from "react";
import { useTranslation } from "react-i18next";
import useStyle from "./style";
import { Icon } from "@svg/antd-cloud-design";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { AddSpaceAreaSubHandler } from "@/Apps/AI2Design/Handlers/SubHandlers/CadEditSubHandlers/AddSpaceAreaSubHandler";

const SubAreaWidget = (props: any) => {
    const { t } = useTranslation();
    const {styles} = useStyle();
    const [value,setValue] = useState<string>(props.value);

    const [isExpanded,setExpanded] = useState<boolean>(false);


    const recommend_areas:{name:string,label:string,color:string}[] = props?.schema?.props?.recommend_areas || [];

    return (
        <>
        <div className={styles.root}>
            <div
                className={`label ${isExpanded ? 'active' : ''}`}
                onClick={() => setExpanded(!isExpanded)}
                >
                <Icon
                iconClass={`${isExpanded? 'iconcaretdown' : 'iconcaretright'}`}
                className="icon"
                size={14}
                />
                <span className="label_name">{t("分区编辑")}</span>
            </div>
        </div>
        {/* {isExpanded &&    
            <div className={styles.root}>
                <div
                    className={"btn"}
                    onClick={() => {
                    }}
                    >
                    {t("分区生成")}
                </div>
            </div>                 
        }  */}
        {isExpanded &&    
            <div className={styles.root}>
                <div>
                    <span className="label_name">{t("分区绘制")}</span>
                </div>
                
            </div>                 
        } 
       {isExpanded &&    
            <div className={styles.root}>
                {
                    recommend_areas.map((data,index)=><div key={"sub_area_btn_"+index} title={t("绘制"+data.label)} className="sub_btn" style={{background:data.color}}
                        onClick={()=>{
                            LayoutAI_App.RunCommand(AddSpaceAreaSubHandler.handler_name); // 进入该handler
                            LayoutAI_App.DispatchEvent(AddSpaceAreaSubHandler.change_area_type_event,data.name);
                        }}
                    >
                        {t(data.label)}
                    </div>)
                }
            </div>                 
        } 
        </>

    );
};
export default SubAreaWidget;