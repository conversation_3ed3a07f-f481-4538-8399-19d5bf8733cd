import { getSettingKeys } from "@/services/user";
export class Permissions {
    private static _instance: Permissions | null = null;

    public static get instance(): Permissions {
        if (!this._instance) {
            this._instance = new Permissions();
        }
        return this._instance;
    }

    private _permissions: Map<string, any> = new Map();

    private constructor() {
    }

    public getPermissions() {
        return this._permissions;
    }
    
    setPermissions(permissions: Map<string, any>) {
        this._permissions = permissions;
    }

    getPermission(map: Map<string, any>) {
        return map;
    }

    async loadPermissions() {
        try {
            const permissions = await getSettingKeys({});
            if (permissions.success) {
                const tenantSettings = permissions.result.tenantSettings || [];
                const globalSettings = permissions.result.globalSettings || [];
                // 使用对象合并的方式
                const mergedSettings: Map<string, any> = new Map();
                // 先添加 globalSettings
                globalSettings.forEach((item: any) => {
                    mergedSettings.set(item.settingKey, item);
                });
                // 再添加 tenantSettings
                tenantSettings.forEach((item: any) => {
                    mergedSettings.set(item.settingKey, item);
                });
                this.setPermissions(mergedSettings);
                console.log('Permissions: 权限加载完成', mergedSettings);
            }
        } catch (error) {
            console.error('Permissions: 加载权限失败', error);
        }
    }

    // 检查是否有某个权限
    hasPermission(permissionCode: string): boolean {
        const permission = this._permissions.get(permissionCode);
        return permission?.settingValue === '1' || false;
    }

    // 获取权限值
    getPermissionValue(permissionCode: string): string | undefined {
        const permission = this._permissions.get(permissionCode);
        return permission?.settingValue;
    }
}