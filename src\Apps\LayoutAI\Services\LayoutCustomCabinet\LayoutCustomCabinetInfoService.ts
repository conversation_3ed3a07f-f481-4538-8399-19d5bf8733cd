import { openApiRequest } from "@/utils";
import { createOpenRequest } from "@svg/request";
/**
 *  {"createDateEnd": "2025-06-13 04:05:12","createDateStart": "2025-06-13 04:05:12","createUser": "string","depth": "Unknown Type: double","hasDecoration": 0,"height": "Unknown Type: double",
                "ids": ["string"],"isDelete": 0,"materialId": "string","materialIdList": ["string"],"orderBy": "string","pageIndex": 0,"pageSize": 0,"styleId": "string",
                "tenantId": "string","updateDateEnd": "2025-06-13 04:05:12","updateDateStart": "2025-06-13 04:05:12","updateUser": "string","valueUrl": "string","width": "Unknown Type: double"
    }
 */
export interface I_CustomCabinetInfoQueryData {createDateEnd?: string;createDateStart?: string;createUser?: string;depth?: number;
    hasDecoration?: number;height?: number;ids?: string[];isDelete?: number;materialId?: string;materialIdList?: string[];
    orderBy?: string;pageIndex?: number;pageSize?: number;styleId?: string;tenantId?: string;updateDateEnd?: string;updateDateStart?: string;
    updateUser?: string;valueUrl?: string;width?: number;
 }
export interface I_CustomCabinetInfoItem {
    createUser:string;
    depth:number;
    hasDecoration:number;
    height:number;
    id:string;
    isDelete:number;
    materialId:string;
    styleId:string;
    tenantId:string;
    updateDate:string;
    updateUser:string;
    valueUrl:string;
    width:number;
}
export class LayoutCustomCabinetInfoService {
 
    public static _openRequest : any;
    public static get openRequest()
    {
        if(!LayoutCustomCabinetInfoService._openRequest)
        {
            LayoutCustomCabinetInfoService._openRequest = openApiRequest;
        }
        return LayoutCustomCabinetInfoService._openRequest;
    }
 
    /**
     * @param queryData 
     */
    static async listByPage(queryData:I_CustomCabinetInfoQueryData={}) {
        queryData = queryData || {}
        queryData.pageIndex = queryData.pageIndex || 1;
        queryData.pageSize = 100;

       let res =  await LayoutCustomCabinetInfoService.openRequest({
            method: 'post',
            url: `/api/njvr/layoutCustomCabinetInfo/listByPage`,
            data: {
                ...queryData,
            },
            timeout: 60000,
        }).catch((e:any):any=>null);
        if (!res || res.success == false || !res.result?.result) {
            return [];
        }
        return res.result.result as I_CustomCabinetInfoItem[];
    }


    /**
     * 结果排序后返回
     * @param queryData  
     * @param targetSize 
     * @returns 
     */
    static async querySimilarInfos(queryData:I_CustomCabinetInfoQueryData={},targetSize:{width?:number,depth?:number,height?:number}={}){
        let width = targetSize.width || 0;
        let height = targetSize.height || 0;
        let depth = targetSize.depth || 0;

        let items = await this.listByPage(queryData);
        items.sort((a,b)=>-a.updateDate.localeCompare(b.updateDate));
        let sort_score = (item:I_CustomCabinetInfoItem)=>{
            let d_width = item.width - width;
            let d_depth = item.depth - depth;
            let d_height = item.height - height;

            let id_weight = 1.;
            let id_error =1000;
            if(item.styleId == "0")
            {
                id_error = 0;
            }
            return (Math.abs(d_width) + Math.abs(d_depth) * 0.1 + Math.abs(d_height) * 0.5 + id_error) * id_weight;
        }
        items.sort((a,b)=>sort_score(a)-sort_score(b));
        return items;

    }

    
}
