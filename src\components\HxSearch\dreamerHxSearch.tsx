import React, { useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import './index.less'
import { hxUrl, mode_type } from '@/config';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { onModal_HxSearch } from '@/utils/common';

const DreamerHxSearch = forwardRef((props, ref) => {
  const [isShowHxSearch,setIsShowHxSearch] = useState<boolean>(false);

  const leaveModal = ()=>{
    const iframe = document.getElementById('searchIframe');
    iframe && iframe.remove();
    setIsShowHxSearch(false);
  }

  const onOpenMyScheme = ()=>{
    LayoutAI_App.emit(EventName.OpenMySchemeList, null);
  }

  const onUploadHxImg = ()=>{
    LayoutAI_App.RunCommand(LayoutAI_Commands.OpenImitateImage);
  }

  useEffect(() => {
    LayoutAI_App.on(EventName.OpenHouseSearching,(t:boolean)=>{
      if(t)
      {
        onModal_HxSearch();
        setIsShowHxSearch(t);
      }
      else{
        leaveModal();
      }
    })
    window.addEventListener('message', (e) => {
      // console.log('梦想家的e',e);
      // console.log('梦想家的e.data',e.data);
      
      if(e.data?.type === 'closeModal') {
        leaveModal();
      }
      if(e?.data && e?.data.origin === 'uiapi.3dhouseplugin.ui') {
        const data: any = e.data.data;
        if(data?.action === 'onBtnClick') {
          const id = data.params.curHouseData.id;
          const name = data.params.curHouseData.buildingName + " " + data.params.curHouseData.roomTypeName + " " + data.params.curHouseData.area + "m²";
          LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:name});
          leaveModal();
          LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "加载方案中...." });
          return;
        }
      }
      if(e?.data && e?.data.origin === 'uiapi.houseplugin') {
        const data: any = e.data.data;
        if(data?.action === 'onExcellentSchemeBtnClick') {
          const id = data.params.curHousetypeObj.id;
          const name = data.params.curHouseData.buildingName + " " + data.params.curHouseData.roomTypeName + " " + data.params.curHouseData.area + "m²" ;
          LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:name});
          leaveModal();
          LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "加载方案中...." });
          return;
        }
      }
      if(e?.data?.id)
      {
        const id = e.data.id;
        LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: id, name:''});
        const iframe = document.getElementById('searchIframe');
        iframe && iframe.remove();
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "加载方案中...." });
        return;
      }
    });

  }, [])

  useImperativeHandle(ref, () => ({
    onModal_HxSearch
  }));

  return (
    <div className='hxsearch'>

      {isShowHxSearch && <div className='hx_background' onClick={()=>{leaveModal()}}></div>}
      <div id='iframe-wrap'>
      </div>
      {isShowHxSearch && <div className='upload_dreamer_hx' onClick={onUploadHxImg}>
        <img style={{marginTop: '20px'}} src={'https://3vj-fe.3vjia.com/layoutai/icons/upload.svg'} alt="" />
        <div  className='upload_title'>
            上传户型
        </div>
      </div>}
      {isShowHxSearch && mode_type !== 'schemecreate' && <div className='my_title_div'>
        <div className='right_btns'>
          
            <div className='btn' onClick={onOpenMyScheme}>
            <img src={'https://3vj-fe.3vjia.com/layoutai/icons/myPlan.svg'} alt="" /><span>我的方案</span>
            </div>

        </div>
      </div>}
    </div>
  )
})

export default DreamerHxSearch;