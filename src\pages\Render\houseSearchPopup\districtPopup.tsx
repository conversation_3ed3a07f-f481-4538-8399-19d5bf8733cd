import { useEffect, useState } from 'react';
import useStyles from './style';

interface I_Distrct
{
    children : I_Distrct[];
    label : string;
    value : string;
    firstLetter ?: string;
}
export interface I_City {
    code : string;
    name : string;
}
var districts : I_Distrct[] = [];
export const DistrictPopup : React.FC<{is_visible:boolean,onSelected?:(city:I_City)=>void}> = (props)=>{
    const { styles } = useStyles();

    const [Districts,setDistricts] = useState<I_Distrct[]>([]);

    const [cities, setCities] = useState<I_City[]>([]);
    useEffect(()=>{
        if(districts.length == 0)
        {
            fetch("https://3vj-fe.3vjia.com/fe_oss_prod/static/swj/district/prod/district-v3.json").then((data)=>data.json()).then(data=>{
                districts = data;    
                districts.sort((a,b)=>a.firstLetter.localeCompare(b.firstLetter));    
                setDistricts(districts)
            });
        }
        else {
            setDistricts(districts);
        }


    },[]);

    if(!props.is_visible) return <></>;

    return <div className={styles.districts}>
        {districts.map((distric,index)=><div key={"Distric_0_"+index} className="row">
            <div className='provice'>{distric.label}</div>
            <div className='cities'>
                {distric.children.map((city,index)=><div className='city' key={"city_sub_"+index} onClick={()=>{
                    if(props.onSelected)
                    {
                        props.onSelected({name:city.label,code:city.value});
                    }
                }}>{city.label}</div>)}
            </div>
        </div>)}

    </div>
}