import { SubHandlerBase } from "@/Apps/AI2Design/Handlers/SubHandlers/SubHandlerBase";
import { EventName } from "@/Apps/EventSystem";
import { I_SwjXmlScheme } from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { g_FigureImagePaths } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import { TAILayoutDrawingLayer } from "@/Apps/LayoutAI/Drawing/TAILayoutDrawingLayer";
import { BaseModeHandler } from "@/Apps/LayoutAI/Handlers/BaseModeHandler";
import { FigureZValRangeType, TsAI_app } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { WPolygon } from "@/Apps/LayoutAI/Layout/TFeatureShape/WPolygon";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TWindowDoorEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity";
import { TLayoutFineTuningManagerToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutFineTuningOperation/TLayoutFineTuningManagerToolUtil";
import { TGroupTemplate } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TLayoutOptimizer } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TLayoutOptimizer";
import { TPostFigureElementAdjust } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostFigureElementAdjust";
import { TPostLayoutCeiling } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutCeiling";
import { TTinyLivingRoomByPathRelation } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TSpacePartionGraph/TSimpleRoomRelations/TTinyLivingRoomByPathRelation";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { I_Range2D, TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TRoom } from "@/Apps/LayoutAI/Layout/TRoom";
import { get_swj_content_xml_from_swj_file, get_swj_xml_from_url } from "@/Apps/LayoutAI/Utils/xml_utils";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { I_MouseEvent, ZDistanceDimension, ZPolygon, ZRect, compareNames, makeDimensionsOfEdge, saveFileAs } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TrainingManager } from "../TrainingManager";
import { TrainingTestingEvents, TrainingTestingSignals } from "../TrainningEvents";
import { TEditRoomSubHandler } from "./SubHandlers/TEditRoomSubHandler";


export const room_data_url = "https://www.abc3d.cc/layout_data/room_db/"
export const room_data_query_url = "https://www.abc3d.cc/index.php?s=/Home/Test/getRoomData/"


export class LayoutGraphTestingHander extends BaseModeHandler {

    _seriesId: string;
    current_room: TRoom;

    _inner_cabinet_polys : ZPolygon[];

    /**
     *  去掉过道的主矩形
     */
    _inner_main_polys : ZPolygon[];

    /**
     * 绘制处剔除过道后的有效区域
     */
    public static _valid_polys: ZPolygon[] = [];

    public static _layer_visible = {
        ceilings : {
            name : "吊顶",
            visible : false
        },
        sub_area : {
            name : "子分区",
            visible: true
        },
        valid_polys : {
            name : "算法过程生成的多边形",
            visible: true
        },
        cabinet_rects: {
            name : "柜体备选",
            visible : false
        },
        inner_main_polys : {
            name : "裁剪后轮廓",
            visible : false
        }
    }

    /**
     *  内部格子点多边形
     */
    _inner_grid_polys : ZPolygon[];

    _room_name: string;


    _is_graph_init: boolean;

    _draw_mode: "draw_funitures" | "draw_group_template";

    _swj_layout_data_layer: TAILayoutDrawingLayer;

    _edit_room_sub_handler: TEditRoomSubHandler;

    _visible_zval_range_type: FigureZValRangeType;


    _moving_count = 0;
    constructor(manager: TrainingManager) {
        super(manager, "LayoutGraphTesting");

        this._seriesId = "default";

        

        this._swj_layout_data_layer = manager._swj_layout_data_layer;

        this._is_graph_init = false;

        this._draw_mode = "draw_funitures";

        this._edit_room_sub_handler = new TEditRoomSubHandler(this);

        this._active_sub_handler = this._edit_room_sub_handler as any as SubHandlerBase;

        this._visible_zval_range_type = FigureZValRangeType.All;


        // testOnlineOptmizer0();

    }

    get layout_graph_solver()
    {
        return this.manager.layout_graph_solver;
    }
    protected async initGraphs() {
        // if (this.layout_graph_solver._query_backend) return;
        // if (this.layout_graph_solver._is_model_rooms_loaded) return;

        // let model_db = await fetch(getPrefix() + "static/model_rooms/model_room_list.json").then(val => val.json()).catch((err) => { return null });

        // console.log(model_db);
        // if (!model_db) return;

        // for (let key in model_db) {
        //     this.layout_graph_solver.addModelRooms(model_db[key]);
        //     // this._layout_graph_solver.addGroupTransferModelRooms(model_db[key]);

        // }
        this.layout_graph_solver._is_model_rooms_loaded = true;
        this._is_graph_init = true;
    }

    async getRoom(id: number): Promise<any> {
        let data = await fetch(room_data_url + this._room_name + "/" + id + ".json").then(val => val.json()).catch((error: any) => {
            console.log(error);
            return null;
        });

        return data;
        // console.log(data);
    }

    async loadLocal() {
        if (localStorage) {

            let item0 = localStorage.getItem("layout_ai_training_current_room_data");
            if(item0)
            {
                let data = JSON.parse(item0);

                TsAI_app.Quiet = false;

                this.current_room = new TRoom(data.room);


                this.current_room.updateFeatures();

                if(!this.current_room._room_entity)
                {
                    this.current_room._room_entity = new TRoomEntity(this.current_room.room_shape._poly,this.current_room.roomname);
                    for(let win of this.current_room.windows)
                    {
                        win.rect._attached_elements['window']  = win;
                        TBaseEntity.set_polygon_type(win.rect,win.type);

                        let win_entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win.rect);
                        this.current_room._room_entity.addAndBindWinRect(win.rect);
                        this.current_room._room_entity._room = this.current_room;
                    }
                    this.current_room._room_entity.update();
                }

                // this.current_room.initAreaGridsBoard(100);

                if(this.current_room.room_shape._feature_shape)
                {

                    let w_poly = this.current_room.room_shape._feature_shape._w_poly;
                    this._inner_main_polys = WPolygon._computeCuttedInnerPolygons(w_poly);
                    this._inner_cabinet_polys = WPolygon._computeValidOnWallCabinetRects(this.current_room.room_shape._feature_shape._w_poly);
                    this._inner_grid_polys = WPolygon._computeInnerGridRects(w_poly,this.current_room.windows.map((win)=>win.type==="Door"&&win.rect).filter(rect=>rect));
                }

                TsAI_app.Quiet = true;

                this.painter.p_center = this.current_room.room_shape._poly.computeBBox().getCenter(new Vector3());

                this.update();


                await this.updateSideList();


                this.update();

            }


        }

        if(this.manager.layout_container)
        {
            this.manager.layout_container._selected_room = this.current_room;
        }
        if (this._edit_room_sub_handler) {
            this._edit_room_sub_handler._room = this.current_room;
        }
        // let text =     await   get_swj_xml_from_url("https://hws-static.3vjia.com/UpFile_Content/*********/20240318/0165dc5ad66c467c9075a491501c27a2/0165dc5ad66c467c9075a491501c27a2_1.txt");

        // console.log(text);
        


        

    }

    async selectRoomFromSwjLayoutData(id:number)
    {
        let rooms = this._swj_layout_data_layer.rooms;
        if(!rooms) return;
        let target_room: TRoom = rooms[id];

        if (target_room) {
            this.current_room = target_room;

            TsAI_app.Quiet = false;
            this.current_room.updateFeatures();
            // TsAI_app.log(this.current_room.windows);

            TsAI_app.Quiet = true;

            this.painter.p_center = this.current_room.room_shape._poly.computeBBox().getCenter(new Vector3());
            this.update();

            this.saveLocal();
            await this.updateSideList();


            if (this._edit_room_sub_handler) {
                this._edit_room_sub_handler._room = this.current_room;
            }
            this.update();

            return;
        }
    }

    saveLocal()
    {
        if(localStorage)
        {
            localStorage.setItem("layout_ai_training_current_room_data", JSON.stringify(this.current_room.exportExtRoomData()));
        }
    }
    async loadSwjRoomsJson(swj_data:I_SwjXmlScheme)
    {
            this._swj_layout_data_layer._src_swj_layout_data = swj_data;
            this._swj_layout_data_layer.rooms.length = 0;
            this._swj_layout_data_layer.makeDirty();
            this._swj_layout_data_layer._updateLayerContent();


            this.updateRightPanelRoomList();

            this.selectRoomFromSwjLayoutData(0);

            this.saveLocal();

        
    }

    async loadSwjRoomsFromXml(xml:XMLDocument)
    {

    }
    async  optimizeGroupTemplates() {
        let room = this.current_room;
        if (!room) return;

        if(!room.room_shape._feature_shape)
        {
            room.updateFeatures();

        }
        if(this.current_room.room_shape._feature_shape)
        {
            let w_poly = this.current_room.room_shape._feature_shape._w_poly;
            this._inner_main_polys = WPolygon._computeCuttedInnerPolygons(w_poly);
                
            this._inner_cabinet_polys = WPolygon._computeValidOnWallCabinetRects(w_poly);
            this._inner_grid_polys =  WPolygon._computeInnerGridRects(w_poly,this.current_room.windows.map((win)=>win.type==="Door"&&win.rect).filter(rect=>rect));
        }


        if (!room._group_template_list) return;

        let feature_shape = room.room_shape._feature_shape;
        TsAI_app.Quiet = false;

        TLayoutOptimizer.optimize_groups_in_shape(feature_shape, room._group_template_list, room.roomname,3,true);

        // TPostProcessLayout.post_back_to_wall_rule_with_src_poly(room.room_shape._feature_shape,room._group_template_list,room.roomname);

        // TPostLayoutWashingPart.instance.postInfillLayout(room,room._group_template_list);

        room.resetFurnitureList();
        for (let template of room._group_template_list) {
            if(template.current_s_group){
                room.addFurnitureElements(template.current_s_group.figure_elements);
            }
        }

        TsAI_app.Quiet = true;

        this.update();
    }

    async updateSideList() {

        if (!this._is_graph_init) {
            await this.initGraphs();

        }
        if (!this.current_room) return;
        let div = document.getElementById("side_list_div") as HTMLDivElement;

        if (!div) return;

        div.innerHTML = "";
        div.style.display = "block";



        let src_canvas = this.painter._canvas;
        let painter = this.painter;
        let ts = this.painter.exportTransformData();

        let scope = this;
        console.time("applyRoom");
        console.log(this.current_room);

        // await this.layout_graph_solver.queryModelRoomsFromServer([this.current_room],false);
        this.current_room._room_data = this.current_room.exportRoomData();
        let result_scheme_list = await this.layout_graph_solver.applyRoom({
            room: this.current_room._room_data
        });
        result_scheme_list.forEach((layout_scheme) => {
            let totalScore = 0;
            if (layout_scheme._scheme_name.includes("相似")) {
                totalScore += 0;
            }
            layout_scheme._layout_scores.forEach((item) => {
                totalScore += item.score;
            });
            layout_scheme.totalScore = totalScore;
        });

        result_scheme_list= result_scheme_list.sort((a, b) => b.totalScore - a.totalScore);
        if(TTinyLivingRoomByPathRelation._valid_polys)
        {
            LayoutGraphTestingHander._valid_polys = TTinyLivingRoomByPathRelation._valid_polys;
        }
        console.timeEnd("applyRoom");

        if (!result_scheme_list) return;

        // console.log(result_scheme_list);
        let current_room = this.current_room;
        for (let id = 0; id < result_scheme_list.length; id++) {
            let result_scheme = result_scheme_list[id];
            let res0 = result_scheme;
            let c_div = document.createElement("div") as HTMLDivElement;
            let canvas = document.createElement("canvas") as HTMLCanvasElement;
            canvas.width = 600;
            canvas.height = 600;
            canvas.style.width = "200px";
            canvas.style.height = "200px";

            c_div.style.width = canvas.style.width;
            c_div.style.height = canvas.style.height;

            let label_div = document.createElement("div") as HTMLDivElement;

            label_div.style.width = "100%";
            label_div.style.position = "relative";
            label_div.style.top = "180px";
            label_div.style.textAlign = "center";
            painter.bindCanvas(canvas);

            painter.p_center = this.current_room.room_shape._poly.computeBBox().getCenter(new Vector3());
            painter._p_sc = 0.04;
            this.current_room._painter = painter;
            painter.enter_drawpoly();
            this.current_room.drawRoom(6, 0);

            this.current_room.drawFigureElements(res0.figure_list.figure_elements);

            painter.leave_drawpoly();
            div.appendChild(c_div);
            c_div.appendChild(label_div);
            c_div.appendChild(canvas);

            // console.log(res0._scheme_name);
            label_div.innerHTML = res0._scheme_name || "";
            canvas.onclick = (ev) => {

                // current_room.importRoomData(current_room._room_data);
                // current_room.updateFeatures();
                
                // 

                current_room.resetFurnitureList();
                current_room.addFurnitureElements(res0.figure_list.figure_elements);

                if(res0.sub_area_list)
                {
                    current_room._sub_space_list = [...res0.sub_area_list];

                    current_room._sub_space_list.forEach((sub_space)=>{
                        sub_space.area_poly = new ZPolygon();
                        sub_space.area_poly.initByVertices(sub_space.area_points);
                    })
                }
                else{
                    current_room._sub_space_list = [];
                }
                current_room._group_template_list = res0.group_templates;

                // current_room._group_template_list = TGroupTemplate.extractGroupTemplates(current_room._furniture_list,current_room.roomname);
                // TPostLayoutCeiling.instance.postAddCeilingFigures(current_room);

                // if(current_room._area_grids_board)
                // {
                //     current_room._area_grids_board.updateByFurnitureList(current_room._furniture_list);
                //     current_room._area_grids_board.updateDrawingImage().then(()=>{
                //         scope.update();
                //     })
                // }
                // console.log(current_room._room_entity);
                if(current_room._room_entity)
                {
                    if(res0._debug_data.scheme_name.includes("动线分区"))
                    {
                        updateLayoutSpaceAreaByPath(current_room, res0);
                    }
                    else
                    {
                        if(current_room._room_entity)
                        {
                            current_room._room_entity.updateSpaceLivingInfo({force_auto_sub_area:true});
                        }
                        TPostLayoutCeiling.instance.postAddCeilingFigures(current_room);
                    }
                    
                }

                LayoutAI_App.emit_M(EventName.UpdateLayoutScore,true);

                // console.log(res0._debug_data, current_room._furniture_list);
                scope.update();
            }

            if (id == 0) {
                current_room.resetFurnitureList();
                current_room.addFurnitureElements(res0.figure_list.figure_elements);
                current_room._group_template_list = res0.group_templates;
                if(current_room._room_entity)
                {
                    if(res0._debug_data.scheme_name.includes("动线分区"))
                    {
                        updateLayoutSpaceAreaByPath(current_room, res0);
                    }
                    else
                    {
                        if(current_room._room_entity)
                        {
                            current_room._room_entity.updateSpaceLivingInfo({force_auto_sub_area:true});
                        }
                        TPostLayoutCeiling.instance.postAddCeilingFigures(current_room);
                    }
                }

                LayoutAI_App.emit_M(EventName.UpdateLayoutScore,true);
                // current_room._group_template_list = TGroupTemplate.extractGroupTemplates(current_room._furniture_list,current_room.roomname);
            }

        }


        painter.bindCanvas(src_canvas);
        painter.importTransformData(ts,false);

    }
    updateRightPanel(visible: boolean) {


        if (!visible) {
            LayoutAI_App.emit(EventName.LayoutGraphTestingRightPanel, null);
            return;
        }

        let scope = this;
        LayoutAI_App.emit(EventName.LayoutGraphTestingRightPanel, {
            onSelectVisibleChange: (event:any)=>{
                let val = ~~event.target.value;
                scope._visible_zval_range_type = val;
                scope.update();
            },
            onUpdateAiLayoutBtnClick: (ev:any)=>{
                scope.updateSideList();
            },
            onLoadTestSwjJsonBtnChange: (ev:any)=>{
                let file = ev.target.files[0];
                if(!file) return;
                var reader = new FileReader();
                reader.readAsText(file);
                reader.onload = function(){
    
                    let json = JSON.parse(this.result as string);
                    scope.loadSwjRoomsJson(json);
                };
            },
            onLoadTestSwjFileBtnChange: (ev:any)=>{
                let file = ev.target.files[0];
                if(!file) return;
                var reader = new FileReader();
                
                reader.readAsArrayBuffer(file);
                reader.onload = async function(){
                    let buffer = this.result;
                    let doc =  get_swj_content_xml_from_swj_file(buffer as Uint8Array);

                    console.log(doc);
      

                }
            },
            onLoadTestUrlChange :async (ev:any)=>{
                console.log(ev.target.value);

                let text = await get_swj_xml_from_url(ev.target.value as string);

            },
            onUpdateFigureSvg : (ev:any)=>{
                scope.onUpdateFigureSvg();
            },
            onFinetuning : ()=>{
                if(this.current_room)
                {
                    TLayoutFineTuningManagerToolUtil.instance.fineTuningRoom(this.current_room);
                    this.update();
                }
            }
        });
    }

    async onUpdateFigureSvg()
    {
        this.painter._svg_pattern_dict = {};
        let dom = new DOMParser().parseFromString('<?xml version="1.0" encoding="UTF-8"?><root></root>',"application/xml");
        let root_node = dom.getElementsByTagName("root")[0];
        for(let key in g_FigureImagePaths)
        {
            let data = g_FigureImagePaths[key];
            if(data.img_path && data.img_path.endsWith("svg"))
            {
                let svg_content = await fetch(data.img_path).then(val=>val.text());
                this.painter._svg_pattern_dict[key] = {
                    xml_url : data.img_path,
                    xml_content : svg_content
                }
                let svg_xml = new DOMParser().parseFromString(svg_content,"application/xml").getElementsByTagName("svg")[0];

                svg_xml.removeAttribute("xmlns");
                svg_xml.removeAttribute("xmlns:xlink");
                svg_xml.setAttribute("figure_name",key);
                root_node.appendChild(svg_xml);
            }
        }
        saveFileAs(new XMLSerializer().serializeToString(dom),"figure_root.xml","application/xml");
        // console.log(new XMLSerializer().serializeToString(dom));

    }
    updateRightPanelRoomList()
    {
        let room_list_select = document.getElementById("room_list_select") as HTMLInputElement;

        console.log(room_list_select);
        if(!room_list_select) return;
        let rooms = this._swj_layout_data_layer.rooms;
        if(!rooms) return;

        room_list_select.innerHTML = "";
        let text = "";
        for(let id in rooms)
        {
            let room = rooms[id];
            text += `<option value='${id}'>${id}:${room.name}-${room.roomname}</option>`
        }

        room_list_select.innerHTML = text;

        room_list_select.onchange = null;
        let scope = this;
        room_list_select.onchange = (ev)=>{
            let val = room_list_select.value;
            scope.selectRoomFromSwjLayoutData(~~val);
        }

    }

    enter(state?: number): void {
        super.enter(state);
        this.manager.EventSystem.emit(EventName.TrainingLabelsListHandle, []);


        // TLayoutOptmizerOnWallRules.testing_online_intervals_center_x();

        
        this.updateRightPanel(true);
        this.loadLocal();

        // if(this._swj_layout_data_layer.rooms.length > 0)
        // {
        //     console.log("swj layout data layer");
        //     this.updateRightPanelRoomList();

        //     this.selectRoomFromSwjLayoutData(0);

        //     this.saveLocal();
        // }
        // else{

        // }
        if(this.manager.layer_DefaultBatchLayer)
        {
            this.manager.layer_DefaultBatchLayer.visible = false;
        }

        this.update();
    }
    leave(state?: number): void {
        super.leave(state);
        this.updateRightPanel(false);
        this.manager.EventSystem.emit(EventName.AttributeHandle, {
            mode: 'hide', // 当前模式，支持edit ,hide 
        });
        let div = document.getElementById("side_list_div") as HTMLDivElement;

        if (!div) return;

        div.style.display = "none";
        if(this.manager.layer_DefaultBatchLayer)
        {
            this.manager.layer_DefaultBatchLayer.visible = true;
        }

    }

    drawCanvas(): void {

        // this.drawSvgTest();

        if (this.current_room) {
            this.current_room._painter = this.painter;


            this.current_room.drawRoomWithWalls(100);
            for(let win of this.current_room.windows)
            {
                let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(win.rect);
                if(entity)
                {
                    entity.drawEntity(this.painter,{is_draw_figure:true});
                }
            }
            

            this.painter.strokeStyle = "#000";
            this.current_room.drawRoom(7, 0, false, true);


            if (this.current_room.room_shape._feature_shape) {
                let w_poly = this.current_room.room_shape._feature_shape._w_poly;
                w_poly.computeZNor();
                // let w_poly = this.current_room.feature_shapes[0]?._w_poly || null; 
                let room = this.current_room;

                if (w_poly) {
                    for (let ei in w_poly.edges) {
                        this.painter.fillStyle = "#000";
                        this.painter.drawPointRect(w_poly.edges[ei].v0.pos);
                        this.painter.drawText(ei, w_poly.edges[ei].unprojectEdge2d({ x: w_poly.edges[ei].length / 2, y: 150 }), 0, 30);
                    }
                }


            }

            if(this._inner_main_polys && LayoutGraphTestingHander._layer_visible.inner_main_polys.visible)
            {
                this.painter.strokeStyle = "#fa0";
                this.painter.fillPolygons(this._inner_main_polys,0.01);
            }

            if (this._draw_mode === "draw_group_template") {
                if (!this.current_room._group_template_list) {
                    let t_room = this.current_room;
                    t_room._group_template_list = TGroupTemplate.extractGroupTemplates([ ...t_room._furniture_list], t_room.roomname);
                }
                this.current_room._painter = this.painter;
                drawGroupTemplateList(this.current_room, this._visible_zval_range_type);

            }
            else {

                this.current_room.drawFigureElements(null,this._visible_zval_range_type);

            }

            if(this.current_room?._room_entity?._sub_room_areas && LayoutGraphTestingHander._layer_visible.sub_area.visible)
            {
                this.current_room._room_entity._sub_room_areas.forEach((area)=>area.drawEntity(this.painter,{is_draw_figure:true}));
            }


            if(this.current_room?._ceilling_list && LayoutGraphTestingHander._layer_visible.ceilings.visible)
            {
                this.current_room._ceilling_list.forEach((ceiling)=>{
                    let color = "#9AE5A5";
                    if(compareNames([ceiling.sub_category],["客厅"]))
                    {
                        color = "#A5C4D4";
                    }
                    else if(compareNames([ceiling.sub_category],["餐厅"]))
                    {
                        color = "#FFB294";
                    }
                    this.painter.fillStyle = color;
                    this.painter.fillPolygons([ceiling.rect],0.2);

                    this.painter.strokeStyle = color;
                    this.painter.strokePolygons([ceiling.rect]);
                    this.painter.fillStyle = "#333";

                    this.painter.drawText(ceiling.sub_category,ceiling.rect.rect_center,0,60);
                });
            }

            if(this.current_room && this.current_room.roomname.includes("卫生间"))
            {
                if(this._inner_grid_polys)
                {
                    this.painter._context.lineWidth = 2;
                    this.painter.strokeStyle = "#f00";
                    this.painter.fillStyle = "#2222bb";
                    this.painter.fillPolygons(this._inner_grid_polys,0.05);
                    this.painter.strokePolygons(this._inner_grid_polys);
                }

                if(this.current_room._group_template_list)
                {
                    let target_names = ["马桶","浴室柜","淋浴房"];

                    let target_elements = this.current_room._group_template_list.filter((ele)=>compareNames([ele?.category,ele?.group_space_category],target_names));
                    target_elements.forEach((ele)=>{
                        let front_edge = ele._target_rect.frontEdge;
                        let dim = new ZDistanceDimension(front_edge.v0.pos,front_edge.v1.pos);
                        dim.nor = front_edge.nor;
                        dim.offset_len = 100;
                        this.painter.drawDimension(dim);

                        if(ele.group_space_category === "一字形淋浴房")
                        {
                            let center =  ele._target_rect.frontEdge.center;
                            let nor =  ele._target_rect.nor;
                            let int_p = this.current_room.room_shape._poly.getRayIntersection(center,nor);
                            if(int_p)
                            {
                                let dim1 = new ZDistanceDimension(center,int_p.point);
                                dim1.nor =  ele._target_rect.dv;
                                dim1.offset_len = 200;
                                this.painter.drawDimension(dim1);
                            }
                        }
                    })

                }
            }
            else{
                if(this._inner_cabinet_polys && LayoutGraphTestingHander._layer_visible.cabinet_rects.visible)
                {
                    this.painter.strokeStyle = "#a00";
                    this.painter.strokePolygons(this._inner_cabinet_polys);
                }
    
                if(this._inner_main_polys && LayoutGraphTestingHander._layer_visible.inner_main_polys.visible)
                {
                    this.painter.strokeStyle = "#fa0";


                    this.painter.strokePolygons(this._inner_main_polys);

                    this._inner_main_polys.forEach((poly)=>{
                        poly.edges.forEach((edge)=>{
                            let dim = makeDimensionsOfEdge(edge);
                            dim.offset_len = -100;
                            this.painter.drawDimension(dim);
                        })
                    })
                }
    
            }

            if(LayoutGraphTestingHander._valid_polys.length > 0 &&  LayoutGraphTestingHander._layer_visible.valid_polys.visible)
            {
                LayoutGraphTestingHander._valid_polys.forEach((poly)=>{
                    this.painter.strokeStyle = "#668B8B";
                    this.painter.fillStyle = "#668B8B";
                    this.painter.fillPolygons([poly.clone()],0.01);
                    this.painter.strokePolygons([poly.clone()]);
                })
            }
  
        }
    }

    handleEvent(evt_name: string, evt_param: any): void {
        if(evt_name === TrainingTestingEvents.TestingDatasetListOnRoomLoaded)
        {
            this.loadLocal();
        }
    }
    onmousemove(ev: I_MouseEvent): void {
        super.onmousemove(ev);
        if (this.manager._moving_canvas_enabled === false) {
            this._moving_count++;
            if(this._moving_count > 3)
            {
                this.optimizeGroupTemplates();
                // this.updateSideList();
                this._moving_count = 0;
            }
        }
    }
    onmouseup(ev: I_MouseEvent): void {
        if(this.manager._moving_canvas_enabled === false)
        {
            this.optimizeGroupTemplates();
            this.updateSideList();
        }
        super.onmouseup(ev);
    }

    onkeydown(ev: KeyboardEvent): boolean {

        if (ev.key == 'p') {
            this.updateSideList();
        }
        else if(ev.key === 'a' || ev.key==="A")
        {
            this.post_adjust_elements();
        }
        else if (ev.key === 'q') {
            this.optimizeGroupTemplates();
        }
        else if (ev.key == 'm' || ev.key==="M") {
            if (this._draw_mode === "draw_funitures") {
                this._draw_mode = "draw_group_template";
            }
            else {
                this._draw_mode = "draw_funitures";
            }
            this.update();
        }
        else if(ev.key ==='1' && ev.ctrlKey)
        {
            LayoutAI_App.emit_M(TrainingTestingSignals.ShowTestingDatasetListPanel,null);
            ev.preventDefault();
        }
        return true;
    }

    post_adjust_elements()
    {
        if(this.current_room)
        {
            TPostFigureElementAdjust.PostAdjustElements(this.current_room,this.current_room._furniture_list);
            this.update();
        }
    }
}

function updateLayoutSpaceAreaByPath(room: TRoom, resultScheme: TRoomLayoutScheme)
{
    // TODO 这个直接进行更改修正这一系列的数据\
    let debugData: any = resultScheme._debug_data;
    if(!debugData) return;
    let livingSpaceRect: ZRect = debugData.livingSpace;
    let diningSpaceRect: ZRect = debugData.diningSpace;
    let hallwayRects: ZRect[] = debugData.hallwaySpace;
    let livingRanges: I_Range2D[] = [TBaseRoomToolUtil.instance.getRange2dByPolygon(livingSpaceRect)];
    let diningRanges: I_Range2D[] = [TBaseRoomToolUtil.instance.getRange2dByPolygon(diningSpaceRect)];
    let hallwayRanges: I_Range2D[] = [];
    if(hallwayRects)
    {
        hallwayRects.map((rect) => { return TBaseRoomToolUtil.instance.getRange2dByPolygon(rect) });
    }
    let spaceInfo: any = {
        "livingSpace": livingRanges,
        "diningSpace": diningRanges,
        "hallwaySpace": hallwayRanges,
    };
    // 这里将户型数据直接更新到户型数据中，内部就不在需要进行户型数据的计算
    room._room_entity.updateSpaceLivingInfo({force_auto_sub_area:true}, spaceInfo);

    TPostLayoutCeiling.instance.postAddCeilingFigures(room);
}

function drawGroupTemplateList(room: TRoom, figureZVvalRangeType: number) {
    let groupTemplateList: TGroupTemplate[] = room._group_template_list;
    let painter = room._painter;
    if (!groupTemplateList || !painter)
    {
        return;
    }

    for (let group_template of groupTemplateList) {
        let rect = group_template._target_rect;

        let config = group_template.getFigureGroupConfigs(room.roomname);
        if (config) {
            let z_val_range_type = config.zval_range_type || FigureZValRangeType.All;
            if ((z_val_range_type & figureZVvalRangeType as number) == 0) {
                continue;
            }
        }

        painter.strokeStyle = "#000";
        painter.fillStyle = getColorByGroupSpaceCategory(group_template?.group_space_category);
        painter.strokePolygons([rect]);
        painter.fillPolygons([rect], 0.05);


        let center = rect.rect_center;
        painter.fillStyle = "#000";
        let text = group_template?.group_space_category || group_template?.seed_figure_group?.group_space_category || group_template?.seed_figure_group?.group_code || "";
        if (text.length > 4) {
            text = text.substring(0, text.length - 4) + "\n" + text.substring(text.length - 4);
        }
        if (text.indexOf("收口板") >= 0) {
            continue;
        }
        painter.drawText(text, center);

        painter.strokeStyle = "#aaa";
        painter.fillStyle = "#aaa";
        if(group_template._target_rect)
        {
            if(group_template._target_rect.min_hh > 400)
            {
                let edges = [group_template._target_rect.frontEdge,group_template._target_rect.leftEdge];
                let dims = edges.map((e)=>{let dim = makeDimensionsOfEdge(e); dim.offset_len = -100;return dim;});
                dims.forEach((dim)=>painter.drawDimension(dim));
            }

        }
        
    }
}

function getColorByGroupSpaceCategory(groupSpaceCategory: string): string
{
    let colorInfo: Map<string, string> = new Map();
    colorInfo.set("电视柜区", "#EEE8AA"); // 淡金色
    colorInfo.set("沙发组合区", "#98FB98"); // 淡绿色
    colorInfo.set("沙发背景墙", "#2E8B57"); // 深绿色
    colorInfo.set("玄关柜区", "#FFD700"); // 金色
    colorInfo.set("地毯区", "#40E0D0"); // 淡青色
    colorInfo.set("餐边柜区", "#FFD700"); // 金色
    colorInfo.set("餐桌区", "#FFA07A"); // 浅红色
    colorInfo.set("卧室-衣柜区", "#FF4500"); // 橙色
    colorInfo.set("卧床区", "#FF69B4"); // 粉色
    colorInfo.set("梳妆台区", "#8B008B"); // 紫色
    colorInfo.set("书桌区", "#8B008B"); // 紫色
    colorInfo.set("书柜区", "#8B008B"); // 紫色
    
    return colorInfo.get(groupSpaceCategory) || "#fff"; // 默认黑色
}