import { ZPolygon } from "@layoutai/z_polygon";

import { TRoomEntity } from "../../Layout/TLayoutEntities/TRoomEntity";
import { ILayoutRoom } from "./ILayoutRoom";


/**
* @description 布局房间实现，用于桥接旧的房间实体
* <AUTHOR>
* @date 2025-07-28
* @lastEditTime 2025-07-28 09:55:16
* @lastEditors xuld
*/
export class LayoutRoom implements ILayoutRoom {
    private _roomEntity: TRoomEntity;
    constructor(room: TRoomEntity) {
        this._roomEntity = room;
    }

    public get roomPoly(): ZPolygon {
        return this._roomEntity._room_poly;
    }

    public get featurePoly(): ZPolygon {
        return this._roomEntity._room.room_shape._feature_shape._w_poly;
    }

    public get name(): string {
        return this._roomEntity.name;
    }

    public get roomEntity(): TRoomEntity {
        return this._roomEntity;
    }
}