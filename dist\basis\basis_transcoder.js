var BASIS=function(){var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!=typeof __filename&&(e=e||__filename),function(r){var t,n,o=void 0!==(r=r||{})?r:{};o.ready=new Promise(function(e,r){t=e,n=r});var i,a={};for(i in o)o.hasOwnProperty(i)&&(a[i]=o[i]);var u,s,c,f,l=[];u="object"==typeof window,s="function"==typeof importScripts,c="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,f=!u&&!c&&!s;var p,d,h,v,y="";c?(y=s?require("path").dirname(y)+"/":__dirname+"/",p=function(e,r){return h||(h=require("fs")),v||(v=require("path")),e=v.normalize(e),h.readFileSync(e,r?null:"utf8")},d=function(e){var r=p(e,!0);return r.buffer||(r=new Uint8Array(r)),C(r.buffer),r},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),l=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof Vr))throw e}),process.on("unhandledRejection",ee),o.inspect=function(){return"[Emscripten Module object]"}):f?("undefined"!=typeof read&&(p=function(e){return read(e)}),d=function(e){var r;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(C("object"==typeof(r=read(e,"binary"))),r)},"undefined"!=typeof scriptArgs?l=scriptArgs:void 0!==arguments&&(l=arguments),"function"==typeof quit&&function(e){quit(e)},"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(u||s)&&(s?y=self.location.href:"undefined"!=typeof document&&document.currentScript&&(y=document.currentScript.src),e&&(y=e),y=0!==y.indexOf("blob:")?y.substr(0,y.lastIndexOf("/")+1):"",p=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},s&&(d=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}));var m=o.print||console.log.bind(console),g=o.printErr||console.warn.bind(console);for(i in a)a.hasOwnProperty(i)&&(o[i]=a[i]);a=null,o.arguments&&(l=o.arguments),o.thisProgram&&o.thisProgram,o.quit&&o.quit;var w,b;o.wasmBinary&&(w=o.wasmBinary),o.noExitRuntime&&o.noExitRuntime,"object"!=typeof WebAssembly&&ee("no native wasm support detected");var T=!1;function C(e,r){e||ee("Assertion failed: "+r)}var $="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function P(e,r,t){for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.subarray&&$)return $.decode(e.subarray(r,o));for(var i="";r<o;){var a=e[r++];if(128&a){var u=63&e[r++];if(192!=(224&a)){var s=63&e[r++];if((a=224==(240&a)?(15&a)<<12|u<<6|s:(7&a)<<18|u<<12|s<<6|63&e[r++])<65536)i+=String.fromCharCode(a);else{var c=a-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&a)<<6|u)}else i+=String.fromCharCode(a)}return i}function A(e,r){return e?P(W,e,r):""}var _,S,W,E,F,k,O,j,R,x="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function I(e,r){for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&F[n];)++n;if((t=n<<1)-e>32&&x)return x.decode(W.subarray(e,t));for(var i="",a=0;!(a>=r/2);++a){var u=E[e+2*a>>1];if(0==u)break;i+=String.fromCharCode(u)}return i}function D(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);E[r>>1]=a,r+=2}return E[r>>1]=0,r-n}function U(e){return 2*e.length}function B(e,r){for(var t=0,n="";!(t>=r/4);){var o=k[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function V(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,o=n+t-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i);if(k[r>>2]=a,(r+=4)+4>o)break}return k[r>>2]=0,r-n}function M(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r}function q(e,r){return e%r>0&&(e+=r-e%r),e}function H(e){_=e,o.HEAP8=S=new Int8Array(e),o.HEAP16=E=new Int16Array(e),o.HEAP32=k=new Int32Array(e),o.HEAPU8=W=new Uint8Array(e),o.HEAPU16=F=new Uint16Array(e),o.HEAPU32=O=new Uint32Array(e),o.HEAPF32=j=new Float32Array(e),o.HEAPF64=R=new Float64Array(e)}o.INITIAL_MEMORY;var z,N=[],G=[],L=[],J=[];function X(e){N.unshift(e)}function K(e){J.unshift(e)}var Q=0,Y=null,Z=null;function ee(e){o.onAbort&&o.onAbort(e),g(e+=""),T=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(e);throw n(r),r}function re(e,r){return String.prototype.startsWith?e.startsWith(r):0===e.indexOf(r)}o.preloadedImages={},o.preloadedAudios={};function te(e){return re(e,"data:application/octet-stream;base64,")}function ne(e){return re(e,"file://")}var oe,ie="basis_transcoder.wasm";function ae(){try{if(w)return new Uint8Array(w);if(d)return d(ie);throw"both async and sync fetching of the wasm failed"}catch(e){ee(e)}}function ue(e){for(;e.length>0;){var r=e.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?z.get(t)():z.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(o)}}te(ie)||(oe=ie,ie=o.locateFile?o.locateFile(oe,y):y+oe);var se={};function ce(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function fe(e){return this.fromWireType(O[e>>2])}var le={},pe={},de={};function he(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=48&&r<=57?"_"+e:e}function ve(e,r){return e=he(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function ye(e,r){var t=ve(r,function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))});return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var me=void 0;function ge(e){throw new me(e)}function we(e,r,t){function n(r){var n=t(r);n.length!==e.length&&ge("Mismatched type converter count");for(var o=0;o<e.length;++o)Ae(e[o],n[o])}e.forEach(function(e){de[e]=r});var o=new Array(r.length),i=[],a=0;r.forEach(function(e,r){pe.hasOwnProperty(e)?o[r]=pe[e]:(i.push(e),le.hasOwnProperty(e)||(le[e]=[]),le[e].push(function(){o[r]=pe[e],++a===i.length&&n(o)}))}),0===i.length&&n(o)}function be(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Te=void 0;function Ce(e){for(var r="",t=e;W[t];)r+=Te[W[t++]];return r}var $e=void 0;function Pe(e){throw new $e(e)}function Ae(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||Pe('type "'+n+'" must have a positive integer typeid pointer'),pe.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;Pe("Cannot register type '"+n+"' twice")}if(pe[e]=r,delete de[e],le.hasOwnProperty(e)){var o=le[e];delete le[e],o.forEach(function(e){e()})}}function _e(e){if(!(this instanceof Be))return!1;if(!(e instanceof Be))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o}function Se(e){Pe(e.$$.ptrType.registeredClass.name+" instance already deleted")}var We=!1;function Ee(e){}function Fe(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function ke(e){return"undefined"==typeof FinalizationGroup?(ke=function(e){return e},e):(We=new FinalizationGroup(function(e){for(var r=e.next();!r.done;r=e.next()){var t=r.value;t.ptr?Fe(t):console.warn("object already deleted: "+t.ptr)}}),ke=function(e){return We.register(e,e.$$,e.$$),e},Ee=function(e){We.unregister(e.$$)},ke(e))}function Oe(){if(this.$$.ptr||Se(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=ke(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r}function je(){this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Pe("Object already scheduled for deletion"),Ee(this),Fe(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Re(){return!this.$$.ptr}var xe=void 0,Ie=[];function De(){for(;Ie.length;){var e=Ie.pop();e.$$.deleteScheduled=!1,e.delete()}}function Ue(){return this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Pe("Object already scheduled for deletion"),Ie.push(this),1===Ie.length&&xe&&xe(De),this.$$.deleteScheduled=!0,this}function Be(){}var Ve={};function Me(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||Pe("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}function qe(e,r,t){o.hasOwnProperty(e)?((void 0===t||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[t])&&Pe("Cannot register public name '"+e+"' twice"),Me(o,e,e),o.hasOwnProperty(t)&&Pe("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),o[e].overloadTable[t]=r):(o[e]=r,void 0!==t&&(o[e].numArguments=t))}function He(e,r,t,n,o,i,a,u){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=u,this.pureVirtualFunctions=[]}function ze(e,r,t){for(;r!==t;)r.upcast||Pe("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function Ne(e,r){if(null===r)return this.isReference&&Pe("null is not a valid "+this.name),0;r.$$||Pe('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return ze(r.$$.ptr,t,this.registeredClass)}function Ge(e,r){var t;if(null===r)return this.isReference&&Pe("null is not a valid "+this.name),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||Pe('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&Pe("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);var n=r.$$.ptrType.registeredClass;if(t=ze(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&Pe("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:Pe("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,br(function(){o.delete()})),null!==e&&e.push(this.rawDestructor,t)}break;default:Pe("Unsupporting sharing policy")}return t}function Le(e,r){if(null===r)return this.isReference&&Pe("null is not a valid "+this.name),0;r.$$||Pe('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||Pe("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&Pe("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return ze(r.$$.ptr,t,this.registeredClass)}function Je(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function Xe(e){this.rawDestructor&&this.rawDestructor(e)}function Ke(e){null!==e&&e.delete()}function Qe(e,r,t){if(r===t)return e;if(void 0===t.baseClass)return null;var n=Qe(e,r,t.baseClass);return null===n?null:t.downcast(n)}function Ye(){return Object.keys(rr).length}function Ze(){var e=[];for(var r in rr)rr.hasOwnProperty(r)&&e.push(rr[r]);return e}function er(e){xe=e,Ie.length&&xe&&xe(De)}var rr={};function tr(e,r){return r=function(e,r){for(void 0===r&&Pe("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}(e,r),rr[r]}function nr(e,r){return r.ptrType&&r.ptr||ge("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&ge("Both smartPtrType and smartPtr must be specified"),r.count={value:1},ke(Object.create(e,{$$:{value:r}}))}function or(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=tr(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?nr(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):nr(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(r),u=Ve[a];if(!u)return o.call(this);i=this.isConst?u.constPointerType:u.pointerType;var s=Qe(r,this.registeredClass,i.registeredClass);return null===s?o.call(this):this.isSmartPointer?nr(i.registeredClass.instancePrototype,{ptrType:i,ptr:s,smartPtrType:this,smartPtr:e}):nr(i.registeredClass.instancePrototype,{ptrType:i,ptr:s})}function ir(e,r,t,n,o,i,a,u,s,c,f){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=u,this.rawConstructor=s,this.rawShare=c,this.rawDestructor=f,o||void 0!==r.baseClass?this.toWireType=Ge:n?(this.toWireType=Ne,this.destructorFunction=null):(this.toWireType=Le,this.destructorFunction=null)}function ar(e,r,t){o.hasOwnProperty(e)||ge("Replacing nonexistant public symbol"),void 0!==o[e].overloadTable&&void 0!==t?o[e].overloadTable[t]=r:(o[e]=r,o[e].argCount=t)}function ur(e,r,t){return-1!=e.indexOf("j")?function(e,r,t){return t&&t.length?o["dynCall_"+e].apply(null,[r].concat(t)):o["dynCall_"+e].call(null,r)}(e,r,t):z.get(r).apply(null,t)}function sr(e,r){var t=-1!=(e=Ce(e)).indexOf("j")?function(e,r){C(e.indexOf("j")>=0,"getDynCaller should only be called with i64 sigs");var t=[];return function(){t.length=arguments.length;for(var n=0;n<arguments.length;n++)t[n]=arguments[n];return ur(e,r,t)}}(e,r):z.get(r);return"function"!=typeof t&&Pe("unknown function pointer with signature "+e+": "+r),t}var cr=void 0;function fr(e){var r=Br(e),t=Ce(r);return Ur(r),t}function lr(e,r){var t=[],n={};throw r.forEach(function e(r){n[r]||pe[r]||(de[r]?de[r].forEach(e):(t.push(r),n[r]=!0))}),new cr(e+": "+t.map(fr).join([", "]))}function pr(e,r){for(var t=[],n=0;n<e;n++)t.push(k[(r>>2)+n]);return t}function dr(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=ve(e.name||"unknownFunctionName",function(){});t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function hr(e,r,t,n,o){var i=r.length;i<2&&Pe("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==r[1]&&null!==t,u=!1,s=1;s<r.length;++s)if(null!==r[s]&&void 0===r[s].destructorFunction){u=!0;break}var c="void"!==r[0].name,f="",l="";for(s=0;s<i-2;++s)f+=(0!==s?", ":"")+"arg"+s,l+=(0!==s?", ":"")+"arg"+s+"Wired";var p="return function "+he(e)+"("+f+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[Pe,n,o,ce,r[0],r[1]];a&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(s=0;s<i-2;++s)p+="var arg"+s+"Wired = argType"+s+".toWireType("+d+", arg"+s+"); // "+r[s+2].name+"\n",h.push("argType"+s),v.push(r[s+2]);if(a&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(s=a?1:2;s<r.length;++s){var y=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==r[s].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[s].name+"\n",h.push(y+"_dtor"),v.push(r[s].destructorFunction))}return c&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),dr(Function,h).apply(null,v)}var vr=[],yr=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function mr(e){e>4&&0===--yr[e].refcount&&(yr[e]=void 0,vr.push(e))}function gr(){for(var e=0,r=5;r<yr.length;++r)void 0!==yr[r]&&++e;return e}function wr(){for(var e=5;e<yr.length;++e)if(void 0!==yr[e])return yr[e];return null}function br(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=vr.length?vr.pop():yr.length;return yr[r]={refcount:1,value:e},r}}function Tr(e,r,t){switch(r){case 0:return function(e){var r=t?S:W;return this.fromWireType(r[e])};case 1:return function(e){var r=t?E:F;return this.fromWireType(r[e>>1])};case 2:return function(e){var r=t?k:O;return this.fromWireType(r[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function Cr(e,r){var t=pe[e];return void 0===t&&Pe(r+" has unknown type "+fr(e)),t}function $r(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function Pr(e,r){switch(r){case 2:return function(e){return this.fromWireType(j[e>>2])};case 3:return function(e){return this.fromWireType(R[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Ar(e,r,t){switch(r){case 0:return t?function(e){return S[e]}:function(e){return W[e]};case 1:return t?function(e){return E[e>>1]}:function(e){return F[e>>1]};case 2:return t?function(e){return k[e>>2]}:function(e){return O[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function _r(e){return e||Pe("Cannot use deleted val. handle = "+e),yr[e].value}var Sr={};function Wr(e){var r=Sr[e];return void 0===r?Ce(e):r}var Er=[];function Fr(){return"object"==typeof globalThis?globalThis:Function("return this")()}var kr={};function Or(e){try{return b.grow(e-_.byteLength+65535>>>16),H(b.buffer),1}catch(e){}}var jr={mappings:{},buffers:[null,[],[]],printChar:function(e,r){var t=jr.buffers[e];0===r||10===r?((1===e?m:g)(P(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return jr.varargs+=4,k[jr.varargs-4>>2]},getStr:function(e){return A(e)},get64:function(e,r){return e}};me=o.InternalError=ye(Error,"InternalError"),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);Te=e}(),$e=o.BindingError=ye(Error,"BindingError"),Be.prototype.isAliasOf=_e,Be.prototype.clone=Oe,Be.prototype.delete=je,Be.prototype.isDeleted=Re,Be.prototype.deleteLater=Ue,ir.prototype.getPointee=Je,ir.prototype.destructor=Xe,ir.prototype.argPackAdvance=8,ir.prototype.readValueFromPointer=fe,ir.prototype.deleteObject=Ke,ir.prototype.fromWireType=or,o.getInheritedInstanceCount=Ye,o.getLiveInheritedInstances=Ze,o.flushPendingDeletes=De,o.setDelayFunction=er,cr=o.UnboundTypeError=ye(Error,"UnboundTypeError"),o.count_emval_handles=gr,o.get_first_emval=wr,G.push({func:function(){Ir()}});var Rr,xr={t:function(e){var r=se[e];delete se[e];var t=r.rawConstructor,n=r.rawDestructor,o=r.fields;we([e],o.map(function(e){return e.getterReturnType}).concat(o.map(function(e){return e.setterArgumentType})),function(e){var i={};return o.forEach(function(r,t){var n=r.fieldName,a=e[t],u=r.getter,s=r.getterContext,c=e[t+o.length],f=r.setter,l=r.setterContext;i[n]={read:function(e){return a.fromWireType(u(s,e))},write:function(e,r){var t=[];f(l,e,c.toWireType(t,r)),ce(t)}}}),[{name:r.name,fromWireType:function(e){var r={};for(var t in i)r[t]=i[t].read(e);return n(e),r},toWireType:function(e,r){for(var o in i)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var a=t();for(o in i)i[o].write(a,r[o]);return null!==e&&e.push(n,a),a},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:n}]})},I:function(e,r,t,n,o){var i=be(t);Ae(e,{name:r=Ce(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===t)n=S;else if(2===t)n=E;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);n=k}return this.fromWireType(n[e>>i])},destructorFunction:null})},x:function(e,r,t,n,o,i,a,u,s,c,f,l,p){f=Ce(f),i=sr(o,i),u&&(u=sr(a,u)),c&&(c=sr(s,c)),p=sr(l,p);var d=he(f);qe(d,function(){lr("Cannot construct "+f+" due to unbound types",[n])}),we([e,r,t],n?[n]:[],function(r){var t,o;r=r[0],o=n?(t=r.registeredClass).instancePrototype:Be.prototype;var a=ve(d,function(){if(Object.getPrototypeOf(this)!==s)throw new $e("Use 'new' to construct "+f);if(void 0===l.constructor_body)throw new $e(f+" has no accessible constructor");var e=l.constructor_body[arguments.length];if(void 0===e)throw new $e("Tried to invoke ctor of "+f+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(l.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),s=Object.create(o,{constructor:{value:a}});a.prototype=s;var l=new He(f,a,s,p,t,i,u,c),h=new ir(f,l,!0,!1,!1),v=new ir(f+"*",l,!1,!1,!1),y=new ir(f+" const*",l,!1,!0,!1);return Ve[e]={pointerType:v,constPointerType:y},ar(d,a),[h,v,y]})},w:function(e,r,t,n,o,i){C(r>0);var a=pr(r,t);o=sr(n,o);var u=[i],s=[];we([],[e],function(e){var t="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new $e("Cannot register multiple constructors with identical number of parameters ("+(r-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[r-1]=function(){lr("Cannot construct "+e.name+" due to unbound types",a)},we([],a,function(n){return e.registeredClass.constructor_body[r-1]=function(){arguments.length!==r-1&&Pe(t+" called with "+arguments.length+" arguments, expected "+(r-1)),s.length=0,u.length=r;for(var e=1;e<r;++e)u[e]=n[e].toWireType(s,arguments[e-1]);var i=o.apply(null,u);return ce(s),n[0].fromWireType(i)},[]}),[]})},d:function(e,r,t,n,o,i,a,u){var s=pr(t,n);r=Ce(r),i=sr(o,i),we([],[e],function(e){var n=(e=e[0]).name+"."+r;function o(){lr("Cannot call "+n+" due to unbound types",s)}u&&e.registeredClass.pureVirtualFunctions.push(r);var c=e.registeredClass.instancePrototype,f=c[r];return void 0===f||void 0===f.overloadTable&&f.className!==e.name&&f.argCount===t-2?(o.argCount=t-2,o.className=e.name,c[r]=o):(Me(c,r,n),c[r].overloadTable[t-2]=o),we([],s,function(o){var u=hr(n,o,e,i,a);return void 0===c[r].overloadTable?(u.argCount=t-2,c[r]=u):c[r].overloadTable[t-2]=u,[]}),[]})},k:function(e,r,t){e=Ce(e),we([],[r],function(r){return r=r[0],o[e]=r.fromWireType(t),[]})},H:function(e,r){Ae(e,{name:r=Ce(r),fromWireType:function(e){var r=yr[e].value;return mr(e),r},toWireType:function(e,r){return br(r)},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:null})},n:function(e,r,t,n){var o=be(t);function i(){}r=Ce(r),i.values={},Ae(e,{name:r,constructor:i,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,r){return r.value},argPackAdvance:8,readValueFromPointer:Tr(r,o,n),destructorFunction:null}),qe(r,i)},a:function(e,r,t){var n=Cr(e,"enum");r=Ce(r);var o=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:ve(n.name+"_"+r,function(){})}});o.values[t]=i,o[r]=i},A:function(e,r,t){var n=be(t);Ae(e,{name:r=Ce(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+$r(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Pr(r,n),destructorFunction:null})},i:function(e,r,t,n,o,i){var a=pr(r,t);e=Ce(e),o=sr(n,o),qe(e,function(){lr("Cannot call "+e+" due to unbound types",a)},r-1),we([],a,function(t){var n=[t[0],null].concat(t.slice(1));return ar(e,hr(e,n,null,o,i),r-1),[]})},j:function(e,r,t,n,o){r=Ce(r),-1===o&&(o=4294967295);var i=be(t),a=function(e){return e};if(0===n){var u=32-8*t;a=function(e){return e<<u>>>u}}var s=-1!=r.indexOf("unsigned");Ae(e,{name:r,fromWireType:a,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+$r(t)+'" to '+this.name);if(t<n||t>o)throw new TypeError('Passing a number "'+$r(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+n+", "+o+"]!");return s?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Ar(r,i,0!==n),destructorFunction:null})},h:function(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=O,t=r[e>>=2],o=r[e+1];return new n(_,o,t)}Ae(e,{name:t=Ce(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},B:function(e,r){var t="std::string"===(r=Ce(r));Ae(e,{name:r,fromWireType:function(e){var r,n=O[e>>2];if(t)for(var o=e+4,i=0;i<=n;++i){var a=e+4+i;if(i==n||0==W[a]){var u=A(o,a-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=a+1}}else{var s=new Array(n);for(i=0;i<n;++i)s[i]=String.fromCharCode(W[e+4+i]);r=s.join("")}return Ur(e),r},toWireType:function(e,r){var n;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Pe("Cannot pass non-string to std::string"),n=t&&o?function(){return function(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}(r)}:function(){return r.length};var i=n(),a=Dr(4+i+1);if(O[a>>2]=i,t&&o)(function(e,r,t,n){if(!(n>0))return 0;for(var o=t,i=t+n-1,a=0;a<e.length;++a){var u=e.charCodeAt(a);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++a)),u<=127){if(t>=i)break;r[t++]=u}else if(u<=2047){if(t+1>=i)break;r[t++]=192|u>>6,r[t++]=128|63&u}else if(u<=65535){if(t+2>=i)break;r[t++]=224|u>>12,r[t++]=128|u>>6&63,r[t++]=128|63&u}else{if(t+3>=i)break;r[t++]=240|u>>18,r[t++]=128|u>>12&63,r[t++]=128|u>>6&63,r[t++]=128|63&u}}r[t]=0})(r,W,a+4,i+1);else if(o)for(var u=0;u<i;++u){var s=r.charCodeAt(u);s>255&&(Ur(a),Pe("String has UTF-16 code units that do not fit in 8 bits")),W[a+4+u]=s}else for(u=0;u<i;++u)W[a+4+u]=r[u];return null!==e&&e.push(Ur,a),a},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:function(e){Ur(e)}})},v:function(e,r,t){var n,o,i,a,u;t=Ce(t),2===r?(n=I,o=D,a=U,i=function(){return F},u=1):4===r&&(n=B,o=V,a=M,i=function(){return O},u=2),Ae(e,{name:t,fromWireType:function(e){for(var t,o=O[e>>2],a=i(),s=e+4,c=0;c<=o;++c){var f=e+4+c*r;if(c==o||0==a[f>>u]){var l=n(s,f-s);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),s=f+r}}return Ur(e),t},toWireType:function(e,n){"string"!=typeof n&&Pe("Cannot pass non-string to C++ string type "+t);var i=a(n),s=Dr(4+i+r);return O[s>>2]=i>>u,o(n,s+4,i+r),null!==e&&e.push(Ur,s),s},argPackAdvance:8,readValueFromPointer:fe,destructorFunction:function(e){Ur(e)}})},u:function(e,r,t,n,o,i){se[e]={name:Ce(r),rawConstructor:sr(t,n),rawDestructor:sr(o,i),fields:[]}},c:function(e,r,t,n,o,i,a,u,s,c){se[e].fields.push({fieldName:Ce(r),getterReturnType:t,getter:sr(n,o),getterContext:i,setterArgumentType:a,setter:sr(u,s),setterContext:c})},J:function(e,r){Ae(e,{isVoid:!0,name:r=Ce(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},m:function(e,r,t){e=_r(e),r=Cr(r,"emval::as");var n=[],o=br(n);return k[t>>2]=o,r.toWireType(n,e)},s:function(e,r,t,n){(e=Er[e])(r=_r(r),t=Wr(t),null,n)},b:mr,y:function(e){return 0===e?br(Fr()):(e=Wr(e),br(Fr()[e]))},p:function(e,r){for(var t=function(e,r){for(var t=new Array(e),n=0;n<e;++n)t[n]=Cr(k[(r>>2)+n],"parameter "+n);return t}(e,r),n=t[0],o=n.name+"_$"+t.slice(1).map(function(e){return e.name}).join("_")+"$",i=["retType"],a=[n],u="",s=0;s<e-1;++s)u+=(0!==s?", ":"")+"arg"+s,i.push("argType"+s),a.push(t[1+s]);var c="return function "+he("methodCaller_"+o)+"(handle, name, destructors, args) {\n",f=0;for(s=0;s<e-1;++s)c+="    var arg"+s+" = argType"+s+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=t[s+1].argPackAdvance;for(c+="    var rv = handle[name]("+u+");\n",s=0;s<e-1;++s)t[s+1].deleteObject&&(c+="    argType"+s+".deleteObject(arg"+s+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",i.push(c);var l,p,d=dr(Function,i).apply(null,a);return l=d,p=Er.length,Er.push(l),p},r:function(e){return e=Wr(e),br(o[e])},e:function(e,r){return br((e=_r(e))[r=_r(r)])},g:function(e){e>4&&(yr[e].refcount+=1)},q:function(e,r,t,n){e=_r(e);var i=kr[r];return i||(i=function(e){for(var r="",t=0;t<e;++t)r+=(0!==t?", ":"")+"arg"+t;var n="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(t=0;t<e;++t)n+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return n+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",n)(Cr,o,br)}(r),kr[r]=i),i(e,t,n)},f:function(e){return br(Wr(e))},l:function(e){ce(yr[e].value),mr(e)},o:function(){ee()},E:function(e,r,t){W.copyWithin(e,r,r+t)},F:function(e){e>>>=0;var r=W.length,t=2147483648;if(e>t)return!1;for(var n=1;n<=4;n*=2){var o=r*(1+.2/n);if(o=Math.min(o,e+100663296),Or(Math.min(t,q(Math.max(16777216,e,o),65536))))return!0}return!1},G:function(e){return 0},C:function(e,r,t,n,o){},z:function(e,r,t,n){for(var o=0,i=0;i<t;i++){for(var a=k[r+8*i>>2],u=k[r+(8*i+4)>>2],s=0;s<u;s++)jr.printChar(e,W[a+s]);o+=u}return k[n>>2]=o,0},D:function(e){0}},Ir=(function(){var e={a:xr};function r(e,r){var t=e.exports;o.asm=t,H((b=o.asm.K).buffer),z=o.asm.L,function(){if(Q--,o.monitorRunDependencies&&o.monitorRunDependencies(Q),0==Q&&(null!==Y&&(clearInterval(Y),Y=null),Z)){var e=Z;Z=null,e()}}()}function t(e){r(e.instance)}function i(r){return(w||!u&&!s||"function"!=typeof fetch||ne(ie)?Promise.resolve().then(ae):fetch(ie,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+ie+"'";return e.arrayBuffer()}).catch(function(){return ae()})).then(function(r){return WebAssembly.instantiate(r,e)}).then(r,function(e){g("failed to asynchronously prepare wasm: "+e),ee(e)})}if(Q++,o.monitorRunDependencies&&o.monitorRunDependencies(Q),o.instantiateWasm)try{return o.instantiateWasm(e,r)}catch(e){return g("Module.instantiateWasm callback failed with error: "+e),!1}(w||"function"!=typeof WebAssembly.instantiateStreaming||te(ie)||ne(ie)||"function"!=typeof fetch?i(t):fetch(ie,{credentials:"same-origin"}).then(function(r){return WebAssembly.instantiateStreaming(r,e).then(t,function(e){return g("wasm streaming compile failed: "+e),g("falling back to ArrayBuffer instantiation"),i(t)})})).catch(n)}(),o.___wasm_call_ctors=function(){return(Ir=o.___wasm_call_ctors=o.asm.M).apply(null,arguments)}),Dr=o._malloc=function(){return(Dr=o._malloc=o.asm.N).apply(null,arguments)},Ur=o._free=function(){return(Ur=o._free=o.asm.O).apply(null,arguments)},Br=o.___getTypeName=function(){return(Br=o.___getTypeName=o.asm.P).apply(null,arguments)};o.___embind_register_native_and_builtin_types=function(){return(o.___embind_register_native_and_builtin_types=o.asm.Q).apply(null,arguments)},o.dynCall_jiji=function(){return(o.dynCall_jiji=o.asm.R).apply(null,arguments)};function Vr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Mr(e){function r(){Rr||(Rr=!0,o.calledRun=!0,T||(ue(G),ue(L),t(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)K(o.postRun.shift());ue(J)}()))}e=e||l,Q>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)X(o.preRun.shift());ue(N)}(),Q>0||(o.setStatus?(o.setStatus("Running..."),setTimeout(function(){setTimeout(function(){o.setStatus("")},1),r()},1)):r()))}if(Z=function e(){Rr||Mr(),Rr||(Z=e)},o.run=Mr,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return Mr(),r.ready}}();"object"==typeof exports&&"object"==typeof module?module.exports=BASIS:"function"==typeof define&&define.amd?define([],function(){return BASIS}):"object"==typeof exports&&(exports.BASIS=BASIS);