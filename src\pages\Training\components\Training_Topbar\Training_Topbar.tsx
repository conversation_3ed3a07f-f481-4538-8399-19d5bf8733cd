
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { Ta<PERSON>, But<PERSON> } from '@svg/antd';
import { CloseOutlined } from '@ant-design/icons';
import { useMemo } from 'react';
// import type { ITopMenuItem } from '@svg/antd-cloud-design/lib/TopMenu'
import { useEffect, useState } from 'react';
import SunvegaAPI from '@api/clouddesign';
import { APP_ID, isLocal3d } from '@/config';
// import { useNavigate } from 'react-router-dom';
import useStyles from './style';
import { EventName } from '@/Apps/EventSystem';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { useStore } from '@/models';
const { Platform } = SunvegaAPI;

/**
 *  扩展带命令得顶部元素
 */
// interface ICommandTopMenuItem extends ITopMenuItem 
// {
//     command_name ?: string;
//     subList ?: ICommandTopMenuItem[];
// }

type LayoutIssueReportListProps = {
  updateIssueReportVisible: (visible: boolean) => void;
  updateLayoutBatchTestVisible: (visible: boolean) => void;
  updateModelLightConfigVisible: (visible: boolean) => void;
};

const Training_Topbar: React.FC<LayoutIssueReportListProps> = ({ updateIssueReportVisible, updateLayoutBatchTestVisible, updateModelLightConfigVisible }) => {
  // const [title_name, set_titile_name] = useState<string>("训练后台");
  const [activeTabKey, setActiveTabKey] = useState<string>("testFigureGroupBtn");
  const { styles } = useStyles();
  const store = useStore();
  const onCloseButtonClick = () => {
    Platform.Application.closeApp({ appId: APP_ID });
  }

  const OperationsSlot: Record<PositionType, React.ReactNode> = {
    left: <span className='tabs-extra-left-title' style={{ marginLeft: "10px", fontSize: "18px" }}>训练后台</span>,
    right: <Button type="dashed" icon={<CloseOutlined />} style={{ marginRight: "10px" }} className="tabs-extra-right-button" onClick={onCloseButtonClick} ></Button>,
  };
  // const options = ['left', 'right'];
  type PositionType = 'left' | 'right';

  const tabItems = [
    {
      key: "LayoutGraphTesting",
      label: '单空间调试',
      command_name: "LayoutGraphTesting"
    },
    {
      key: "LayoutTaskTesting",
      label: '布局任务测试',
      command_name: "LayoutTaskTesting"
    },
    {
      key: "LayoutSchemeTesting",
      label: '全屋测试(旧)',
      command_name: "LayoutSchemeTesting"
    },
    {
      key: "ModelRoomList",
      label: "布局模板",
      command_name: "ModelRoomList"
    },
    {
      key: "FigureTemplateTesting",
      label: "组合模板",
      command_name: "FigureTemplateTesting"
    },
    {
      key: "LayoutScoreConfig",
      label: "布局评分配置",
      command_name: "LayoutScoreConfig"
    },
    {
      key: "ModelLightConfig",
      label: "灯光模板",
      command_name: "ModelLightConfig"
    },
    // {
    //   key :"QuickTestingHandler",
    //   label :"其他测试",
    //   command_name :"QuickTestingHandler"
    // }
  ];

  if (store.userStore?.userInfo?.tenantId !== "*********") {
    // tabItems.length = 0;
  }



  const [position, setPosition] = useState<PositionType[]>(['left', 'right']);

  const slot = useMemo(() => {
    if (position.length === 0) return null;

    return position.reduce(
      (acc, direction) => ({ ...acc, [direction]: OperationsSlot[direction] }),
      {},
    );
  }, [position]);

  // const navigate = useNavigate();


  // const rightBtnList: ICommandTopMenuItem[] = [
  //   {
  //     id: 'helpBtn',
  //     title: '',
  //     icon: 'iconclose1',
  //     type: 'label'
  //   },
  // ]

  const onTabChange = (key: string) => {
    setActiveTabKey(key);
    const selectedTabItem = tabItems.find(item => item.key === key);
    if (selectedTabItem) {
      LayoutAI_App.RunCommand(selectedTabItem["command_name"]);
    }
    if (key === "LayoutIssueReport") {
      updateIssueReportVisible(true);
    } else {
      updateIssueReportVisible(false);
    }
    if (key === "LayoutBatchTest") {
      updateLayoutBatchTestVisible(true);
    } else {
      updateLayoutBatchTestVisible(false);
    }
    if (key === "ModelLightConfig") {
      updateModelLightConfigVisible(true);
    } else {
      updateModelLightConfigVisible(false);
    }
    LayoutAI_App.instance.update();
  };
  LayoutAI_App.on(EventName.TrainingTabChanged, (c_key: string) => {
    onTabChange(c_key);
  });


  useEffect(() => {
    if ((LayoutAI_App.instance as TAppManagerBase)._current_handler) {
      (LayoutAI_App.instance as TAppManagerBase)._current_handler.enter();
    }

  }, []); // 空数组-> 渲染完成后第一次启动



  return (
    <div className={styles.title_bar}>
      <Tabs rootClassName={styles.tab_root} size="middle" centered defaultActiveKey={activeTabKey} activeKey={activeTabKey} tabBarExtraContent={slot} items={tabItems} onTabClick={onTabChange} />
    </div>
  )
}

export default Training_Topbar;
