import { g_FigureImagePaths } from '@/Apps/LayoutAI/Drawing/FigureImagePaths';
import { I_MaterialMatchingItem } from '@/Apps/LayoutAI/Layout/IMaterialInterface';
import { FigureCategoryManager } from '@/Apps/LayoutAI/Layout/TFigureElements/FigureCategoryManager';
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import Icon from '@/components/Icon/icon';
import NewReplaceProduct from "@/components/NewReplaceProduct/newReplaceProduct";
import { ResourceTipsDetailData } from '@/components/NewReplaceProduct/services/material/ResourceTipsDetailData';
import { AIDeskUrl, checkIsMobile, imgHostUrl } from '@/config';
import { useStore } from '@/models';
import { getMaterialTopViewImage } from '@/services/material';
import { getPrefix } from '@/utils/common';
import { Button } from '@svg/antd';
import { observer } from "mobx-react-lite";
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useStyles from './style';
// const NewReplaceProduct = lazy(() => import('@/components/NewReplaceProduct/newReplaceProduct'));

interface RoomSeriesPlanProps {
  selectedFigureElement: TFigureElement
}
interface Figure {
  imageUrl: string;
  name: string;
  modelId: string;
  length: number;
  width: number;
  height: number;
}
const FloorProperties: React.FC<RoomSeriesPlanProps> = ({ selectedFigureElement }) => {
  const store = useStore();
  const { styles } = useStyles();
  const { t } = useTranslation();
  const [topInfo, setTopInfo] = useState<Figure>({
    imageUrl: '',
    name: '',
    modelId: '',
    length: 0,
    width: 0,
    height: 0,
  });
  const aicabinetRef = useRef(null);
  const [hoverInfo, setHoverInfo] = useState<I_MaterialMatchingItem>({} as I_MaterialMatchingItem);
  const [popoverTop, setPopoverTop] = useState(0);
  const [isShowPopover, setIsShowPopover] = useState(false);
  const [hideTimeoutId, setHideTimeoutId] = useState(null);
  // const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [inputValue, setInputValue] = useState<string>('');
  const [locked, setLocked] = useState<number>(-1);
  const [replacing, setIsReplacing] = useState(false);

  const [materialList, setMaterialList] = useState<I_MaterialMatchingItem[]>(selectedFigureElement?._candidate_materials);
  const divRef = useRef(null);

  const findMaterial = () => {
    const list = selectedFigureElement._candidate_materials.filter((item: any) => {
      return item.name.includes(inputValue);
    });
    setMaterialList(list);
  }

  const openAICabinet = async () => {
    aicabinetRef.current.onModal();
  }

  const openNewReplaceProduct = async () => {
    setIsReplacing(true);
  }

  const handleParams = (params: any) => {
    setTopInfo(params);
  }

  const onClickLock = () => {
    if (selectedFigureElement._room.locked) {
      return;
    }

    if (locked == 1) {
      setLocked(0);
      selectedFigureElement.locked = false;
    } else {
      setLocked(1);
      selectedFigureElement.locked = true;
    }
  }

  useEffect(() => {
    if (selectedFigureElement._matched_material && selectedFigureElement._matched_material.modelId) {
      setTopInfo({
        imageUrl: selectedFigureElement._matched_material.imageUrl,
        name: selectedFigureElement._matched_material?.name,
        modelId: selectedFigureElement._matched_material?.modelId,
        length: selectedFigureElement._matched_material?.targetSize?.length || 0,
        width: selectedFigureElement._matched_material?.targetSize?.width || 0,
        height: selectedFigureElement._matched_material?.targetSize?.height || 0,
      });
    }
    else {
      setTopInfo({
        imageUrl: g_FigureImagePaths[selectedFigureElement.sub_category]?.img_path || getPrefix() + 'https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg',
        name: selectedFigureElement.modelLoc,
        modelId: null,
        length: selectedFigureElement.length,
        width: selectedFigureElement.depth,
        height: selectedFigureElement.height,
      });
    }
    if (selectedFigureElement._candidate_materials) {
      selectedFigureElement._candidate_materials.map((item: any, index: number) => {
        if (item.modelId === selectedFigureElement._matched_material.modelId) {
          store.designStore.setSelectedIndex(index);
        }
      })
    }
    setMaterialList(selectedFigureElement._candidate_materials);
    setLocked(selectedFigureElement.locked == null ? -1 : selectedFigureElement.locked ? 1 : 0);
  }, [selectedFigureElement]);

  return (
    <div className={styles.root}>
      <div className={styles.findInfo}>
        <input
          value={inputValue}
          onChange={(event) => {
            if (event.currentTarget.value === '') {
              setMaterialList(selectedFigureElement._candidate_materials);
            }
            setInputValue(event.currentTarget.value);
          }}
          onKeyDown={(data) => {
            if (data.key != 'Enter') return;
            findMaterial();
          }}
          className={styles.container_input}
          placeholder={t("搜索全部素材")} />
        <Icon
          className={styles.Icon}
          iconClass="iconsearch"
          style={{
            fontSize: '16px',
            color: '#6C7175'
          }}
        >
        </Icon>
        {!selectedFigureElement.locked && selectedFigureElement.haveMatchedMaterial() && !FigureCategoryManager.isCustomCabinet(selectedFigureElement)
          && (<Button style={{ marginLeft: 5, fontSize: "12px" }} type="primary" size='small' onClick={openNewReplaceProduct}>{t('更多')}</Button>)
        }
        <Icon
          iconClass="iconClose_Large"
          onClick={() => {
              store.homeStore.setShowReplace(false);
          }}
        ></Icon>
      </div>
      <div className={styles.replaceInfo}>
        <div>
          <div>{t(selectedFigureElement?.sub_category)}</div>
          <div>{`${Math.round(selectedFigureElement?.length)}*${Math.round(selectedFigureElement?.depth)}*${Math.round(selectedFigureElement?.height)}`}</div>
        </div>
        <div className={styles.line}></div>
        {!replacing && materialList && materialList.length > 0
          ?
          <div className={styles.goodListRoot}>
            <div className={styles.goodsList}>
              {materialList &&
                materialList.map((item: I_MaterialMatchingItem, index: number) => {
                  return (
                    <div
                      className={`${styles.goodsItem} ${index === store.designStore.selectedIndex ? styles.selected : ''}`}
                      key={index}
                      ref={divRef}
                      onMouseEnter={(e: any) => {
                        if (checkIsMobile()) return;
                        setHoverInfo(item);
                        const rect = e.currentTarget.getBoundingClientRect();
                        const middlePoint = rect.top + rect.height / 2 - 180;
                        const pageHeight = document.documentElement.clientHeight - 550;
                        const maxTop = Math.min(middlePoint, pageHeight);
                        setPopoverTop(maxTop);
                        setIsShowPopover(true);
                      }}
                      onMouseLeave={() => {
                        setIsShowPopover(false);
                      }}
                      onClick={() => {
                        if (selectedFigureElement.locked) return;
                        store.designStore.setSelectedIndex(index);
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, item);
                        setTopInfo({
                          imageUrl: item.imageUrl,
                          name: item.name,
                          modelId: item.modelId,
                          length: item.length,
                          width: item.width,
                          height: item.targetSize?.height,
                        });
                      }}
                    >
                      {index === store.designStore.selectedIndex &&
                        <div className={styles.selectIcon}>
                          <Icon iconClass="iconxuanzhong" style={{ color: '#fff', fontSize: '13px' }}></Icon>
                        </div>
                      }
                      <img src={item.imageUrl} alt="" />
                      <div className={styles.sizeInfo}>{item.name}</div>
                      <div className={styles.sizeInfo}>{(Math.round(item?.length) + '*' + Math.round(item?.width) + '*' + Math.round(item?.height))}</div>
                    </div>
                  )
                })
              }
            </div>
          </div>
          :
          <div className={styles.emptyInfo}>
            <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div className={'desc'}>{selectedFigureElement.category.includes('组合') ? t('暂无组合素材') : t('暂无内容')}</div>
            {
              selectedFigureElement.category.includes('组合') ? <div className={'desc'}>{t('双击组合内替换')}</div> : null
            }
            <div className={'desc'}>{selectedFigureElement.category.includes('组合') ? t('或联系管理员补全组合') : t('请联系管理员补全')}</div>
            {
              store.userStore.userInfo?.regSource !== 'aihouse' &&
              <Button type="primary" onClick={() => {
                window.open(`${AIDeskUrl}/rule`);
              }}>{t('去补全')}</Button>
            }

          </div>
        }
        {replacing && <NewReplaceProduct roomName={selectedFigureElement._room?.roomname} materialId={selectedFigureElement._candidate_materials?.[store.designStore.selectedIndex < 0 ? 0 : store.designStore.selectedIndex]?.modelId}
          modelLoc={selectedFigureElement.modelLoc}
          seriesId={selectedFigureElement._candidate_materials?.[store.designStore.selectedIndex < 0 ? 0 : store.designStore.selectedIndex]?.seriesId}
          onReplaceMaterial={async (item: ResourceTipsDetailData) => {
            const topViewImage: string = await getMaterialTopViewImage(item.resourceId);
            const mm: I_MaterialMatchingItem = {
              modelId: item.resourceId,
              imageUrl: imgHostUrl + item.imageUrl,
              name: item.name,
              length: item.length,
              width: item.width,
              height: item.height,
              modelLoc: selectedFigureElement.modelLoc,
              modelFlag: item.modelFlag.toString(),
              topViewImage: topViewImage
            } as I_MaterialMatchingItem;
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, mm);
            setTopInfo({
              imageUrl: mm.imageUrl,
              name: mm.name,
              modelId: mm.modelId,
              length: mm.length,
              width: mm.width,
              height: mm.height,
            });
            const mindex = materialList.findIndex((m) => m.modelId === item.resourceId);
            store.designStore.setSelectedIndex(mindex);
          }}
          onClose={() => { setIsReplacing(false) }} />}
      </div>
    </div>
  );
};


export default observer(FloorProperties);
