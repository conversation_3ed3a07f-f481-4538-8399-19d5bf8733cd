import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { ZPolygon } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";

export class RmodelingUtils {
    /**
     * 获取最外层的多边形（移除被其他多边形包含的多边形）
     * @param allPolygons 所有多边形
     * @returns 最外层的多边形数组
     */
    static getOuterPolygons(allPolygons: ZPolygon[]): ZPolygon[] {
        if (!allPolygons || allPolygons.length === 0) {
            return [];
        }

        // 初始化结果数组
        let outerPolygons: ZPolygon[] = [...allPolygons];

        // 遍历所有多边形，检查是否被其他多边形包含
        for (let i = outerPolygons.length - 1; i >= 0; i--) {
            const currentPolygon = outerPolygons[i];

            for (let j = 0; j < outerPolygons.length; j++) {
                if (i === j) continue;

                const otherPolygon = outerPolygons[j];
                // 如果当前多边形被其他多边形完全包含，则从结果中移除
                let unionPolygons = otherPolygon.union(currentPolygon);
                if (unionPolygons?.length == 1 && unionPolygons[0].checkSamePoly(otherPolygon)) {
                    outerPolygons.splice(i, 1);
                    break;
                }
            }
        }

        return outerPolygons;
    }

    /**
     * 检查墙体是否是外墙
     * @param wall 待检查的墙体
     * @param outerPolygons 外部多边形数组
     * @returns 是否是外墙
     */
    static checkWallIsOuter(wall: TWall, outerPolygons: ZPolygon[]): boolean {
        // 将墙体转换为多边形
        let wallPolygon = new ZPolygon();
        wallPolygon.initByVertices(wall.boundary.map(b => new Vector3(b.start.x, b.start.y, 0)));

        // 遍历所有外部多边形
        for (let outerPolygon of outerPolygons) {
            // 使用-1.0的误差值来检查相交，以处理可能的间隙
            if (outerPolygon.checkIntersection(wallPolygon, -1.0)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取房间的外轮廓多边形
     * @param container 布局容器
     * @returns 外轮廓多边形数组
     */
    static getRoomOutlinePolygons(container: TLayoutEntityContainer): ZPolygon[] {
        // 获取所有外边界多边形
        const allPolygons = container._outter_border_polygons || [];
        return this.getOuterPolygons(allPolygons);
    }

    /**
     * 获取外墙列表
     * @param container 布局容器
     * @returns 外墙数组
     */
    static getOuterWalls(container: TLayoutEntityContainer): TWall[] {
        const outerPolygons = this.getRoomOutlinePolygons(container);
        return container._wall_entities.filter(wall => 
            this.checkWallIsOuter(wall, outerPolygons)
        );
    }
}
