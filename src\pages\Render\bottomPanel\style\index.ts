import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    root: css`
      position:fixed;
      left:0;
      bottom:0;
      height : 50px;
      z-index:5;
      width:100%;
      background:#fff;
      display:flex;
      /* justify-content: center; */
      padding: 0 10px;
    `,
    bottom_btn:css`
      float:left;
      width:60px;
      height:50px;
      line-height: 24px;
      padding-top:1px;
      padding-bottom:3px;
      font-size:11px;
      text-align:center;
      margin-right:5px;
      &.active {
        background:rgba(0,0,0,0.05);
      }
      .iconfont {
         font-size:20px;
      }
      &:hover {
        background:rgba(0,0,0,0.05);
      }
    `
  }

});
