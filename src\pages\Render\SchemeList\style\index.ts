import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }, length: number) => {
  // color: ${token.colorPrimary};
  const keyframesFadeIn = css`
    @keyframes fade-in {
      0% { opacity: 0; }
      100% { opacity: 1; }
    }
  `;
  return {
    left_panel: css`
      left: 0;
      position: fixed;
      color: #6c7175;
      background-color: #FFF;
      width: 288px;
      z-index: 999;
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);
      height: 100%;
    `,
    left_panel_title: css`
      color: #000;
      font-weight: bold;
      font-size: 20px;
      line-height: 1.67;
      padding: 14px 0 16px 16px;
      height: 60px;
      background-color: #fff;
      width: 100%;
    `,
    left_panel_placeholder: css`
      display: flex;
      justify-content: center;
      align-items: center;
      height: 850px;
      color: #6c7175;
      text-align: center;
      flex-direction: column;
      img{
        width: 80px;
        height: 80px;
      }
    `,
    left_panel_placeholder_image: css`
      margin-bottom: '12px';
      img {
        cursor : pointer;
        box-sizing: border-box;
        width: 300px;
      }
    `,
    left_panel_placeholder_text: css`    
      margin-top: 15px;
      font-size: 12px;
      line-height: 18px;
      color: #A2A2A5;
    `,
    left_panel_container: css`
      position:absolute;
    `,
    mobile:css`
      height: calc(100vh - 165px) !important;
      min-width: 220px !important;
      img{
        height: 216px !important;
      }
    `,
    left_panel_layout_list: css`
      width: 100%;
      min-width: 300px;
      background-color: #FFF;
      height: calc(100vh - 130px - 38px);
      overflow-y: scroll;
      text-align: center;
      padding: 2px 12px 2px 2px;
      canvas {
        cursor : pointer;
        box-sizing: border-box;
        width: 100%;
        padding: 10px;
        background-color: #F2F3F5; 
      }
      canvas:hover {
        outline: 2px solid #9242FB;
      }
      .iconfont {
        width:20px;
        height:20px;
        line-height:20px;
        float:right;
        color:#1790ff;
        font-weight: 100;
        cursor:pointer;

      }
      .mobile
      {
        height: 216px !important;
      }
      img {
        cursor : pointer;
        box-sizing: border-box;
        width: 100%;
        height: 310px;
        background-color: #F2F3F5; 
      }
      img:hover {
        outline: 2px solid #9242FB;
      }
      .emptyImg
      {
        height: 80px !important;
        width: 80px !important;
        display: flex;
        justify-content: center;
        border-radius: 50%;
      }
      > div {
          position: relative;
      }


    `,
    bottom_panel_container:css`
      height: 300px;
      @media screen and (max-width: 450px) { // 手机宽度
        height: 250px;
      }
      @media screen and (orientation: landscape) {
        height: calc(var(--vh, 1vh) * 100 - 300px);
        min-width: 224px;
      }
    `,
    roomListBar: css`
      height: 56px;
      padding-top: 16px;
      @media screen and (orientation: landscape) {
        height: 38px;
        padding-top: 0px;
      }
    `,


    bottom_panel_layout_list:css`
        width: 100%;
        height:100%;
        background-color: #FFF;
        overflow-x: scroll;
        overflow-y:hidden;
        text-align: center;
        display:flex;
        @media screen and (orientation: landscape) {
          overflow-y:scroll !important;
          /* width: 200px !important; */
          height: calc(var(--vh, 1vh) * 100 - 135px - 50px);
          display: block !important;
          padding: 0 4px;
          ::-webkit-scrollbar {
            display: none; /* 隐藏滚动条 */
          }
        }
        .scheme_div {
          width: 200px;
          margin: 8px 8px;
          position: relative;
          flex: 0 0 auto;
          @media screen and (max-width: 450px) { // 手机宽度
            width: 150px;
          }
        }
        .scheme_name {
          margin: 4px 0px;
          .ant-rate
          {
            font-size: 16px !important;
          }
          @media screen and (max-width: 450px) {
            .ant-rate
            {
              font-size: 12px !important;
              .ant-rate-star:not(:last-child)
              {
                margin-inline-end: 3px;
              }
            }
          }
        }
        .iconfont {
          width:20px;
          height:20px;
          line-height:20px;
          float:right;
          color:#1790ff;
          font-weight: 100;
          cursor:pointer;
  
        }

        img {
          cursor : pointer;
          box-sizing: border-box;
          background-color: #F2F3F5; 
          width: 100%;
          border-radius: 2px;
          padding: 15px;
        }
        .emptyImg
        {
          height: 80px !important;
          width: 80px !important;
          display: flex;
          justify-content: center;
          border-radius: 50%;
        }
    `,
    active: css`
      outline: 2px solid #9242FB;
    `,

    activeTitle: css`
      position: absolute;
      top: 4px;
      right: 4px;
      border-radius: 4px;
      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
      color: #fff;
      padding: 5px 4px;
    `,
    activeTitleNone: css`
      display: none;
    `,
    _scheme_name: css`
      color: #282828;
      font-family: PingFang SC;
      font-size: 14px;
      line-height: 1.57;
      letter-spacing: 0px;
      text-align: left;
      font-weight: 600;
      margin: 8px 0px;
      text-align: center;
      user-select:text;
    `,
    star_name: css`
      display: flex;
      padding: 0px 10px;
      justify-content: space-between;
    `,
    line: css`
      height: 100%;
      position: absolute;
      right: 0;
      position: absolute;
      top: 0;
      width: 4px;
      cursor: col-resize;
      z-index: 998;
    `,
    letfEmpty: css`
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      img{
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: auto;
      }
    `,
    letfEmptyItem: css`
      text-align: center;
    `,
    text: css`
      font-size: 12px;
      color: #6c7175;
      margin-top: 10px;
      color: #A2A2A5;
      line-height: 18px;
    `,
    difference: css`
      position: absolute;
      top: 0px;
      left: 0px;
      background-color: red;
      color: #fff;
      width: 25px;
      height: 25px;
      line-height: 25px;
      border-bottom-right-radius: 45%;
    `,
  }
});
