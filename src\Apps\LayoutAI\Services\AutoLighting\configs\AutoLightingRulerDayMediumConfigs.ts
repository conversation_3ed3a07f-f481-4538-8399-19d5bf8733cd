import { CategoryName } from "../../../Scene3D/NodeName";
import { AlignTypeXY, AutoLightingRulerType, LightingRuler } from "../AutoLightingRuler";

// 自动灯光规则配置
// lightMode.Day
export const AutoLightingRulerDayMediumConfigs: LightingRuler[] = [
    {
        name: '吊顶灯槽灯光',
        typeId: AutoLightingRulerType.CeilingSlotLight,
        lighting: {
            type: 1, // 1 矩形灯光
            color: 0xffffff,
            intensity: 10,
            targetObjectName: 'id5251202_Node_3',
            materialId: '368346740',
            // materialId: '35944925',
            length: 16,
            width: 16,
        },
        condition: {
            roomName: '客餐厅|卧室|主卧|次卧|客卧',
        },
    },
    {
        typeId: AutoLightingRulerType.CorridorDayLight,
        name: '过道灯光',
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 0.8,
            length: '25%',
            width: '90%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            spaceArea: '过道区',
        },
    },
    {
        name: '沙发灯光',
        typeId: AutoLightingRulerType.SofaLight,
        category: CategoryName.Sofa,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 6,
            width: '80%',
            length: '50%',
        },
        pose: {
            z: 2350,
            gapOffset: 100,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '沙发侧光',
        typeId: AutoLightingRulerType.SofaSideLight,
        category: CategoryName.Teapoy,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: 600,
            length: 600,
        },
        pose: {
            gapOffset: 550,
            floorOffset: 1000,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '客厅窗户灯光',
        typeId: AutoLightingRulerType.LivingWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '客厅门洞灯光',
        typeId: AutoLightingRulerType.LivingDoorHoleLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '40%',
            length: '40%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1000,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '客厅阳台门灯光',
        typeId: AutoLightingRulerType.BalconyDoorLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '40%',
            length: '40%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1000,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '客厅边缘灯光',
        typeId: AutoLightingRulerType.LivingEdgeLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2.5,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 1300,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
    {
        name: '餐厅窗户灯光',
        typeId: AutoLightingRulerType.DingingWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            spaceArea: '餐厅区',
        },
    },
    {
        name: '餐桌灯光',
        typeId: AutoLightingRulerType.TableLight,
        category: CategoryName.Table,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: '35%',
            length: '35%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            spaceArea: '餐厅区',
        },
    },
    {
        name: '厨房窗户灯光',
        typeId: AutoLightingRulerType.KitchenWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 24,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '厨房',
        },
    },
    {
        name: '厨房灯光',
        typeId: AutoLightingRulerType.KitchenLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 10,
            width: '30%',
            length: '30%',
        },
        pose: {
            z: 2270,
        },
        condition: {
            roomName: '厨房',
        },
    },
    {
        name: '卧室窗户灯光',
        typeId: AutoLightingRulerType.BedroomWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 8,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '床体灯光',
        typeId: AutoLightingRulerType.BedCenterLight,
        category: CategoryName.Bed,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 6,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 2370,
            align: AlignTypeXY.Center,
        },
        condition: {
            roomName: '卧室|主卧|次卧|客卧',
        },
    },
    {
        name: '卫生间窗户灯光',
        typeId: AutoLightingRulerType.BathroomWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 8,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '卫生间',
        },
    },
    {
        name: '卫生间灯光',
        typeId: AutoLightingRulerType.BathroomLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 8,
            width: '30%',
            length: '30%',
        },
        condition: {
            roomName: '卫生间',
        },
    },
    {
        name: '书房窗户灯光',
        typeId: AutoLightingRulerType.StudyroomWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 6,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '书房',
        },
    },
    {
        name: '书桌灯光',
        typeId: AutoLightingRulerType.DeskLight,
        category: CategoryName.Desk,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            roomName: '书房|茶室',
        },
    },
    {
        name: '茶室窗户灯光',
        typeId: AutoLightingRulerType.TearoomWindowLight,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 4,
            width: '140%',
            length: '140%',
        },
        pose: {
            norOffset: 300,
            floorOffset: 1500,
            lookAt: AlignTypeXY.Center,
        },
        condition: {
            roomName: '茶室',
        },
    },
    {
        name: '茶台灯光',
        typeId: AutoLightingRulerType.TeaTableLight,
        category: CategoryName.TeaTable,
        lighting: {
            type: 1,
            color: 0xffffff,
            intensity: 2,
            width: '40%',
            length: '40%',
        },
        pose: {
            z: 2370,
        },
        condition: {
            roomName: '茶室',
        },
    },
]