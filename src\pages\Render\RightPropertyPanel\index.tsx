import React, { useEffect, useState, useRef, useContext } from 'react';
import useStyles from './style';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { EventName } from '@/Apps/EventSystem';
import  FloorProperties from './widget/FloorProperties/index';
import RoomProperties from './widget/RoomProperties/index';
import GoodsProperties from './widget/GoodsProperties/index';
import { TSeriesSample } from '@/Apps/LayoutAI/Layout/TSeriesSample';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { DesignContext } from "@/pages/Design/design";
import { useStore } from '@/models';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface RoomSeriesPlanProps {
  // showLayoutList: boolean;
}
const MyComponent: React.FC<RoomSeriesPlanProps> = () => {
  const { t } = useTranslation()
  const { styles }: any = useStyles();
  const [preparation, setPreparation] = useState<boolean>(false);
  const [entity, setEntity] = useState<string>('');
  const [showLayoutList, setShowLayoutList] = useState<boolean>(true);   // 区分选布局还是选搭配
  const { setArrangeable, selectedFigureElement } = useContext(DesignContext);
  const lineRef = useRef(null);
  const store = useStore();
  const location = useLocation();
  const currentPath = location.pathname;
  useEffect(() => {
    if(store.homeStore.room2SeriesSampleArray?.length > 0) {
      const allItems = store.homeStore.room2SeriesSampleArray?.every?.((item: [TRoom, TSeriesSample]) => {
        const room = item[0];
        return  room._furniture_list.length > 0;
      });
      setArrangeable(allItems);
    } else {
      setArrangeable(false);
    }
  },[store.homeStore.room2SeriesSampleArray]);

  useEffect(() => {
    setPreparation(true);
    setTimeout(() => {
      setPreparation(false);
    },1000);
    LayoutAI_App.on(EventName.Room2SeriesSampleRoom, (array:[TRoom, TSeriesSample][]) => {
      store.homeStore.setRoom2SeriesSampleArray(array);

    });
  },[]);

  useEffect(() => {
    if(currentPath == '/design')
    {
      if(store.homeStore.selectData.rooms.length == 1 && store.homeStore.selectData.clickOnRoom === true) {
        setEntity('空间'); /*[i18n:ignore]*/
      } else {
        setEntity('楼层');/*[i18n:ignore]*/
      }
      if(selectedFigureElement && selectedFigureElement.modelLoc != "相机" && selectedFigureElement.modelLoc != "人物")   /*[i18n:ignore]*/
      {
        setEntity('商品');/*[i18n:ignore]*/
      }
      if(store.homeStore.selectData.rooms.length == 1 && store.homeStore.selectData.rooms[0].isSelectSeries)
      {
        setShowLayoutList(false);
      } else 
      {
        setShowLayoutList(true);
      }
    } else 
    {
      setEntity('楼层');/*[i18n:ignore]*/
    }
  },[store.homeStore.selectData.rooms, selectedFigureElement, store.homeStore.room2SeriesSampleArray]);

  return (
      <div className={styles.root}>
        <div style={{width: '400px'}}>
          {/* 空间信息 */}
          {entity === '楼层' /*[i18n:ignore]*/
           && 
          <FloorProperties 
            showLayoutList={showLayoutList} 
            roomInfos={store.homeStore.roomInfos} />}

          {entity === '空间' && store.homeStore.selectData.rooms[0]  /*[i18n:ignore]*/
          && 
            <RoomProperties 
              showLayoutList={showLayoutList} 
              roomInfos={store.homeStore.roomInfos} 
              roomInfo={store.homeStore.selectData.rooms[0]} 
              currenScheme={store.homeStore.currenScheme}
              selectedFigureElement = {selectedFigureElement}/>
          }

          {entity === '商品' && selectedFigureElement /*[i18n:ignore]*/
           && 
            <GoodsProperties selectedFigureElement = {selectedFigureElement}></GoodsProperties>
          }
        </div>
      </div>
  );
};


export default observer(MyComponent);
