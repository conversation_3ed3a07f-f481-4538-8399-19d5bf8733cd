import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { IframeBusinessType, IframeMessage, IframeMessageType, IframeSceneStatus, IframeSceneStatusType } from "./RoyEngineProtocol";
import { RoyMsgEngine } from "./RoyMsgEngine";
import { MaterialManager } from "@/Apps/LayoutAI/Scene3D/MaterialManager";
import { Group, Material, Mesh, MeshStandardMaterial, Object3D, SRGBColorSpace } from "three";
import { SimpleGlbParser } from "@/Apps/LayoutAI/Scene3D/parsers/SimpleGlbParser";
import { CapinetQueryResp } from "./CapinetQueryResp";
import { GlbFixUtils } from "@/Apps/LayoutAI/Scene3D/parsers/GlbFixUtils";

export enum RoyCabinetEvents {
    CabinetInitFinished = "CabinetInitFinished",
}

/**
* @description 柜体消息处理
* <AUTHOR>
* @date 2025-04-11
* @lastEditTime 2025-04-11 14:27:34
* @lastEditors xuld
*/
export class RoyMsgCabinet {
    private static _isInitFinishedEngine: boolean = false;
    private static _isInitFinishedCabinet: boolean = false;
    private static _iframeRef: HTMLIFrameElement;
    private static _glbCbMap: { [key: string]: Array<(glb: Group) => void> } = {};
    private static _midQueue: string[] = [];
    private static _isExportingGLB: boolean = false;
    private static _glbCache: { [key: string]: Group } = {};

    public static onMessage(msg: IframeMessage) {
        switch (msg.handleType) {
            case IframeMessageType.SceneStatus: {
                let data = msg.data as IframeSceneStatus;
                this.onSceneStatus(data);
            }
                break;
            case IframeMessageType.ExportGLB: {
                let data = msg.data as { glbData: ArrayBuffer, id: string };
                console.log("onExportGLB", data.id);
                this.onExportGLB(data);
            }
                break;
            default:
                console.warn("unknown cmd type", JSON.stringify(msg));
                break;
        }
    }

    public static setIframeRef(iframeRef: HTMLIFrameElement) {
        this._iframeRef = iframeRef;
    }

    public static postMessage(msg: { handleType: IframeMessageType; data?: object | string; }) {
        if (!this._isInitFinishedEngine) {
            console.warn("engine not init finished");
            return;
        }

        if (!this._iframeRef) {
            console.warn("iframeRef not set");
            return;
        }

        RoyMsgEngine.postMessage(this._iframeRef, {
            businessType: IframeBusinessType.ParamAICabinet,
            handleType: msg.handleType,
            data: msg.data || ""
        });
    }

    private static onSceneStatus(data: IframeSceneStatus) {
        if (data.status == IframeSceneStatusType.EngineInit) {
            this._isInitFinishedEngine = true;
            LayoutAI_App.instance.EventSystem.emit(RoyCabinetEvents.CabinetInitFinished);
            this._checkNext();
        }
        else if (data.status == IframeSceneStatusType.SceneInit) {
            this._isInitFinishedCabinet = true;
        }
    }

    public static requestGLB(materialId: string, cb: (glbData: Group) => void) {
        if (!materialId) {
            console.warn("materialId is required");
            return;
        }

        console.log("requestGLB", materialId);

        if (this._glbCache[materialId]) {
            let glb = this._glbCache[materialId].clone();
            cb(glb);
            return;
        }

        if (!this._glbCbMap[materialId]) {
            this._glbCbMap[materialId] = [];
        }
        this._glbCbMap[materialId].push(cb);

        if (!this._midQueue.includes(materialId)) {
            this._midQueue.push(materialId);
        }

        this._checkNext();
    }

    public static _checkNext() {
        if (this._isExportingGLB) {
            return;
        }

        if (this._midQueue.length == 0) {
            console.log("All GLB export finished");
            return;
        }

        this._isExportingGLB = true;
        let mid = this._midQueue.shift();
        MaterialManager.getDesignMaterialInfoByIds([mid]).then((dvoList) => {
            let dvo = dvoList[0];
            let data: CapinetQueryResp = {
                id: dvo.MaterialId as any,
                material_id: dvo.MaterialId,
                depth: dvo.PICWidth,
                width: dvo.PICLength,
                height: dvo.PICHeight,
            }
            this.postMessage({
                handleType: IframeMessageType.ExportGLB,
                data: data
            });
            // 结果回调 onExportGLB
        });
    }

    private static onExportGLB(data: { glbData: ArrayBuffer, id: string }) {
        this._isExportingGLB = false;

        let cbs = this._glbCbMap[data.id];
        if (cbs?.length) {
            for (let cb of cbs) {
                SimpleGlbParser.getGlbFromBuffer(data.glbData, '', false).then((group: Group | null) => {
                    // 处理材质颜色问题, 米转毫米
                    GlbFixUtils.fixMaterialAndUnit(group, 1000);

                    if (!this._glbCache[data.id]) {
                        this._glbCache[data.id] = group;
                    }
                    cb(group.clone());
                });
            }
            delete this._glbCbMap[data.id];
        }
        this._checkNext();
    }
}

(globalThis as any).RoyMsgCabinet = RoyMsgCabinet;