import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { EventName } from '@/Apps/EventSystem';
import { CadDrawingLayerType } from '@/Apps/LayoutAI/Drawing/TDrawingLayer';
import { TLayoutEntityContainer } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { ITopMenuItem } from '@svg/antd-cloud-design/lib/TopMenu';
import { useEffect, useState } from 'react';
import useStyles from './style/index'; // 导入样式
import { Checkbox } from '@svg/antd';

interface ICommandTopMenuItem extends ITopMenuItem {
  command_name?: string;
  titleCn?: string;
  subList?: ICommandTopMenuItem[];
}
const layer_state_data: { [key: string]: boolean } = {};
layer_state_data[CadDrawingLayerType.CadEzdxfDrawing] = false;
layer_state_data[CadDrawingLayerType.CadRoomStrucure] = true;
layer_state_data[CadDrawingLayerType.CadFurniture] = true;
layer_state_data[CadDrawingLayerType.CadCabinet] = true;
layer_state_data[CadDrawingLayerType.CadOutLine] = true;
layer_state_data[CadDrawingLayerType.CadLighting] = false;
layer_state_data[CadDrawingLayerType.CadCeiling] = false;
layer_state_data[CadDrawingLayerType.CadDecorates] = false;
layer_state_data[CadDrawingLayerType.CadSubRoomAreaDrawing] = false;
layer_state_data[CadDrawingLayerType.CadDimensionWallElement] = false;
layer_state_data[CadDrawingLayerType.CadDimensionOutterWallElement] = false;
layer_state_data[CadDrawingLayerType.CadRoomName] = true;
layer_state_data[CadDrawingLayerType.RulerDrawing] = true;
const DisplayCheckBoxes: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  const { styles } = useStyles();
  const t = LayoutAI_App.t;
  const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase)
    .layout_container;
  const [layerBtnState, setLayerBtnState] = useState<{ [key: string]: boolean }>(layer_state_data);
  let display_btnList: ICommandTopMenuItem[] = [
    {
      id: CadDrawingLayerType.CadRoomStrucure,
      title: t('墙体结构'),
      titleCn: '墙体结构', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadRoomStrucure]
    },
    {
      id: CadDrawingLayerType.CadFurniture,
      title: t('家具'),
      titleCn: '家具', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadFurniture]
    },
    {
      id: CadDrawingLayerType.CadCabinet,
      title: t('定制柜'),
      titleCn: '定制柜', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadCabinet]
    },
    // {
    //   id: CadDrawingLayerType.CadDecorates,
    //   title: t('饰品'),
    //   titleCn: '饰品',//[i18n:ignore]
    //   type: 'checkbox',
    //   checked: layerBtnState[CadDrawingLayerType.CadDecorates]
    // },
    {
      id: CadDrawingLayerType.CadSubRoomAreaDrawing,
      title: t('区域'),
      titleCn: '区域', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadSubRoomAreaDrawing]
    },
    {
      id: CadDrawingLayerType.CadCeiling,
      title: t('吊顶'),
      titleCn: '吊顶', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadCeiling]
    },
    {
      id: CadDrawingLayerType.CadRoomName,
      title: t('空间名称'),
      titleCn: '空间名称', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.CadRoomName]
    },
    {
      id: CadDrawingLayerType.RulerDrawing,
      title: t('量尺'),
      titleCn: '量尺', //[i18n:ignore]
      type: 'checkbox',
      checked: layerBtnState[CadDrawingLayerType.RulerDrawing]
    }
  ];

  display_btnList = display_btnList.filter(ele => ele);
  const onClickItem = (item: ICommandTopMenuItem) => {
    if (layerBtnState[item.id] === undefined) {
      layerBtnState[item.id] = false;
    }
    if (layerBtnState[item.id] !== undefined) {
      let state = { ...layerBtnState };

      state[item.id] = !state[item.id];
      LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleSwitchDrawingLayer, state);
    }
  };

  useEffect(() => {
    LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleSwitchDrawingLayer, layer_state_data);

    LayoutAI_App.on_M(EventName.SwitchDrawingLayer, 'display-check-box', (state_data: { [key: string]: boolean }) => {
      let state = { ...layerBtnState, ...state_data };

      setLayerBtnState(state);
    });
    // LayoutAI_App.DispatchEvent(LayoutAI_Events.HandleSwitchDrawingLayer, layerBtnState);

    return () => {
      // LayoutAI_App.off(EventName.SwitchDrawingLayer);
    };
  }, []);
  return (
    <div className={styles.checkBoxes} style={{ display: isVisible ? 'block' : 'none' }}>
      {display_btnList.map((item, index) => (
        <div key={'display_check_' + index}>
          <Checkbox
            checked={layerBtnState[item.id]}
            onChange={ev => {
              onClickItem(item);
            }}
          >
            {item.title}
          </Checkbox>
        </div>
      ))}
    </div>
  );
};

export default DisplayCheckBoxes;
