import React, { useEffect } from 'react';
import styles from './style/index.module.less';
import { Button, Form, FormProps, Input } from '@svg/antd';
import { CloseOutlined } from '@ant-design/icons';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { useTranslation } from 'react-i18next';
import { SdkService } from '@/services/SdkService';
import { useStore } from '@/models';
import { workDomainMap } from '@/config';
import { observer } from 'mobx-react-lite';

interface SaveLayoutSchemeDialogProps {
    exitCb?:()=>void;
    closeCb: () => void;
    schemeName?: string;
    isSaveAs?: boolean;
    isSaveAndExit?: boolean;
}

type FieldType = {
    username?: string;
    telephone?: string;
    schemename?: string;
    address?: string;
};

const SaveLayoutSchemeDialog: React.FC<SaveLayoutSchemeDialogProps> = ({closeCb, schemeName, isSaveAs ,isSaveAndExit = false}) => {
    const { t } = useTranslation()
    const store = useStore();
    let {
        showSaveLayoutSchemeDialog,
        setShowSaveLayoutSchemeDialog
    } = store.homeStore;
    useEffect(()=> {
        form.setFieldValue("schemename", schemeName);
    }, []);

    const onFinish: FormProps<FieldType>['onFinish'] = (values) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutSchemeAs, values);
        closeCb();
        if(showSaveLayoutSchemeDialog.source == 'exitBtn') {
            store.homeStore.setIsAutoExit('autoExit');
        }
        if(showSaveLayoutSchemeDialog.source == 'enterpage')
        {
            store.homeStore.setShowEnterPage({show: true, source: 'padExitBtn'})
        }
    };
    
    const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    };

    const [form] = Form.useForm();

    // 统一退出功能
    const exitCb = () => {
        // pad端退出
        if(showSaveLayoutSchemeDialog.source === 'padExitBtn' || showSaveLayoutSchemeDialog.source === 'padSideBar'){
            store.homeStore.setShowEnterPage({show: true, source: null})
            store.homeStore.setShowSaveLayoutSchemeDialog({show: false, source: null})
            return
        }
        // 慧引流退出
        if(showSaveLayoutSchemeDialog.source == 'topMenu') {
            setShowSaveLayoutSchemeDialog({show: false, source: ''});
            return;
        }
        SdkService.exitSDK();
        // 梦想家退出
        window.parent.postMessage({
            origin: 'layoutai.api',
            type: 'canClose',
            data: {
            canClose: true
            }
        }, '*');
        setTimeout(() => {
            window.location.href = workDomainMap;
        }, 200);
    }


    return (
      <div className={styles.dialog_root}>
        <div className={styles.dialog_header}>
            <span className={styles.dialog_tile}>{isSaveAs? t("方案另存为") : t("保存方案")}</span>
            <Button size='small' icon={<CloseOutlined rev={undefined} />} onClick={closeCb} className={styles.dialog_close} />
        </div>
        <div className={styles.dialog_content}>
            <Form
                form={form}
                name="basic"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}
                style={{ maxWidth: 500 }}
                initialValues={{ remember: true }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="on"
            >
                <Form.Item<FieldType>
                label={t("方案名称")}
                name="schemename"
                wrapperCol={{ span: 14 }}
                rules={[{ required: true, message: t('请输入方案名称')}]}
                >
                <Input placeholder={t("请输入方案名称")} />
                </Form.Item>

                <Form.Item<FieldType>
                label={t("姓名")}
                name="username"
                wrapperCol={{ span: 6 }}
                >
                <Input placeholder={t("请输入姓名")} />
                </Form.Item>

                <Form.Item<FieldType>
                label={t("手机号")}
                name="telephone"
                wrapperCol={{ span: 8 }}
                getValueFromEvent={event => {
                    return event.target.value.replace(/\D/g, '');
                  }}
                >
                <Input  
                    placeholder={t('请输入手机号')} />
                </Form.Item>

                <Form.Item<FieldType>
                label={t("地址")}
                name="address"
                >
                <Input placeholder={t('请输入地址')} />
                </Form.Item>

                <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
                      <Button type="primary" htmlType="submit" >{showSaveLayoutSchemeDialog.source == 'exitBtn' ? t('保存退出') : t('保存')}</Button>
                      {<>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<Button onClick={exitCb}>{showSaveLayoutSchemeDialog.source == 'exitBtn' ? t('直接退出') : t("退出")}</Button></>}
                </Form.Item>
            </Form>
        </div>
      </div>
    );
}

export default observer(SaveLayoutSchemeDialog);
