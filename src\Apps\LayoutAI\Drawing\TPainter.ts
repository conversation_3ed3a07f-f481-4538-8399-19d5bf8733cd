import { getPrefix } from '@/utils/common';
import { BufferGeometry, Float32BufferAttribute, LineSegments, Mesh, Path, Vector2, Vector3 } from "three";
import { I_EzdxfJsonData } from "../AICadData/EzdxfEntity";
import { SimpleShape, SimpleShapeGeometry } from "../Scene3D/geometries/SimpleShapeGeometry";
import { MaterialManager } from "../Scene3D/MaterialManager";
import { SimpleFigure2DMaterial } from "../Scene3D/materials/entityMaterials/SimpleFigure2DMaterial";
import { MeshName } from "../Scene3D/NodeName";
import { Vec3FromArray } from "@layoutai/z_polygon";
import { SvgPainter } from "@layoutai/z_polygon";
import { I_EzdxfEntity, I_PainterPattern } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { GetDefaultFigureDrawingFunc } from "./DefaultFigureDrawingFuncs";
export interface I_RectDrawingFunc {
    _label?: string;
    fill?: string;
    stroke?: string;
    opacity?: number;
    pattern_name?: string;
    drawing_rects_func?: (parent_rect: ZRect) => ZRect[];
}


export class TPainter extends SvgPainter {
    constructor(canvas: HTMLCanvasElement) {
        super(canvas);
    }

    enter_drawpoly(): void {


        // 再进入
        super.enter_drawpoly();

    }
    public async prepareDefaultBlocks() {
        let data: I_EzdxfJsonData = await fetch(getPrefix() + "./static/db/ezdxf_blocks_default.json").then(val => val.json()).catch(e => null);
        if (data) {
            if (data.blocks) {
                for (let key in data.blocks) {
                    let update_entity = (ele: I_EzdxfEntity, is_add_point: boolean = true) => {
                        let t_ele = ele;

                        if (t_ele.attribs) {
                            for (let key in t_ele.attribs) {
                                let val = (t_ele.attribs as any)[key];

                                if (val instanceof Array) {
                                    if (val.length == 3) {
                                        (t_ele.attribs as any)[key + "_v3"] = Vec3FromArray(val);

                                    }
                                }
                            }
                        }
                        if (t_ele.lwpoints) {
                            t_ele.lwpoints_v3 = [];
                            for (let val of t_ele.lwpoints) {
                                t_ele.lwpoints_v3.push(Vec3FromArray(val));


                            }
                        }

                    }
                    for (let entity of data.blocks[key].entities) {
                        update_entity(entity);
                    }
                    this._ezdxf_blockrecords[key] = data.blocks[key];
                }
            }
        }
    }
    drawWindowRect(rect: ZRect, window_type: string = "") {
        // console.log(window_type);
        if (window_type == "") {
            this.strokePolygons([rect]);
        }
        else if (window_type == "door") {
            this.fillStyle = "#ff0";

            let t_rect = new ZRect(20, 600);
            t_rect.nor = rect.nor.clone().negate();
            t_rect.back_center = rect.vertices[3].pos;
            t_rect.updateRect();
            this.fillPolygon(rect, 0.5);
            this.fillPolygon(t_rect, 0.5);

            this.strokePolygons([t_rect]);

        }
        else if (window_type == "slidedoor") {
            this.strokeStyle = "#000";

            this.fillStyle = "#fff";

            this.fillPolygon(rect, 0.2);

            let t_rect0 = rect.clone();

            t_rect0._h /= 2;
            t_rect0._w = t_rect0._w / 10 * 6;
            t_rect0.rect_center = rect.rect_center.clone().sub(rect.dv.clone().multiplyScalar((rect._w - t_rect0._w) / 2)).add(rect._nor.clone().multiplyScalar(t_rect0._h / 2));

            let t_rect1 = t_rect0.clone();
            t_rect1.rect_center = rect.rect_center.clone().add(rect.dv.clone().multiplyScalar((rect._w - t_rect0._w) / 2)).sub(rect._nor.clone().multiplyScalar(t_rect0._h / 2));
            this.drawEdges(t_rect0.edges);
            this.drawEdges(t_rect1.edges);

        }
        else if (window_type == "window") {
            this.strokeStyle = "#000";

            let t_rect = rect.clone();
            let rect_center = t_rect.rect_center;
            t_rect._h /= 3;
            t_rect.rect_center = rect_center;

            this.fillStyle = "#fff";
            this.fillPolygon(rect, 0.75);
            this.strokePolygons([rect]);

            this.strokeStyle = "#777";

            this.strokePolygons([t_rect]);

        }
    }

    drawPreviewFigureRect(rect: ZRect, img: HTMLImageElement, is_filling: boolean = false, check_u_dv_flag: boolean = true) {

        if (!img) return;
        if (!img.width || !img.height) return;
        let u_dv_flag = check_u_dv_flag ? rect.u_dv_flag : 1;

        let imgWidth = img.width;
        let imgHeight = img.height;

        if(!is_filling)
        {
            const is_portrait: boolean = (rect._h > rect._w && imgHeight < imgWidth) || (rect._h < rect._w && imgHeight > imgWidth);
    
            if (is_portrait) {
                imgWidth = img.height;
                imgHeight = img.width;
            }
    
            let xOffset = is_portrait ? -imgHeight / 2 : -imgWidth / 2;
            let yOffset = is_portrait ? -imgWidth / 2 : -imgHeight / 2;

            let sc_x = rect._w / img.width * this._p_sc;
            let sc_y = rect._h / img.height * this._p_sc;
    
            sc_x = Math.max(sc_x,sc_y);
            sc_y = sc_x;


            let pp = this.project2D(rect.rect_center);
            let angle = -Math.atan2(rect._nor.x, -rect._nor.y) / Math.PI * 180;

            let matrix = this._context.getTransform();
            let transform = this._context.getTransform().translate(pp.x,pp.y).rotate(angle)
            .scale( u_dv_flag * sc_x,sc_y).translate(xOffset, yOffset);

            this._context.setTransform(transform);
            this._context.imageSmoothingEnabled = true;
            this._context.imageSmoothingQuality = 'high';
            this._context.drawImage(img, 0, 0);
            this._context.setTransform(matrix);
        }
        else{            
            let xOffset = -imgWidth / 2;
            let yOffset = -imgHeight / 2;

            let sc_x = u_dv_flag* rect._w / img.width * this._p_sc;
            let sc_y = rect._h / img.height * this._p_sc;
            let pp = this.project2D(rect.rect_center);
            let angle = -Math.atan2(rect._nor.x, -rect._nor.y) / Math.PI * 180;

            let matrix = this._context.getTransform();
    
            let transform = this._context.getTransform().translate(pp.x,pp.y).rotate(angle)
            .scale(sc_x,sc_y).translate(xOffset, yOffset);
    
            this._context.setTransform(transform);
            this._context.imageSmoothingEnabled = true;
            this._context.imageSmoothingQuality = 'high';
            this._context.drawImage(img, 0, 0);
            this._context.setTransform(matrix);
        }
    }

    drawFigureRect(rect: ZRect, label: string = "", candidate_labels: string[] = []) {
        let drawing_func_list = GetDefaultFigureDrawingFunc([label, ...candidate_labels]);

        if (drawing_func_list) {
            for (let func of drawing_func_list) {
                let rects = func.drawing_rects_func(rect);
                let pattern = func.pattern_name;
                if (!rects) continue;
                if (pattern) {
                    rects.forEach((sub_rect) => {
                        this.drawFigureRect(sub_rect, pattern, []);
                    })
                }
                else {
                    if (func.fill) {
                        this.fillStyle = func.fill;
                        this.fillPolygons(rects, func.opacity || 1);
                    }
                    if (func.stroke) {
                        this.strokeStyle = func.stroke;
                        this.strokePolygons(rects);

                    }
                }

            }
            return;
        }

        let svg_pattern = this._svg_pattern_dict[label];
        if (!svg_pattern) {
            for (let t_label of candidate_labels) {
                if (!t_label) continue;
                svg_pattern = this._svg_pattern_dict[t_label];
                if (svg_pattern) break;
            }
        }

        if (svg_pattern && svg_pattern?.drawing_elements) {

            this.drawSvgDrawingElements(svg_pattern.drawing_elements, rect, null, this._context.globalAlpha);
            return;
        }

        let pattern = this.getPattern(label);

        if (!pattern) {
            for (let t_label of candidate_labels) {
                if (!t_label) continue;
                pattern = this.getPattern(t_label);
                if (pattern) break;
            }

        }


        // unvalid_names.push(s)
        if (pattern?.img?.width) {
            this.fillRectWithPattern(rect.clone(), pattern, 0.8);
        }
        else {
            let alpha = 0.5;


            this.fillPolygons([rect], alpha);

            this.strokePolygons([rect]);

            this.fillStyle = "#000";


            this._context.globalAlpha = 1.;

        }

    }

    drawRectText(pos: Vector3, width: number, height: number, line_width: number, text: string) {
        this._context.beginPath();
        this.strokeStyle = "#454647";
        let pp = { x: pos.x, y: pos.y, z: 0 };
        this.fillStyle = "#454647";
        this.drawRect(pos, width, height, 1);
        this.fillStyle = "#fff";
        let _pos = this.project2D(pp);
        this._context.translate(_pos.x, _pos.y);
        this._context.font = `500 ${10}px Arial 宋体`;
        let measure = this._context.measureText(text);
        let t_h = measure.actualBoundingBoxAscent + (measure.actualBoundingBoxDescent || 0);
        let t_width = measure.width;
        this._context.fillText(text, -t_width / 2, t_h / 2);
    }
    fillRectWithPattern(rect: ZRect, painter_pattern: I_PainterPattern, alpha: number = 0.5) {
        if (!painter_pattern || !painter_pattern.img || !painter_pattern.img.width) return;
        // this.fillStyle = painter_pattern.pattern;
        // 在ios如果this.fillStyle = painter_pattern.pattern; 会报错不知道为什么，先暂时赋值#fff
        this.fillStyle = '#fff';

        let rect_center = rect.rect_center;

        // rect._h += 1000;
        rect.rect_center = rect_center;
        let sc_x = rect.u_dv_flag * rect._w / painter_pattern.img.width * this._p_sc;
        let sc_y = rect._h / painter_pattern.img.height * this._p_sc;
        let pp = this.project2D(rect.rect_center);
        let angle = -Math.atan2(rect._nor.x, -rect._nor.y) / Math.PI * 180;

        let matrix = this._context.getTransform();



        let transform = this._context.getTransform().translate(pp.x, pp.y).rotate(angle)
            .scale(sc_x, sc_y).translate(-painter_pattern.img.width / 2, -painter_pattern.img.height / 2);

        if(isNaN(pp.x) || isNaN(pp.y) || isNaN(sc_x) || isNaN(sc_y) || isNaN(angle))
        {
            return;
        }
        this._context.setTransform(transform);

        this._context.drawImage(painter_pattern.img, 0, 0);
        this._context.setTransform(matrix);


        // this.strokePolygons([rect]);

        // this.drawEdges([rect.backEdge.makeOffsetEdge(-40)]);


    }
    /**
     *  生成图元3D网格
     */
    _makeFigureMesh(label: string = "", candidate_labels: string[] = []) {
        let svg_pattern = this._svg_pattern_dict[label];
        if (!svg_pattern) {
            for (let t_label of candidate_labels) {
                if (!t_label) continue;
                svg_pattern = this._svg_pattern_dict[t_label];
                if (svg_pattern) break;
            }
        }
        if (!svg_pattern) return;

        let drawing_elements = svg_pattern.drawing_elements;
        let curve_paths: Path[] = [];

        let lineGeometry = new BufferGeometry();
        let lineVertices: number[] = [];


        let simpleShapes: SimpleShape[] = [];
        drawing_elements.forEach((ele) => {
            if (ele.type === "path") {
                let points: Vector2[] = [];
                ele.curve_path.curves.forEach((curve, index) => {
                    if (curve.type === "LineCurve") {
                        points.push(...curve.getPoints(2));
                    }
                    else {
                        points.push(...curve.getPoints(40));
                    }
                });

                let t_points: Vector3[] = [];
                for (let p of points) {
                    if (t_points.length == 0) t_points.push(new Vector3(p.x, p.y, 0));
                    else if (t_points[t_points.length - 1].distanceTo({ x: p.x, y: p.y, z: 0 }) > 0.0001) {
                        t_points.push(new Vector3(p.x, p.y, 0));
                    }
                }
                for (let i = 0; i < t_points.length - 1; i++) {
                    let p0 = t_points[i];
                    let p1 = t_points[i + 1];

                    lineVertices.push(...p0.toArray());
                    lineVertices.push(...p1.toArray());
                }

                if (ele.drawing_state.fill && ele.drawing_state.fill !== "none") {
                    let new_shape = new SimpleShape(t_points.map(v => new Vector2(v.x, v.y)));
                    new_shape.alpha = ele.drawing_state.opacity || 1.;
                    new_shape.fill = ele.drawing_state.fill;

                    simpleShapes.push(new_shape);
                }
            }
            else if (ele.type === "rect") {
                let t_points = ele.rect.positions;

                for (let i = 0; i < t_points.length; i++) {
                    let p0 = t_points[i];
                    let p1 = t_points[(i + 1) % 4];

                    lineVertices.push(...p0.toArray());
                    lineVertices.push(...p1.toArray());
                }

                if (ele.drawing_state.fill && ele.drawing_state.fill !== "none") {
                    let new_shape = new SimpleShape(t_points.map(v => new Vector2(v.x, v.y)));
                    (new_shape as any).fillText = ele.drawing_state.fill;
                    new_shape.alpha = ele.drawing_state.opacity || 1.;
                    new_shape.fill = ele.drawing_state.fill;

                    simpleShapes.push(new_shape);
                }
            }
        });
        lineGeometry.setAttribute('position', new Float32BufferAttribute(lineVertices, 3));
        let figure_mesh = new Mesh();
        figure_mesh.name = MeshName.Painter;

        figure_mesh.add(new LineSegments(lineGeometry, MaterialManager.box_edges_material));


        if (simpleShapes.length > 0) {
            let shape_geometry = new SimpleShapeGeometry(simpleShapes);
            let t_mesh = new Mesh(shape_geometry, SimpleFigure2DMaterial.create());
            t_mesh.name = MeshName.PainterPart;
            t_mesh.position.set(0, 0, -0.001);
            figure_mesh.add(t_mesh);
        }
        return figure_mesh;

    }



    drawSelectedRect(rect: ZRect, p_radius: number = 10) {
        this._context.lineWidth = 4;
        this.drawEdges(rect.edges);
        let points = rect.getRectPoints(1);

        for (let point of points) {
            let pos = this.project2D(point);
            this._context.beginPath();
            this._context.strokeRect(pos.x - p_radius / 2, pos.y - p_radius / 2, p_radius, p_radius);
            this._context.fillRect(pos.x - p_radius / 2, pos.y - p_radius / 2, p_radius, p_radius);
            this._context.closePath();
            this._context.stroke();

            this._context.lineWidth = 4;
        }
    }
}