import React, { useEffect, useState } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { useStore } from '@/models';
import { observer } from 'mobx-react-lite';
import { useTranslation } from 'react-i18next'
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { EventName } from '@/Apps/EventSystem';
import { TLayoutEntityContainer } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter';
import { Select } from '@svg/antd';
import { Else, If, Then } from 'react-if';

const RoomAreaBtns: React.FC<{mode?:number}> = (props:{mode?:number}={mode:0}) => {
    const [btnList, setBtnList] = useState<any>([]);
    const { styles } = useStyles();
    const store = useStore();
    const { t } = useTranslation();
    const [options, setOptions] = useState<any>([]);
    const [singleSelectRoom, setSingleSelectRoom] = useState<TRoomEntity>(null);
    const [isLandscape, setIsLandscape] = useState<boolean>(window.innerWidth < window.innerHeight);
    const layoutContainer: TLayoutEntityContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    useEffect(() => {
        setBtnList(store.homeStore.roomEntities);
    }, [store.homeStore.roomEntities]);

    const handleAreaClick = (area: TRoomEntity) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, area);
    };

    useEffect(() => {
        let options = store.homeStore.roomEntities.map((item: TRoomEntity) => {
          return {
            label: item.aliasName,
            value: item.uidN
          }
        })
        options.unshift({
          label: t('全屋'),
          value: 'all'
        })
        setOptions(options);
      }, [store.homeStore.roomEntities]);

    
    useEffect(() => {
        LayoutAI_App.on(EventName.selectRoom, (event: TRoomEntity) => {
            if (event) {
                store.homeStore.setSelectedRoom(event);
            } else {
                store.homeStore.setSelectedRoom(null);
            }

        });
    }, [])

    useEffect(() => {
        if((store.homeStore.selectedRoom))
        {
            setSingleSelectRoom(store.homeStore.selectedRoom);
        }
    }, [store.homeStore.selectedRoom])
    const isSingleRoomMode = (props.mode==1 && store.homeStore.isSingleRoom);

    const roomAreaBtns = () => {
        return (
            <div className={styles.container_listInfo}>
            {!isSingleRoomMode?
                <div
                    className={`${styles.btn} ${!store.homeStore.selectedRoom ? styles.selected : ''}`}
                    onClick={() => {
                        handleAreaClick(null)
                    }}
                >
                    {t('全屋')}
                </div> :
                <div className={styles.btn} onClick={() => {
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
                    store.homeStore.setIsSingleRoom(false);
                }}>
                    {t('返回全屋')}
                </div>
            }
            {btnList.map((area: TRoomEntity) => (
                <div
                    className={`${styles.btn} ${singleSelectRoom?.aliasName === area.aliasName ? styles.selected : ''}`}
                    key={area.aliasName}
                    onClick={() => {
                        if (store.homeStore.isSingleRoom) {
                            LayoutAI_App.DispatchEvent(LayoutAI_Events.SingleRoomLayout, area);
                        } else {
                            handleAreaClick(area);
                        }
                    }}
                >
                    {t(area.aliasName)}
                </div>
            ))}
        </div>
        )
    }

    return (
        <div className={styles.root} id='RoomAreaBtns'>

            <If condition = {isSingleRoomMode}>
                <Then>
                    {roomAreaBtns()}
                </Then>
                <Else>
                    <If condition = {!store.homeStore.IsLandscape}>
                        <Then>
                            {roomAreaBtns()}
                        </Then>
                        <Else>
                        <div className={styles.selectListBar}>
                                <Select
                                    value={store.homeStore.selectedRoom?.uidN || 'all'}
                                    style={{ width: '100%' }}
                                    size='small'
                                    options={options}
                                    dropdownStyle={{zIndex: 9999}}
                                    onChange={(value: any) => {
                                        if (value === 'all') {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, null);
                                        }
                                        else {
                                            LayoutAI_App.DispatchEvent(LayoutAI_Events.selectRoomArea, store.homeStore.roomEntities.find((item: TRoomEntity) => item.uidN === value));
                                        }
                                    }}
                                ></Select>
                            </div>
                        </Else>
                    </If>
                </Else>
            </If>
        </div>
    )
}

export default observer(RoomAreaBtns);