import React, { useState, useRef } from 'react';
import { Card, Typography, Button, Dropdown, Tooltip, message } from '@svg/antd';
import type { MenuProps } from '@svg/antd';
import useStyles from "./style"
import IconFont from '@/components/IconFont/iconFont';
import { useStore } from '@/models';
import { editSchemeInfo } from '../services/scheme';
import { getFullAddress } from '../services/address';

type ListItem = {
  area: number;
  layoutSchemeName: string;
  id: string;
  coverImage: string;
  updateDate: string;
  address?: string;
  province?: string;
  city?: string;
  district?: string;
};

const GridList: React.FC<{
  list: ListItem[];
  operation: (item: ListItem, event: string) => void;
  refresh: () => void;
  itemsToDisplay?: MenuProps['items'];
}> = ({ list, operation, refresh, itemsToDisplay = null }) => {

  const { styles } = useStyles();
  const [activeId, setActiveId] = useState<string | null>(null);
  const store = useStore();
  const allItems: MenuProps['items'] = [
    {
      key: 'open',
      label: '打开方案',
    },
    {
      key: 'newPageOpen',
      label: '新标签页打开方案',
    },
    {
      key: 'viewGallery',
      label: '查看方案图册',
    },
    {
      type: 'divider',
    },
    {
      key: 'copyScheme',
      label: '创建副本',
    },
    {
      key: 'renameScheme',
      label: '重命名',
    },
    {
      key: 'shareScheme',
      label: '分享方案',
      disabled: true,
    },
    {
      key: 'editScheme',
      label: '编辑方案信息',
    },
    {
      type: 'divider',
    },
    {
      key: 'deleteScheme',
      label: '删除',
    },
  ];

  // itemsToDisplay 必须是allItems的子集
  const schemeItems: MenuProps['items'] = itemsToDisplay ? itemsToDisplay : allItems

  const schemeOnClick =
    (item: ListItem): MenuProps['onClick'] =>
      ({ key }) => {
        if (key === 'renameScheme') {
          setEditingId(item.id);
          setNewSchemeName(item.layoutSchemeName);
        } else {
          operation(item, key);
        }
    };

  const formatTime = (time: string) => {
    const date = new Date(time);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60 && diffInSeconds > 0) {
      return `${diffInSeconds}秒前`;
    } else if (diffInSeconds <= 0) {
      return `刚刚`;
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}分钟前`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}小时前`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}天前`;
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months}个月前`;
    } else {
      return date.toISOString().split('T')[0]; // 返回 yyyy-MM-dd 格式
    }
  };

  const [editingId, setEditingId] = useState<string | null>(null);
  const [newSchemeName, setNewSchemeName] = useState<string>('');

  // 双击进入编辑模式
  const handleDoubleClick = (id: string, currentName: string) => {
    setEditingId(id);
    setNewSchemeName(currentName);
  };

  // 保存新名称
  const handleSave = async (id: string) => {
    // 在这里添加保存逻辑，例如更新状态或发送请求
    console.log(`保存新名称: ${newSchemeName}，ID: ${id}`);
    const res = await editSchemeInfo({ id, layoutSchemeName: newSchemeName });
    if (res.success) {
      message.success('重命名成功');
      refresh();
    }
    setTimeout(() => {
      setEditingId(null);
    }, 500); // 延迟
  };
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleContextMenu = (item: ListItem, event: React.MouseEvent) => {
    event.preventDefault(); // 阻止默认右键菜单
    const dropdownElement = event.currentTarget.querySelector(`.${styles.ellipsis}`) as HTMLElement;
    if (dropdownElement) {
      dropdownElement.click(); // 模拟点击下拉菜单图标
    }
  };


  return (
    <div>
      <div className={styles.cardContainer}>
        {list.map((item) => (
          <Card
            key={item.id}
            className={styles.card}
            style={{ width: 288 }}
            onClick={() => setActiveId(item.id)}
            styles={{
              body: {
                padding: '16px',
                minHeight: '76px',
              },
            }}
            onContextMenu={(e) => handleContextMenu(item, e)}
            cover={
              <div className={styles.imageContainer}>
                <img src={item.coverImage} />
                <div className={styles.overlay} style={activeId === item.id ? { opacity: 1 } : {}}>
                  <Button type='primary' className={styles.createButton} onClick={() => operation(item, 'open')}>
                    打开方案
                  </Button>
                  <Button className={styles.editButton} onClick={() => operation(item, 'viewGallery')}>
                    查看方案图册
                  </Button>
                </div>
              </div>
            }
          >
            <div className={styles.footerContainer}>
              {editingId === item.id ? (
                <input
                  ref={inputRef} // 绑定 ref
                  className={styles.focusInput}
                  value={newSchemeName}
                  onChange={(e) => setNewSchemeName(e.target.value)}
                  onBlur={() => handleSave(item.id)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSave(item.id)}
                  autoFocus
                />
              ) : (
                <Typography.Text
                  className={styles.buildingName}
                  onDoubleClick={() => handleDoubleClick(item.id, item.layoutSchemeName)}
                >
                  {item.layoutSchemeName}
                </Typography.Text>
              )}
              <div className={styles.buttonContainer}>
                <Dropdown
                  menu={{ items: schemeItems, onClick: schemeOnClick(item) }}
                  placement="bottomLeft"
                  overlayClassName={styles.dropdownMenu}
                  trigger={['click']}
                  overlayStyle={{zIndex: 10000}}
                >
                  <IconFont type="icon-gengduo_bold" className={''} />
                  {/* <Space size={4}>
                    <IconFont type="icon-gengduo_bold" className={`${styles.ellipsis} ${styles.iconHidden}`} />
                  </Space> */}
                </Dropdown>
              </div>
            </div>
            <div className={styles.footerContainer}>
              <div className={styles.roomAreaContainer}>
                <Typography.Text className={styles.roomType}>
                  {getFullAddress(item, store.homeStore.tmp)}
                </Typography.Text>
              </div>
              <Tooltip>
                <Typography.Text className={styles.time} title={`最后修改: ${item.updateDate}`}>
                  {formatTime(item.updateDate)}
                </Typography.Text>
              </Tooltip>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default GridList;
