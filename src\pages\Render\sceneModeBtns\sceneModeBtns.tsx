import { useTranslation } from "react-i18next";
import useCommonStyles from '../common_style';
import { observer } from "mobx-react-lite";
import { useEffect, useState } from "react";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { LightMainEvents } from "../lightMain/lightMain";
import { Segmented } from "@svg/antd";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import { Vector3 } from "three";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { SceneViewMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { CameraViewMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";

/**
 * @description 主页
 */
export enum LayoutPopEvents {
    setIsVisible = "setIsVisible",
    showPopup = "showPopup"
}
interface I_Btn {
    id: string;
    icon: string;
    label: string;
    onClick?: () => void;
}
const SceneModeBtns: React.FC = () => {
    const { t } = useTranslation()
    const { styles: common_styles } = useCommonStyles();
    const store = useStore();
    const [sceneMode, setSceneMode] = useState<SceneViewMode>(store.homeStore.viewMode as any || "2D");

    const object_id = "SceneModeBtns";
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let scene3D =  (LayoutAI_App.instance).scene3D as Scene3D;
    useEffect(() => {
        LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, true);
        if (sceneMode === "2D") {
            if (scene3D) {
                scene3D.stopRender();
            }
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, false);
            store.homeStore.setViewMode('2D');
        }
        else if (sceneMode === "3D") {
            LayoutAI_App.emit_M(LightMainEvents.showLight3DViewer, true);
            scene3D.setCemeraMode(CameraViewMode.Perspective);
            store.homeStore.setViewMode('3D');
            if (container._drawing_layer_mode === 'SingleRoom') {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.leaveSingleRoomLayout, {});
            }
            if (scene3D) {
                scene3D.startRender();
            }
        } else if (sceneMode === '3D_FirstPerson') /*[i18n:ignore]*/ {

            scene3D.setCemeraMode(CameraViewMode.FirstPerson);

            let room = store.homeStore.roomEntities.reduce((maxRoom: TRoomEntity | null, currentRoom: TRoomEntity) => {
                if (!maxRoom) return currentRoom; // 第一次迭代，返回当前房间
                return currentRoom._area > maxRoom._area ? currentRoom : maxRoom; // 返回面积更大的房间
            }, null);
            if (room) {
                scene3D.setCenter(room?._main_rect?.rect_center || new Vector3(0, 0, 0));
                scene3D.update();
            } else {
                scene3D.setCenter(store.homeStore.roomEntities[0]?._main_rect?.rect_center || new Vector3(0, 0, 0));
            }
            store.homeStore.setViewMode(sceneMode); /*[i18n:ignore]*/
            if (scene3D) {
                scene3D.startRender();
            }
        }
        LayoutAI_App.emit_M(EventName.Scene3DUpdated, true);
    }, [sceneMode])

    const options: { value: SceneViewMode, label: string }[] = [
        {
            value: "2D",
            label: t("2D"),
        },
        {
            value: '3D_FirstPerson', // [i18n:ignore]
            label: t("漫游")
        },
        {
            value: '3D', // [i18n:ignore]
            label: t("鸟瞰")
        }
    ]
    return <div className={common_styles.root} >
        <Segmented value={sceneMode} onChange={(item: any) => {
            setSceneMode(item);
        }} block size="middle" options={options} />
    </div>
};

export default observer(SceneModeBtns);
