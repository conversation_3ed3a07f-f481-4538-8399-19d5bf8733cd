"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[500],{65810:function(n,t,e){e.d(t,{z:function(){return k},A:function(){return I}});var r=e(13274),i=e(69802),o=e(79316),a=e(15696),c=e(41594),u=e(27347),s=e(98612),l=e(9003),f=e(20932),d=e(88934);function h(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function p(){var n=h(["\n      position:absolute;\n      left:0;\n      bottom:0;\n      height : 40px;\n      z-index:5;\n      width:100%;\n      background:#fff;\n      display:flex;\n      justify-content: center;\n    "]);return p=function(){return n},n}function b(){var n=h(["\n      float:left;\n      width:60px;\n      height:40px;\n      padding-top:1px;\n      padding-bottom:3px;\n      font-size:11px;\n      text-align:center;\n      margin-right:5px;\n      &.active {\n        background:rgba(0,0,0,0.05);\n      }\n      .iconfont {\n         font-size:20px;\n      }\n      &:hover {\n        background:rgba(0,0,0,0.05);\n      }\n    "]);return b=function(){return n},n}var v=(0,e(8268).rU)(function(n){var t=n.css;return{root:t(p()),bottom_btn:t(b())}}),m=e(73751);function g(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function x(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return g(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return g(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var y=(0,a.observer)(function(){var n=(0,i.B)().t,t=v().styles,e=x((0,c.useState)(!0),2),o=e[0],a=e[1],s=x((0,c.useState)(""),2),l=s[0],f=s[1],d=[{id:"Layout",label:n("AI布局"),icon:"iconbujusheji"},{id:"Matching",label:n("风格套系"),icon:"iconfenggetaoxi"}],h=x((0,c.useState)(d),2),p=h[0],b=h[1];return(0,c.useEffect)(function(){u.nb.on("setIsVisible",function(n){a(n)}),u.nb.on_M(k.showLight3DViewer,"bottom_pannel",function(t){b(t?[{id:"Layout",label:n("AI布局"),icon:"iconbujusheji"},{id:"Matching",label:n("风格套系"),icon:"iconfenggetaoxi"},{id:"CameraViews",label:n("视角"),icon:"icona-viewsswitching"}]:[{id:"Layout",label:n("AI布局"),icon:"iconbujusheji"},{id:"Matching",label:n("风格套系"),icon:"iconfenggetaoxi"}])})},[]),(0,c.useEffect)(function(){u.nb.emit(m.$.showPopup,l)},[l]),o?(0,r.jsx)("div",{className:t.root,onClick:function(){f("")},children:p.map(function(n,e){return(0,r.jsxs)("div",{className:t.bottom_btn+(n.id===l?" active":""),onClick:function(t){n.onClick?n.onClick():function(n){n.id===l?f(""):f(n.id)}(n),t.stopPropagation()},children:[(0,r.jsx)("div",{className:"iconfont "+n.icon}),(0,r.jsx)("div",{className:"btn_label",children:n.label})]},"bottom_btn_"+e)})}):(0,r.jsx)(r.Fragment,{})}),w=e(78644),j=e(93365),_=e(23825),S=e(99030),A=e(10371),D=e(32184);function z(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function C(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return z(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return z(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var k=function(n){return n.showLight3DViewer="showLight3DViewer",n}({}),I=(0,a.observer)(function(){var n=(0,i.B)().t,t=(0,o.A)().styles,e=C((0,c.useState)(""),2),a=(e[0],e[1]),h=C((0,c.useState)(-2),2),p=h[0],b=h[1],v=(0,l.P)();(0,c.useRef)(null);u.nb.UseApp(s.e.AppName),u.nb.instance&&(u.nb.t=n);var g=function(){u.nb.instance&&(u.nb.instance.bindCanvas(document.getElementById("cad_canvas")),u.nb.instance.update())};return(0,c.useEffect)(function(){u.nb.instance&&(u.nb.instance._is_website_debug=_.iG),window.addEventListener("resize",g),g(),u.nb.instance&&(u.nb.instance.initialized||(u.nb.instance.init(),u.nb.RunCommand(s.f.AiCadMode),u.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,u.nb.instance.layout_container.drawing_figure_mode=D.qB.Texture,u.nb.instance.prepare().then(function(){}),u.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),u.nb.instance.update()),u.nb.on_M("showLight3DViewer","LightMain",function(n){if(2==n)b(2),u.nb.instance.scene3D.setCemeraMode(A.I5.FirstPerson),u.nb.emit(S.r.UpdateScene3D,!0);else if(1==n){b(2);var t=u.nb.instance.scene3D;t&&t.setCemeraMode(A.I5.Perspective),u.nb.emit(S.r.UpdateScene3D,!1)}else b(-1)}),u.nb.on(d.U.LayoutSchemeOpened,function(n){a(n.name),u.nb.emit(j.$T,j.Kw.Default)})},[]),(0,r.jsxs)("div",{className:t.root,children:[(0,r.jsx)(j.Ay,{}),(0,r.jsx)(m.A,{}),(0,r.jsxs)("div",{id:"Canvascontent",className:t.content,children:[(0,r.jsx)("div",{className:"3d_container "+t.canvas3d,style:{zIndex:p},children:(0,r.jsx)(w.A,{defaultViewMode:5})}),(0,r.jsx)("div",{id:"body_container",className:t.canvas_pannel,children:(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){v.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){v.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var t=n.touches[0].clientX-n.touches[1].clientX,e=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(t*t+e*e);v.homeStore.setInitialDistance(r/v.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var t=n.touches[0].clientX-n.touches[1].clientX,e=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(t*t+e*e)/v.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),v.homeStore.setScale(r),u.nb.DispatchEvent(u.n0.scale,r)}},onTouchEnd:function(){v.homeStore.setInitialDistance(null)}})})]}),(0,r.jsx)(y,{}),(0,r.jsx)(f.A,{})]})})},79316:function(n,t,e){var r=e(23825),i=e(8268);function o(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=o(["\n      width:100%;\n      height:100vh;\n    "]);return a=function(){return n},n}function c(){var n=o(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return c=function(){return n},n}function u(){var n=o(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return u=function(){return n},n}function s(){var n=o(["\n        position: absolute;\n        left:-100px;\n        top: -100px;\n        background-color: #EAEAEB;\n        width : calc(100% + 200px);\n        height : calc(100% + 200px);\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(./static/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(./static/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(./static/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(./static/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(./static/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(./static/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(./static/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return s=function(){return n},n}function l(){var n=o(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return l=function(){return n},n}function f(){var n=o(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return f=function(){return n},n}function d(){var n=o(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return d=function(){return n},n}function h(){var n=o(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return h=function(){return n},n}t.A=(0,i.rU)(function(n){var t=n.css;return{root:t(a()),content:t(c()),canvas3d:t(u()),canvas_pannel:t(s(),(0,r.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:t(l()),backBtn:t(f()),forwardBtn:t(d()),schemeNameSpan:t(h())}})},93365:function(n,t,e){e.d(t,{$T:function(){return C},Kw:function(){return z},Ay:function(){return k}});var r=e(13274),i=e(69802),o=e(8268);function a(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function c(){var n=a(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:40px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:15;\n    "]);return c=function(){return n},n}function u(){var n=a(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:40px;\n      color:#333;\n      float:left;\n    "]);return u=function(){return n},n}function s(){var n=a(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:40px;\n      color:#333;\n    "]);return s=function(){return n},n}function l(){var n=a(["\n      width:100%;\n      font-size:16px;\n      line-height:40px;\n      text-align:center;\n    "]);return l=function(){return n},n}var f=(0,o.rU)(function(n){var t=n.css;return{navigation:t(c()),backBtn:t(u()),forwardBtn:t(s()),schemeNameSpan:t(l())}}),d=e(15696),h=e(41594),p=e(27347);function b(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function v(){var n=b(["\n      width:calc(100% - 180px);\n      max-width:240px;\n      margin:auto;\n      height:100%;\n      border-radius:2px;\n      display:flex;\n      justify-content: center;\n    "]);return v=function(){return n},n}function m(){var n=b(["\n      width:80px;\n      text-align:center;\n      line-height:40px;\n      font-size:16px;\n      color : #777;\n      &.active {\n        background:#147ffa33;\n        color : #000;\n      }\n    "]);return m=function(){return n},n}var g=(0,o.rU)(function(n){var t=n.css;return{root:t(v()),state_btn:t(m())}}),x=e(65810);function y(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function w(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return y(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return y(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var j=(0,d.observer)(function(){var n=(0,i.B)().t,t=g().styles,e=w((0,h.useState)(!0),2),o=(e[0],e[1],w((0,h.useState)("2D"),2)),a=o[0],c=o[1],u=[{id:"2D",label:"2D",icon:"",onClick:function(){c("2D")}},{id:"3D",label:"3D",icon:"",onClick:function(){c("3D")}},{id:"3D_travel",label:n("漫游"),icon:"",onClick:function(){c("3D_travel")}}],s=w((0,h.useState)(u),2),l=s[0];s[1];return(0,h.useEffect)(function(){},[]),(0,h.useEffect)(function(){"2D"===a?p.nb.emit_M(x.z.showLight3DViewer,0):"3D"===a?p.nb.emit_M(x.z.showLight3DViewer,1):"3D_travel"===a&&p.nb.emit_M(x.z.showLight3DViewer,2)},[a]),(0,r.jsx)("div",{className:t.root,children:l.map(function(n,e){return(0,r.jsxs)("div",{className:t.state_btn+(n.id===a?" active":""),onClick:function(t){n.onClick&&n.onClick(),t.stopPropagation()},children:[(0,r.jsx)("div",{className:"iconfont "+n.icon}),(0,r.jsx)("div",{className:"btn_label",children:n.label})]},"scene_mode_btn"+e)})})}),_=e(20932),S=e(65640);function A(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function D(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,i,o=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(n){c=!0,i=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw i}}return o}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return A(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return A(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var z=function(n){return n.Default="Default",n.HouseSearch="HouseSearch",n.HuaweiDemo="HuaweiDemo",n}({}),C="NavigationEvent",k=(0,d.observer)(function(n){(0,i.B)().t;var t=f().styles,e=D((0,h.useState)(!0),2),o=(e[0],e[1],D((0,h.useState)((null==n?void 0:n.state)||"Default"),2)),a=o[0],c=o[1];return(0,h.useEffect)(function(){p.nb.on(C,function(n){n||(n="Default"),S.log(n),c(n)})},[]),(0,h.useEffect)(function(){"HouseSearch"===a?p.nb.emit(_.u.setIsVisible,!0):p.nb.emit(_.u.setIsVisible,!1)},[a]),"HouseSearch"===a?(0,r.jsx)("div",{className:t.navigation,children:(0,r.jsxs)("div",{className:t.forwardBtn,onClick:function(){p.nb.emit(C,"Default")},children:[" 布局"," ->"]})}):"HuaweiDemo"===a?(0,r.jsx)("div",{className:t.navigation,children:(0,r.jsx)(j,{})}):(0,r.jsxs)("div",{className:t.navigation,children:[(0,r.jsx)("div",{className:t.backBtn+" iconfont iconreturn",onClick:function(){c("HouseSearch")},children:" 户型"}),(0,r.jsxs)("div",{className:t.forwardBtn,onClick:function(){},children:[" 完成"," ->"]}),(0,r.jsx)(j,{})]})})}}]);