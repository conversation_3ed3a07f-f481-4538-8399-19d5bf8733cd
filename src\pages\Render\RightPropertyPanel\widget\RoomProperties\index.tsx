import React, { useEffect, useState} from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { EventName } from '@/Apps/EventSystem';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import MenuList from '../menuList';
import { g_FigureImagePaths } from "@/Apps/LayoutAI/Drawing/FigureImagePaths";
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import { getPrefix } from '@/utils/common';
import { useStore } from '@/models';
import { useTranslation } from 'react-i18next';
import { TAppManagerBase } from '@/Apps/AppManagerBase';

interface RoomSeriesPlanProps {
  roomInfo: TRoom;
  currenScheme: any;
  roomInfos: TRoom[];
  showLayoutList: boolean;
  selectedFigureElement: TFigureElement;
}
interface FigureProps  { image_path: string, title: string, centerTitle: string, bottomTitle: string, checked?: boolean, figure_element: TFigureElement, room?:TRoom }
const RoomProperties: React.FC<RoomSeriesPlanProps> = ({ roomInfo, currenScheme, roomInfos, showLayoutList, selectedFigureElement }) => {
  const { t } = useTranslation();
  const { styles }: any = useStyles();
  const [menuList, setMenuList] = useState<any>([]);
  const [area, setArea] = useState(0);
  const [updated, setUpdate] = useState(0);
  let figure_list:FigureProps[] = [];
  let good_list:FigureProps[] = [];
  let hard_decoration_list : FigureProps[] = [];
  let custom_cabinet_list : FigureProps[] = [];
  const [locked, setLocked] = useState<number>(0);

  const store = useStore();
  const UpdateFigureList = () => {

    let styleList: any = [];
    for (let room of (LayoutAI_App.instance as TAppManagerBase).layout_container._rooms) {
        if (room.uuid === roomInfo.uuid) {
          styleList.push({
            title: room._scope_series_map?.soft?.ruleName||null,
            title2: room._scope_series_map?.cabinet?.ruleName||null,
            title3: room._scope_series_map?.hard?.ruleName||null,
            img: room._scope_series_map?.soft?.thumbnail||null,
            img1: room._scope_series_map?.cabinet?.thumbnail||null,
            img2: room._scope_series_map?.hard?.thumbnail || null,
            bottomTitle: room.roomname,
              area: room.area?.toFixed(2),
              room: room
            })
        }
    }

    if (showLayoutList) {
      setMenuList([
        {
          label: t('风格'),
          figureList: styleList,
        },
        {
          label: t('图例'),
          figureList: figure_list,
        },
      ])
    } else {
      let menuList = [
        {
          label: t('风格'),
          figureList: styleList,
        },
        {
          label: t('定制'),
          figureList: custom_cabinet_list
        },
        {
          label: t('软装'),
          figureList: good_list
        },
        {
          label: t('硬装'),
          figureList: hard_decoration_list,
        }
      ];
      // if (figure_list.length > 0) {
      //   menuList.push({
      //     label: t('图例'),
      //     figureList: figure_list,
      //   });
      // }
      setMenuList(menuList);
    }
  }

  const onClickLock = () => {
    if (locked == 1) {
      setLocked(0);
      roomInfo.locked = false;
    } else {
      setLocked(1);
      roomInfo.locked = true;
    }
  }

  useEffect(() => {
    for (let fig_data of figure_list) {
      if (fig_data.figure_element === selectedFigureElement) {
        fig_data.checked = true;
      }
      else {
        fig_data.checked = false;
      }
    }
    UpdateFigureList();
  }, [selectedFigureElement]);
  useEffect(() => {
    if (!roomInfo) return;
    setLocked(roomInfo.locked ? 1 : 0);
    figure_list = [];
    good_list = [];
    if (showLayoutList) {
      let figure_ele_list = [...roomInfo._furniture_list];
      figure_ele_list.sort((a,b)=>{
        return b.default_drawing_order - a.default_drawing_order;
      })
      figure_ele_list.map((item: TFigureElement) => {

        if(!LayoutAI_App.IsDebug && (item._is_decoration || item._is_sub_board )) return;
        figure_list.push({
          image_path: g_FigureImagePaths[item.sub_category]?.png || 'https://3vj-fe.3vjia.com/layoutai/image/square_pillar.svg',
          title: t(item.modelLoc) + " | " + t(item.sub_category),
          centerTitle: `${Math.round(item.length)}*${Math.round(item.depth)}*${Math.round(item.height)}`,
          bottomTitle: LayoutAI_App.IsDebug?(t('离地高')+Math.round(item.min_z))+"mm":'',
          figure_element: item,
          room: roomInfo
        })
      });
    }
    else {
      // 选择了套系后才展示商品列表
      let hasSeries = store.homeStore.room2SeriesSampleArray.some((item: any) => item[0].uuid === roomInfo.uuid);
      if (hasSeries) {
        let figure_ele_list = [...roomInfo._furniture_list];
        roomInfo._furniture_list.forEach(fe => figure_ele_list.push(...fe.getAlternativeFigureElements()));
        figure_ele_list.sort((a,b)=>{
          return b.default_drawing_order - a.default_drawing_order;
        })
        figure_ele_list.map((item) => {

          if(!LayoutAI_App.IsDebug && (item._is_decoration || item._is_sub_board )) return;
          if(!item.haveMatchedMaterial() && !item.haveDeletedMaterial()) {
            figure_list.push({
              image_path: g_FigureImagePaths[item.sub_category]?.png || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",
              title: t(item.modelLoc) + " | " + t(item.sub_category),
              centerTitle: `${Math.round(item.length)}*${Math.round(item.depth)}*${Math.round(item.height)}`,
              bottomTitle: LayoutAI_App.IsDebug?(t('离地高')+Math.round(item.min_z)+"mm"):'',
              figure_element: item,
              room: roomInfo
            })
            return;
          }
          if(item._decoration_type==="Electricity") return;
          let img_path = item._matched_material?.imageUrl || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || "无"; //[i18n:ignore]
          const good:FigureProps = {
            image_path: img_path,
            title: t(item.sub_category) +" "+ t("素材ID:") + modelId,
            centerTitle: `${t('目标尺寸')} ${Math.round(item.rect.length)}*${Math.round(item.rect.depth)}`,
            bottomTitle: item?._matched_material && item?._matched_material?.modelId ? (`${t('素材尺寸')} ${Math.round(item?._matched_material?.length)}*${Math.round(item?._matched_material?.width)}*${Math.round(item?._matched_material?.height)}`
              + ` Z${Math.round(item?._matched_material?.targetPosition?.z || 0)}`) : '',
            figure_element: item,
            room:roomInfo
          };
          if (item.haveMatchedCustomCabinet()) {
            custom_cabinet_list.push(good);
          } else {
            good_list.push(good);
          }
        });

        // 更新硬装的内容
        let hard_figures = roomInfo.getHardDecorationList();

        hard_figures.forEach(item=>{
          if (!item.haveMatchedMaterial()) return;
          let img_path = item._matched_material?.imageUrl || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg";
          let modelId = item._matched_material?.modelId || "无"; //[i18n:ignore]
          hard_decoration_list.push({
            image_path: img_path,
            title: t(item.sub_category) +" "+ t("素材ID:") + modelId,
            centerTitle: t("名称:")+(item?._matched_material?.name||"")+" "+((item.category==="吊顶"&&item._matched_material?.topOffset)?(t("下吊")+item._matched_material.topOffset)+"mm":""),
            bottomTitle: t("适用: ")+((t(item?._matched_material?.applySpace) || false) ? item._matched_material.applySpace.map(space => t(space)).join(",") : "全屋"),
            figure_element: item
          })
        })
      }
    }

    // console.log('roomInfo', roomInfo);
    UpdateFigureList();
    let area = 0;
    roomInfos.map((room: TRoom) => {
      area += room?.room_shape?._area || 0;
    });
    setArea(parseFloat(area.toFixed(2)));

    if (LayoutAI_App.instance) {
      LayoutAI_App.on(EventName.RoomMaterialsUpdated, () => {
        setUpdate(updated + 1);
      });
    }    
  }, [roomInfo, currenScheme, updated, showLayoutList, store.homeStore.room2SeriesSampleArray]);


  return (
    <div className={styles.root} >
      <div className={styles.rootInfo}>
        <div>
         <div className={styles.rootItem}>
            <div>{t('名称')}</div>
            <div>{t(roomInfo.name)}</div>
          </div>
          <div className={styles.rootItem}>
            <div>{t('房屋所使用的面积')}</div>
            <div>{area}m²</div>
          </div>
        </div>
        <div className={styles.line}>
        </div>
        <MenuList menuList={menuList} showLayoutList={showLayoutList}></MenuList>
      </div>
      {showLayoutList ?
        <></>
      : 
        <div className={styles.clearBtn}>
          <button onClick={() => {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ClearSeries, roomInfo);
            setMenuList([
              {
                label: t('风格'),
                figureList: [],
              },
              {
                label: t('家具'),
                figureList: []
              }
            ])
          }}>{t('清除套系')}</button>
        </div>
      }
    </div>
  );
};


export default observer(RoomProperties);
