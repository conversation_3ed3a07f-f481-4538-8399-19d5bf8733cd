
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      position:fixed;
      left:0;
      bottom:0px;
      width:100%;
      overflow: hidden;
      background-color: #fff;
      border-radius: 16px 16px 0px 0px;
      box-shadow: 0px -16px 16px 0px #00000005;
      z-index: 99;
      @media screen and (orientation: landscape) {
        position:fixed;
        left: -1px !important;
        top: 52px !important;
        bottom: 0 !important;
        right: auto !important;
        max-height: calc(var(--vh, 1vh) * 100);
        width: auto;
        border-radius: 0px;
        box-shadow: 0px 0px 16px 10px #0000000A;
      }
    `,
    topTitle: css`
      display: flex;
      height: 40px;
      padding: 0 24px;
      align-items: center;
      font-size: 20px;
      color: #282828;
      font-weight: 600;
      margin-top: 16px;
      justify-content: space-between;
      @media screen and (max-width: 450px) { // 手机宽度
        height: 15px;
        font-size: 16px;
      }
      @media screen and (orientation: landscape) {
        height: 40px;
        font-size: 14px;
      }
    `,
    listContainer: css`
      height:100%;
      width:100%;
    `,
    open: css`
      position: absolute; 
      right: 10px;
      top: 10px;
      z-index: 9;
    `
  }

});
