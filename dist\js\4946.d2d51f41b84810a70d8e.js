"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4946],{11192:function(n,e,t){t.d(e,{a:function(){return r}});var i=t(27347),r=function(n){var e=new Date,t=new Date(n),r=e.getTime()-t.getTime(),a=i.nb.t,o=Math.floor(r/1e3),s=Math.floor(o/60),l=Math.floor(s/60),c=Math.floor(l/24),d=Math.floor(c/30);return o>0&&o<60?"".concat(o).concat(a("秒前")):o<=0?a("刚刚"):s<60?"".concat(s).concat(a("分钟前")):l<24?"".concat(l).concat(a("小时前")):c<31?"".concat(c).concat(a("天前")):d<12?"".concat(d).concat(a("个月前")):n.split(" ")[0]}},14946:function(n,e,t){t.r(e),t.d(e,{default:function(){return Ce}});var i=t(13274),r=t(41594),a=t(15696),o=t(69802),s=t(9003),l=t(27347),c=t(98612),d=t(88934),u=t(23825),p=t(84872),f=t(8268);function g(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function h(){var n=g(["\n      width:100%;\n      height:100vh;\n      display:flex;\n      flex-direction: column;\n      background: #F6F7F9;\n    "]);return h=function(){return n},n}function x(){var n=g(["\n      width:100%;\n      padding: 20px;\n      display:flex;\n      justify-content: space-between;\n\n      @media screen and (max-width: 450px){\n        padding: 8px;\n      }\n      .left{\n        display:flex;\n        align-items: center;\n        span{\n          color: #000000;\n          font-family: PingFang SC;\n          font-weight: 600;\n          font-size: 16px;\n          line-height: 1.5;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n        .back_button{\n          display:flex;\n          align-items: center;\n          height: 30px;\n          width: 74px;\n          border-radius: 8px;\n          background: #FFFFFF;\n          border: 1px solid #00000026;\n          cursor: pointer;\n          span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n        }\n      }\n      .right {\n        display: flex;\n        align-items: center;\n        justify-content: flex-end;\n                \n        .history_button{\n          display:flex;\n          align-items: center;\n          justify-content: center;\n          height: 30px;\n          width: 98px;\n          // margin-right: 12px;\n          border-radius: 8px;\n          background: #FFFFFF;\n          border: 1px solid #00000026;\n          cursor: pointer;\n          gap: 2px;\n          span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 12px;\n            line-height: 1.67;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n          svg {\n            width: 16px;\n            height: 16px;\n          }\n        }\n      }\n    "]);return x=function(){return n},n}function m(){var n=g(["\n      display: flex;\n      width: 100%;\n      height: calc(var(--vh, 1vh) * 100 - 72px);\n      padding: 0 20px 20px 20px;\n      gap: 20px;\n      position: relative;\n    "]);return m=function(){return n},n}function b(){var n=g(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n      width: 100%;\n      height: calc(var(--vh, 1vh) * 100 - 72px);\n      padding: 20px;\n      gap: 20px;\n      position: relative;\n\n      @media screen and (max-width: 450px){\n        padding: 8px;\n        gap: 8px;\n        height: calc(var(--vh, 1vh) * 100 - 48px);\n      }\n\n      .FilterFieldBtn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 254px;\n        height: 40px;\n        border-radius: 6px;\n        background: #EAEAEB;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        gap: 8px;\n      }\n    "]);return b=function(){return n},n}function y(){var n=g(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: 100%;\n\n      @media screen and (max-width: 450px){\n        height: 120px;\n      }\n    "]);return y=function(){return n},n}function v(){var n=g(["\n      padding: 20px;\n      position:absolute;\n      left: 0;\n      top: 0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n      background: #f6f7f9;\n    "]);return v=function(){return n},n}function w(){var n=g(["\n      position:absolute;\n      left: -1500px;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return w=function(){return n},n}function F(){var n=g(["\n      position: absolute;\n      top: 48px;\n      left: 500px; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n      visibility:hidden;\n    "]);return F=function(){return n},n}function _(){var n=g(["\n      position: absolute;\n      left: 0px;\n      top: -100px;\n      background-color: #EAEAEB;\n      width : calc(100% + 100px);\n      height : calc(100% + 200px);\n      overflow: hidden;\n      .canvas {\n        position : absolute;\n        left: 0px;\n        top: 0px;\n        &.canvas_drawing {\n          cursor : url(./static/icons/cursor_drawing.png) 8 8,auto;\n        }\n        &.canvas_moving {\n          cursor : url(./static/icons/cursor_moving.png) 16 16,auto;\n        }\n        &.canvas_leftmove {\n          cursor : url(./static/icons/cursor_leftmove.png) 16 16,auto;\n        }\n        &.canvas_rightmove {\n          cursor : url(./static/icons/cursor_rightmove.png) 16 16,auto;\n        }\n        &.canvas_acrossmove {\n          cursor : url(./static/icons/cursor_acrossmove.png) 16 16,auto;\n        }\n        &.canvas_verticalmove {\n          cursor : url(./static/icons/cursor_verticalmove.png) 16 16,auto;\n        }\n        &.canvas_text {\n          cursor : text;\n        }\n        &.canvas_pointer {\n          cursor : pointer;\n        }\n        &.canvas_splitWall {\n          cursor : url(./static/icons/split.png) 8 16,auto;\n        }\n      }\n\n      .canvas_btns {\n        width: auto;\n        margin: 0 auto;\n        position: fixed;\n        display: flex;\n        justify-content: center;\n        bottom: 35px;\n        z-index:10;\n        left: 50%;\n        transform: translateX(-50%);\n        .btn {\n          ","\n          border-radius: 6px;\n          border: none;\n\n          font-weight: 600;\n          margin-right: 10px;\n          margin-left: 10px;\n        }\n        .design_btn {\n          background: #e6e6e6;\n          margin-right: 20px;\n        }\n        @media screen and (max-height: 600px){\n          bottom: 50px !important;\n        }\n      }\n    "]);return _=function(){return n},n}var j=(0,f.rU)(function(n){var e=n.css;return{root:e(h()),topMenu:e(x()),main_container:e(m()),IsLandscape_main_container:e(b()),drawer_btn:e(y()),mobile_atlas_container:e(v()),canvas3d:e(w()),content:e(F()),Scene3DDivcanvas_pannel:e(_(),(0,u.fZ)()?"\n            width: 120px;\n            height: 36px;\n            font-size: 14px;\n          ":"\n            width: 200px;\n            height: 48px;\n            font-size: 16px;\n          ")}}),k=t(10371),S=t(76330),A=t(79750),I=t(43417),C=t(99030),N=t(78644),L=t(62634),E=t(37112),z=t(90110),P=t(72978),O=t(51187),B=t(17655);function D(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function R(){var n=D(["\n      width: 440px;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      padding: 20px;\n\n      @media screen and (max-width: 1600px) {\n        width: 302px;\n      }\n    "]);return R=function(){return n},n}function T(){var n=D(["\n      width: 100%;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      padding: 20px;\n      min-height: 200px;\n    "]);return T=function(){return n},n}function M(){var n=D(["\n      width: 100%;\n      height: calc(100% - 64px);\n      overflow-y: auto;\n      /* 隐藏滚动条 - Webkit浏览器 */\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      \n      /* 隐藏滚动条 - Firefox */\n      scrollbar-width: none;\n      \n      /* 隐藏滚动条 - IE */\n      -ms-overflow-style: none;\n    "]);return M=function(){return n},n}function H(){var n=D(["\n      width: 100%;\n      height: auto;\n\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .Hx_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 92px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        .room_button {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 58px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n          padding: 0;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n      \n      .HxSearch {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 120px;\n        margin-top: 12px;\n        border-radius: 8px;\n        cursor: pointer;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n\n        svg {\n          width: 54.05px;\n          height: 45.81px;\n          color: #736AFF;\n        }\n\n        .HxSearch_text {\n          display: flex;\n          flex-direction: column;\n          margin-left: 8px;\n          .HxSearch_text_title {\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: 600;\n            font-size: 16px;\n            line-height: 1.5;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n          .HxSearch_text_content {\n            color: #959598;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n          }\n        }\n      }\n    "]);return H=function(){return n},n}function W(){var n=D(["\n      width: 100%;\n      margin-top: 12px;\n      \n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 300px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        // @media screen and (max-width: 1600px) {\n        //   height: 197px;\n        // }\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .loading_list_container{\n        display: flex;\n        align-items: center;\n        width: 100%;\n        gap: 8px;\n        overflow-x: auto;\n        flex-wrap: nowrap;\n        margin-top: 8px;\n        position: relative;\n          \n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        \n        .list_loading_item{\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 94px;\n          height: 70.5px;\n          flex-shrink: 0;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                         linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n        .IsLandscape_loading_item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 127px;\n          height: 95.25px;\n          flex-shrink: 0;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n                      \n          @media screen and (max-width: 450px) {\n            width: 94px;\n            height: 70.5px;\n          }\n\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                         linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n      }\n\n      .main_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 300px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: 1px solid #0000000F;\n        position: relative;\n\n        // @media screen and (max-width: 1600px) {\n        //   height: 197px;\n        // }\n\n        .IsLandscape_main_img{\n          width: auto;\n          height: 100%;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n        }\n        .main_img{\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n        }\n        .edit_btn{\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 4px;\n          background: #0000007F;\n          width: 64px;\n          height: 28px;\n          color: #FFFFFF;\n          font-family: PingFang SC;\n          font-weight: 600;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          position: absolute;\n          bottom: 12px;\n          cursor: pointer;\n        }\n\n        .page_button {\n          position: absolute;\n          top: 50%;\n          transform: translateY(-50%);\n          width: 14px;\n          height: 26px;\n          color: white;\n          border-radius: 2px;\n          background: #0000007F;\n          border: none;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 2;\n          padding: 0;\n\n          &:hover {\n            background: #0000007F;\n          }\n\n          &.left {\n            left: 12px;\n          }\n\n          &.right {\n            right: 12px;\n          }\n        }\n      }\n\n      .list_wrapper {\n        position: relative;\n        width: 100%;\n        margin-top: 8px;\n\n        .scroll_button {\n          position: absolute;\n          top: 50%;\n          transform: translateY(-50%);\n          width: 14px;\n          height: 26px;\n          border-radius: 2px;\n          background: #0000004C;\n          border: none;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 2;\n          padding: 0;\n          color: #FFFFFF;\n\n          &:hover {\n            background: #00000066;\n          }\n\n          &.left {\n            left: 4px;\n          }\n\n          &.right {\n            right: 4px;\n          }\n        }\n\n        .list_container{\n          display: flex;\n          width: 100%;\n          gap: 8px;\n          overflow-x: auto;\n          flex-wrap: nowrap;\n          position: relative;\n          \n          scrollbar-width: none;\n          -ms-overflow-style: none;\n          &::-webkit-scrollbar {\n            display: none;\n          }\n\n          .list_img {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            min-width: 94px;\n            width: 94px;\n            height: 70.5px;\n            flex-shrink: 0;\n            border-radius: 4px;\n            border: 1px solid #0000000F;\n            position: relative;\n            cursor: pointer;\n\n            &:hover {\n              border: 2px solid #3D9EFF;\n            }\n\n            &.select {\n              border: 2px solid #147FFA;\n            }\n          }\n          .IsLandscape_list_img {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            min-width: 94px;\n            width: 127px;\n            height: 95.25px;\n            flex-shrink: 0;\n            border-radius: 4px;\n            border: 1px solid #0000000F;\n            position: relative;\n            cursor: pointer;\n            \n            @media screen and (max-width: 450px) {\n              width: 94px;\n              height: 70.5px;\n            }\n\n            &.select {\n              border: 2px solid #147FFA;\n            }\n          }\n          .icon-check {\n            position: absolute;\n            top: 0;\n            right: 0;\n            z-index: 1;\n          }\n        }\n      }\n    "]);return W=function(){return n},n}function U(){var n=D(["\n      margin-top: 20px;\n      width: 100%;\n      height: auto;\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        button {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 65px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n    "]);return U=function(){return n},n}function Z(){var n=D(["\n      margin-top: 20px;\n      width: 100%;\n      height: auto;\n      .title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n      .btn_group {\n        height: auto;\n        width: 100%;\n        margin-top: 12px;\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        .selectNum {\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 58px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 24px;\n          letter-spacing: 0px;\n          text-align: center;\n\n          background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n          border: 1px solid transparent;\n          background-clip: padding-box;\n          position: relative;\n\n          &::after {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 4px;\n            padding: 1px;\n            background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n                  linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                          linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n      }\n    "]);return Z=function(){return n},n}function X(){var n=D(["\n      margin-top: 20px;\n      width: 100%;\n      height: 44px;\n      max-width: 300px;\n      border-radius: 8px;\n      border: none;\n      background: #EAEAEB;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 14px;\n      line-height: 1.5;\n      letter-spacing: 0px;\n      text-align: center;\n      box-shadow: none;\n      margin-right: 12px;\n    "]);return X=function(){return n},n}function Y(){var n=D(["\n      margin-top: 20px;\n      width: 100%;\n      height: 44px;\n      max-width: 300px;\n      border-radius: 8px;\n      border: none;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #FFFFFF;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 14px;\n      line-height: 1.57;\n      letter-spacing: 0px;\n      text-align: center;\n      box-shadow: none;\n\n      &:hover {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n        color: #FFFFFF !important;\n      }\n\n      &:disabled {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n        color: #FFFFFF !important;\n        cursor: not-allowed;\n        opacity: 0.5;\n      }\n    "]);return Y=function(){return n},n}function V(){var n=D(["\n      position:absolute;\n      width:100%;\n      height:100%;\n    "]);return V=function(){return n},n}function q(){var n=D(["\n      width: 1000px;\n      height: 500px;      \n      canvas {\n        position:absolute;\n        left:0;\n        top:0;\n      }  \n    "]);return q=function(){return n},n}function $(){var n=D(["\n      width: 50%;\n      // height: 700px;\n      height: 100%;\n      position: relative;\n\n      .name_tag {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 58px;\n        height: 24px;\n        position: absolute;\n        top: 10px;\n        left: 10px;\n        border-radius: 4px;\n        background: #0000007F;\n        color: #FFFFFF;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        z-index: 4;\n      }\n\n      .list_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 60px;\n        position: absolute;\n        bottom: 12px;\n        gap: 8px;\n        z-index: 4;\n\n        .list_img {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          min-width: 94px;\n          width: 94px;\n          height: 70.5px;\n          flex-shrink: 0;\n          border-radius: 4px;\n          border: 1px solid #0000000F;\n          position: relative;\n          cursor: pointer;\n\n          &:hover {\n            border: 2px solid #3D9EFF;\n          }\n\n          &.select {\n            border: 2px solid #147FFA;\n          }\n        }\n      }\n    "]);return $=function(){return n},n}function G(){var n=D(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n      height: calc(100% - 40px);\n      width: 100%;\n    "]);return G=function(){return n},n}function Q(){var n=D(["\n      position: relative;\n      width: 44%;\n      height: 100%;  \n      overflow: hidden;\n    "]);return Q=function(){return n},n}function J(){var n=D(["\n      position: absolute;\n      left: 0px;\n      top: 0px;\n      background-color: #EAEAEB;\n      width : 100%;\n      height : 100%;\n      overflow: hidden;\n      canvas{\n        position: absolute;\n        left: 0px;\n        top: 0px;\n      }\n    "]);return J=function(){return n},n}function K(){var n=D(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 16px;\n      height: 32px;\n      width: 100%;\n      position: absolute;\n      bottom: 20px;\n      z-index: 4;\n\n      button{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        cursor: pointer;\n        width: 93px;\n        height: 32px;\n        border-radius: 6px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return K=function(){return n},n}function nn(){var n=D(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      flex-direction: column;\n      width: 284px;\n      height: 162px;\n      position: absolute;\n      padding: 20px;\n      border-radius: 12px;\n      background: #FFFFFF;\n      box-shadow: 0px 8px 24px 0px #00000028;\n      z-index: 5;\n      bottom: 40px;\n      left: 500px;\n      transform: translateX(-50%);\n      .setting_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: auto;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return nn=function(){return n},n}function en(){var n=D(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      width: 100%;\n      height: 40px;\n      .confirm_btn {\n        width: 160px;\n        height: 40px;\n        border-radius: 8px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #FFFFFF;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: center;\n        box-shadow: none;\n\n        &:hover {\n          background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%) !important;\n          color: #FFFFFF !important;\n        }\n      }\n      .cancel_btn {\n        width: 160px;\n        height: 40px;\n        border-radius: 8px;\n        border: none;\n        background: #EAEAEB;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: center;\n        box-shadow: none;\n        margin-right: 12px;\n\n        &:hover {\n          background: #EAEAEB !important;\n          color: #282828 !important;\n        }\n      }\n    "]);return en=function(){return n},n}function tn(){var n=D(["\n      position: relative;\n      width: 100%;\n      margin-top: 8px;\n\n      .scroll_button {\n        position: absolute;\n        top: 50%;\n        transform: translateY(-50%);\n        width: 14px;\n        height: 26px;\n        border-radius: 2px;\n        background: #0000004C;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n        padding: 0;\n        color: #FFFFFF;\n\n        &:hover {\n          background: #00000066;\n        }\n\n        &.left {\n          left: 4px;\n        }\n\n        &.right {\n          right: 4px;\n        }\n      }\n\n      .btn_group {\n        display: flex;\n        width: 100%;\n        gap: 10px;\n        overflow-x: auto;\n        flex-wrap: nowrap;\n        position: relative;\n        \n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        &::-webkit-scrollbar {\n          display: none;\n        }\n\n        .room_button {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          flex-shrink: 0;\n          position: relative;\n\n          border-radius: 4px;\n          background: #FAFAFA;\n          border: 1px solid #EAEAEB;\n          width: 58px;\n          height: 28px;\n          cursor: pointer;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 20px;\n          letter-spacing: 0px;\n          text-align: center;\n          padding: 0;\n\n          &.selected {\n            background: linear-gradient(90deg, rgba(181, 152, 248, 0.2) 0%, rgba(137, 164, 245, 0.2) 100%);\n            border: 1px solid transparent;\n            background-clip: padding-box;\n            position: relative;\n\n            &::after {\n              content: '';\n              position: absolute;\n              inset: 0;\n              border-radius: 4px;\n              padding: 1px;\n              background: linear-gradient(90deg, #BD92FF 0%, #7788FF 100%);\n              mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n              mask-composite: exclude;\n              -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                           linear-gradient(#fff 0 0);\n              -webkit-mask-composite: xor;\n              pointer-events: none;\n            }\n          }\n        }\n      }\n    "]);return tn=function(){return n},n}var rn=(0,f.rU)(function(n){var e=n.css;return{root:e(R()),IsLandscape_root:e(T()),info_content:e(M()),Housetype_perspective:e(H()),Housetype_Info:e(W()),style_type:e(U()),quantity:e(Z()),cancelBtn:e(X()),submitBtn:e(Y()),viewerContainer:e(V()),scene3D:e(q()),canvas3d:e($()),edit_content:e(G()),content:e(Q()),canvas_pannel:e(J()),operation_panel:e(K()),cameraSettingsPopup:e(nn()),footer:e(en()),mobile_btn_container:e(tn())}}),an=t(89264),on=t(65640);function sn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function ln(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function cn(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var a=n.apply(e,t);function o(n){ln(a,i,r,o,s,"next",n)}function s(n){ln(a,i,r,o,s,"throw",n)}o(void 0)})}}function dn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||pn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function un(n){return function(n){if(Array.isArray(n))return sn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||pn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pn(n,e){if(n){if("string"==typeof n)return sn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?sn(n,e):void 0}}function fn(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var gn=(0,a.observer)(function(){var n,e=(0,o.B)().t,t=(0,s.P)(),a=rn().styles,c=dn((0,r.useState)(null),2),p=c[0],f=c[1],g=dn((0,r.useState)(null),2),h=g[0],x=g[1],m=dn((0,r.useState)(0),2),b=m[0],y=m[1],v=dn((0,r.useState)(!1),2),w=v[0],F=v[1],_=(0,r.useRef)(null),j=(0,r.useRef)([]),D=dn((0,r.useState)(window.innerWidth<window.innerHeight),2),R=D[0],T=D[1],M=dn((0,r.useState)(!1),2),H=M[0],W=M[1],U=dn((0,r.useState)(null),2),Z=U[0],X=U[1],Y=dn((0,r.useState)(null),2),V=Y[0],q=Y[1],$=dn((0,r.useState)(!1),2),G=$[0],Q=$[1],J=dn((0,r.useState)([]),2),K=J[0],nn=J[1],en=dn((0,r.useState)([]),2),tn=en[0],sn=en[1],ln=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("edit_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=R),T(window.innerWidth<window.innerHeight)};(0,r.useEffect)(function(){window.addEventListener("resize",ln),ln();cn(function(){var n,e;return fn(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,I.Y.getLightTemplate()];case 1:return(n=t.sent()).length>0?(nn(n),an.W.instance.getBlackboard().getSelectedTemplates().length>0?an.W.instance.getBlackboard().getSelectedTemplates().forEach(function(n){sn(function(e){return un(e).concat([n.id])})}):sn([n[0].id])):on.error("获取到错误的灯光模板数据",n),[3,3];case 2:return e=t.sent(),on.error("获取到灯光模板报错:",e),[3,3];case 3:return[2]}})})()},[]);(0,r.useEffect)(function(){if(t.homeStore.roomInfos.length>0){e=l.nb.instance.layout_container,i=l.nb.instance.scene3D,l.nb.emit(C.r.UpdateScene3D,!1),i.setCemeraMode(k.I5.FirstPerson),A.q.updateViewCameraEntities(e);var n=setTimeout(function(){return cn(function(){var n;return fn(this,function(e){return t.homeStore.roomInfos.length>0&&(n=j.current[0])&&n.click(),y(b+1),[2]})})()},5e3);return function(){return clearTimeout(n)}}var e,i},[]),(0,r.useEffect)(function(){t.aiLightStore.setAllowCommit(!1)},[]);var pn=function(n){n.currentTarget.scrollLeft+=n.deltaY,n.preventDefault()},gn=(0,r.useRef)(null),hn=dn((0,r.useState)(!1),2),xn=hn[0],mn=hn[1],bn=dn((0,r.useState)(!1),2),yn=bn[0],vn=bn[1],wn=function(){if(gn.current){mn(gn.current.scrollLeft>0);var n=gn.current.scrollLeft+gn.current.clientWidth>=gn.current.scrollWidth-1;vn(!n)}},Fn=function(n){if(gn.current){var e="left"===n?gn.current.scrollLeft-102:gn.current.scrollLeft+102;gn.current.scrollTo({left:e,behavior:"smooth"})}};(0,r.useEffect)(function(){wn()},[p]);var _n=function(){on.log("handleDeleteView")},jn=["客餐厅","卧室","厨房","卫生间","阳台","其他"],kn=function(n,e){var t=jn.findIndex(function(e){return n._room_entity.aliasName.includes(e)}),i=jn.findIndex(function(n){return e._room_entity.aliasName.includes(n)});return-1!==t&&-1!==i?t-i:-1!==t?-1:-1!==i?1:n._room_entity.aliasName.localeCompare(e._room_entity.aliasName)};return(0,i.jsxs)("div",{className:R?a.IsLandscape_root:a.root,children:[(0,i.jsxs)("div",{className:a.info_content,children:[(0,i.jsxs)("div",{className:a.Housetype_perspective,children:[(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,i.jsx)("span",{className:"title",children:e("选择视角")}),0==t.homeStore.roomInfos.length&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"Hx_button",children:(0,i.jsx)("span",{onClick:function(){l.nb.emit(d.U.OpenHouseSearching,!0)},style:{height:20,width:60},children:e("搜索户型图")})})})]}),t.homeStore.roomInfos.length>0&&(0,i.jsxs)("div",{className:a.Housetype_Info,children:[(0,i.jsx)("div",{className:"btn_group",style:{marginBottom:10},children:t.homeStore.roomInfos.slice().sort(kn).map(function(n,r){return(0,i.jsx)("button",{ref:function(n){return j.current[r]=n},className:"room_button ".concat(p===n._room_entity?"selected":""),onClick:function(){return cn(function(){var e,i,r,a,o,s,c,d,u;return fn(this,function(p){switch(p.label){case 0:t.aiLightStore.setAllowCommit(!1),f(n._room_entity),e=l.nb.instance.scene3D,i=!0,r=!1,a=void 0,p.label=1;case 1:p.trys.push([1,6,7,8]),o=n._room_entity._view_cameras[Symbol.iterator](),p.label=2;case 2:return(i=(s=o.next()).done)?[3,5]:(c=s.value,e.active_controls.bindViewEntity(c),e.update(),d=l.nb.instance.layout_container,[4,c.updatePerspectiveViewImg(d.painter)]);case 3:p.sent(),p.label=4;case 4:return i=!0,[3,2];case 5:return[3,8];case 6:return u=p.sent(),r=!0,a=u,[3,8];case 7:try{i||null==o.return||o.return()}finally{if(r)throw a}return[7];case 8:return t.aiLightStore.setAllowCommit(!0),x(n._room_entity._view_cameras[0]),y(b+1),[2]}})})()},children:e(n._room_entity.aliasName)},n.uid)})}),!t.aiLightStore.allowCommit&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"main_loading_container",children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"28px"}}),(0,i.jsx)("span",{children:e("AI视角生成中...")})]}),(0,i.jsx)("div",{className:"loading_list_container",children:Array.from({length:4}).map(function(n,e){return(0,i.jsx)("div",{className:R?"IsLandscape_loading_item":"list_loading_item"},e)})})]}),p&&t.aiLightStore.allowCommit&&h&&(0,i.jsxs)("div",{className:"main_container",onMouseEnter:function(){return W(!0)},onMouseLeave:function(){return W(!1)},children:[(0,i.jsx)("img",{className:R?"IsLandscape_main_img":"main_img",src:h._perspective_img.src}),(0,i.jsx)("button",{className:"page_button left",onClick:function(){if(p&&h){var n=p._view_cameras.findIndex(function(n){return n===h});n>0&&x(p._view_cameras[n-1])}},style:{display:p&&h&&0!==p._view_cameras.findIndex(function(n){return n===h})&&(H||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo",size:24})}),(0,i.jsx)("button",{className:"page_button right",onClick:function(){if(p&&h){var n=p._view_cameras.findIndex(function(n){return n===h});n<p._view_cameras.length-1&&x(p._view_cameras[n+1])}},style:{display:p&&h&&p._view_cameras.findIndex(function(n){return n===h})!==p._view_cameras.length-1&&(H||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou",size:24})})]},b),p&&t.aiLightStore.allowCommit&&(0,i.jsxs)("div",{className:"list_wrapper",children:[(0,i.jsx)("div",{className:"list_container",ref:gn,onWheel:pn,onScroll:wn,children:p._view_cameras.map(function(n,e){return(0,i.jsxs)("div",{style:{position:"relative"},children:[(0,i.jsx)("img",{className:"".concat(R?"IsLandscape_list_img":"list_img"," ").concat(h===n?"select":""),src:n._perspective_img.src,onClick:function(){x(n),l.nb.instance.scene3D.active_controls.bindViewEntity(n)}}),h===n&&(0,i.jsx)(S.A,{className:"icon-check",type:"icon-check"})]},e)})},b),xn&&(0,i.jsx)("button",{className:"scroll_button left",onClick:function(){return Fn("left")},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo"})}),yn&&(0,i.jsx)("button",{className:"scroll_button right",onClick:function(){return Fn("right")},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou"})})]})]}),t.homeStore.roomInfos.length<=0&&(0,i.jsxs)("div",{className:"HxSearch",onClick:function(){l.nb.emit(d.U.OpenHouseSearching,!0)},children:[(0,i.jsx)(S.A,{type:"icon-huxing_L"}),(0,i.jsxs)("div",{className:"HxSearch_text",children:[(0,i.jsx)("span",{className:"HxSearch_text_title",children:e("搜索户型图")}),(0,i.jsx)("span",{className:"HxSearch_text_content",children:e("从户型库中搜索户型")})]})]})]}),t.homeStore.roomInfos.length>0&&K.length>0&&(0,i.jsxs)("div",{className:a.style_type,children:[(0,i.jsx)("span",{className:"title",children:e("灯光模板")}),(0,i.jsx)("div",{className:"btn_group",children:K.map(function(n){return(0,i.jsx)("button",{onClick:function(){return e=n.id,void sn(function(n){if(n.findIndex(function(n){return n===e})>-1)return n.filter(function(n){return n!==e});var t=K.find(function(n){return n.id===e});return t?un(n).concat([t.id]):n});var e},className:tn.includes(n.id)?"selected":"",children:n.categoryName},n.id)})}),(0,i.jsxs)("div",{className:a.quantity,children:[(0,i.jsx)("span",{className:"title",children:e("数量")}),(0,i.jsx)("div",{className:"btn_group",children:(0,i.jsx)("div",{className:"selectNum",children:tn.length})})]})]})]}),(0,i.jsx)("div",{style:{width:"100%",display:"flex",justifyContent:"center",alignContent:"center"},children:(0,i.jsx)(E.A,{className:a.submitBtn,onClick:function(){return cn(function(){var n;return fn(this,function(i){switch(i.label){case 0:return null!==p&&h?tn.length<=0?(L.A.warning(e("请选择灯光模板")),[2]):l.nb.instance.layout_container._layout_scheme_id?[3,2]:[4,cn(function(){return fn(this,function(n){return l.nb.DispatchEvent(l.n0.SaveLayoutSchemeAs,{address:void 0,schemename:l.nb.instance.layout_container._layout_scheme_name,telephone:void 0,username:void 0}),[2]})})()]:(L.A.warning(e("请选择视角")),[2]);case 1:i.sent(),i.label=2;case 2:return n=K.filter(function(n){return tn.includes(n.id)}),an.W.instance.getBlackboard().setSelectedTemplates(n),on.log("selectedTemplates length",n.length),t.aiLightStore.setIsEdit(!1),t.aiLightStore.setIsLoading(!0),t.aiLightStore.setIsSelectPerspective(!1),t.aiLightStore.setCurrentAILightImageID(null),an.W.instance.commitTask(),[2]}})})()},disabled:t.aiLightStore.isLoading||t.homeStore.roomInfos.length<=0||tn.length<=0||!t.aiLightStore.allowCommit,children:e("提交任务")})}),(0,i.jsx)(B._w,{center:!0,height:800,width:1500,draggable:!0,title:e("编辑视角"),onClose:function(){F(!1)},style:{zIndex:w?1e3:-1},children:(0,i.jsxs)("div",{style:{padding:"20px",display:"flex",flexDirection:"column",height:"100%"},children:[(0,i.jsxs)("div",{className:a.edit_content,children:[(0,i.jsxs)("div",{className:"3d_container "+a.canvas3d,children:[(0,i.jsx)(N.A,{defaultViewMode:7}),(0,i.jsx)("div",{className:"name_tag",children:null==V?void 0:V.aliasName}),(0,i.jsx)("div",{className:"list_container",ref:gn,onWheel:pn,onScroll:wn,children:null==V||null===(n=V._view_cameras)||void 0===n?void 0:n.map(function(n,e){return(0,i.jsx)("div",{style:{position:"relative"},children:(0,i.jsx)("img",{className:"list_img ".concat(Z===n?"select":""),src:n._perspective_img.src,onClick:function(){X(n),l.nb.instance.scene3D.active_controls.bindViewEntity(n)}})},e)})},b)]}),(0,i.jsxs)("div",{id:"Canvascontent",className:a.content,children:[(0,i.jsx)("div",{ref:_,id:"body_container",className:a.canvas_pannel,children:(0,i.jsx)("canvas",{id:"edit_canvas",className:"canvas",onMouseEnter:function(){t.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){t.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+i*i);t.homeStore.setInitialDistance(r/t.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,i=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+i*i)/t.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),t.homeStore.setScale(r),l.nb.DispatchEvent(l.n0.scale,r)}},onTouchEnd:function(){t.homeStore.setInitialDistance(null)}})}),(0,i.jsxs)("div",{className:a.operation_panel,children:[(0,i.jsx)("button",{children:e("新增视角")}),(0,i.jsx)(z.A,{title:e("确认删除当前视角？"),onConfirm:_n,okText:e("确定"),cancelText:e("取消"),children:(0,i.jsx)("button",{children:e("删除视角")})}),(0,i.jsx)(z.A,{title:e("确认重置当前视角？"),onConfirm:_n,okText:e("确定"),cancelText:e("取消"),children:(0,i.jsx)("button",{children:e("重置视角")})}),(0,i.jsx)("button",{onClick:function(){Q(!G)},children:e("相机设置")}),G&&(0,i.jsxs)("div",{className:a.cameraSettingsPopup,children:[(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("空间")}),(0,i.jsx)(P.A,{defaultValue:null==V?void 0:V.aliasName,style:{width:"80%"},onChange:function(n){var e=t.homeStore.roomInfos.find(function(e){return e._room_entity.aliasName===n});q(e._room_entity)},options:t.homeStore.roomInfos.slice().sort(kn).map(function(n){return{value:n._room_entity.aliasName,label:n._room_entity.aliasName}})})]}),(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("镜头")}),(0,i.jsx)(O.A,{style:{width:"75%"},defaultValue:33,marks:{0:e("特写"),33:e("人眼"),66:e("标准"),100:e("广角")}})]}),(0,i.jsxs)("div",{className:"setting_item",children:[(0,i.jsx)("span",{style:{marginRight:"12px"},children:e("裁剪")}),(0,i.jsx)(O.A,{style:{width:"75%"},defaultValue:50})]})]})]})]})]}),(0,i.jsxs)("div",{className:a.footer,children:[(0,i.jsx)(E.A,{className:"confirm_btn",onClick:function(){on.log("handleEditConfirm")},children:e("确定")}),(0,i.jsx)(E.A,{className:"cancel_btn",onClick:function(){return F(!1)},children:e("取消")})]})]})})]})}),hn=t(5723),xn=t(80277),mn=t(30268);function bn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function yn(){var n=bn(["\n      width: ",";\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n      \n      @media screen and (max-width: 1600px) {\n        width: ",";\n      }\n    "]);return yn=function(){return n},n}function vn(){var n=bn(["\n      width: 100%;\n      height: 100%;\n      min-height: 400px;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n              \n      @media screen and (max-width: 450px) {\n        padding: 8px 8px 20px 8px;\n      }\n    "]);return vn=function(){return n},n}function wn(){var n=bn(["\n      display: flex;\n      width: 100%;\n      height: 56px;\n    "]);return wn=function(){return n},n}function Fn(){var n=bn(["\n      display: flex;\n      flex-direction: column;\n      margin-left: 8px;\n      .title_info_name {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        max-width: 60vw;\n        @media screen and (max-width: 450px) { // 手机宽度\n          font-size: 14px;\n        }\n      }\n      .design_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 92px;\n        margin-left: 12px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n      .title_info_other{\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        margin-top: 4px;\n        color: #5B5E60;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n\n      }\n    "]);return Fn=function(){return n},n}function _n(){var n=bn(["\n      width: 100%;\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n      position: relative;\n\n      .page_button {\n        position: absolute;\n        top: calc(50% - 55px);\n        transform: translateY(-50%);\n        width: ",";\n        height: ",";\n        color: white;\n        border-radius: 2px;\n        background: #0000004C;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n        padding: 0;\n\n        &:hover {\n          background: #0000007F;\n        }\n\n        &.left {\n          left: 12px;\n        }\n\n        &.right {\n          right: 12px;\n        }\n      }\n      \n      .empty_content {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        flex: 1;\n        min-height: 0;\n        border-radius: 8px;\n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          margin-top: 10px;\n        }\n        svg {\n          width: 100px;\n          height: 100px;\n        }\n      }\n      \n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        flex: 1;\n        min-height: 0;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          margin-top: 4px;\n          @media screen and (max-width: 450px) {\n            width: 65%;\n          }\n        }\n      }\n\n      .main_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        flex: 1;\n        min-height: 100px;\n        background: #EAEAEB;\n        position: relative;\n\n        @media screen and (max-width: 450px) {\n          background: #F5F5F5;\n        }\n        .ant-image-mask\n        {\n          display: none !important;\n        }\n        img{\n          max-height: 100%;\n          max-width: 100%;\n        }\n      }\n\n      .bottom_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 8px;\n        margin-top: 20px;\n        width: 100%;\n        overflow: hidden;\n      }\n      \n      .view_icon {\n        position: absolute;\n        left: 0;\n        top: 0;\n        z-index: 1;\n        width: 65px;\n        height: 26px;\n        border-radius: 4px;\n        background: #0000007F;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        opacity: 0.8;\n\n        span {\n          color: #FFFFFF;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n\n      .diffuse_img_container {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        position: relative;\n        width: 120px;\n        height: 90px;\n        flex-shrink: 0;\n\n        .diffuse_img {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          border: 1px solid #FFFFFF;\n        }\n      }\n\n      .list_container{\n        display: flex;\n        align-items: center;\n        margin: 0;\n        gap: 8px;\n        min-width: 0;\n        overflow-x: auto;\n        // padding: 0 20px;\n        position: relative;\n        \n        /* 隐藏滚动条 */\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        -ms-overflow-style: none;\n        scrollbar-width: none;\n\n        .list_img_container{\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            position: relative;\n            width: 120px;\n            height: 90px;\n            flex-shrink: 0;\n\n        .img_item{\n          flex-shrink: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 120px;\n          height: 90px;\n          border-radius: 4px;\n          border: 1px solid #FFFFFF;\n          cursor: pointer;\n\n          &.selected {\n            border-radius: 4px;\n              border: 2px solid #147FFA;\n            }\n          }\n        }\n\n        .loading_item{\n          flex-shrink: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 120px;\n          height: 90px;\n          border-radius: 8px;\n          background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n          position: relative;\n          &::before {\n            content: '';\n            position: absolute;\n            inset: 0;\n            border-radius: 8px;\n            padding: 1px;\n            background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n            mask: linear-gradient(#fff 0 0) content-box, \n            linear-gradient(#fff 0 0);\n            mask-composite: exclude;\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, \n            linear-gradient(#fff 0 0);\n            -webkit-mask-composite: xor;\n            pointer-events: none;\n          }\n        }\n      }\n      .bottom_button_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-top: 20px;\n        gap: 20px;\n        width: 100%;\n\n        border-radius: 8px;\n        border: none;\n        color: white;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: center;\n        box-shadow: none;\n      }\n    "]);return _n=function(){return n},n}function jn(){var n=bn(["\n      width: 100%;\n      height: 40px;\n      padding: 0 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: absolute;\n      bottom: 0px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 1;\n      background-color: #eaeaeb;\n      opacity: 0.8;\n    "]);return jn=function(){return n},n}function kn(){var n=bn(["\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n      position: absolute;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 1;\n      \n      .iconBtn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 28px;\n        height: 28px;\n        border-radius: 2.4px;\n        background: #FFFFFF33;\n        backdrop-filter: blur(4px);\n        box-shadow: ;\n        cursor: pointer;\n        color: #F2F2F2;\n        svg{ \n          width: 16px;\n          height: 16px;\n        }\n      }\n      .mobile_icon_btn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 48px;\n        height: 36px;\n        border-radius: 4px;\n        background: #0000004C;\n        backdrop-filter: blur(4px);\n        box-shadow: ;\n        cursor: pointer;\n        color: #FFFFFF;\n        svg{ \n          width: 16px;\n          height: 16px;\n        }\n        span{\n          color: #FFFFFF;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 10px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n        }\n      }\n    "]);return kn=function(){return n},n}var Sn=(0,f.rU)(function(n){var e=n.css;return{root:e(yn(),(0,u.fZ)()?"calc(100% - 460px)":"calc(100% - 760px)",(0,u.fZ)()?"calc(100% - 322px)":"calc(100% - 582px)"),IsLandscape_root:e(vn()),title:e(wn()),title_info:e(Fn()),img_content:e(_n(),(0,u.fZ)()?"20px":"28px",(0,u.fZ)()?"42px":"56px"),slider_container:e(jn()),operation_container:e(kn())}}),An=t(87927),In=t(32085),Cn=t(65640);function Nn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Ln(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function En(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var a=n.apply(e,t);function o(n){Ln(a,i,r,o,s,"next",n)}function s(n){Ln(a,i,r,o,s,"throw",n)}o(void 0)})}}function zn(n,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](n):n instanceof e}function Pn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||Bn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function On(n){return function(n){if(Array.isArray(n))return Nn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Bn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bn(n,e){if(n){if("string"==typeof n)return Nn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Nn(n,e):void 0}}function Dn(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var Rn=(0,a.observer)(function(){var n=(0,o.B)().t,e=(0,s.P)(),t=Sn().styles,a=Pn((0,r.useState)({designStyle:[],rooms:[]}),2),d=a[0],p=a[1],f=Pn((0,r.useState)([]),2),g=f[0],h=f[1],x=Pn((0,r.useState)(null),2),m=x[0],b=x[1],y=Pn((0,r.useState)(!1),2),v=y[0],w=y[1],F=(0,r.useRef)(null),_=Pn((0,r.useState)(window.innerWidth<window.innerHeight),2),j=_[0],k=_[1],A=Pn((0,r.useState)(0),2),I=A[0],C=A[1],N=Pn((0,r.useState)(!1),2),z=N[0],P=N[1],B=Pn((0,r.useState)(5),2),D=B[0],R=B[1];l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=n);var T=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("cad_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=j),k(window.innerWidth<window.innerHeight)};(0,r.useEffect)(function(){if(h([]),l.nb.instance.layout_container.aidraw_img){var n,e=(null===(n=l.nb.instance.layout_container.aidraw_img)||void 0===n?void 0:n.startsWith("https://"))?l.nb.instance.layout_container.aidraw_img:"https://img3.admin.3vjia.com/".concat(l.nb.instance.layout_container.aidraw_img);Cn.log("imageUrl",e)}},[l.nb.instance.layout_container._layout_scheme_id]),(0,r.useEffect)(function(){window.addEventListener("resize",T),T();En(function(){var n,e;return Dn(this,function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return n=t.sent(),p(n),Cn.log("获取到配置数据:",d),[3,4];case 3:return e=t.sent(),Cn.error("获取配置失败:",e),[3,4];case 4:return[2]}})})()},[]),(0,r.useEffect)(function(){e.aiLightStore.currentAILightImageID&&(h([e.aiLightStore.currentAILightImageID]),b({imageResult:e.aiLightStore.currentAILightImageID}),Cn.log("设置了selectedImg",e.aiLightStore.currentAILightImageID))},[e.aiLightStore.currentAILightImageID]),(0,r.useEffect)(function(){var n=function(n){h(function(e){var t=an.W.instance.getBlackboard().getSelectedTemplates(),i=On(e).concat([{imageResult:n.detail,tag:t[e.length].categoryName}]);return Cn.log("更新后的 length",i.length,t.length),i.length===t.length?(Cn.log("所有图片加载完成，图片列表为",i),i):i})};return window.addEventListener("updateAtlasPic",n),function(){window.removeEventListener("updateAtlasPic",n)}},[]),(0,r.useEffect)(function(){e.aiLightStore.isLoading&&g.length===an.W.instance.getBlackboard().getSelectedTemplates().length&&(e.aiLightStore.setIsLoading(!1),e.aiLightStore.setIsEdit(!0),b(g[0]))},[g]),(0,r.useEffect)(function(){e.aiLightStore.isLoading&&h([])},[e.aiLightStore.isLoading]);var M=function(){return En(function(){var e,t,i,r;return Dn(this,function(a){switch(a.label){case 0:if(!m)return[2];a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(m.imageResult)];case 2:return[4,a.sent().blob()];case 3:return e=a.sent(),t=window.URL.createObjectURL(e),(i=document.createElement("a")).href=t,i.download="image_".concat(Date.now(),".png"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t),[3,5];case 4:return r=a.sent(),Cn.error("下载失败:",r),L.A.error(n("下载失败")),[3,5];case 5:return[2]}})})()},H=function(){if(g&&m){var n=g.findIndex(function(n){return n===m});n>0&&b(g[n-1])}},W=function(){if(g&&m){var n=g.findIndex(function(n){return n===m});n<g.length-1&&b(g[n+1])}},U=function(n){if(null!==n){var e="string"==typeof n?parseFloat(n):n;R(e)}};return(0,i.jsx)("div",{className:j?t.IsLandscape_root:t.root,children:(0,i.jsxs)("div",{className:t.img_content,children:[g.length>1&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("button",{className:"page_button left",onClick:H,style:{display:!e.aiLightStore.isLoading&&g&&m&&0!==g.findIndex(function(n){return n===m})&&(v||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangzuo",size:24})}),(0,i.jsx)("button",{className:"page_button right",onClick:W,style:{display:!e.aiLightStore.isLoading&&g&&m&&g.findIndex(function(n){return n===m})!==g.length-1&&(v||(0,u.fZ)())?"flex":"none"},children:(0,i.jsx)(S.A,{type:"icon-a-fangxiangyou",size:24})})]}),e.aiLightStore.isLoading&&(0,i.jsxs)("div",{className:"main_loading_container",onMouseEnter:function(){return w(!0)},onMouseLeave:function(n){var e=n.relatedTarget;zn(e,HTMLElement)&&!e.classList.contains("page_button")&&w(!1)},children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:n("AI效果图生成中，预计等待1分钟。若退出本页，可在历史任务查看结果。")})]}),!e.aiLightStore.isLoading&&!m&&(0,i.jsxs)("div",{className:"empty_content",children:[(0,i.jsx)(S.A,{type:"icon-none"}),(0,i.jsxs)("span",{children:["-",n("暂无内容"),"-"]})]}),!e.aiLightStore.isLoading&&m&&(0,i.jsxs)("div",{className:"main_container",onMouseEnter:function(){return w(!0)},onMouseLeave:function(n){var e=n.relatedTarget;zn(e,HTMLElement)&&!e.classList.contains("page_button")&&w(!1)},onTouchStart:function(n){var e=n.touches[0];C(e.clientX),P(!1)},onTouchMove:function(n){if(!z){var e=n.touches[0],t=e.clientX-I;Math.abs(t)>50&&(P(!0),t>0?H():W(),C(e.clientX))}},children:[(0,i.jsx)(mn.A,{height:"100%",width:"100%",style:{objectFit:"contain"},src:m.imageResult}),(0,i.jsx)(i.Fragment,{children:e.aiLightStore.isEdit?(0,i.jsxs)("div",{className:t.slider_container,children:[(0,i.jsx)(hn.A,{span:3,children:(0,i.jsx)("span",{children:n("灯光亮度")})}),(0,i.jsx)(hn.A,{span:12,children:(0,i.jsx)(O.A,{min:1,max:20,onChange:U,value:"number"==typeof D?D:0,step:.1})}),(0,i.jsx)(hn.A,{span:3,children:(0,i.jsx)(xn.A,{min:1,max:20,style:{margin:"0 16px",width:"100%"},value:D,onChange:U})}),(0,i.jsx)(hn.A,{span:2,children:(0,i.jsx)(E.A,{style:{borderRadius:"20px",marginLeft:"10px"},onClick:function(){return U(5)},children:(0,i.jsx)("span",{children:n("恢复默认")})})})]}):(0,u.fZ)()?(0,i.jsx)("div",{className:t.operation_container,children:(0,i.jsxs)("div",{className:"mobile_icon_btn",onClick:M,children:[(0,i.jsx)(S.A,{type:"icon-xiazai"}),(0,i.jsx)("span",{children:n("下载")})]})}):(0,i.jsx)("div",{className:t.operation_container,children:(0,i.jsx)(An.A,{title:n("下载"),children:(0,i.jsx)("div",{className:"iconBtn",onClick:M,children:(0,i.jsx)(S.A,{type:"icon-xiazai"})})})})})]}),(0,i.jsx)("div",{className:"bottom_container",children:!e.aiLightStore.isLoading&&m&&e.aiLightStore.isEdit?(0,i.jsx)("div",{className:"list_container",ref:F,onWheel:function(n){F.current&&(n.preventDefault(),F.current.scrollLeft+=n.deltaY)},children:g.map(function(n,e){return(0,i.jsxs)("div",{className:"list_img_container",children:[(0,i.jsx)("div",{className:"view_icon",children:(0,i.jsx)("span",{children:n.tag})}),(0,i.jsx)("img",{className:"img_item ".concat(m===n?"selected":""),src:n.imageResult,alt:"",onClick:function(){b(n)}},e)]},n.tag)})}):e.aiLightStore.isLoading&&(0,i.jsx)("div",{className:"list_container",children:an.W.instance.getBlackboard().getSelectedTemplates().map(function(n){return(0,i.jsx)("div",{className:"loading_item",children:(0,i.jsx)("div",{className:"view_icon",children:(0,i.jsx)("span",{children:n.categoryName})})},n.id)})})}),m&&e.aiLightStore.isEdit&&(0,i.jsxs)("div",{className:"bottom_button_container",children:[(0,i.jsx)(E.A,{type:"primary",onClick:function(){return En(function(){var t,i,r,a;return Dn(this,function(o){switch(o.label){case 0:return e.aiLightStore.setRefreshAtlas(!0),t=l.nb.instance.layout_container._layout_scheme_id,i=In.s.instance,[4,an.W.instance.renderSchemeOffline(t,c.e.instance.scene3D.scene)];case 1:return r=o.sent(),a=In.s.instance.atlasQueueId,r.success?(L.A.success(r.msg),e.aiLightStore.setRefreshAtlas(!0),[4,i.getTargetAtlasUrl(t,a)]):[3,3];case 2:return o.sent(),e.aiLightStore.setRefreshAtlas(!0),[3,4];case 3:L.A.error(n("离线渲染失败:")+r.msg),o.label=4;case 4:return[2]}})})()},style:{width:"180px"},children:n("保存到图册")}),(0,i.jsx)(E.A,{type:"primary",onClick:function(){return En(function(){return Dn(this,function(e){return L.A.info(n("暂未开放")),[2]})})()},style:{width:"180px",background:"linear-gradient(90deg, #fc454b 0%, #9304f4 100%)"},children:n("基于此图重新渲染")})]})]})})});function Tn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Mn(){var n=Tn(["\n      width: 280px;\n      height: 100%;\n      border-radius: 12px;\n      background: #FFFFFF;\n      border-left: 1px solid #0000000F;\n      padding: 20px;\n      display: flex;\n      flex-direction: column;\n      \n      @media screen and (max-width: 1600px) {\n        width: 240px;\n      }\n\n      .content_container {\n        flex: 1;\n        overflow-y: auto;\n        margin-right: -8px;\n        padding-right: 8px;\n\n        /* 隐藏滚动条 - Webkit浏览器 */\n        &::-webkit-scrollbar {\n          display: none;\n        }\n        \n        /* 隐藏滚动条 - Firefox */\n        scrollbar-width: none;\n        \n        /* 隐藏滚动条 - IE */\n        -ms-overflow-style: none;\n      }\n    "]);return Mn=function(){return n},n}function Hn(){var n=Tn(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: 600;\n      font-size: 16px;\n      line-height: 1.5;\n      letter-spacing: 0px;\n      text-align: left;\n      margin-bottom: 12px;\n\n      .more_button{\n        display:flex;\n        align-items: center;\n        justify-content: center;\n        height: 28px;\n        width: 80px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        cursor: pointer;\n\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n    "]);return Hn=function(){return n},n}function Wn(){var n=Tn(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      position: relative;\n      margin-bottom: 12px;\n\n      .backdrop_content{\n        border-radius: 4px 4px 0 0;\n        background: #E0E1E1;\n        width: calc(100% - 20px);\n        height: 10px;\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        height: 181px;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        \n        &.select{\n          border: 2px solid #147FFA;\n        }\n\n        @media screen and (max-width: 1600px) {\n          height: 150.83px;\n        }\n\n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin-top: 7px;\n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 65%;\n        }\n        .time{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n        }\n        svg {\n          color: #5B5E60 !important;\n        }\n\n        .icon_wrapper {\n            padding: 4px;\n            margin-left: 4px;\n            cursor: pointer;\n            border-radius: 2px;\n            \n            &:hover, &.hover {\n                background: #EAEAEB;\n            }\n\n            svg {\n                color: #5B5E60 !important;\n            }\n        }\n      }\n\n      .main_img_container{\n        width: 100%;\n        height: 181px;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n\n        &.select{\n          border: 2px solid #147FFA;\n        }\n\n        @media screen and (max-width: 1600px) {\n          height: 150.83px;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            border-radius: 4px;\n            object-fit: cover;\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n    "]);return Wn=function(){return n},n}function Un(){var n=Tn(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      flex-direction: column;\n      gap: 4px;\n      height: 44px;\n      width: 100%;\n      .more_text {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n      }\n      .more_button{\n        color: #147FFA;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n        cursor: pointer;\n      }\n    "]);return Un=function(){return n},n}function Zn(){var n=Tn(["\n      .ant-modal-content {\n        height: 80vh;\n        overflow-y: auto;\n        padding: 0;\n      }\n      .ant-modal-header {\n        height: 40px;\n        background: #F4F5F5;\n        padding: 8px 20px;\n        margin-bottom: 0;\n      }\n      \n      .ant-modal-close {\n        width: 24px;\n        height: 24px;\n        top: 8px;\n        &:hover {\n          background: #F4F5F5;\n        }\n      }\n      .ant-modal-title {\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n      }\n    "]);return Zn=function(){return n},n}function Xn(){var n=Tn(["\n      display: grid;\n      grid-gap: 20px;\n      padding: 20px 0 20px 20px;\n      max-height: calc(80vh - 40px);\n      overflow-y: auto;\n      position: relative;\n\n      @media screen and (min-width: 1400px) {\n          grid-template-columns: repeat(5, calc(20% - 20px));\n      }\n      \n      @media screen and (max-width: 1400px) and (min-width: 960px) {\n          grid-template-columns: repeat(4, calc(25% - 20px));\n      }\n      \n      @media screen and (max-width: 960px) {\n          grid-template-columns: repeat(3, calc(33.33% - 20px));\n      }\n\n      &::-webkit-scrollbar {\n          width: 6px;\n      }\n      \n      &::-webkit-scrollbar-thumb {\n          background: #00000026;\n          border-radius: 3px;\n      }\n      \n      &::-webkit-scrollbar-track {\n          background: transparent;\n      }\n    "]);return Xn=function(){return n},n}function Yn(){var n=Tn(["\n      border: 1px solid transparent;\n      position: relative;\n      .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        \n        img {\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          object-fit: contain;\n          position: absolute;\n          left: 50%;\n          transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 180px;\n          \n          @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n          }\n          \n          @media screen and (max-width: 960px) {\n            max-width: 120px;\n          }\n\n        }\n        \n        .time {\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          // text-overflow: ellipsis;\n          max-width: 80px;\n        }\n      }\n      .view_button\n      {\n        position: absolute;\n        bottom: 50%;\n        left: 50%;\n        transform: translate(-50%, 50%);\n        width: 148px;\n        height: 32px;\n        border-radius: 8px;\n        opacity: 0;\n        transition: all 0.3s;\n        border-radius: 6px;\n        background: #FFFFFF;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        cursor: pointer;\n      }\n      &:hover {\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #0000000F;\n        box-shadow: 0px 20px 40px 0px #0000000A;\n        .view_button {\n          opacity: 1;\n        }\n      }\n    "]);return Yn=function(){return n},n}var Vn=(0,f.rU)(function(n){var e=n.css;return{root:e(Mn()),title:e(Hn()),img_content:e(Wn()),more_content:e(Un()),history_modal:e(Zn()),modal_content:e(Xn()),modal_item:e(Yn())}}),qn=t(62428),$n=t(49450),Gn=t(56070),Qn=t(11192),Jn=t(65640);function Kn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function ne(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function ee(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var a=n.apply(e,t);function o(n){ne(a,i,r,o,s,"next",n)}function s(n){ne(a,i,r,o,s,"throw",n)}o(void 0)})}}function te(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function ie(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),i.forEach(function(e){te(n,e,t[e])})}return n}function re(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,i)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function ae(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||se(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oe(n){return function(n){if(Array.isArray(n))return Kn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||se(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function se(n,e){if(n){if("string"==typeof n)return Kn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Kn(n,e):void 0}}function le(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var ce=(0,a.observer)(function(){var n=(0,o.B)().t,e=(0,s.P)(),t=Vn().styles,a=ae((0,r.useState)([]),2),d=a[0],u=a[1],p=ae((0,r.useState)([]),2),f=p[0],g=p[1],h=ae((0,r.useState)(!1),2),x=h[0],m=h[1],b=ae((0,r.useState)(1),2),y=b[0],v=b[1],w=ae((0,r.useState)(15),2),F=w[0],_=(w[1],ae((0,r.useState)(0),2)),j=_[0],k=_[1];l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=n);var A={items:[{key:"reEdit",label:n("重新查看")},{key:"delete",label:n("删除"),danger:!0}],onClick:function(n){var e=n.key,t=n.item;return I(e,t)}},I=function(n,t){return ee(function(){return le(this,function(i){switch(i.label){case 0:return"reEdit"!==n?[3,1]:(e.aiLightStore.setCurrentAILightImageID(t.imageResult),e.aiLightStore.setIsEdit(!1),e.aiLightStore.setIsLoading(!1),[3,3]);case 1:return"delete"!==n?[3,3]:[4,In.s.instance.delTargetAtlas(t.id)];case 2:i.sent(),N(),i.label=3;case 3:return[2]}})})()},C=function(){return ee(function(){var n,e,t,i,r,a,o,s;return le(this,function(c){switch(c.label){case 0:return i=(0,Gn._$)(),r=(0,Gn.mB)(i),a=l.nb.instance.layout_container._layout_scheme_id,[4,In.s.instance.sendAtlas(a,y,F)];case 1:return(o=c.sent()).success?(Jn.log("historyRes",o),(null===(t=o.queueList)||void 0===t||null===(e=t.data)||void 0===e||null===(n=e.ReturnList)||void 0===n?void 0:n.length)>0?(s=o.queueList.data.ReturnList.map(function(n){return{imageResult:"".concat(r,"/").concat(n.FileIdOutPut2),layoutName:n.Remark,createDate:n.CreateDate,id:n.QueueId}}),u(y>1?function(n){return oe(n).concat(oe(s))}:s),k(s.length)):(u([]),k(0)),[2]):[2]}})})()},N=function(){return ee(function(){var n,e,t,i,r,a,o;return le(this,function(s){switch(s.label){case 0:return t=(0,Gn._$)(),i=(0,Gn.mB)(t),r=l.nb.instance.layout_container._layout_scheme_id,[4,In.s.instance.sendAtlas(r,1,30)];case 1:return(a=s.sent()).success?(o=null===(e=a.queueList)||void 0===e||null===(n=e.data)||void 0===n?void 0:n.ReturnList.map(function(n){return{imageResult:n.FileIdOutPut2?"".concat(i,"/").concat(n.FileIdOutPut2):"",layoutName:n.Remark,createDate:n.CreateDate,id:n.QueueId}}),g(o),k(o.length),[2]):[2]}})})()};(0,r.useEffect)(function(){C()},[y]),(0,r.useEffect)(function(){e.aiLightStore.refreshAtlas&&(N(),e.aiLightStore.setRefreshAtlas(!1))},[e.aiLightStore.refreshAtlas]),(0,r.useEffect)(function(){e.homeStore.roomInfos.length>0&&N()},[]);var L=function(){v(1),C()};return(0,i.jsxs)("div",{className:t.root,children:[(0,i.jsxs)("div",{className:t.title,children:[(0,i.jsx)("span",{children:n("历史任务")}),(0,i.jsx)("div",{className:"more_button",onClick:function(){m(!0),e.homeStore.roomInfos.length>0&&L()},children:(0,i.jsx)("span",{style:{height:20,width:48},children:n("查看更多")})})]}),(0,i.jsxs)("div",{className:"content_container",children:[f.map(function(r,a){return(0,i.jsxs)("div",{className:t.img_content,children:[(0,i.jsx)("div",{className:"backdrop_content"}),r.imageResult?(0,i.jsxs)("div",{className:"main_img_container ".concat(e.aiLightStore.currentAILightImageID===r.imageResult?"select":""),children:[(0,i.jsx)("img",{src:r.imageResult,alt:""}),(0,i.jsx)("div",{className:"number_tag",children:"1"})]}):(0,i.jsxs)("div",{className:"main_loading_container ".concat(e.aiLightStore.currentAILightImageID===r.imageResult?"select":""),children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:n("生成中...")})]}),(0,i.jsxs)("div",{className:"info_content",children:[(0,i.jsx)(An.A,{title:n(r.layoutName),children:(0,i.jsx)("span",{className:"name",children:n(r.layoutName)})}),(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,i.jsx)(An.A,{title:n(r.createDate),children:(0,i.jsx)("span",{className:"time",children:(0,Qn.a)(r.createDate)})}),(0,i.jsx)(qn.A,{menu:re(ie({},A),{onClick:function(n){var e=n.key;return A.onClick({key:e,item:r})}}),trigger:["click"],children:(0,i.jsx)("div",{className:"icon_wrapper",children:(0,i.jsx)(S.A,{type:"icon-gengduo_bold"})})})]})]})]},a)}),j>30&&(0,i.jsxs)("div",{className:t.more_content,children:[(0,i.jsx)("span",{className:"more_text",children:n("-显示近30条任务-")}),(0,i.jsx)("span",{className:"more_button",onClick:L,children:n("查看更多")})]})]}),(0,i.jsx)($n.A,{title:n("历史记录"),open:x,onCancel:function(){return m(!1)},width:"80%",footer:null,className:t.history_modal,children:(0,i.jsx)("div",{className:t.modal_content,onScroll:function(n){var e=n.target;e.scrollTop+e.offsetHeight>=e.scrollHeight&&d.length<j&&v(y+1)},children:e.homeStore.roomInfos.length>0&&d.map(function(r,a){return(0,i.jsxs)("div",{className:t.modal_item,children:[r.imageResult?(0,i.jsxs)("div",{className:"main_img_container",children:[(0,i.jsx)("img",{src:r.imageResult,alt:""}),(0,i.jsx)("div",{className:"number_tag",children:"1"})]}):(0,i.jsxs)("div",{className:"main_loading_container",children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:n("生成中...")})]}),(0,i.jsxs)("div",{className:"info_content",children:[(0,i.jsx)(An.A,{title:n(r.layoutName),children:(0,i.jsx)("span",{className:"name",children:n(r.layoutName)})}),(0,i.jsx)(An.A,{title:n(r.createDate),children:(0,i.jsx)("span",{className:"time",children:n(r.createDate.split(" ")[0])})})]}),(0,i.jsx)("div",{className:"view_button",onClick:function(){return function(n){m(!1),e.aiLightStore.setCurrentAILightImageID(n.imageResult),e.aiLightStore.setIsEdit(!1),e.aiLightStore.setIsLoading(!1)}(r)},children:n("查看")})]},a)})})})]})});function de(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ue(){var n=de(["\n      width: 100%;\n      max-height: 100%;\n      border-radius: 12px;\n      padding: 0 0 20px 20px;\n      display: flex;\n      flex-direction: column;\n    "]);return ue=function(){return n},n}function pe(){var n=de(["\n      width:100%;\n      height: 100%;\n      padding-right: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #959598;\n    "]);return pe=function(){return n},n}function fe(){var n=de(["\n      display: grid;\n      gap: 20px;\n      flex: 1;\n      overflow-y: auto;\n\n      /* 隐藏滚动条 - Webkit浏览器 */\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      \n      /* 隐藏滚动条 - Firefox */\n      scrollbar-width: none;\n      \n      /* 隐藏滚动条 - IE */\n      -ms-overflow-style: none;\n\n      // grid-template-rows: repeat(auto-fill, 200px);\n\n      @media screen and (min-width: 1400px) {\n          grid-template-columns: repeat(5, calc(20% - 20px));\n      }\n      \n      @media screen and (max-width: 1400px) and (min-width: 960px) {\n          grid-template-columns: repeat(4, calc(25% - 20px));\n      }\n      \n      @media screen and (max-width: 960px) and (min-width: 560px) {\n          grid-template-columns: repeat(3, calc(33.33% - 20px));\n      }\n      @media screen and (max-width: 560px) and (min-width: 320px) {\n          grid-template-columns: repeat(2, calc(50% - 20px));\n      }\n      @media screen and (max-width: 320px) {\n          grid-template-columns: repeat(1, 100%);\n      }\n\n      &::-webkit-scrollbar {\n          width: 6px;\n      }\n      \n      &::-webkit-scrollbar-thumb {\n          background: #00000026;\n          border-radius: 3px;\n      }\n      \n      &::-webkit-scrollbar-track {\n          background: transparent;\n      }\n    "]);return fe=function(){return n},n}function ge(){var n=de(["\n      border: 1px solid transparent;\n      // margin-bottom: 20px;\n      .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        \n        img {\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          object-fit: contain;\n          position: absolute;\n          left: 50%;\n          transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 180px;\n          \n          @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n          }\n          \n          @media screen and (max-width: 960px) and (min-width: 560px) {\n            max-width: 120px;\n          }\n          @media screen and (max-width: 560px) {\n            max-width: 80px;\n          }\n        }\n        \n        .time {\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          // text-overflow: ellipsis;\n          max-width: 80px;\n        }\n      }\n    "]);return ge=function(){return n},n}var he=(0,f.rU)(function(n){var e=n.css;return{root:e(ue()),noData:e(pe()),content:e(fe()),item:e(ge())}});function xe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function me(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function be(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||ve(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ye(n){return function(n){if(Array.isArray(n))return xe(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||ve(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ve(n,e){if(n){if("string"==typeof n)return xe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?xe(n,e):void 0}}function we(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var Fe=(0,a.observer)(function(n){var e=n.setZIndexOfMobileAtlas,t=(0,o.B)().t,a=(0,s.P)(),d=he().styles,u=be((0,r.useState)([]),2),p=u[0],f=u[1],g=be((0,r.useState)(1),2),h=g[0],x=g[1],m=be((0,r.useState)(10),2),b=m[0],y=(m[1],be((0,r.useState)(0),2)),v=y[0],w=y[1];l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=t);var F=function(){return(n=function(){var n,e,t,i;return we(this,function(r){switch(r.label){case 0:return n=(0,Gn.mB)(),e=l.nb.instance.layout_container._layout_scheme_id,[4,In.s.instance.sendAtlas(e,h,b)];case 1:return t=r.sent(),i=t.queueList.data.ReturnList.map(function(e){return{imageResult:"".concat(n,"/").concat(e.FileIdOutPut2),layoutName:e.Remark,createDate:e.CreateDate,id:e.QueueId}}),f(h>1?function(n){return ye(n).concat(ye(i))}:i),w(i.length),[2]}})},function(){var e=this,t=arguments;return new Promise(function(i,r){var a=n.apply(e,t);function o(n){me(a,i,r,o,s,"next",n)}function s(n){me(a,i,r,o,s,"throw",n)}o(void 0)})})();var n};(0,r.useEffect)(function(){F()},[h]),(0,r.useEffect)(function(){a.aiLightStore.refreshAtlas&&(x(1),F(),a.aiLightStore.setRefreshAtlas(!1))},[a.aiLightStore.refreshAtlas]);return(0,i.jsxs)("div",{className:d.root,children:[(a.homeStore.roomInfos.length<=0||p.length<=0)&&(0,i.jsx)("div",{className:d.noData,children:t("-暂无数据-")}),(0,i.jsx)("div",{className:d.content,onScroll:function(n){var e=n.target;e.scrollTop+e.offsetHeight>=e.scrollHeight&&p.length<v&&x(h+1)},children:a.homeStore.roomInfos.length>0&&p.map(function(n,r){return(0,i.jsxs)("div",{className:d.item,onClick:function(){return function(n){a.aiLightStore.setCurrentAILightImageID(n.imageResult),a.aiLightStore.setIsEdit(!1),a.aiLightStore.setIsLoading(!1),a.aiLightStore.setIsSelectPerspective(!1),e(-2)}(n)},children:[n.imageResult?(0,i.jsxs)("div",{className:"main_img_container",children:[(0,i.jsx)("img",{src:n.imageResult,alt:""}),(0,i.jsx)("div",{className:"number_tag",children:1})]}):(0,i.jsxs)("div",{className:"main_loading_container",children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,i.jsx)("span",{children:t("生成中...")})]}),(0,i.jsxs)("div",{className:"info_content",children:[(0,i.jsx)(An.A,{title:t(n.layoutName),children:(0,i.jsx)("span",{className:"name",children:t(n.layoutName)})}),(0,i.jsx)(An.A,{title:t(n.createDate),children:(0,i.jsx)("span",{className:"time",children:t(n.createDate.split(" ")[0])})})]})]},r)})})]})}),_e=t(6768),je=t(49063);function ke(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Se(n,e,t,i,r,a,o){try{var s=n[a](o),l=s.value}catch(n){return void t(n)}s.done?e(l):Promise.resolve(l).then(i,r)}function Ae(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,a=[],o=!0,s=!1;try{for(t=t.call(n);!(o=(i=t.next()).done)&&(a.push(i.value),!e||a.length!==e);o=!0);}catch(n){s=!0,r=n}finally{try{o||null==t.return||t.return()}finally{if(s)throw r}}return a}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ke(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ke(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ie(n,e){var t,i,r,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(n,a)}catch(n){s=[6,n],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var Ce=(0,a.observer)(function(n){var e=n.modelType,t=(0,o.B)().t,a=(0,s.P)(),f=j().styles,g=(0,r.useRef)(),h=(0,r.useRef)(null),x=Ae((0,r.useState)(-2),2),m=(x[0],x[1]),b=Ae((0,r.useState)(-2),2),y=b[0],v=b[1],w=Ae((0,r.useState)(window.innerWidth<window.innerHeight),2),F=w[0],_=w[1],A=Ae((0,r.useState)(!1),2),I=A[0],C=A[1],N=(0,je.Zp)();l.nb.UseApp(c.e.AppName),l.nb.instance&&(l.nb.t=t);var L=function(){l.nb.instance&&(l.nb.instance.bindCanvas(document.getElementById("edit_canvas")),l.nb.instance.update()),l.nb.instance&&(l.nb.instance._is_landscape=F),_(window.innerWidth<window.innerHeight)},E=function(){return(n=function(){var n,e,t;return Ie(this,function(i){switch(i.label){case 0:return u.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:u.uN},[4,p.D.getLayoutSchemeList(n)]):[2];case 1:return e=i.sent(),t=e.layoutSchemeDataList,e.total,t&&(l.nb.DispatchEvent(l.n0.OpenMyLayoutSchemeData,t[0]),l.nb.emit(d.U.OpenHouseSearching,!1)),[2]}})},function(){var e=this,t=arguments;return new Promise(function(i,r){var a=n.apply(e,t);function o(n){Se(a,i,r,o,s,"next",n)}function s(n){Se(a,i,r,o,s,"throw",n)}o(void 0)})})();var n};(0,r.useEffect)(function(){window.addEventListener("resize",L),L(),l.nb.instance&&!e&&(l.nb.instance.initialized||(l.nb.instance.init(),l.nb.RunCommand(c.f.AiCadMode),l.nb.instance.prepare().then(function(){E()}),l.nb.instance.bindCanvas(document.getElementById("edit_canvas"))),l.nb.instance.update());m(-1)},[]);return(0,i.jsxs)("div",{className:f.root,children:[(0,i.jsxs)("div",{className:f.topMenu,style:{height:window.innerWidth<450?"48px":"72px",borderBottom:F?"1px solid #00000026":"none"},children:[(0,i.jsxs)("div",{className:"left",children:[window.innerWidth>=460&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"28px"}}),(0,i.jsx)("div",{style:{borderLeft:"2px solid #a1a3a5",height:"16px",margin:"0 12px"}}),(0,i.jsx)("span",{style:{marginRight:"24px"},children:t("AI灯光")})]}),(0,i.jsxs)("div",{className:"back_button",onClick:function(){l.nb.instance.layout_container;var n=l.nb.instance.scene3D;I?(C(!1),v(-2)):(a.aiLightStore.isSelectPerspective&&(an.W.instance.closeAILightUI(),a.aiLightStore.setIsLoading(!1),a.homeStore.viewMode,n.setCemeraMode(k.I5.Perspective),N("/"),l.nb.RunCommand(c.f.AiCadMode)),a.aiLightStore.setIsSelectPerspective(!0))},children:[(0,i.jsx)(S.A,{style:{margin:"7px 2px 7px 12px"},type:"icon-line_left"}),(0,i.jsx)("span",{style:{height:22,width:28},children:t("返回")})]})]}),(0,i.jsx)("div",{className:"right",children:(0,u.fZ)()&&(0,i.jsxs)("div",{className:"history_button",onClick:function(){I?(C(!1),v(-2)):(C(!0),a.aiLightStore.setRefreshAtlas(!0),v(3))},children:[(0,i.jsx)(S.A,{type:"icon-tuku"}),(0,i.jsx)("span",{style:{height:20,width:48},children:t("历史任务")})]})})]}),(0,i.jsx)(i.Fragment,{children:(0,u.fZ)()?F?a.aiLightStore.isSelectPerspective?(0,i.jsxs)("div",{className:f.IsLandscape_main_container,children:[(0,i.jsx)(gn,{}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:y},children:(0,i.jsx)(Fe,{setZIndexOfMobileAtlas:v})})]}):(0,i.jsxs)("div",{className:f.IsLandscape_main_container,children:[(0,i.jsx)(Rn,{}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:y},children:(0,i.jsx)(Fe,{setZIndexOfMobileAtlas:v})})]}):(0,i.jsxs)("div",{className:f.main_container,children:[(0,i.jsx)(gn,{}),(0,i.jsx)(Rn,{}),(0,i.jsx)("div",{className:f.mobile_atlas_container,style:{zIndex:y},children:(0,i.jsx)(Fe,{setZIndexOfMobileAtlas:v})})]}):(0,i.jsxs)("div",{className:f.main_container,children:[(0,i.jsx)(gn,{}),(0,i.jsx)(Rn,{}),(0,i.jsx)(ce,{})]})}),(0,i.jsx)(_e.A,{ref:g}),!e&&(0,i.jsx)("div",{id:"Canvascontent",className:f.content,children:(0,i.jsx)("div",{ref:h,id:"body_container",children:(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas"})})})]})})},90110:function(n,e,t){t.d(e,{A:function(){return i}});var i=t(17254).A}}]);