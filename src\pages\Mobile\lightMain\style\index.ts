import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      width:100%;
      height:100vh;
    `,
    content:css`
      position: absolute;
      top: 0px;
      left: 0; 
      right: 0;
      bottom: 0;
      overflow: hidden;
    `,
    canvas3d:css`
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      z-index:-1;
    `,
    canvas_pannel: css`
        position: absolute;
        left:-100px;
        top: -100px;
        background-color: #EAEAEB;
        width : calc(100% + 200px);
        height : calc(100% + 200px);
        overflow: hidden;
        .canvas {
          position : absolute;
          left: 0px;
          top: 0px;
          &.canvas_drawing {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
          }
          &.canvas_moving {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;
          }
          &.canvas_leftmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
          }
          &.canvas_rightmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
          }
          &.canvas_acrossmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
          }
          &.canvas_verticalmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
          }
          &.canvas_text {
            cursor : text;
          }
          &.canvas_pointer {
            cursor : pointer;
          }
          &.canvas_splitWall {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;
          }
        }

        .canvas_btns {
          width: auto;
          margin: 0 auto;
          position: fixed;
          display: flex;
          justify-content: center;
          bottom: 35px;
          z-index:10;
          left: 50%;
          transform: translateX(-50%);
          .btn {
            ${checkIsMobile() ?
            `
              width: 120px;
              height: 36px;
              font-size: 14px;
            `
            :
            `
              width: 200px;
              height: 48px;
              font-size: 16px;
            `
          }
            border-radius: 6px;
            border: none;

            font-weight: 600;
            margin-right: 10px;
            margin-left: 10px;
          }
          .design_btn {
            background: #e6e6e6;
            margin-right: 20px;
          }
          @media screen and (max-height: 600px){
            bottom: 50px !important;
          }
    }
    `,
    navigation : css`
      position:absolute;
      top:0px;
      width:100%;
      height:50px;
      border-bottom:1px solid #eee;
      background:#fff;
      z-index:5;
    `,
    backBtn : css`
      position:absolute;
      z-index:2;
      padding-left:2px;
      font-size:14px;
      line-height:50px;
      color:#333;
      float:left;
    `,
    forwardBtn : css`
      position:absolute;
      right:0;
      z-index:2;
      padding-right:10px;
      font-size:14px;
      line-height:50px;
      color:#333;
    `,
    schemeNameSpan:css`
      width:100%;
      font-size:16px;
      line-height:50px;
      text-align:center;
    `
  }

});
