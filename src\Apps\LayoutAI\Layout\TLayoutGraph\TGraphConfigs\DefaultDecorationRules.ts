import { I_DecorationRule } from "../TGraphConfigureInterface";
import { DefaultElectricityRules } from "./DefaultElectricityRules";


export var DefaultDecorationRules : I_DecorationRule[] = [
    ...DefaultElectricityRules,
    
    {
        decoration_name : "床头吊灯",
        decortation_type :"Lighting",
        decoration_figure :  {
            category : "床头吊灯",
            public_category :"床头吊灯",
            sub_category : "床头吊灯",
            _is_decoration : true,
            min_z : 2500,
            max_z : 2600,
            params : {
                length : 300,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories : ["床头柜"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '0',
        array_pos_y_func : '(t_d > m_d)?0:m_d/2-t_d/2',
        adjust_pos_z_func : '(storey_height-m_h)'

    },
    {
        decoration_name :"餐桌饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "餐桌饰品",
            public_category :"餐桌饰品",
            sub_category : "餐桌饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 300,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories :["餐桌"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '0',
        adjust_pos_z_func : 'p_h'

    },
    {
        decoration_name :"餐具饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "餐具饰品",
            public_category :"餐具饰品",
            sub_category : "餐具饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 300,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories :["餐椅"],
        target_parent_categories :["餐桌"],
        target_parent_distance : 100,
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : 'Math.max(p_d - 100 - m_d/2,100)',
        adjust_pos_z_func : 'p_h'

    },
    {
        decoration_name :"书桌饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "书桌饰品",
            public_category :"办公文具",
            sub_category : "书桌饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 400,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories :["书桌"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '100',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h'

    },
    {
        decoration_name :"茶几饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "茶几饰品",
            public_category :"茶几饰品",
            sub_category : "茶几饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 400,
                depth : 400,
                height : 100
            }
        },
        target_figure_categories :["矩形茶几","圆形茶几"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '0',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h'
    },
    {
        decoration_name :"边几饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "边几饰品",
            public_category :"边几饰品",
            sub_category : "边几饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 200,
                depth : 200,
                height : 100
            }
        },
        target_figure_categories :["边几","圆形边几"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '0',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h'
    },      
    {
        decoration_name :"餐边柜饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "餐边柜饰品",
            public_category :"餐边柜饰品",
            sub_category : "餐边柜饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 600,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories :["餐边柜"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '1',
        array_pos_y_func : '0',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h',
        check_condition : '(m_z < 1500)'
    },      
    {
        decoration_name :"电视柜饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "电视柜饰品",
            public_category :"电视柜饰品",
            sub_category : "电视柜饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 600,
                depth : 300,
                height : 100
            }
        },
        target_figure_categories :["电视柜"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        // array_colN : 'Math.max(Math.floor(t_l / array_x_len),1)',
        // array_pos_x_func : '(j - (array_colN-1) / 2) * array_x_len',
        array_colN : '2',
        array_pos_x_func : '(t_l/2-m_l/2-200)*((j<0.5)?1:-1)',
        array_pos_y_func : '0',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h',
        needs_different_material : true,
        check_condition : '(m_z < 1009)'
    },
    {
        decoration_name :"床头柜饰品",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "床头柜饰品",
            public_category :"相框",
            sub_category : "床头柜饰品",
            _is_decoration : true,
            min_z : 1000,
            max_z : 1100,
            params : {
                length : 200,
                depth : 200,
                height : 100
            }
        },
        target_figure_categories :["床头柜"],
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '0',
        array_angle_func : '180',
        adjust_pos_z_func : 'p_h',
        needs_different_material : true

    },
    {
        decoration_name :"客厅墙饰",
        decortation_type :"OnTable",
        decoration_figure : {
            category : "客厅墙饰",
            public_category :"油画",
            sub_category : "客厅墙饰",
            _is_decoration : true,
            min_z : 1200,
            max_z : 2000,
            params : {
                length : 800,
                depth : 30,
                height : 800
            }
        },
        target_figure_categories :["沙发背景墙"],
        target_parent_categories :["多人沙发","转角沙发","直排沙发"],
        target_parent_distance : 1000,
        target_wall_distance : 200,
        target_wall_min_length : 1600,
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_angle_func : '180',
        array_pos_x_func : '0',
        array_pos_y_func : '(t_d/2+Math.min(m_d/2,50))',
        adjust_pos_z_func : 'Math.min(Math.max(1600 - m_h/2, p_h + 200),2350-m_h)'

    },
    {
        decoration_name :"餐厅墙饰",
        decortation_type :"OnWall",
        decoration_figure : {
            category : "餐厅墙饰",
            public_category :"装饰画",
            sub_category : "餐厅墙饰",
            _is_decoration : true,
            min_z : 1200,
            max_z : 2000,
            params : {
                length : 600,
                depth : 30,
                height : 800
            }
        },
        target_figure_categories :["餐桌"],
        target_wall_distance : 5000,
        target_wall_min_length : 1600,
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '0',
        array_pos_y_func : '-(m_d/2+t_d/2+10)',
        adjust_pos_z_func : 'Math.max(1600 - m_h/2, p_h + 200)'

    },
    {
        decoration_name :"墙饰",
        decortation_type :"OnWall",
        decoration_figure : {
            category : "墙饰",
            public_category :"油画",
            sub_category : "卧室墙饰",
            _is_decoration : true,
            min_z : 1500,
            max_z : 2000,
            params : {
                length : 800,
                depth : 30,
                height : 800
            }
        },
        target_figure_categories :["床","双人床","床具组合"],
        target_wall_distance : 200,
        target_wall_min_length : 1600,
        ruleType : "Array",
        array_x_len : 1000,
        array_y_len : 500,
        array_rowN :'1',
        array_colN : '1',
        array_pos_x_func : '(p_x)',
        array_pos_y_func : '-(m_d/2+t_d/2+50)',
        adjust_pos_z_func : 'Math.max(1600 - m_h/2, p_h + 200)'
        
    },
]
