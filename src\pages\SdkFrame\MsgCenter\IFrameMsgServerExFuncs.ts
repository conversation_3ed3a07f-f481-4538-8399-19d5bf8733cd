import { Perspective<PERSON><PERSON><PERSON>, Vector3 } from "three";

import { getKgSchemeList } from "@/Apps/AI2Design/Services";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import * as SwjLayoutData from "@/Apps/LayoutAI/AICadData/SwjLayoutData";
import { TCadCeilingLayer } from "@/Apps/LayoutAI/Drawing/TCadCeilingDrawingLayer";
import { TScene3DViewImageLayer } from "@/Apps/LayoutAI/Drawing/TScene3DViewImageLayer";
import * as IRoomInterface from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TFigureElement } from "@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement";
import { LayoutSchemeXmlJsonParser } from "@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeXmlJsonParser";
import { TCeilingLayerEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TCeilingLayerEntity";
import { TViewCameraEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { LayoutContainerUtils } from "@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils";
import { CeilingConfigReader } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGraphConfigs/CeilingConfigReader";
import { TSeriesSample } from "@/Apps/LayoutAI/Layout/TSeriesSample";
import { BirdsEyeControls } from "@/Apps/LayoutAI/Scene3D/controls/BirdsEyeControls";
import { RenderReqOffline } from "@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline";
import { MeshName, UserDataKey } from "@/Apps/LayoutAI/Scene3D/NodeName";
import { IModelLight, IModelMeshLight } from "@/Apps/LayoutAI/Scene3D/parsers/IModelLight";
import { Scene3D } from "@/Apps/LayoutAI/Scene3D/Scene3D";
import { CameraViewMode } from "@/Apps/LayoutAI/Scene3D/SceneMode";
import { AIGCService } from "@/Apps/LayoutAI/Services/AIGC/AIGCService";
import { AutoLightingService } from "@/Apps/LayoutAI/Services/AutoLighting/AutoLightService";
import { BuildingService } from "@/Apps/LayoutAI/Services/Basic/BuildingService";
import { LayoutSchemeService } from "@/Apps/LayoutAI/Services/Basic/LayoutSchemeService";
import { SchemeXmlParseService } from "@/Apps/LayoutAI/Services/Basic/SchemeXmlParseService";
import { TDesignMaterialUpdater } from "@/Apps/LayoutAI/Services/MaterialMatching/TDesignMaterialUpdater";
import { TSeriesFurnisher } from "@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher";
import { ServerRenderService } from "@/Apps/LayoutAI/Services/ServerRender/ServerRenderService";
import { compareNames, GenDateUUid, Sleep } from "@layoutai/z_polygon";
import { ZRectShapeType } from "@layoutai/z_polygon";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { getCookie } from "@/utils";

import * as IMsgType from "./IMsgType";
import { Msg_Events } from "./MsgEvents";
import { ORenderConfig, ORenderConfigID } from "@/Apps/LayoutAI/Scene3D/light/req/ORenderConfig";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { FigureViewControls } from "@/Apps/LayoutAI/Scene3D/controls/FigureViewControls";
import { DefaultLights } from "./DefaultLights";
import { TExtDrawingEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TExtDrawingEntity";
import { RoomSubAreaService } from "@/Apps/LayoutAI/Services/Basic/RoomSubAreaService";

/**
 * @description 服务端扩展函数
 * <AUTHOR>
 * @date 2025-04-07 19:26:00
 * @lastEditTime 2025-04-18 14:10:48
 * @lastEditors xulidong
 */
export var IFrameMsgServerExFuncs = {
  /**
   * @description 纯纯测试用
   * @param input.word
   * @returns
   */
  testA: async (input: { word: string }): Promise<string> => {
    await Sleep(1000);
    console.log('---testA', input.word);
    return input.word;
  }, // [End:Async]

  /**
   * 重绘画布---一般其他函数都会自动重绘
   * --- 如果没有重绘就画以下
   * --- 一般都在100ms
   * @param input
   */
  updateCanvas: async (input: {} = {}) => {
    LayoutAI_App.instance.update();
  }, // [End:Async]
  /**
   * @description 通过layoutschemeId载入布局方案
   * @param input.scheme_id
   * @returns
   */
  loadByLayoutSchemeId: async (input: { scheme_id: string }) => {
    let schemeData = await LayoutSchemeService.getLayoutSchemeById(input.scheme_id);
    let res = await LayoutSchemeXmlJsonParser.openLayoutSchemeData(schemeData);
    return res;
  }, // [End:Async]

  /**
   * @description 通过三维家3D方案Id载入布局方案
   * @param input.scheme_id
   * @returns
   */
  loadBySwjSchemeId: async (input: { scheme_id: string }): Promise<number> => {
    if (!input.scheme_id) return null;
    let authCode = getCookie('authCode');

    let res = await SchemeXmlParseService.getSchemeXmlBySchemeId(
      input.scheme_id,
      authCode,
      'code_debug'
    );

    if (res) {
      LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res);
      return 1;
    } else {
      return null;
    }
  }, // [End:Async]

  /**
   * 根据户型ID,打开户型方案
   * @param input.building_id
   * @returns
   */
  loadByBuidlingId: async (input: { building_id: string }): Promise<number> => {
    let authCode = getCookie('authCode');
    let res = await BuildingService.getBuildingRoomSchemeById(input.building_id, authCode);
    LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(res, {
      updateUid: true,
      layout_scheme_id: null,
      layout_scheme_name: null,
      schemeSource: SwjLayoutData.SchemeSourceType.LayoutLibrary,
      auto_layout: false
    });
    return 1;
  }, // [End:Async]

  /**
   *
   * @param input.building_id 户型Id
   * @param input.access_token 开发者访问的access_token
   * @returns
   */
  loadByOpenBuildingId: async (input: {
    building_id: string;
    access_token: string;
  }): Promise<number> => {
    let room_content = await fetch(
      'https://open-gateway.3vjia.com/api/v1/buildingroom/getRoomContentById',
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Access-Token': input.access_token
        },
        body: JSON.stringify({
          buildingRoomId: input.building_id
        })
      }
    )
      .then(res => res.json())
      .then(res => (res.success ? res.data : null))
      .catch(e => null);

    if (!room_content || !room_content?.roomXmlContentUrl) {
      console.log('loadByOpenBuildingId', 'fetch buildingRoom FAILED');
      return null;
    }
    return await IFrameMsgServerExFuncs.loadSchemeXmlUrl({
      xml_url: room_content.roomXmlContentUrl
    });
  }, // [End:Async]
  /**
   * 根据SchemeXmlUrl 打开方案
   * @param input.xml_url
   * @returns
   */
  loadSchemeXmlUrl: async (input: { xml_url: string }): Promise<number> => {
    if (!input?.xml_url) {
      console.log('loadSchemeXmlUrl', 'xml_url is undefined');
      return null;
    }
    let response = await fetch(input.xml_url)
      .then(val => val.text())
      .catch(e => null);
    if (!response) {
      console.log('loadSchemeXmlUrl', 'fetch xml_url failed!');
      return null;
    }

    return IFrameMsgServerExFuncs.loadSchemeXmlBase64({ base64text: response });
  }, // [End:Async]

  /**
   * 根据Base64字符串 打开方案
   * @param input
   * @returns
   */
  loadSchemeXmlBase64: async (input: { base64text: string }) => {
    let xmlSchemeJson: SwjLayoutData.I_SwjXmlScheme =
      await SchemeXmlParseService.parseSchemeXml2Json(input.base64text, GenDateUUid());
    if (!xmlSchemeJson) {
      console.log('loadSchemeXmlBase64', 'parse xml failed');
      return null;
    }
    xmlSchemeJson.xml_str = input.base64text;
    LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(xmlSchemeJson, {
      updateUid: true,
      layout_scheme_id: null,
      layout_scheme_name: null,
      schemeSource: SwjLayoutData.SchemeSourceType.LayoutLibrary,
      auto_layout: false
    });
    return 1;
  }, // [End:Async]

  /**
   * 直接读方案json
   * @param input
   * @returns
   */
  loadSchemeXmlJson: async (input: { scheme: SwjLayoutData.I_SwjXmlScheme }) => {
    LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(input.scheme, {
      updateUid: true,
      layout_scheme_id: null,
      layout_scheme_name: null,
      schemeSource: SwjLayoutData.SchemeSourceType.LayoutLibrary,
      auto_layout: false
    });
    return 1;
  }, // [End:Async]
  /**
   * 获得所有房间信息
   * @param input
   */
  getAllRoomInfos: async (input: {} = {}): Promise<IRoomInterface.I_Room[]> => {
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    return container._room_entities.map(room_entity => {
      let info = room_entity._room.exportRoomData();
      info.storey_height = room_entity.storey_height;
      info.ceiling_infos = room_entity.room_ceiling_entity.exportCeilingData();
      info.sub_areas = room_entity._sub_room_areas.map((sub_area, index) => sub_area.exportSubAreaSimpleData(index));
      return info;
    });
  }, // [End:Async]

  /**
   * 对不同的房间应用Ai布局
   * @param input.filteredRoomUids  基于uid过滤房间
   * @param input.filterRoomUuids   基于uuid过滤房间
   * @param input.filteredRoomNames   基于房间名称过滤房间
   * @param input.append_furniture_entites 是否自动应用最高分的布局
   * @param input.solver_methods 不同的自动推荐的算法
   * @param input.output_details 是否输出布局细节信息
   * @returns
   */
  applyAiLayoutInRooms: async (
    input: {
      filteredRoomUids?: string[];
      filterRoomUuids?: string[];
      filteredRoomNames?: string[];
      solver_methods?: IRoomInterface.SolverMethods[];
      append_furniture_entites?: boolean;
      output_details?: boolean;
    } = {}
  ): Promise<IRoomInterface.I_RoomLayoutSchemeResult[]> => {
    if (!input.solver_methods) {
      input.solver_methods = ['BasicTransfer', 'GroupTransfer', 'SpacePartition'];
    }

    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const solver = (LayoutAI_App.instance as TAppManagerBase).layout_graph_solver;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);

    if (
      input.solver_methods.includes('BasicTransfer') ||
      input.solver_methods.includes('GroupTransfer')
    ) {
      await solver.queryModelRoomsFromServer(
        room_entities.map(entity => entity._room),
        false,
        false
      );
    }
    await container.applyRoomEntitiesWithSolvingMethods(
      room_entities,
      input.solver_methods,
      {
        append_furniture_entites: input.append_furniture_entites || false,
        needs_make_group_templates: false,
        force_auto_sub_area: true
      },
      null
    );

    return room_entities.map(room_entity => {
      let room = room_entity._room;
      let layout_scheme_list = room._layout_scheme_list || [];
      let scheme_list = layout_scheme_list.map(scheme =>
        scheme.exportScheme({ export_debug_data: false })
      );
      let result: IRoomInterface.I_RoomLayoutSchemeResult = {
        room_name: room.roomname,
        uid: room.uid,
        uuid: room.uuid,
        layout_scheme_num: scheme_list.length
      };
      if (input.output_details) {
        result.layout_scheme_list = scheme_list;
      }
      return result;
    });
  }, // [End:Async]

  /**
   * 对不同的子空间应用Ai布局
   */
  applyAiLayoutInSubspaces: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
    filterSubSpaceNames?: string[];
    filterSubSpaceUuids?: string[];
    queryTemplates?: boolean;
    solver_methods?: IRoomInterface.SolverMethods[];
    append_furniture_entites?: boolean;
    output_details?: boolean;
  }) => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const solver = (LayoutAI_App.instance as TAppManagerBase).layout_graph_solver;
    let queryTemplates = input?.queryTemplates === undefined ? true : input.queryTemplates;
    if (queryTemplates) {
      let promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          solver.queryModelSubSpacesFromServer(null, { pageIndex: i + 1, source: 'SubSpace' })
        );
      }
      await Promise.allSettled(promises);
    }

    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);

    let sub_space_entities: TSubSpaceAreaEntity[] = [];
    room_entities.forEach(room_entity => {
      sub_space_entities.push(
        ...room_entity._sub_room_areas.filter(entity => {
          if (input.filterSubSpaceUuids) {
            return input.filterSubSpaceUuids.includes(entity._uuid);
          }
          if (input.filterSubSpaceNames) {
            return compareNames(input.filterSubSpaceNames, [entity._space_area_room?.roomname]);
          }
          return true;
        })
      );
    });
    let promises: Promise<any>[] = [];
    sub_space_entities.forEach(space => {
      promises.push(
        space.updateSubLayoutScheme({
          displayInUI: false,
          auto_layout: false,
          solver_methods: ['SubSpaceTransfer']
        })
      );
    });
    await Promise.allSettled(promises);

    return sub_space_entities.map(entity => {
      let room = entity._space_area_room;
      let layout_scheme_list = room._layout_scheme_list || [];
      let scheme_list = layout_scheme_list.map(scheme =>
        scheme.exportScheme({ export_debug_data: false })
      );
      let result: IRoomInterface.I_RoomLayoutSchemeResult = {
        room_name: room.roomname,
        uid: room.uid,
        uuid: room.uuid,
        layout_scheme_num: scheme_list.length
      };
      if (input.output_details) {
        result.layout_scheme_list = scheme_list;
      }
      return result;
    });
  }, // [End:Async]

  /**
   * 获得房间的分区---直接是吊顶分区
   * @param input
   */
  getCeilingAreasInRoom: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
  }): Promise<IRoomInterface.I_RoomSubAreasResult[]> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);
    let rooms = container._rooms;
    if (rooms) {
      rooms = [...rooms];
      rooms.sort((a, b) => (b.area || 0) - (a.area || 0));
      rooms.forEach((room, index) => (room._t_id = index + 1));
    }
    room_entities = [...room_entities];
    room_entities.sort((a, b) => b._area - a._area);
    return room_entities.map(room_entity => {
      let room = room_entity._room;
      // if(room_entity.is_auto_sub_area && room_entity.is_auto_ceiling)
      // {
      //     room_entity.updateSpaceLivingInfo();
      // }
      // room_entity.updateCeilingEntity();
      let result: IRoomInterface.I_RoomSubAreasResult = {
        room_name: room.roomname,
        room_uid: room.uid,
        room_uuid: room.uuid,
        sub_areas: room_entity.room_ceiling_entity.ceiling_layer_entities.map(
          (layerEntity, index) => {
            return layerEntity.exportSubAreaSimpleData(index, false);
          }
        )
      };
      return result;
    });
  }, // [End:Async]

  /**
   * 修改分区, 要用uuid来指定分区来修改
   * @param input
   * @returns 0: 没找到对应的吊顶
   */
  editCeilingAreaInRoom: async (input: IRoomInterface.I_RoomSubAreaSimpleData): Promise<IRoomInterface.I_RoomSubAreaSimpleData> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container);
    let target_layer_area: TCeilingLayerEntity = null;
    let target_room_entity: TRoomEntity = null;
    let target_layer_index = 0;
    room_entities.forEach(room_entity => {
      let res = room_entity.room_ceiling_entity.ceiling_layer_entities.find(
        (layer, index) =>
          layer._uuid === input.uuid ||
          (room_entity._room?.uuid === input.room_uuid && index === input.index)
      );
      if (res) {
        target_layer_area = res;
        target_room_entity = room_entity;
        target_layer_index = room_entity.room_ceiling_entity.ceiling_layer_entities.indexOf(target_layer_area);
      }
    });
    if (!target_layer_area) {
      return null;
    }
    target_layer_area.importSubAreaSimpleData(input);
    if (input.area_rect && target_room_entity) {
      if (target_layer_area?.ceiling_figure_element?.rect?._attached_elements[TSubSpaceAreaEntity.EntityType]) {
        let sub_area: TSubSpaceAreaEntity = target_layer_area.ceiling_figure_element.rect._attached_elements[TSubSpaceAreaEntity.EntityType];
        if (sub_area?.rect) {
          sub_area.rect.copy(target_layer_area.rect);
        }
      }
    }
    if (input.is_auto_sub_area !== undefined) {
      if (target_room_entity) {
        target_room_entity.is_auto_sub_area = input.is_auto_sub_area;
      }
    }
    if (target_room_entity) {
      target_room_entity.room_ceiling_entity.update();
    }
    LayoutAI_App.emit_M(EventName.SceneContentStateChanged, { state: 0 });

    const scene3D = LayoutAI_App.instance.scene3D;
    if (scene3D && scene3D.isValid()) {
      scene3D.makeDirty(3);
      target_room_entity.updateMesh3D();

    }

    LayoutAI_App.emit_M(EventName.SceneContentStateChanged, { state: 1 });

    LayoutAI_App.instance.update();
    return target_layer_area.exportSubAreaSimpleData();
  }, //[End:Async]

  /**
   *  清空每个空间的筒灯
   */
  clearDownLightsInRoom: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
  }) => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const scene3D = LayoutAI_App.instance.scene3D;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);

    room_entities.forEach(room_entity => {
      let figure_elements = room_entity._room._furniture_list;
      let down_lights = figure_elements.filter(light =>
        compareNames([light.category, light.sub_category], ['筒灯', '射灯'])
      );

      down_lights.forEach(ele => {
        let id = figure_elements.indexOf(ele);
        if (id >= 0) figure_elements.splice(id, 1);
      });
      down_lights.forEach(ele => {
        ele.dispose3d();
      });

      // bindDownLights 会先清空, 所以先绑定一个空的列表
      room_entity.room_ceiling_entity.ceiling_layer_entities.forEach((entity) => entity.cleanDownLights());
      room_entity.room_ceiling_entity.bindDownLights([]);

      room_entity.updateMesh3D();
    });
  }, //[End:Async]

  /**
   *  @description 添加筒灯列表: 会按位置自动添加到对应的空间
   *  @param input.down_lights 要添加灯具的列表
   *  @param input.not_clean_down_lights  默认为false,即默认会清空现有筒灯;如果true, 则不会
   *  @param input.add_lights_to_room  默认为false, 即默认不会添加筒灯到房间中
   *  @returns 返回添加的筒灯的uuid列表
   */
  addDownLights: async (input: {
    down_lights: IRoomInterface.I_SimpleFigureElement[];
    not_clean_down_lights?: boolean;
    add_lights_to_room?: boolean;
  }): Promise<string[]> => {
    let not_clean_down_lights = input?.not_clean_down_lights || false;
    let add_lights_to_room = input?.add_lights_to_room || false;
    input.down_lights = input.down_lights || [];
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    scene3D.makeDirty();
    if (!not_clean_down_lights) {
      await IFrameMsgServerExFuncs.clearDownLightsInRoom({});
    }

    if (input.down_lights.length == 0) {
      if (scene3D?.isValid()) {
        // container.updateScene3D(true);
        scene3D.update();
        LayoutAI_App.emit_M(EventName.Scene3DCameraChanged, this);
      }
      LayoutAI_App.instance.update();
      return [];
    }

    // 对入参进行校验，过滤掉不支持的灯具类型
    let validDownLights = input.down_lights.filter((ele) => {
      if (!DefaultLights[ele.category]) {
        console.warn('不支持的灯具类型', ele.category);
        return false;
      }
      return true;
    });


    let lightFigures = validDownLights.map(ele => {
      let figure_ele = TFigureElement.createSimple(ele.category || '筒灯');
      figure_ele._is_decoration = true;
      figure_ele._rect_shape = ele._rect_shape || ZRectShapeType.Circle;
      figure_ele.topOffset = ele.topOffset || 0;
      ele._rect_data && figure_ele.rect.importRectData(ele._rect_data);
      ele.length && (figure_ele.length = ele.length);
      ele.depth && (figure_ele.depth = ele.depth);
      ele.height && (figure_ele.height = ele.height);
      ele.nor && (figure_ele.nor = ele.nor);
      ele.pos && (figure_ele.rect.rect_center = ele.pos);
      figure_ele.installType = ele.installType; // 安装方式 
      if (ele.materialId) {
        figure_ele._matched_material = figure_ele.makeMaterialItemByMaterialId(ele.materialId);
        figure_ele._matched_rect = figure_ele.rect.clone();
        figure_ele.updateMesh3D();
      }
      return figure_ele;
    });

    container._room_entities.forEach(room_entity => {
      room_entity.room_ceiling_entity.bindDownLights(lightFigures, {
        add_figures_to_room: add_lights_to_room
      });
      room_entity.updateMesh3D();
    });

    LayoutAI_App.instance.update();
    await TDesignMaterialUpdater.instance.updateFurnituresDesignMaterials(lightFigures);
    scene3D.makeDirty();
    scene3D.update();
    let uuidList: string[] = [];

    let failModel = 0;
    let failParent = 0;
    for (let i = 0; i < lightFigures.length; i++) {
      let ele = lightFigures[i];
      let down_light = validDownLights[i];
      if (!ele._solid_mesh3D) {
        console.error('灯具模型下载失败', ele.uuid, JSON.stringify(down_light));
        failModel++;
        continue;
      }
      if (!ele._solid_mesh3D.parent) {
        console.error('灯具没有父节点', ele.uuid, JSON.stringify(down_light));
        failParent++;
        continue;
      }

      ele._solid_mesh3D.name = MeshName.LightModel;
      let modelLights = ele._solid_mesh3D.userData[UserDataKey.ModelLights];
      if (modelLights) {
        modelLights.forEach((light: any) => {
          light.color = down_light.color;
          light.power = down_light.brightness;
        });
      }
      uuidList.push(ele.uuid);
    }

    console.log(
      'addDownLights',
      `输入灯数: ${input.down_lights.length}`,
      `有效类型灯数: ${validDownLights.length}`,
      `添加成功灯数: ${uuidList.length}`,
      `模型下载失败: ${failModel}`,
      `没有父节点: ${failParent}`
    );

    return uuidList
  }, //[End:Async]

  /**
   *  @description 更新筒灯的颜色和亮度
   *  @param input.uuid 筒灯的uuid
   *  @param input.color 颜色
   *  @param input.brightness 亮度
   */
  updateDownLight: async (input: {
    uuid: string;
    color?: number;
    brightness?: number;
  }) => {
    console.log('执行更新灯具', input.uuid, input.color, input.brightness);
    let isFound = false;
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    container._room_entities.forEach(room_entity => {
      room_entity.room_ceiling_entity.ceiling_layer_entities.forEach(layer => {
        let down_lights = layer.getDownLights();
        down_lights.forEach(ele => {
          if (ele.uuid === input.uuid) {
            isFound = true;
            let modelLights = ele._solid_mesh3D.userData[UserDataKey.ModelLights];
            if (modelLights) {
              modelLights.forEach((light: IModelLight) => {
                if (input.color) {
                  light.color = input.color;
                }
                if (input.brightness) {
                  light.power = input.brightness;
                }
              });
            }

            let meshLight: IModelMeshLight = ele._solid_mesh3D.userData[UserDataKey.MeshLight];
            if (meshLight) {
              if (input.color) {
                meshLight.color = input.color;
              }
              if (input.brightness) {
                meshLight.power = input.brightness;
              }
            }

          }
        })
      });
    });
    if (!isFound) {
      console.error('未找到灯具', input.uuid);
    }
  }, //[End:Async]

  /**
   *  @description 自动灯光
   *  @param input.isClean 是否清空现有灯光
   *  @param input.brightness 亮度
   *  @param input.color 颜色
   *  @returns 返回自动灯光实体的uuid列表
   * */
  autoLighting: async (input: {
    isClean?: boolean;
    config?: any;
  }): Promise<string[]> => {
    let isClear = !!input.isClean;
    if (input.config) {
      AutoLightingService.instance.setRulers(input.config);
    }
    let entities = await AutoLightingService.instance.updateLighting(isClear);
    let uuidList: string[] = [];
    entities.forEach(entity => {
      uuidList.push(entity._uuid);
    });
    return uuidList;
  }, //[End:Async]


  /**
   *  @param input.ruleType 1：平台套系,  2:企业套系
   *  获得风格套系列表
   */
  getStyleSeriesList: async (
    input: {
      ruleType?: number;
      pageIndex?: number;
      pageSize?: number;
      orderBy?: string;
      schemeKeyWord?: string;
      ruleKeyWord?: string;
      spaceName?: string[];
      schemeStyleId?: string;
      ruleStyleId?: string;
      queryType?: number;
    } = {}
  ): Promise<IRoomInterface.I_StyleSeriesItem[]> => {
    let params = {
      orderBy: 'sort asc',
      ruleType: 1,
      pageSize: 100,
      pageIndex: 1,
      schemeKeyWord: '',
      ruleKeyWord: '',
      spaceName: null as any,
      schemeStyleId: '',
      ruleStyleId: '',
      queryType: 2,
      ...input
    };
    const res = await getKgSchemeList(params);
    let result = res?.result;

    return result;
  }, //[End:Async]

  /**
   * 对不同的房间应用风格套系
   * @param input.seriesKgId  套系KgId
   * @param input.filteredRoomUids  基于uid过滤房间
   * @param input.filterRoomUuids   基于uuid过滤房间
   * @param input.filteredRoomNames   基于房间名称过滤房间
   * @param input.needsDiversityMatched  是否每次匹配的结果随机
   * @returns
   */
  applyStyleSeriesInRooms: async (
    input: {
      seriesKgId?: string;
      filteredRoomUids?: string[];
      filterRoomUuids?: string[];
      filteredRoomNames?: string[];
      needsDiversityMatched?: boolean;
      needsUpdateScene3d?: boolean;
    } = {}
  ): Promise<number> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);
    let current_rooms = room_entities.map(entity => entity._room);

    let series = new TSeriesSample({
      kgId: input.seriesKgId
    });
    await TSeriesFurnisher.instance.onSeriesSampleSelected(
      { soft: true, cabinet: true, hard: true, remaining: false },
      series,
      current_rooms,
      {
        needAutoFinetune: false,
        needsDiversifyMatched: input.needsDiversityMatched || false,
        updateTopViewBy3d: false
      }
    );

    if (input.needsUpdateScene3d !== false) {

      if (LayoutAI_App.instance.scene3D?.isValid()) {
        if (LayoutAI_App.instance?.scene3DManager?.UpdateScene3DWithMaterials) {
          await LayoutAI_App.instance.scene3DManager.UpdateScene3DWithMaterials(true);
        }
      }

    }


    return 1;
  }, // [End:Async]

  /**
   *  设置2D绘制样式
   */
  setDrawing2DMode: async (input: { mode: IRoomInterface.DrawingFigureMode }): Promise<void> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    container.drawing_figure_mode = input.mode || IRoomInterface.DrawingFigureMode.Figure2D;
    LayoutAI_App.instance.update();
    return;
  }, // [End:Async]

  /**
   * 显示不同的图层
   * @param input
   */
  showDrawingLayer: async (input: {
    layers: { layerName: IRoomInterface.DrawingLayerNames; visible: boolean }[];
  }): Promise<void> => {
    if (!input.layers) return null;

    let state: { [key: string]: boolean } = {};
    input.layers.forEach(layer_data => (state[layer_data.layerName] = layer_data.visible));
    LayoutAI_App.DispatchEvent(Msg_Events.HandleSwitchDrawingLayer, state);
    LayoutAI_App.instance.update();
  }, // [End:Async]

  /**
   * @description 基于整体包围盒，2D画布场景居中
   * @param input.p_scale 直接设置scale
   * @param input.fixed_scale 相对于屏幕长宽的放缩比 如果是1.0， 那么整体包围盒长边或宽边会贴边。
   */
  focusCenterByWholeBox: async (
    input: { fixed_scale: number; p_scale?: number } = { fixed_scale: 0.8 }
  ) => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    LayoutContainerUtils.focusCenterByWholeBox(container, input.fixed_scale);
    if (input.p_scale) {
      container.painter._p_sc = input.p_scale;
    }
    LayoutAI_App.instance.update();
  }, // [End:Async]

  /**
   * 设置画布的放缩比
   * @param input
   */
  setCanvasTransform: async (input: { p_center: { x: number; y: number }; scale: number }) => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const painter = container.painter;
    painter.p_center.x = input.p_center.x;
    painter.p_center.y = input.p_center.y;
    painter._p_sc = input.scale;
    LayoutAI_App.instance.update();
  }, //[End:Async]

  /**
   * @description 获得当前画布的图像base64---canvas.toDataUrl()
   * @param input.width  如果没有设置，则默认是当前显示画布的宽
   * @param input.height  如果没有设置height，默认跟width相同
   * @param input.p_scale 直接设置画布的p_scale --- 如果设置了p_scale, 则fixed_scale无效
   * @param input.fixed_scale, 新设置了长宽才会启用，默认值是0.8
   * @param input.posNum 对应buildingInfo的信息, 但好像没有用
   * @param input.scaleNum 对应buildingInfo的信息, 实际上用的是input.scaleNum.x/2的值, 即设置p_scale = scaleNum.x/2
   * @return
   */
  getCanvas2dImage: async (
    input: {
      width?: number;
      height?: number;
      fixed_scale?: number;
      p_scale?: number;
      posNum?: { x: number; y: number };
      scaleNum?: { x: number; y: number };
    } = {}
  ): Promise<IRoomInterface.I_CanvasImageResult> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    const manager = LayoutAI_App.instance as TAppManagerBase;
    if (input.width) {
      let canvas = document.createElement('canvas');
      canvas.width = input.width;
      canvas.height = input.height || input.width;

      const painter = container.painter;
      let input_ts: {
        _p_center: { x: number; y: number; z: number };
        _p_zval: number;
        _p_sc: number;
      } = null;
      if (input.posNum && input.scaleNum) {
        input_ts = {
          _p_center: { x: input.posNum.x, y: input.posNum.y, z: 0 },
          _p_sc: input.scaleNum.x || 1,
          _p_zval: 0
        };
        input_ts._p_sc /= 2;
        let ts = LayoutContainerUtils.computePainterTransformWithWholeBox(
          input.width,
          input.height,
          container,
          0.8
        );
        input_ts._p_center = ts._p_center as any;
      }
      if (input.p_scale) {
        input_ts = LayoutContainerUtils.computePainterTransformWithWholeBox(
          input.width,
          input.height,
          container,
          0.8
        ) as any;
        input_ts._p_sc = input.p_scale;
      }
      LayoutContainerUtils.BeginDrawOnCanvas(canvas, container, {
        fixed_scale: input.fixed_scale || 0.8,
        ts: input_ts
      });
      if (manager.layer_DefaultBatchLayer) {
        manager.layer_DefaultBatchLayer.onDraw();
      }
      let ts_data = painter.exportTransformData();
      let matrix = painter.getCanvasProject2D_Matrix3(canvas);
      LayoutContainerUtils.EndDrawOnCanvas(canvas, container);

      return {
        width: canvas.width,
        height: canvas.height,
        base64: canvas.toDataURL(),
        transform: ts_data as any,
        project_matrix3: matrix.toArray()
      };
    } else {
      const painter = container.painter;
      let ts_data = painter.exportTransformData();
      let canvas = painter._canvas;
      if (manager.layer_DefaultBatchLayer) {
        canvas = manager.layer_DefaultBatchLayer.layerCanvas;
      }
      let matrix = painter.getCanvasProject2D_Matrix3(canvas);

      return {
        width: canvas.width,
        height: canvas.height,
        base64: canvas.toDataURL(),
        transform: ts_data as any,
        project_matrix3: matrix.toArray()
      };
    }
  }, // [End:Async]

  /**
   * 初始化3D场景, 默认是没有3D的，需要初始化
   * @param input
   */
  initScene3D: async (input: {} = {}): Promise<number> => {
    LayoutAI_App.emit_M(Msg_Events.Init3DScene, true);
    IFrameMsgServerExFuncs.changeSceneMode({ sceneMode: "3D_FirstPerson", isHidden3D: false });
    IFrameMsgServerExFuncs.changeSceneMode({ sceneMode: "2D", isHidden3D: false });
    return 1;
  }, //[End:Async]
  /**
   * 去掉3D场景
   * @param input
   */
  removeScene3D: async (input: {} = {}): Promise<number> => {
    LayoutAI_App.emit_M(Msg_Events.Init3DScene, false);
    return 1;
  }, //[End:Async]

  /**
   *
   * @param input.sceneMode
   * @param input.isHidden3D --- 是否显示3D-如果是true 则在后台执行(实际上是zIndex=-2)
   * @returns  1: 正常改变;  0: 如果3D没有初始化就设置3D模式
   *   *
   */
  changeSceneMode: async (input: {
    sceneMode: IRoomInterface.SceneViewMode;
    isHidden3D?: boolean;
  }): Promise<number> => {
    const scene3d = LayoutAI_App.instance.scene3D;
    if (!scene3d) {
      return 0;
    }
    if (input.sceneMode !== '2D' && !scene3d.isValid()) {
      return 0;
    }

    LayoutAI_App.emit_M(Msg_Events.ChangeSceneMode, input);

    return 1;
  }, //[End:Async]

  /**
   *
   * @param input.mode - 0: 白模模式 1：线框模式 2：材质模式 3：材质-线框模式
   */
  changeMaterialMode: async (input: { mode: number }) => {
    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    scene3D.outlineMaterialMode = input?.mode || 0;

    scene3D.update();
    return;
  }, //[End:Async]

  /**
   * 鸟瞰模式的居中
   * @param input 
   */
  focusCenter3d: async (input: { fixed_scale?: number; }) => {
    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    if (!scene3D || !scene3D.isValid()) return;
    if (scene3D.camera_view_mode !== CameraViewMode.Perspective) return;

    let controls = scene3D.active_controls as BirdsEyeControls;

    controls.focusCenterWithFixedScale(input.fixed_scale);

    scene3D.update();

  }, //[End:Async]
  /**
   * 获得当前3D画布的图像
   * @param input  
   * @returns 
   */
  getCanvas3dImage: async (input: {}) => {

    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    if (!scene3D || scene3D.isValid()) return null;
    let current_canvas = scene3D.renderer.domElement as HTMLCanvasElement;

    return {
      width: current_canvas.width,
      height: current_canvas.height,
      base64: current_canvas.toDataURL()
    }
  }, //[End:Async]

  /**
   * @description 获得白模3D图像base64---canvas.toDataUrl()
   * @param input.width  如果没有设置，则默认是当前显示画布的宽
   * @param input.height  如果没有设置height，默认跟width相同
   */
  getWhiteModelCanvas3dImage: async (input: {
    fixed_scale?: number;
    width?: number;
    height?: number;
  }): Promise<IRoomInterface.I_CanvasImageResult> => {
    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    let grid_helper_visible = false;
    if (scene3D.grid_helper) {
      grid_helper_visible = scene3D.gridHelper.visible;
      scene3D.gridHelper.visible = false;
    }
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    container._room_entities.forEach((room_entity) => {
      if (room_entity.ceiling_mesh) {
        room_entity.ceiling_mesh.visible = false;
      }
    });
    container._furniture_entities.forEach((furniture) => {
      if (furniture.category.includes("地毯")) {
        let mesh3d_list = [furniture._mesh3d, furniture._figure_element?._simple_mesh3D, furniture._figure_element?._solid_mesh3D].filter((ele) => ele);
        mesh3d_list.forEach((mesh) => mesh && (mesh.visible = false));
      }
    })
    const canvas_width = scene3D.viewWidth;
    const canvas_height = scene3D.viewHeight;

    const drawing_layers = (LayoutAI_App.instance as TAppManagerBase).drawing_layers;
    const layers_visible: { [key: string]: boolean } = {};
    for (let layer in drawing_layers) {
      layers_visible[layer] = drawing_layers[layer].visible;
      drawing_layers[layer].visible = false;
    }

    const batch_layer = (LayoutAI_App.instance as TAppManagerBase).layer_DefaultBatchLayer;
    const scene3d_layer = drawing_layers[IRoomInterface.DrawingLayerNames.Scene3DViewImageLayer] as TScene3DViewImageLayer;
    const ceiling_layer = drawing_layers[IRoomInterface.DrawingLayerNames.CadCeiling] as TCadCeilingLayer;
    if (scene3d_layer) {
      await scene3d_layer.updateImage(input.fixed_scale || 1.);
      scene3d_layer.visible = true;
    }
    ceiling_layer && (ceiling_layer.visible = false);
    batch_layer.makeDirty();
    LayoutAI_App.instance.update();


    container._room_entities.forEach((room_entity) => {
      if (room_entity.ceiling_mesh) {
        room_entity.ceiling_mesh.visible = true;
      }
    })
    container._furniture_entities.forEach((furniture) => {
      if (furniture.category.includes("地毯")) {
        let mesh3d_list = [furniture._mesh3d, furniture._figure_element?._simple_mesh3D, furniture._figure_element?._solid_mesh3D].filter((ele) => ele);
        mesh3d_list.forEach((mesh) => mesh && (mesh.visible = true));
      }
    })

    if (scene3D.grid_helper) {
      scene3D.gridHelper.visible = grid_helper_visible;
    }
    (scene3D.active_controls as any)._updateMeshVisibleByCameraPosition();
    scene3D.update();
    // if (input.width && input.height && !(input.width == canvas_width && input.height == canvas_height)) {
    //   scene3D.setSize(canvas_width, canvas_height, 1);
    // }


    let res = await IFrameMsgServerExFuncs.getCanvas2dImage(input);
    for (let layer in drawing_layers) {
      drawing_layers[layer].visible = layers_visible[layer];
    }
    if (scene3d_layer) {
      scene3d_layer.visible = false;
    }
    LayoutAI_App.instance.update();
    return res;
  }, // [End:Async]
  /**
   * 对不同的房间, 生成相机视角
   * @param input
   * @param input.method 0:默认方法-数量更多 1:调整后更精简的方法。 默认值为1
   * @param input.camera_zval : 默认不用设置(为1400), 为了看吊顶可以设置成1600
   */
  generateViewCameraList: async (input: {
    filteredRoomUids?: string[];
    filterRoomUuids?: string[];
    filteredRoomNames?: string[];
    method?: number;
    camera_zval?: number;
  }): Promise<SwjLayoutData.I_SwjViewCameraData[]> => {
    let method = 1;
    if (input.method !== undefined) {
      method = input.method;
    }
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room_entities = LayoutContainerUtils.getRoomEntitiesWithFilter(container, input);
    container._ext_drawing_entities.length = 0;
    TViewCameraEntity.updateViewCameraEntities(container, room_entities, { methods: method, camera_zval: input.camera_zval || 1400 });
    let viewCameras = container._ext_drawing_entities.filter(
      entity => entity.realType === 'ViewCamera'
    );
    if (room_entities.length > 0) {
      let _viewCameras = [] as TExtDrawingEntity[];
      room_entities.forEach(room_entity => {
        let viewCamera = room_entity?._view_cameras || [];
        _viewCameras.push(...viewCamera);
      });
      viewCameras = _viewCameras;
      return viewCameras.map(entity => entity.exportData() as SwjLayoutData.I_SwjViewCameraData);
    }
    return viewCameras.map(entity => entity.exportData() as SwjLayoutData.I_SwjViewCameraData);
  }, //[End:Async]
  /**
   * 获得视角的预览图像
   * @param input.uuid 指定的uuid
   * @param input.preview_type  "Plane2D"|"View3D"  <=> 平面2D  | 3D预览截图
   * @param input.img_width  图像的尺寸width - 暂时只影响Plane2D模式, 默认为300
   * @param input.img_height 图像的尺寸height
   * @param input.force 是否强制重新生成 --- 若不强制，会返回上次生成的结果
   * @returns  图像的Base64字符串 - 如果没有会返回 ""(空字符串)
   */
  getViewCameraImage: async (input: {
    uuid: string;
    preview_type: SwjLayoutData.ViewCameraImgPreviewType;
    img_width?: number;
    img_height?: number;
    force?: boolean;
  }): Promise<string> => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let view_camera = TViewCameraEntity.getViewCameraEntityByUuid(input.uuid, container);
    if (view_camera) {
      if (input.preview_type === 'Plane2D') {
        view_camera.updateViewImg(
          container.painter,
          input.img_width || 300,
          input.img_height || 300
        );
        return view_camera._view_img.src;
      } else if (input.preview_type === 'View3D') {
        if (!view_camera._perspective_img || !view_camera._perspective_img.width || input.force) {
          const scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
          let controls = scene3d.controls[CameraViewMode.FirstPerson] as FigureViewControls;
          controls.immediatebindViewEntity(view_camera, scene3d);
          await view_camera.updatePerspectiveViewImg(container.painter);
          scene3d.update();
        }
        return view_camera._perspective_img.src;
      }
    }

    return '';
  }, //[End:Async]

  /**
   * 应用当前视角
   * @param input.uuid 视角的uuid
   * @param input.fov 视场角，单位角度，默认一般是 65
   * @param input.pitch 俯仰角,单位角度，默认一般是90度
   * 
   */
  applyCurrentViewCamera: async (input: { uuid: string, fov?: number, pitch?: number }) => {
    const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let view_camera = TViewCameraEntity.getViewCameraEntityByUuid(input.uuid, container);
    if (view_camera) {
      const scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
      scene3d.active_controls.bindViewEntity(view_camera);
      if (scene3d.active_controls instanceof FigureViewControls) {
        let rect = scene3d.active_controls._rect;
        if (!isNaN(input.fov)) {
          let camera = scene3d.active_controls.camera as PerspectiveCamera;
          console.log("fov", camera.fov, " to ", input.fov);
          camera.fov = input.fov;
          camera.updateProjectionMatrix();
        }
        if (!isNaN(input.pitch)) {
          console.log("pitch", 180 * rect.rotation_x / Math.PI, " to ", input.pitch);
          if (rect.rotation_x < 0) {
            rect.rotation_x = -Math.PI * input.pitch / 180;
          } else {
            rect.rotation_x = Math.PI * input.pitch / 180;
          }
        }

      }
      scene3d.update();
    }
  }, //[End:Async]


  /**
   * 获取当前视角参数
   * 
   */
  getCurrentViewCameraParams: async (input: {}) => {
    const scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
    let camera = scene3d.active_controls.camera as PerspectiveCamera;
    console.log(
      'fov值：', camera.fov,
      '裁剪值：', camera.near,
      '高度：', (scene3d.active_controls as FigureViewControls)._rect.rect_center_3d.z,
      '俯仰角：', (scene3d.active_controls as FigureViewControls)._rect.rotation_x * (180 / Math.PI),
    );
    return camera;
  }, //[End:Async]

  /**
   * 是否打开Panel
   * @param input.panelType : 0 无;  1: 有对应标准移动端的Panel
   */
  setUsePanelType: async (input: { panelType: number }) => {
    LayoutAI_App.emit_M(Msg_Events.SetUsePanelType, input);
  }, //[End:Async]

  /**
   * 离线渲染当前视角场景
   * @returns  {
   * success: boolean,  // 是否提交成功
   * msg: string,  // 提示信息，如提交离线渲染任务成功
   * schemeId: string  // 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * queueId: string,  // 离线渲染任务的队列ID，如 QO202504180000000000000842980652
   * }
   */
  offlineRenderScene: async (input: {}): Promise<{
    success: boolean;
    msg: string;
    queueId: string;
    schemeId: string;
  }> => {
    ORenderConfig.sceneMode = ORenderConfigID.Clear;
    let res = await ServerRenderService.commitOfflineRender();
    return res;
  }, //[End:Async]

  /**
   * 获取离线渲染任务的图册url，需要轮询，可能在排队中（success为true，url为空）
   * @param input.schemeId 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * @param input.queueId 离线渲染任务的队列ID，如 QO202504180000000000000842980652
   * @returns  {
   * success: boolean,  // 是否获取成功
   * msg: string,  // 提示信息，如 获取图片url成功
   * url: string  // 离线渲染任务的图册url，如 https://img3.admin.3vjia.com///UpFile_Render/C00000022/DesignSchemeRenderFile/68d1762afd5b4940b9dfe75e68c2e1e2/QO202504180000000000000842980652.jpg
   * }
   */
  getOfflineRenderUrl: async (input: {
    schemeId: string;
    queueId: string;
  }): Promise<{ success: boolean; msg: string; url: string }> => {
    let res = await ServerRenderService.getRenderImgUrl(input.schemeId, input.queueId);
    return res;
  }, //[End:Async]

  /**
   * 获取离线渲染任务的图册列表
   * @param input.schemeId 离线渲染任务的方案ID，如 68d1762afd5b4940b9dfe75e68c2e1e2
   * @param input.pageIndex 页码，默认1
   * @param input.pageSize 每页数量，默认10
   * @returns  {
   * success: boolean,  // 是否获取成功
   * msg: string,  // 提示信息，如 获取图册列表成功
   * data: {}[];
   * }
   */
  getOfflineRenderAtlasList: async (input: {
    schemeId: string;
    pageIndex?: number;
    pageSize?: number;
  }): Promise<{ success: boolean; msg: string; data: {}[] }> => {
    let resList = await RenderReqOffline.instance.sendAtlas(
      input.schemeId,
      input.pageIndex,
      input.pageSize
    );
    return {
      success: true,
      msg: '获取图册列表成功',
      data: resList.queueList.data.ReturnList
    };
  }, //[End:Async]

  /**
   * 获取AI绘图风格类型
   * @returns 风格类型列表
   * {
   *  text: string; // 风格类型名称
   *  value: string; // 风格类型值
   *  cover: string; // 风格类型封面
   * }[]
   */
  getAigcDesignStyles: async (input: {}): Promise<
    {
      text: string;
      value: string;
      cover: string;
    }[]
  > => {
    return AIGCService.instance.getDesignStyles();
  }, //[End:Async]

  /**
   * 自动保存方案
   * @param input
   */
  autoSaveScheme: async (input: {}) => {
    const layoutContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    if (layoutContainer._room_entities.length == 0) {
      return {
        success: false,
        msg: '请先创建方案'
      };
    }
    await LayoutSchemeService.autoSave(layoutContainer);

    return {
      success: true,
      msg: '保存中，请稍候'
    };
  }, //[End:Async]

  /**
   * 提交绘图请求
   * @param input.roomUid 当前选中房间uid
   * @param input.imgUrl 视角的预览图像 url
   * @param input.imageNum 图片数量（1,2,4）
   * @param input.style 风格类型（现代,中式,北欧,奶油风,法式,混搭,轻奢,诧寂）
   * @returns 绘图结果 {
   *  success: boolean;
   *  msg: string;
   *  schemeId: string;
   *  atlasId: string;
   * }
   */
  submitAigcDraw: async (input: {
    roomUid: string;
    imgUrl: string;
    imageNum: number;
    style: string;
  }): Promise<{ success: boolean; msg: string; schemeId: string; atlasId: string }> => {
    let res = await AIGCService.instance.submitDraw(input);
    return res;
  }, //[End:Async]

  /**
     * 查询绘图结果
     * @param input.atlasId 图册ID
     * @returns 绘图结果 {
        success: boolean;
        msg: string;
        data: AigcAtlasInfo
     */
  getAigcAtlasInfo: async (input: {
    atlasId: string;
  }): Promise<{ success: boolean; msg: string; data: IMsgType.AigcAtlasInfo }> => {
    let data = await AIGCService.instance.queryImageInfo(input.atlasId);
    return {
      success: !!data,
      msg: data ? '获取图册信息成功' : '获取图册信息失败',
      data: data
    };
  }, //[End:Async]

  /**
   * 删除图册
   * @param input.atlasId 图册ID
   * @returns 删除结果 {
   *  success: boolean;
   *  msg: string;
   * }
   */
  deleteAigcAtlasInfo: async (input: {
    atlasId: string;
  }): Promise<{ success: boolean; msg: string }> => {
    try {
      let res = await AIGCService.instance.deleteImage(input.atlasId);
      return {
        success: true,
        msg: '删除图册成功'
      };
    } catch (error) {
      return { success: false, msg: '删除图册失败' };
    }
  }, //[End:Async]

  /**
   * 删除图册中的图片，只有一张图片时不允许删除，可以选择删除图册
   * @param input.imgId 图片ID
   * @returns 删除结果 {
   *  success: boolean;
   *  msg: string;
   * }
   */
  deleteAigcAtlasImage: async (input: {
    imgId: string;
  }): Promise<{ success: boolean; msg: string }> => {
    let res = await AIGCService.instance.deleteImageItem(input.imgId);
    return res;
  }, //[End:Async]

  /**
   * 获取图册列表
   * @param input.schemeId 方案ID
   * @param input.pageIndex 页码，从1开始
   * @param input.pageSize 每页数量，默认10
   * @returns 图册列表 {
   *  success: boolean;
   *  msg: string;
   *  data: AigcAtlasListItem[];
   * }
   */
  getAigcAtlasList: async (input: {
    schemeId: string;
    pageIndex: number;
    pageSize: number;
  }): Promise<{
    success: boolean;
    msg: string;
    data: IMsgType.AigcAtlasListItem[];
  }> => {
    let data = await AIGCService.instance.queryImageList(
      input.schemeId,
      input.pageIndex,
      input.pageSize
    );
    return {
      success: !!data,
      msg: data ? '获取图册列表成功' : '获取图册列表失败',
      data: data
    };
  }, //[End:Async]


  /**
  * 设置场景灯光模式
  * @param input.lightMode 灯光模式
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setSceneLightMode: async (input: {
    lightMode: IMsgType.LightMode;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    if (!scene3D) {
      return {
        success: false,
        msg: '场景未初始化'
      };
    }
    scene3D.setLightMode(input.lightMode as any);
    return {
      success: true,
      msg: '设置灯光模式成功'
    };
  }, //[End:Async]

  /**
  * 设置灯光调试是否显示
  * @param input.showOfflineLight 是否显示光
  * @param input.showTestLight 是否显示补光板
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setDebugLightVisible: async (input: {
    showOfflineLight: boolean;
    showTestLight: boolean;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    let scene3D = LayoutAI_App.instance.scene3D as Scene3D;
    if (!scene3D) {
      return {
        success: false,
        msg: '场景未初始化'
      };
    }
    scene3D.setLightGroupVisible(!!input.showOfflineLight, !!input.showTestLight, false);
    return {
      success: true,
      msg: '设置成功'
    };
  }, //[End:Async]


  /**
  * 设置分辨率
  * @param input.resolutionTag 分辨率类型（SD,HD,FHD,4K,8K） 默认HD
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  setResolution: async (input: {
    resolutionTag: string;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    let config = ServerRenderService.getResolutionConfigByTag(input.resolutionTag);
    if (!config) {
      return {
        success: false,
        msg: '分辨率类型不存在'
      };
    }
    ServerRenderService.resolutionTag = input.resolutionTag as any;
    return {
      success: true,
      msg: '设置成功'
    };
  }, //[End:Async]

  /**
  * 更新吊顶类型
  * @param input.tp 吊顶类型
  * @param input.config 吊顶类型配置
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  updateCeilingLayerTemplate: async (input: {
    tp: string;
    config: IRoomInterface.I_CeilingLayerData;
  }): Promise<{
    success: boolean;
    msg: string;
  }> => {
    console.log("设置吊顶类型", input.tp, input.config);

    // 更新吊顶类型配置
    CeilingConfigReader.setConfig(input.tp, input.config);

    // 更新同类型吊顶的外形
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    container._room_entities.forEach(room_entity => {
      room_entity.room_ceiling_entity.ceiling_layer_entities.forEach(entity => {
        if (entity.ceilingType === input.tp) {
          entity.importSubAreaSimpleData({ ceiling_type: input.tp }, true);
        }
      });
    });
    container._room_entities.forEach(room_entity => {
      room_entity.updateMesh3D();
    });

    return {
      success: true,
      msg: '设置成功'
    };
  }, //[End:Async]

  /**
  * 获取高度超过指定高度的家具列表
  * @param input.roomUUID 房间UID，不传默认所有房间
  * @param input.height 高度，不传默认2500
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  *  data: IRoomInterface.I_FurnitureData[];
  * }
  */
  getFurnitureList: async (input: {
    roomUUID: string;
    height: number;
  }): Promise<IRoomInterface.I_SimpleFigureElement[]> => {
    let furnitureList: IRoomInterface.I_SimpleFigureElement[] = [];

    let h = input.height || 2500;

    const entity2SimpleFigureElement = (entity: TFurnitureEntity) => {
      let rect = entity.figure_element.getTargetRect();
      return {
        uuid: entity.figure_element.uuid,
        category: entity.figure_element.category,
        sub_category: entity.figure_element.sub_category,
        _rect_shape: entity.figure_element._rect_shape,
        length: rect.w,
        depth: rect.h,
        height: entity.height,
        nor: { x: rect.nor.x, y: rect.nor.y, z: rect.nor.z },
        pos: { x: rect.rect_center.x, y: rect.rect_center.y, z: rect.rect_center.z },
      }
    };

    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    if (input.roomUUID) {
      let room = container.getRoomEntityByUUID(input.roomUUID);
      if (!room) {
        return [];
      }
      room.furniture_entities.forEach(entity => {
        if (entity.height >= h) {
          furnitureList.push(entity2SimpleFigureElement(entity));
        }
      });
    }
    else {
      container._furniture_entities.forEach(entity => {
        if (entity.height >= h) {
          furnitureList.push(entity2SimpleFigureElement(entity));
        }
      });
    }

    return furnitureList;
  }, //[End:Async]

  /**
  * 创建房间子区域
  * @param input.roomUUID 房间UID
  * @param input.spaceType 空间类型
  * @param input.center 中心点
  * @param input.w 宽度
  * @param input.h 高度
  * @param input.nor 法向量
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  createRoomSubArea: async (input: {
    roomUUID: string;
    spaceAreaType: IRoomInterface.IRoomSpaceAreaType;
    center: { x: number, y: number, z: number };
    w: number;
    h: number;
    nor: { x: number, y: number, z: number };
  }): Promise<{ success: boolean, msg: string }> => {
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room = container.getRoomEntityByUUID(input.roomUUID);
    if (!room) {
      return { success: false, msg: '房间不存在' };
    }
    if (!input.spaceAreaType) {
      return { success: false, msg: '空间类型不能为空' };
    }

    if (!input.center) {
      return { success: false, msg: '中心点不能为空' };
    }

    if (!input.w || !input.h || input.w <= 0 || input.h <= 0) {
      return { success: false, msg: '尺寸有误' };
    }

    if (!input.nor) {
      return { success: false, msg: '法向量不能为空' };
    }

    const center = new Vector3(input.center.x, input.center.y, input.center.z);
    const w = input.w;
    const h = input.h;
    const nor = new Vector3(input.nor.x, input.nor.y, input.nor.z);

    let subArea: TSubSpaceAreaEntity = RoomSubAreaService.getInstance().createSubAreaPreview(room, input.spaceAreaType);
    subArea.rect.rect_center = center;
    subArea.rect.length = w;
    subArea.rect.depth = h;
    subArea.rect.nor = nor;
    subArea.rect.updateRect();

    RoomSubAreaService.getInstance().addSubAreaToRoomAndContainer(subArea);
    subArea.update();
    room.update();

    return { success: true, msg: '创建成功' };
  }, //[End:Async]

  /**
  * 更新房间子区域
  * @param input.roomUUID 房间UID
  * @param input.subAreaIndex 子区域索引
  * @param input.center 中心点
  * @param input.w 宽度
  * @param input.h 高度
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  updateRoomSubArea: async (input: {
    roomUUID: string;
    subAreaIndex: number;
    center?: { x: number, y: number, z: number };
    w?: number;
    h?: number;
  }): Promise<{ success: boolean, msg: string }> => {
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room = container.getRoomEntityByUUID(input.roomUUID);
    if (!room) {
      return { success: false, msg: '房间不存在' };
    }
    let subArea: TSubSpaceAreaEntity = room._sub_room_areas[input.subAreaIndex];
    if (!subArea) {
      return { success: false, msg: '子区域不存在' };
    }
    if (input.center) {
      subArea.rect.rect_center = new Vector3(input.center.x, input.center.y, input.center.z);
    }
    if (input.w && input.w > 0) {
      subArea.rect.length = input.w;
    }
    if (input.h && input.h > 0) {
      subArea.rect.depth = input.h;
    }
    subArea.rect.updateRect();
    subArea.update();
    room.update();

    RoomSubAreaService.getInstance().updateSubAreaLayoutScheme(subArea, true);

    return { success: true, msg: '更新成功' };
  }, //[End:Async]

  /**
  * 更新房间子区域
  * @param input.roomUUID 房间UID
  * @param input.subAreaIndex 子区域索引
  * @returns 返回 {
  *  success: boolean;
  *  msg: string;
  * }
  */
  deleteRoomSubArea: async (input: {
    roomUUID: string;
    subAreaIndex: number;
  }): Promise<{ success: boolean, msg: string }> => {
    let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
    let room = container.getRoomEntityByUUID(input.roomUUID);
    if (!room) {
      return { success: false, msg: '房间不存在' };
    }
    let subArea: TSubSpaceAreaEntity = room._sub_room_areas[input.subAreaIndex];
    if (!subArea) {
      return { success: false, msg: '子区域不存在' };
    }
    RoomSubAreaService.getInstance().removeSubAreaFromRoomAndContainer(subArea);
    room.update();
    return { success: true, msg: '删除成功' };
  }, //[End:Async]
};

(globalThis as any).IFrameMsgServerExFuncs = IFrameMsgServerExFuncs;