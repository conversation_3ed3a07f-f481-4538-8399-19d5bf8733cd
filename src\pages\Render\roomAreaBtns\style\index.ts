import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => ({
    root: css`
        width:100%;
        height:100%;
        padding: 0 16px;
    `,
    container_listInfo: css`
        overflow-y: hidden;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        overflow-x: auto;
        -ms-overflow-style: none;  /* 适用于 Internet Explorer 和 Edge */
        scrollbar-width: none;  /* 适用于 Firefox */
        &::-webkit-scrollbar {
            width: 0; /* Safari 和 Chrome */
            height: 0; /* Safari 和 Chrome */
        }
    `,
    btn: css`
        // 按钮样式
        flex: 0 0 auto;
        width: 80px;
        height: 28px;
        background-color: #FFFFFF;
        border: 1px solid #00000026;
        border-radius: 4px;
        overflow: hidden;
        color: #515151;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 12px;

        letter-spacing: 0px;
        text-align: center;
        margin-right: 8px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        display: flex;
        align-items: center;
        justify-content: center;
    `,

    selected: css`
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
        color: #FFFFFF;
        font-weight: semibold;
    `,
    selectListBar: css`
       height: 32px;
       .ant-select-selector
       {
         border-color: #00000026 !important;
         box-shadow: none !important;
       }
     `,
}));
