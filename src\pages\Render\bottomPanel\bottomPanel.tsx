import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Input } from "@svg/antd";
import { useEffect, useRef, useState } from "react";
import { BuildingService, I_BuildRecord } from "@/Apps/LayoutAI/Services/Basic/BuildingService";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { Sleep } from "@/Apps/LayoutAI/Utils/basic_utils";
import { EventName } from "@/Apps/EventSystem";
import { LayoutPopEvents } from "../layoutPopup/layoutPopup";
import sceneModeBtns from "../sceneModeBtns/sceneModeBtns";
import { LightMainEvents } from "../lightMain/lightMain";
import Icon from '@/components/Icon/icon'
/**
 * @description 主页
 */
export enum BottomPanelEvents{
    setIsVisible = "setIsVisible"
}
interface I_Btn {
    id:string;
    icon:string;
    label:string;
    onClick?:()=>void;
}
const BottomPanel: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const [isVisible,setIsVisible] = useState<boolean>(true);

    const [activeState, setActiveState] = useState<string>("");

    const default_btns :I_Btn[] = [
        {
            id : "Layout",
            label : t("AI布局"),
            icon :"iconbujusheji"
        },
        {
            id : "material",
            label : t("图例"),
            icon :"iconformatter"
        },
        {
            id : "attribute",
            label :t("属性"),
            icon :"iconinfo_boole"
        },
        {
            id:"Matching",
            label :t("风格套系"),
            icon :"iconfenggetaoxi"
        }
    ]
    
    const default_state_onClick = (btn:I_Btn)=>{
        if(btn.id === activeState)
        {
            setActiveState("");
        }
        else{
            setActiveState(btn.id);
        }
    }
    const [buttons,setButtons] = useState<I_Btn[]>(default_btns);

    const object_id = "bottom_pannel";
    useEffect(()=>{
        LayoutAI_App.on(BottomPanelEvents.setIsVisible,(t:boolean)=>{
            setIsVisible(t);
        });

        LayoutAI_App.on_M(LightMainEvents.showLight3DViewer,object_id,(tt:boolean)=>{
            if(tt)
            {
                setButtons([
                    {
                        id : "Layout",
                        label :t("布局"),
                        icon :"iconbujusheji"
                    },
                    {
                        id:"Matching",
                        label :t("风格"),
                        icon :"iconfenggetaoxi"
                    },
                    {
                        id : "attribute",
                        label :t("属性"),
                        icon :"iconinfo_boole"
                    },
                    {
                        id:"CameraViews",
                        label:t("视角"),
                        icon:"icona-viewsswitching"
                    }]
                );
            }  
            else{
                setButtons([
                    {
                        id : "Layout",
                        label :t("布局"),
                        icon :"iconbujusheji"
                    },
                    {
                        id : "material",
                        label :t("图例"),
                        icon :"iconformatter"
                    },
                    {
                        id : "attribute",
                        label :t("属性"),
                        icon :"iconinfo_boole"
                    },
                    {
                        id:"Matching",
                        label: t("风格套系"),
                        icon :"iconfenggetaoxi"
                    }]
                ); 
            }
        })
    },[]);

    useEffect(()=>{
        LayoutAI_App.emit(LayoutPopEvents.showPopup,activeState);

    },[activeState]);
    if(!isVisible)
    {
        return (<></>);
    }

    return (
    <div className={styles.root} onClick={()=>{
        setActiveState("");
    }}>
        {buttons.map((btn,index)=>{
            return <div className={styles.bottom_btn + ((btn.id===activeState)?" active":"")} key={"bottom_btn_"+index} onClick={(ev)=>{
                if(btn.onClick) { btn.onClick(); }
                else {
                    default_state_onClick(btn);
                }
                
                ev.stopPropagation();
            }}>
                <div className={"iconfont "+btn.icon}></div>
                <div className="btn_label">
                    {btn.label}
                </div>
            </div>
        })}


    </div>);
};

export default observer(BottomPanel);
