import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    root: css`
      .swj-top-menu-button-item {
        width: 80px;
        text-align: center;
      }
      .ant-modal-content
      {
        padding: 10px 10px !important;
      }
      /* @keyframes slideIn {
        from {
          transform: translateY(-48px);
        }
        to {
          transform: translateY(0);
        }
      }
      @keyframes slideIn1 {
        from {
          transform: translateX(-340px);
        }
        to {
          transform: translateX(0);
        }
      }
      .svg-friga1
      {
        animation: slideIn .8s forwards;
      }
      .swj-left-menu-bar-container{
        animation: slideIn1 .8s forwards;
      } */
    `,
    landscape:css`
        position:absolute;
        -webkit-transform:rotate(90deg);
        -webkit-transform-origin:0% 0%;/*1.重置旋转中心*/
        
        -moz-transform: rotate(90deg);
        -moz-transform-origin:0% 0%;
        
        -ms-transform: rotate(90deg);
        -ms-transform-origin:0% 0%;
        
        transform: rotate(90deg);
        transform-origin:0% 0%;
        
        width: 100vh;/*2.利用 vh 重置 ‘宽度’ */
        height: 100vw;/* 3.利用 vw 重置 ‘高度’ */
        top: 0;
        left: 100vw;

    `,
    loading: css`
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: calc(100% - 48px);
      background-color: rgba(255, 255, 255);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    `,
    content: css`
      position: absolute;
      top: 48px;
      left: 0; 
      right: 0;
      bottom: 0;
      overflow: hidden;
    `,
    model_capture_container: css`
      position: fixed;
      top: -200px;
      left: 700px;
      width: 100vw;
      height: 500px;
      background: #FFF;
      z-index: 9999999;
    `,
    side_pannel: css`
      position: fixed;
      top: 48px;
      left: 0;
      width: 0px;
      bottom: 0;
      background-color: #fff;
      z-index:1;
      .iconfont
      {
        width: 20px !important;
        height: 24px !important;
      } 
    `,
    left_panel: css`
      position: fixed;
      top: 48px;
      left: 0;
      width: 280px;
      margin-left: 0px;
      bottom: 0;
      background-color: #fff;
      z-index:1;
      .iconfont
      {
        width: 20px !important;
        height: 24px !important;
      } 
    `,
    canvas_pannel_mobile: css`
      left: -30px !important;
      top: -200px !important;
    `,
    canvas_pannel: css`
      position: absolute;
      left: 0px;
      top: -100px;
      background-color: #EAEAEB;
      width : calc(100% + 100px);
      height : calc(100% + 200px);
      overflow: hidden;
      .canvas {
        position : absolute;
        left: 0px;
        top: 0px;
        &.canvas_drawing {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
        }
        &.canvas_moving {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png) 16 16,auto;
        }
        &.canvas_leftmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
        }
        &.canvas_rightmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
        }
        &.canvas_acrossmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
        }
        &.canvas_verticalmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
        }
        &.canvas_text {
          cursor : text;
        }
        &.canvas_pointer {
          cursor : pointer;
        }
        &.canvas_splitWall {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 8 16,auto;
        }
      }

      .canvas_btns {
        width: auto;
        margin: 0 auto;
        position: fixed;
        display: flex;
        justify-content: center;
        bottom: 35px;
        z-index:10;
        left: 50%;
        transform: translateX(-50%);
        .btn {
          ${checkIsMobile() ? 
          `
            width: 120px;
            height: 36px;
            font-size: 14px;
          `
          :
          `
            width: 200px;
            height: 48px;
            font-size: 16px;
          `
          }
          border-radius: 6px;
          border: none;

          font-weight: 600;
          margin-right: 10px;
          margin-left: 10px;
        }
        .design_btn {
          background: #e6e6e6;
          margin-right: 20px;
        }
        @media screen and (max-height: 600px){
          bottom: 50px !important;
        }
      }
    `,
    layout_steps: css`
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      margin-left: -120px;
      width: 500px;
      top: 20px;
      z-index: 20;
    `,
    progressInfo: css`
      position: absolute;
      top: 0%;
      padding-top: 21%;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    left_content: css`
      z-index: 99;
  
      ${checkIsMobile() ? 
      `
        width: 180px !important;
        min-width: 180px !important;
      ` 
      :
       `
        min-width: 280px !important;
       `
      }
    `,
    overlay: css`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    scene3d: css`
        width: 400px;
        height: 300px;
        position:absolute;
        right:245px;
        top:5px;
    `,
    MeasurScaleMode: css`
      height: 48px;
      width: 100%;
      position: fixed;
      top: 48px;
      background-color: #262626;
      text-align: center;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    `,
    expireEmpty: css`
      /* z-index: 99999; */
      display: flex;
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      justify-content: center;
      text-align: center;
      img{
        width: 256px;
        height: 256px;
      }
      .first
      {
        color: #1C1F23;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 24px;
        line-height: 32px;
        font-weight: 600;
        margin: 20px 0 8px 0;
      }
      .second
      {
        color: #00000072;
        font-weight: regular;
        font-size: 14px;
        line-height: 22px;
      }
    `,
    mobileBottom: css`
      position: fixed;
      height: 250px;
      bottom: 0px;
      background-color: #fff;
      width: 100%;
      padding: 15px 48px;
      .shcmeName
      {
        color: #282828;
        font-family: SF Pro Text;
        font-weight: semibold;
        font-size: 20px;
        line-height: 1.4;
        font-weight: 600;
      }
      .info
      {
        display: inline-flex;
        font-weight: 600;
        margin-top: 10px;
        .name
        {
          color: #5B5E60;
          font-family: PingFang SC;
          font-weight: regular;
          font-size: 16px;
          line-height: 1.5;
          letter-spacing: 0px;
          text-align: left;
          margin-bottom: 5px;
          width: 140px;
        }
      }
      .btn
      {
        text-align: center;
        margin: 0 auto;

      }
      .btnInfo
      {
        text-align: center;
        margin-top: 20px;
        .btn
        {
          border-radius: 8px;
          background: #F4F5F5;
          width: 200px;
          height: 48px;
          border: none;
          color: #282828;
          font-family: PingFang SC;
          font-weight: semibold;
          font-size: 16px;
          line-height: 1.5;
          font-weight: 600;
        }
      }
      .shareImage
      {
        color: #5B5E60;
        margin-top: 12px;
        font-size: 16px;
        img{
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 5px;
        }

      }
    `,
    shareBox: css`
      padding: 24px;
      font-size: 14px; 
      font-weight: 600;
      img{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin-right: 10px;
      }
    `,
    canvas3d:css`
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      z-index:-1;
    `,
    aiDraw: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 9999;
    `,
    material_iframe: css`
      width: 100%;
      height: 99%;
      border: none;
      overflow-y: hidden;
    `,
    comfirmFurnishDialog: css`
      position: fixed;
      display: flex;
      flex-direction: column;
      align-items: center;
      align-content: space-between;
      div#unfurnish-tip-container {
        margin-top: 25px;
        flex: 1;
        span#unfurnish-tip {
          margin-top: 30px;
          font-size: 16px;
          font-weight: 600;
          color: #000;
        }
      }
      div#unfurnish-btn-container {
        flex: 1;
        Button {
          margin-left: 16px;
          margin-right: 16px;
        }
      }
      top: 40%;
      left: 40%;
      width: 450px;
      height: 130px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.9); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
  };
});
