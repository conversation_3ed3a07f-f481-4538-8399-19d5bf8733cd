import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { Input } from "@svg/antd";
import { useEffect, useRef, useState } from "react";
import { BuildingService, I_BuildRecord } from "@/Apps/LayoutAI/Services/Basic/BuildingService";
import { LayoutAI_App, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { AI2DesignBasicModes, AI2DesignManager } from "@/Apps/AI2Design/AI2DesignManager";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import LayoutPopup from "../layoutPopup/layoutPopup";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import SceneModeBtns from "../sceneModeBtns/sceneModeBtns";
import Scene3DDiv from "@/components/Scene3DDiv/scene3DDiv";
import MobileNavigation, { NavigationEvent, PageStates } from "../navigation/navigation";
import { is_debugmode_website } from "@/config";
import { Scene3DEvents } from "@/Apps/LayoutAI/Scene3D/Scene3DEvents";
import { DrawingFigureMode } from "@/Apps/LayoutAI/Layout/IRoomInterface";

/**
 * @description 主页
 */
export enum LightMainEvents {
    showLight3DViewer = "showLight3DViewer"
}
const LightMain: React.FC = () => {
    const { t } = useTranslation()
    const { styles } = useStyles();
    const [schemeName, setLayoutSchemeName] = useState<string>("");
    const [zIndexOf3DViewer, setZIndexOf3DViwer] = useState<number>(-2);
    let store = useStore();

    const schemeNameRef = useRef(null);
    LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
    if (LayoutAI_App.instance) {
        LayoutAI_App.t = t;
    }

    const object_id = "LightMain";

    const updateCanvasSize = () => {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
            LayoutAI_App.instance.update();
        }
    };
    const updateIsWebSiteDebug = () => {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance._is_website_debug = is_debugmode_website;
        }
    }
    useEffect(() => {
        updateIsWebSiteDebug();
        // LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
        window.addEventListener('resize', updateCanvasSize);
        updateCanvasSize();

        if (LayoutAI_App.instance) {

            if (!LayoutAI_App.instance.initialized) {
                LayoutAI_App.instance.init();

                LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
                (LayoutAI_App.instance as TAppManagerBase).layout_graph_solver._is_query_server_model_rooms = false;
                (LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode = DrawingFigureMode.Texture;
                LayoutAI_App.instance.prepare().then(() => {

                })
                LayoutAI_App.instance.bindCanvas(document.getElementById("cad_canvas") as HTMLCanvasElement);
            }

            LayoutAI_App.instance.update();
        }
        LayoutAI_App.on_M(LightMainEvents.showLight3DViewer, object_id, (t: boolean) => {
            if (t) {
                setZIndexOf3DViwer(2);
                LayoutAI_App.emit(Scene3DEvents.UpdateScene3D, false);
            }
            else {
                setZIndexOf3DViwer(-1);
            }
        });

        LayoutAI_App.on(EventName.LayoutSchemeOpened, (event: any) => {
            setLayoutSchemeName(event.name);
            LayoutAI_App.emit(NavigationEvent, PageStates.Default);
        });
    }, []);

    return (
        <div className={styles.root}>
            <MobileNavigation></MobileNavigation>
            <LayoutPopup></LayoutPopup>
            <div id='Canvascontent' className={styles.content}>
                <div className={"3d_container " + styles.canvas3d} style={{ zIndex: zIndexOf3DViewer }}>
                    <Scene3DDiv defaultViewMode={4}></Scene3DDiv>
                </div>

                <div id="body_container" className={styles.canvas_pannel}>
                    <canvas
                        id="cad_canvas"
                        className="canvas"
                        onMouseEnter={() => {
                            store.homeStore.setIsmoveCanvas(false);
                        }}
                        onMouseLeave={() => {
                            store.homeStore.setIsmoveCanvas(true);
                        }}
                        onTouchStart={(e) => {
                            if (e.touches.length === 2) {
                                const dx = e.touches[0].clientX - e.touches[1].clientX;
                                const dy = e.touches[0].clientY - e.touches[1].clientY;
                                const distance = Math.sqrt(dx * dx + dy * dy)
                                store.homeStore.setInitialDistance(distance / store.homeStore.scale);
                            }
                        }}
                        onTouchMove={(e) => {
                            if (e.touches.length === 2) {
                                const dx = e.touches[0].clientX - e.touches[1].clientX;
                                const dy = e.touches[0].clientY - e.touches[1].clientY;
                                const distance = Math.sqrt(dx * dx + dy * dy);
                                let newScale = distance / store.homeStore.initialDistance;
                                if (newScale > 5) {
                                    newScale = 5;
                                } else if (newScale < 0.05) {
                                    newScale = 0.05;
                                }
                                store.homeStore.setScale(newScale);

                                LayoutAI_App.DispatchEvent(LayoutAI_Events.scale, newScale)
                            }
                        }}
                        onTouchEnd={() => {
                            store.homeStore.setInitialDistance(null);
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

export default observer(LightMain);
