import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      width:100%;
      height:100vh;
      display:flex;
      flex-direction: column;
      background: #F6F7F9;
    `,
    topMenu:css`
      width:100%;
      padding: 20px;
      display:flex;
      justify-content: space-between;

      @media screen and (max-width: 450px){
        padding: 8px;
      }
      .left{
        display:flex;
        align-items: center;
        span{
          color: #000000;
          font-family: PingFang SC;
          font-weight: 600;
          font-size: 16px;
          line-height: 1.5;
          letter-spacing: 0px;
          text-align: left;
        }
        .back_button{
          display:flex;
          align-items: center;
          height: 30px;
          width: 74px;
          border-radius: 8px;
          background: #FFFFFF;
          border: 1px solid #00000026;
          cursor: pointer;
          span{
            color: #282828;
            font-family: PingFang SC;
            font-weight: normal;
            font-size: 14px;
            line-height: 1.57;
            letter-spacing: 0px;
            text-align: left;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
                
        .history_button{
          display:flex;
          align-items: center;
          justify-content: center;
          height: 30px;
          width: 98px;
          // margin-right: 12px;
          border-radius: 8px;
          background: #FFFFFF;
          border: 1px solid #00000026;
          cursor: pointer;
          gap: 2px;
          span{
            color: #282828;
            font-family: PingFang SC;
            font-weight: normal;
            font-size: 12px;
            line-height: 1.67;
            letter-spacing: 0px;
            text-align: left;
          }
          svg {
            width: 16px;
            height: 16px;
          }
        }
      }
    `,
    main_container:css`
      display: flex;
      width: 100%;
      height: calc(var(--vh, 1vh) * 100 - 72px);
      padding: 0 20px 20px 20px;
      gap: 20px;
      position: relative;
    `,
    IsLandscape_main_container:css`
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      height: calc(var(--vh, 1vh) * 100 - 72px);
      padding: 20px;
      gap: 20px;
      position: relative;

      @media screen and (max-width: 450px){
        padding: 8px;
        gap: 8px;
        height: calc(var(--vh, 1vh) * 100 - 48px);
      }

      .FilterFieldBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 254px;
        height: 40px;
        border-radius: 6px;
        background: #EAEAEB;
        color: #282828;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: 0px;
        text-align: left;
        gap: 8px;
      }
    `,
    drawer_btn: css`
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;

      @media screen and (max-width: 450px){
        height: 120px;
      }
    `,
    mobile_atlas_container:css`
      padding: 20px;
      position:absolute;
      left: 0;
      top: 0;
      width:100%;
      height:100%;
      z-index:-1;
      background: #f6f7f9;
    `,
    canvas3d:css`
      position:absolute;
      left: -1500px;
      width:100%;
      height:100%;
      z-index:-1;
    `,
    content: css`
      position: absolute;
      top: 48px;
      left: 500px; 
      right: 0;
      bottom: 0;
      overflow: hidden;
      visibility:hidden;
    `,
    Scene3DDivcanvas_pannel: css`
      position: absolute;
      left: 0px;
      top: -100px;
      background-color: #EAEAEB;
      width : calc(100% + 100px);
      height : calc(100% + 200px);
      overflow: hidden;
      .canvas {
        position : absolute;
        left: 0px;
        top: 0px;
        &.canvas_drawing {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
        }
        &.canvas_moving {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png) 16 16,auto;
        }
        &.canvas_leftmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
        }
        &.canvas_rightmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
        }
        &.canvas_acrossmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
        }
        &.canvas_verticalmove {
          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
        }
        &.canvas_text {
          cursor : text;
        }
        &.canvas_pointer {
          cursor : pointer;
        }
        &.canvas_splitWall {
          cursor : url(./static/icons/split.png) 8 16,auto;
        }
      }

      .canvas_btns {
        width: auto;
        margin: 0 auto;
        position: fixed;
        display: flex;
        justify-content: center;
        bottom: 35px;
        z-index:10;
        left: 50%;
        transform: translateX(-50%);
        .btn {
          ${checkIsMobile() ? 
          `
            width: 120px;
            height: 36px;
            font-size: 14px;
          `
          :
          `
            width: 200px;
            height: 48px;
            font-size: 16px;
          `
          }
          border-radius: 6px;
          border: none;

          font-weight: 600;
          margin-right: 10px;
          margin-left: 10px;
        }
        .design_btn {
          background: #e6e6e6;
          margin-right: 20px;
        }
        @media screen and (max-height: 600px){
          bottom: 50px !important;
        }
      }
    `,
  }

});
