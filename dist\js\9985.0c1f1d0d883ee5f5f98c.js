"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[9985],{55032:function(n,e,t){t.r(e),t.d(e,{default:function(){return st}});var i=t(13274),r=t(69802),o=t(66910),a=t(15696),c=t(49450),l=t(62634),s=t(37112),u=t(41594),M=t(27347),d=t(98612),g=t(9003),f=t(88934),x=t(78644),N=t(83657),p=t(23825),D=t(44466),j=t(90503),I=t(19356),h=t(29933),m=t(22640),y=t(84872),b=t(58567),w=t(20995),z=t(56697),A=t(42751),v=t(49816),T=t(90112),S=t(51010),O=t(75670),E=t(53704),L=t(99030),C=t(17365),k=t(61928),U=t(8268);function Q(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Y(){var n=Q(["\n      position:fixed;\n      left:0;\n      bottom:0;\n      height: calc(var(--vh, 1vh) * 100);\n      width:100%;\n      z-index: 999;\n      background: #fff;\n      .slide-enter {\n        transform: translateX(-100%);\n        opacity: 0;\n      }\n\n      .slide-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-exit-active {\n        transform: translateX(100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n\n\n      .slide-reverse-enter {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n\n      .slide-reverse-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-reverse-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-reverse-exit-active {\n        transform: translateX(-100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .upload_hx\n      {\n        position: fixed;\n        right: 25px;\n        bottom: 60%;\n      }\n    "]);return Y=function(){return n},n}function Z(){var n=Q(["\n      padding: 0px 10px;\n      height: 100%;\n      /* .right_btns\n      {\n        position: fixed;\n        right: 25px;\n        top: 25px;\n      } */\n\n    "]);return Z=function(){return n},n}function _(){var n=Q(["\n      width: 100%;\n      height: 100%;\n    "]);return _=function(){return n},n}function P(){var n=Q(["\n      padding: 0px 40px;\n      height: calc(var(--vh, 1vh) * 100 - 170px);\n      margin-top: 16px;\n      overflow-y: scroll;\n      ::-webkit-scrollbar\n      {\n        display: none;\n      }\n      .demandLabel\n        {\n          font-weight: 600;\n          font-size: 16px;\n          color: #000;\n          margin-bottom: 8px;\n          margin-top: 20px;\n        }\n      .demandItem\n      {\n       \n        .tabRoot\n        {\n          display: flex;\n          flex-wrap: wrap;\n        }\n      }\n      .demandtab\n      {\n        display: flex;\n        width: 100px;\n        height: 32px;\n        padding: 4px 16px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n        background: #F2F3F4;\n        margin-right: 12px;\n        margin-bottom: 12px;\n      }\n      .selected\n      {\n        border-radius: 6px;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);\n        color: #fff;\n      }\n    "]);return P=function(){return n},n}function G(){var n=Q(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    "]);return G=function(){return n},n}function F(){var n=Q(["\n      width: 100%;\n      height: 100%;\n    "]);return F=function(){return n},n}function B(){var n=Q(["\n      display: flex;\n      padding: 40px 40px 0px 40px;\n      justify-content: space-between;\n      align-items: center;\n\n      .title{\n        font-size: 24px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        .back {\n          width: 28px;\n          height: 28px;\n          border-radius: 6px;\n          background: #E9EBEB;\n          padding: 4px;\n          margin-right: 8px;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 14px;\n        }\n      }\n\n      .mySchemeButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 120px;\n        font-size: 11px;\n      }\n      .myAtlasButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 20px;\n        font-size: 11px;\n      }\n    "]);return B=function(){return n},n}function R(){var n=Q(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: 88px;\n      background: #fff;\n      display: flex;\n      align-items: center;\n      padding: 20px 60px;\n      justify-content: space-between;\n      .ant-btn\n      {\n        width: 160px;\n        height: 48px;\n        border-radius: 24px;\n      }\n      .rotate\n      {\n        font-size: 16px;\n        color: #5B5E60;\n      }\n    "]);return R=function(){return n},n}function V(){var n=Q(["\n      display: flex;\n      flex-wrap: wrap;\n      box-sizing: border-box;\n      margin-top: 20px;\n    "]);return V=function(){return n},n}function H(){var n=Q(["\n      width: calc(20% - 10px);\n      height: auto;\n      padding: 2px;\n      box-sizing: border-box;\n      position: relative;\n      margin-right: 10px;\n      @media (max-width: 800px) {\n        width: calc(33.33% - 10px);\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 5/3;\n      }\n    "]);return H=function(){return n},n}function W(){var n=Q(["\n      padding: 0 5px;\n      "]);return W=function(){return n},n}function X(){var n=Q(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n      margin-top: 5px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 10px;\n      .ant-rate\n      {\n        color: #FFAA00;\n        font-size: 16px !important;\n        .ant-rate-star:not(:last-child)\n        {\n          margin-inline-end: 3px;\n        }\n      }\n    "]);return X=function(){return n},n}function J(){var n=Q(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      display: flex;\n      margin-top: 5px;\n    "]);return J=function(){return n},n}var K=(0,U.rU)(function(n){n.token;var e=n.css;return{enterPage:e(Y()),selectHx:e(Z()),hxRoot:e(_()),selectDemand:e(P()),styleTitle:e(G()),demandRoot:e(F()),hxHeader:e(B()),bottom:e(R()),container_listInfo:e(V()),container_list:e(H()),textInfo:e(W()),container_title:e(X()),container_desc:e(J())}}),$=t(95301),q=t(46396),nn=t(76330),en=t(53837),tn=t(47299),rn=t(61307),on=t(5640),an=t(17655),cn=t(87961),ln=t(9455),sn=t(7332);function un(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Mn(){var n=un(["\n    display: flex;\n    flex-direction: row-reverse;\n    background-color: #fff;\n    position: sticky;\n    bottom: 0;\n    align-items: center;\n    background-color: #f6f7f9;\n    padding-top: 7px;\n    "]);return Mn=function(){return n},n}function dn(){var n=un(["\n    width: 100%;\n    height: 100vh;\n    border-radius: 12px;\n    overflow-y: auto;\n    display: flex;\n    flex-direction: column;\n    background-color: #f6f7f9;\n    position: fixed;\n    z-index: 999;\n    .atlas_header{\n        display:flex;\n        justify-content: space-between;\n\n        .segmented{\n\n        }\n\n        .back_button{\n            display:flex;\n            align-items: center;\n            margin-right: 20px;\n            height: 30px;\n            width: 74px;\n            border-radius: 8px;\n            background: #FFFFFF;\n            border: 1px solid #00000026;\n            margin-bottom: 10px;\n            cursor: pointer;\n            span{\n            color: #282828;\n            font-family: PingFang SC;\n            font-weight: normal;\n            font-size: 14px;\n            line-height: 1.57;\n            letter-spacing: 0px;\n            text-align: left;\n            }\n        }\n        }\n    "]);return dn=function(){return n},n}function gn(){var n=un(["\n    width:100%;\n    height: 100%;\n    padding-right: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: #959598;\n    "]);return gn=function(){return n},n}function fn(){var n=un(["\n    display: grid;\n    gap: 20px;\n    flex: 1;\n    overflow-y: auto;\n\n    /* 隐藏滚动条 - Webkit浏览器 */\n    &::-webkit-scrollbar {\n        display: none;\n    }\n    \n    /* 隐藏滚动条 - Firefox */\n    scrollbar-width: none;\n    \n    /* 隐藏滚动条 - IE */\n    -ms-overflow-style: none;\n\n    // grid-template-rows: repeat(auto-fill, 200px);\n\n    @media screen and (min-width: 1400px) {\n        grid-template-columns: repeat(5, calc(20% - 20px));\n    }\n    \n    @media screen and (max-width: 1400px) and (min-width: 960px) {\n        grid-template-columns: repeat(4, calc(25% - 20px));\n    }\n    \n    @media screen and (max-width: 960px) and (min-width: 560px) {\n        grid-template-columns: repeat(3, calc(33.33% - 20px));\n    }\n    @media screen and (max-width: 560px) and (min-width: 320px) {\n        grid-template-columns: repeat(2, calc(50% - 20px));\n    }\n    @media screen and (max-width: 320px) {\n        grid-template-columns: repeat(1, 100%);\n    }\n\n    &::-webkit-scrollbar {\n        width: 6px;\n    }\n    \n    &::-webkit-scrollbar-thumb {\n        background: #00000026;\n        border-radius: 3px;\n    }\n    \n    &::-webkit-scrollbar-track {\n        background: transparent;\n    }\n    "]);return fn=function(){return n},n}function xn(){var n=un(["\n    border: 1px solid transparent;\n    // margin-bottom: 20px;\n    .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        .ant-image {\n        width: 100%;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        }\n        .ant-image-mask{\n        opacity: 0;\n        }\n        img {\n        max-width: 100%;\n        max-height: 100%;\n        border-radius: 4px;\n        object-fit: contain;\n        position: absolute;\n        left: 50%;\n        transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n    }\n\n    .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n        content: '';\n        position: absolute;\n        inset: 0;\n        border-radius: 8px;\n        padding: 1px;\n        background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n        mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n        mask-composite: exclude;\n        -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                    linear-gradient(#fff 0 0);\n        -webkit-mask-composite: xor;\n        pointer-events: none;\n        }\n        \n        span{\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        margin-top: 4px;\n        }\n    }\n\n    .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: center;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        max-width: 180px;\n        \n        @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n        }\n        \n        @media screen and (max-width: 960px) and (min-width: 560px) {\n            max-width: 120px;\n        }\n        @media screen and (max-width: 560px) {\n            max-width: 80px;\n        }\n        }\n        \n        .time {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: center;\n        white-space: nowrap;\n        overflow: hidden;\n        // text-overflow: ellipsis;\n        max-width: 80px;\n        }\n    }"]);return xn=function(){return n},n}function Nn(){var n=un(["\n        padding-top: 20px;\n    "]);return Nn=function(){return n},n}var pn=(0,U.rU)(function(n){var e=n.css;return{PageContainer:e(Mn()),root:e(dn()),noData:e(gn()),content:e(fn()),item:e(xn()),display:e(Nn())}}),Dn=t(44404);function jn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function In(){var n=jn(["\n        display: flex;\n        flex-wrap: wrap;\n        height: auto;\n        margin: 0 -20px;\n    "]);return In=function(){return n},n}function hn(){var n=jn(["\n        margin: 10px 20px !important;\n        border-radius: 8px;\n        overflow: hidden;\n\n        @media screen and (min-width: 700px) {\n        width: calc((100% / 2) - 40px) !important;\n        }\n        @media screen and (min-width: 900px) {\n        width: calc((100% / 3) - 40px) !important;\n        }\n        @media screen and (min-width: 1150px) {\n        width: calc((100% / 4) - 40px) !important;\n        }\n        /* @media screen and (min-width: 1500px) {\n        width: calc((100% / 5) - 40px) !important;\n        }\n        @media screen and (min-width: 2000px) {\n        width: calc((100% / 6) - 40px) !important;\n        } */\n    "]);return hn=function(){return n},n}function mn(){var n=jn(["\n        display: flex;\n        width: auto;\n        img {\n        width: 48px;\n        margin-top: -3px;\n        }\n    "]);return mn=function(){return n},n}function yn(){var n=jn(["\n        margin-left: 8px;\n        justify-content: center;\n    "]);return yn=function(){return n},n}function bn(){var n=jn(["\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: bold;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n    "]);return bn=function(){return n},n}function wn(){var n=jn(["\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n    "]);return wn=function(){return n},n}function zn(){var n=jn(["\n        border: none;\n        padding: 0 8px;\n        margin-left: 30px;\n        height: 32px;\n        background-color: #f4f5f5;\n        color: #282828;\n        font-family: PingFang SC;\n        font-weight: normal;\n        font-size: 14px;\n        line-height: 1.57;\n        letter-spacing: 0px;\n        text-align: left;\n        &:hover {\n        color: #282828 !important;\n        background-color: #f4f5f5 !important;\n        }\n    "]);return zn=function(){return n},n}function An(){var n=jn(["\n        transform: rotate(45deg);\n    "]);return An=function(){return n},n}var vn=(0,U.rU)(function(n){var e=n.css;return{cardContainer:e(In()),card:e(hn()),content:e(mn()),right:e(yn()),title:e(bn()),desc:e(wn()),insertBtn:e(zn()),rotatedIcon:e(An())}});var Tn=t(41282),Sn=t(65640);function On(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function En(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var o=n.apply(e,t);function a(n){On(o,i,r,a,c,"next",n)}function c(n){On(o,i,r,a,c,"throw",n)}a(void 0)})}}function Ln(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var Cn=function(){var n=function(){return new Promise(function(n,e){var t=indexedDB.open("LayoutAI_DB",1);t.onupgradeneeded=function(n){var e=n.target.result;e.objectStoreNames.contains("files")||e.createObjectStore("files",{keyPath:"id"})},t.onsuccess=function(){n(t.result)},t.onerror=function(){e(t.error)}})};return{saveImageToDB:function(e){return En(function(){var t,i,r,o,a;return Ln(this,function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"CopyingBase64",data:e,type:"png"})).onsuccess=function(){Sn.log("House ID saved to IndexedDB")},o.onerror=function(n){Sn.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),Sn.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}})})()},saveDwgToDB:function(e){return En(function(){var t,i,r,o,a;return Ln(this,function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"DwgBase64",data:e,type:"dwg"})).onsuccess=function(){Sn.log("House ID saved to IndexedDB")},o.onerror=function(n){Sn.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),Sn.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}})})()},saveHouseIdToDB:function(e){return En(function(){var t,i,r,o,a;return Ln(this,function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"HouseId",data:e})).onsuccess=function(){Sn.log("House ID saved to IndexedDB")},o.onerror=function(n){Sn.error("Error saving House ID to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),Sn.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}})})()},saveInfoToDB:function(e){return En(function(){var t,i,r,o,a;return Ln(this,function(c){switch(c.label){case 0:return c.trys.push([0,2,,3]),[4,n()];case 1:return t=c.sent(),i=t.transaction("files","readwrite"),r=i.objectStore("files"),(o=r.put({id:"HxInfo",data:e})).onsuccess=function(){Sn.log("House information saved to IndexedDB")},o.onerror=function(n){Sn.error("Error saving house information to IndexedDB:",n)},[3,3];case 2:return a=c.sent(),Sn.error("Error opening IndexedDB:",a),[3,3];case 3:return[2]}})})()}}},kn=t(65640);function Un(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function Qn(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var o=n.apply(e,t);function a(n){Un(o,i,r,a,c,"next",n)}function c(n){Un(o,i,r,a,c,"throw",n)}a(void 0)})}}function Yn(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var Zn=function(n){var e=n.toSelectHX,t=vn().styles,r=Cn(),o=r.saveImageToDB,a=r.saveDwgToDB,c=r.saveInfoToDB,l=[{key:"search",title:"搜索户型图",description:"从户型库中搜索户型",img:"data:image/svg+xml;base64,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",styles:"#E1F2FF"},{key:"upload",title:"上传临摹图",description:"通过照片草图进行AI生成",img:"data:image/svg+xml;base64,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",styles:"#FFF6DE"}],s=function(n){return new Promise(function(e,t){var i=new FileReader;i.readAsDataURL(n),i.onload=function(){return e(i.result)},i.onerror=function(n){return t(n)}})},M=(0,u.useCallback)(function(n){var e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=function(e){return Qn(function(){var t,i,r,a;return Yn(this,function(l){switch(l.label){case 0:return(t=e.target.files).length>0?(i=t[0],[4,s(i)]):[3,3];case 1:return r=l.sent(),[4,o(r)];case 2:l.sent(),n||(a={houseTypeName:"未命名",source:"3",from:"myScheme"},kn.log(a),c(a)),l.label=3;case 3:return[2]}})})()},e.click()},[]),d=(0,u.useCallback)(function(n){var e=document.createElement("input");e.type="file",e.accept=".dwg",e.onchange=function(e){return Qn(function(){var t,i,r;return Yn(this,function(o){switch(o.label){case 0:return(t=e.target.files).length>0?(i=t[0],[4,s(i)]):[3,3];case 1:return r=o.sent(),[4,a(r)];case 2:o.sent(),n||c({houseTypeName:"未命名",source:"2",from:"myScheme"}),o.label=3;case 3:return[2]}})})()},e.click()},[]);return(0,i.jsx)("div",{className:t.cardContainer,children:l.map(function(n){return(0,i.jsx)(Tn.A,{style:{margin:20,cursor:"pointer"},className:t.card,onClick:(r=n.key,function(){"search"===r?e():"upload"===r?M(!1):"CAD"===r&&d(!1)}),bodyStyle:{padding:"15px 20px",height:"72px",background:n.styles},children:(0,i.jsxs)("div",{className:t.content,children:[(0,i.jsx)("div",{className:"",children:(0,i.jsx)("img",{src:n.img,alt:"img"})}),(0,i.jsxs)("div",{className:t.right,children:[(0,i.jsx)("div",{className:t.title,children:n.title}),(0,i.jsx)("div",{className:t.desc,children:n.description})]})]})},n.key);var r})})},_n=t(22977);function Pn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Gn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Pn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Pn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Fn=(0,a.observer)(function(n){var e=n.toSelectHX,t=pn().styles,r=Gn((0,u.useState)("我的方案"),2),o=r[0],a=r[1];return(0,i.jsx)("div",{className:t.root,children:(0,i.jsxs)(Dn.LN,{title:"开始设计",children:[(0,i.jsx)("div",{style:{padding:"0 0 20px 0"},children:(0,i.jsx)(Zn,{toSelectHX:e})}),(0,i.jsxs)("div",{className:"displayContent",children:[(0,i.jsx)("div",{className:"tab",children:(0,i.jsx)($.A,{options:["我的方案","我的图册"],onChange:function(n){a(n)}})}),(0,i.jsxs)("div",{className:t.display,children:["我的方案"===o&&(0,i.jsx)(_n.A,{source:"startDesignPage"}),"我的图册"===o&&(0,i.jsx)(O.A,{setZIndexOfMobileAtlas:null})]})]})]})})});function Bn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Rn(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function Vn(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var o=n.apply(e,t);function a(n){Rn(o,i,r,a,c,"next",n)}function c(n){Rn(o,i,r,a,c,"throw",n)}a(void 0)})}}function Hn(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Wn(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),i.forEach(function(e){Hn(n,e,t[e])})}return n}function Xn(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,i)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function Jn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||$n(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(n){return function(n){if(Array.isArray(n))return Bn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||$n(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $n(n,e){if(n){if("string"==typeof n)return Bn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Bn(n,e):void 0}}function qn(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var ne=(0,a.observer)(function(){var n,e,t,o=(0,u.useRef)(null),a=(0,r.B)().t,c=K().styles,d=Jn((0,u.useState)(null),2),x=d[0],N=d[1],D=Jn((0,u.useState)([]),2),j=D[0],I=D[1],h=Jn((0,u.useState)(-1),2),m=h[0],y=h[1],b=Jn((0,u.useState)([{label:"居住人口",multiple:!1,tabList:[{label:"单身独居",selected:!1},{label:"二人世界",selected:!1},{label:"三口之家",selected:!1},{label:"四口之家",selected:!1},{label:"多代同堂",selected:!1}]},{label:"房屋类型",multiple:!1,tabList:[{label:"毛坯房",selected:!1},{label:"精装修",selected:!1},{label:"旧房改造",selected:!1}]},{label:"功能需求",multiple:!0,tabList:[{label:"聚会",selected:!1},{label:"品茗",selected:!1},{label:"健身",selected:!1},{label:"绿植",selected:!1},{label:"收纳",selected:!1},{label:"梳妆",selected:!1},{label:"休闲",selected:!1},{label:"西厨",selected:!1},{label:"宠物",selected:!1},{label:"办公",selected:!1},{label:"适老",selected:!1},{label:"孩童",selected:!1}]},{label:"装修预算",multiple:!1,tabList:[{label:"10万以下",selected:!1},{label:"10-20万",selected:!1},{label:"20-50万",selected:!1},{label:"50万以上",selected:!1}]}]),2),w=b[0],z=b[1],A=(0,g.P)(),v=Jn((0,u.useState)({orderBy:"sort asc",ruleType:(null===(n=A.userStore.userInfo)||void 0===n?void 0:n.isFactory)?2:1,pageSize:50,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),T=v[0],S=v[1];(0,u.useEffect)(function(){M.nb.on_M(f.U.xmlSchemeLoaded,"enterPage",function(n){if("Finish"===n.mode&&x){M.nb.DispatchEvent(M.n0.SeriesSampleSelected,{series:x,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}});var e="";w.forEach(function(n){n.tabList.forEach(function(n){n.selected&&(e+=n.label+",")})}),e=e.slice(0,-1),M.nb.instance.layout_container._funcRequire=e,N(null),A.trialStore.setHouseData(null),M.nb.off(f.U.xmlSchemeLoaded)}A.homeStore.setRoomEntites(M.nb.instance.layout_container._room_entities)})},[x,w]);var O=function(){0===m?(k.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=p.O9):1===m&&y(0)};(0,u.useEffect)(function(){"HouseId"!==p.Zx&&"CopyingBase64"!==p.Zx||y(1)},[]),(0,u.useEffect)(function(){1===m&&Vn(function(){var n,e;return qn(this,function(t){switch(t.label){case 0:return[4,(0,rn.Ic)(T)];case 1:return(null==(n=t.sent())?void 0:n.result)?(e=Array.isArray(j)&&j.length>0&&T.pageIndex>1?Kn(j).concat(Kn((null==n?void 0:n.result)||[])):(null==n?void 0:n.result)||[],I(e),1!==m||x||N(e[0])):I([]),[2]}})})()},[T,m]),(0,u.useEffect)(function(){var n,e;0===m&&(null===(e=A.trialStore)||void 0===e||null===(n=e.houseData)||void 0===n?void 0:n.id)&&(y(1),A.homeStore.setImgBase64(null))},[null===(t=A.trialStore)||void 0===t||null===(e=t.houseData)||void 0===e?void 0:e.num]);return(0,i.jsxs)("div",{className:c.enterPage,children:[-1===m&&(0,i.jsx)(Fn,{toSelectHX:function(){y(0)}}),(0,i.jsxs)("div",{className:c.hxHeader,children:[(0,i.jsxs)("div",{className:"title",children:[(0,i.jsx)("div",{className:"back",onClick:O,style:{display:(0,sn.yk)()||0!==m?"block":"none"},children:(0,i.jsx)(nn.A,{type:"icon-line_left"})}),(0,i.jsx)(q.If,{condition:0===m,children:(0,i.jsx)("span",{children:a("找户型")})}),(0,i.jsx)(q.If,{condition:1===m,children:(0,i.jsx)("span",{children:a("选需求")})})]}),(0,i.jsxs)(s.A,{type:"primary",className:"mySchemeButton",color:"orange",variant:"filled",onClick:function(){A.homeStore.setShowMySchemeList(!0),N(null)},children:[(0,i.jsx)(an.In,{iconClass:"iconwenjianjia",style:{fontSize:"12px"}}),(0,i.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的方案"})]}),(0,i.jsxs)(s.A,{type:"primary",className:"myAtlasButton",color:"purple",variant:"filled",onClick:function(){A.homeStore.setShowAtlas(!0)},children:[(0,i.jsx)(an.In,{iconClass:"icontuce1",style:{fontSize:"12px"}}),(0,i.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的图册"})]})]}),(0,i.jsxs)("div",{className:"upload_hx",onClick:function(){return Vn(function(){var n;return qn(this,function(e){switch(e.label){case 0:return[4,(0,on.L7)("image/*").catch(function(){return null})];case 1:return(n=e.sent()).content?(A.homeStore.setImgBase64(n.content),l.A.success(a("上传户型图成功")),y(1)):l.A.warning(a("上传户型图失败")),[2]}})})()},style:{display:0===m?"block":"none"},children:[(0,i.jsx)("img",{style:{marginTop:"20px",width:"50px",height:"auto"},src:"https://3vj-fe.3vjia.com/layoutai/icons/upload.svg",alt:""}),(0,i.jsx)("div",{className:"upload_title",children:"上传户型"})]}),(0,i.jsx)(tn.A,{in:0===m,timeout:300,classNames:1===m?"slide-reverse":"slide",mountOnEnter:!0,appear:!0,style:{display:0===m?"block":"none"},children:(0,i.jsx)("div",{className:c.selectHx,children:(0,i.jsx)("div",{className:c.hxRoot,children:(0,i.jsx)(en.A,{})})})}),(0,i.jsx)(tn.A,{in:1===m,timeout:300,classNames:0===m?"slide":"slide-reverse",mountOnEnter:!0,appear:!0,style:{display:1===m?"block":"none"},children:function(){var n,e;return(0,i.jsxs)("div",{className:c.selectDemand,children:[w.map(function(n,e){return(0,i.jsxs)("div",{className:"demandItem",children:[(0,i.jsx)("div",{className:"demandLabel",children:n.label},e),(0,i.jsx)("div",{className:"tabRoot",children:n.tabList.map(function(e,t){return(0,i.jsx)("div",{onClick:function(){return t=e,i=n.label,r=w.map(function(n){return n.label===i?Xn(Wn({},n),{tabList:n.tabList.map(function(e){return n.multiple?Xn(Wn({},e),{selected:e.label===t.label?!e.selected:e.selected}):Xn(Wn({},e),{selected:e.label===t.label})})}):n}),void z(r);var t,i,r},className:"demandtab"+(e.selected?" selected":""),children:e.label},t)})})]})}),(0,i.jsxs)("div",{className:c.styleTitle,children:[(0,i.jsxs)("div",{className:"demandLabel",children:[a("风格偏好"),(0,i.jsxs)("span",{style:{color:"#959598",fontSize:"12px"},children:["（",a("必选"),"）"]})]}),(0,i.jsx)($.A,{onChange:function(n){return Vn(function(){return qn(this,function(e){return S(function(e){return Xn(Wn({},e),{pageIndex:1,ruleType:"平台"===n?1:2})}),N(null),[2]})})()},defaultValue:(null===(n=A.userStore.userInfo)||void 0===n?void 0:n.isFactory)?"企业":"平台",options:ln.x.instance.hasPermission(cn.J.SERIES.SHOW_PLATFORM_SERIES)?["平台","企业"]:["企业"]})]}),(0,i.jsx)("div",{className:"".concat(c.container_listInfo),ref:o,children:(0,i.jsx)(i.Fragment,{children:null==j||null===(e=j.map)||void 0===e?void 0:e.call(j,function(n,e){return(0,i.jsx)("div",{className:c.container_list,onClick:function(){N(n)},children:(0,i.jsxs)("div",{style:{border:(null==x?void 0:x.ruleId)===n.ruleId?"2px solid #9242FB":"2px solid #fff",borderRadius:"8px",overflow:"hidden"},children:[(0,i.jsx)("img",{src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,i.jsx)("div",{className:c.container_title,title:n.seedSchemeName||n.ruleName,children:a(n.seedSchemeName)||a(n.ruleName)})]})},"series_"+e)})})})]})}()}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(q.If,{condition:0===m}),(0,i.jsx)(q.If,{condition:1===m,children:(0,i.jsxs)("div",{className:c.bottom,children:[(0,i.jsx)("div",{className:"bottomLeft",children:(0,i.jsxs)("div",{className:"rotate",onClick:function(){N(null),z(w.map(function(n){return Xn(Wn({},n),{tabList:n.tabList.map(function(n){return Xn(Wn({},n),{selected:!1})})})}))},children:[(0,i.jsx)(nn.A,{type:"icon-rotate",style:{marginRight:"5px"}}),a("重置选项")]})}),(0,i.jsxs)("div",{className:"bottomRight",children:[(0,i.jsx)(s.A,{style:{marginRight:"10px"},onClick:function(){return y(0)},color:"default",variant:"filled",children:a("上一步")}),(0,i.jsx)(s.A,{style:{background:"linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%)",color:"#fff"},onClick:function(){x?(y(0),A.homeStore.setShowEnterPage({show:!1,source:""}),A.homeStore.img_base64?M.nb.DispatchEvent(M.n0.LoadImitateImageFile,A.homeStore.img_base64):A.trialStore.houseData.id&&M.nb.DispatchEvent(M.n0.PostBuildingId,{id:A.trialStore.houseData.id,name:""})):l.A.warning(a("请选择风格偏好"))},color:"default",variant:"filled",children:a("下一步")})]})]})})]})]})});function ee(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function te(){var n=ee(["\n      background-color: #fff;\n      border-radius: 8px !important;\n      position: relative;\n      .swj-baseComponent-Containersbox-title\n      {\n        background-color: #fff !important;\n      }\n    "]);return te=function(){return n},n}function ie(){var n=ee(["\n      .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell\n      {\n        background-color: #fff !important;\n      }\n    "]);return ie=function(){return n},n}function re(){var n=ee(['\n      color: #282828;\n      font-family: "PingFang SC";\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n    ']);return re=function(){return n},n}function oe(){var n=ee(["\n      display: flex;\n      flex-direction: row;\n      gap: 10px;\n      margin: 16px 0px;\n      color: #282828;\n      .tabItem\n      {\n        display: flex;\n        width: 64px;\n        height: 28px;\n        padding: 0px 0px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n      }\n      .selected\n      {\n        background: #F2F3F4;\n        font-weight: 600;\n      }\n    "]);return oe=function(){return n},n}function ae(){var n=ee(["\n      .ant-table-thead .ant-table-selection-column .ant-checkbox-wrapper\n      {\n         display: none;\n      }\n      .ant-table-thead .ant-table-cell\n      {\n        background: #F2F3F4;\n      }\n      .ant-table-container\n      {\n        border: 2px solid #F2F3F4;\n      }\n    "]);return ae=function(){return n},n}function ce(){var n=ee(["\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      position: absolute;\n      bottom: 0;\n      width: 100%;\n      padding: 16px;\n      background-color: #fff;\n      width: 95%;\n      font-size: 16px;\n    "]);return ce=function(){return n},n}function le(){var n=ee(["\n      font-size: 16px;\n      font-weight: 600;\n    "]);return le=function(){return n},n}var se=(0,U.rU)(function(n){var e=n.css;return{panelContainer:e(te()),content:e(ie()),title:e(re()),tab:e(oe()),table:e(ae()),bottom:e(ce()),bottomRight:e(le())}}),ue=t(29686),Me=t(63616);function de(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function ge(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function fe(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),i.forEach(function(e){ge(n,e,t[e])})}return n}function xe(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,i)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function Ne(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return de(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return de(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var pe=(0,a.observer)(function(){var n,e,t,o,a=(0,g.P)(),c=((0,r.B)().t,se().styles),l=Ne((0,u.useState)("checkbox"),2),s=l[0],d=(l[1],Ne((0,u.useState)([]),2)),f=d[0],x=d[1],N=Ne((0,u.useState)([]),2),p=N[0],D=N[1],j=Ne((0,u.useState)("延米"),2),I=j[0],h=j[1],m=Ne((0,u.useState)(0),2),y=m[0],b=m[1],w=Ne((0,u.useState)([{name:"定制柜",selected:!1},{name:"橱柜",selected:!1},{name:"卫阳",selected:!1}]),2),z=w[0],A=w[1],v=[{title:"类型",dataIndex:"name"},{title:"尺寸",dataIndex:"size"},{title:(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},onClick:function(){h("延米"===I?"投影面积":"延米")},children:[(0,i.jsx)(q.If,{condition:null===(n=z.find(function(n){return"定制柜"===n.name}))||void 0===n?void 0:n.selected,children:(0,i.jsx)(q.al,{children:"投影面积"})}),(0,i.jsx)(q.If,{condition:null===(e=z.find(function(n){return"橱柜"===n.name}))||void 0===e?void 0:e.selected,children:(0,i.jsx)(q.al,{children:"延米"})}),(0,i.jsx)(q.If,{condition:null===(t=z.find(function(n){return"卫阳"===n.name}))||void 0===t?void 0:t.selected,children:(0,i.jsxs)(q.al,{children:[(0,i.jsx)(nn.A,{type:"icon-change_logo"}),I]})})]}),dataIndex:"meter",render:function(n,e){return(0,i.jsx)("span",{children:"延米"===I?e.meter:e.area})}},{title:"空间",dataIndex:"space",hidden:null===(o=z.find(function(n){return"橱柜"===n.name}))||void 0===o?void 0:o.selected}],T={onChange:function(n,e){x(n)},getCheckboxProps:function(n){return{disabled:"Disabled User"===n.name,name:n.name}}};return(0,u.useEffect)(function(){var n,e=z.find(function(n){return n.selected}),t=M.nb.instance.layout_container;if(D([]),0!=(null==t||null===(n=t._room_entities)||void 0===n?void 0:n.length)){if("橱柜"===(null==e?void 0:e.name)){var i,r;h("延米");var o=null===(i=t._room_entities.find(function(n){return"厨房"===n.name}))||void 0===i?void 0:i._room;if(o&&(null===(r=o._furniture_list)||void 0===r?void 0:r.length)>0){var a=[];o._furniture_list.forEach(function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;a.push({key:n.uuid,name:n.category,size:"".concat(e._w,"*").concat(e._h,"*").concat(e.rect_center_3d.z||n.height),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:o.name,area:"".concat((e._w*(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}}),D(a)}}if("卫阳"===(null==e?void 0:e.name)){var c=t._room_entities.filter(function(n){if(n.name.includes("卫生间")||n.name.includes("阳台"))return n});if(c&&c.length>0){var l=[];c.forEach(function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach(function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;l.push({key:n.uuid,name:n.category,size:"".concat(Math.round(e._w),"*").concat(Math.round(e._h),"*").concat(Math.round(e.rect_center_3d.z||n.height)),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((e._w*Math.round(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}})}),D(l)}}if("定制柜"===(null==e?void 0:e.name)){h("投影面积");var s=t._room_entities.filter(function(n){if(!n.name.includes("卫生间")&&!n.name.includes("阳台")&&!n.name.includes("厨房"))return n});if(s&&s.length>0){var u=[];s.forEach(function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach(function(n){n.category.endsWith("柜")&&u.push({key:n.uuid,name:n.category,size:"".concat(Math.round(n.rect._w),"*").concat(Math.round(n.rect._h),"*").concat(Math.round(n.rect.rect_center_3d.z||n.height)),meter:"".concat((n.rect._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((n.rect._w*Math.round(n.rect.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})})}),D(u)}}x([]),b(y+1)}},[z]),(0,u.useEffect)(function(){a.homeStore.showCabinetCompute&&A(z.map(function(n,e){return xe(fe({},n),{selected:0===e})}))},[a.homeStore.showCabinetCompute]),(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(q.If,{condition:a.homeStore.showCabinetCompute,children:(0,i.jsx)(an._w,{center:!0,height:446,width:600,showHeader:!0,showCloseInContainerbox:!1,title:"基本算量",className:c.panelContainer,onClose:function(){a.homeStore.setShowCabinetCompute(!1)},draggable:!0,children:(0,i.jsxs)("div",{style:{padding:"0 20px 20px 20px"},className:c.content,children:[(0,i.jsx)("div",{className:c.tab,children:z.map(function(n,e){return(0,i.jsx)("div",{onClick:function(){A(z.map(function(n,t){return xe(fe({},n),{selected:t===e})}))},className:"tabItem"+(n.selected?" selected":""),children:n.name},e)})}),(0,i.jsx)(ue.A,{rowSelection:fe({type:s,columnTitle:"",selectedRowKeys:f},T),columns:v,dataSource:p,pagination:!1,scroll:{y:240},className:c.table},y),(0,i.jsxs)("div",{className:c.bottom,children:[(0,i.jsx)("div",{children:(0,i.jsx)(Me.A,{checked:f.length===p.length,indeterminate:f.length>0&&f.length<p.length,onChange:function(n){n.target.checked?x(p.map(function(n){return n.key})):x([])},children:"全选"})}),(0,i.jsx)("div",{className:c.bottomRight,children:(0,i.jsxs)(q.If,{condition:"延米"===I,children:[(0,i.jsxs)(q.al,{children:["共 ",p.filter(function(n){return f.includes(n.key)}).reduce(function(n,e){return n+parseFloat(e.meter)},0).toFixed(2),"m"]}),(0,i.jsxs)(q._I,{children:["共 ",p.filter(function(n){return f.includes(n.key)}).reduce(function(n,e){return n+parseFloat(e.area)},0).toFixed(2),"m²"]})]})})]})]})})})})}),De=t(67869);function je(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ie(){var n=je(["\n      position: fixed;\n      background: #fff;\n      z-index: 10;\n      transition: all 0.3s ease-in-out;\n      .closeBtn {\n        display: none;\n        position: absolute;\n        right: 6px;\n        top: 6px;\n        font-size: 20px;\n        width: 60px;\n        height: 24px;\n        text-align: right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        left: 12px;\n        top: 52px;\n        bottom: 12px;\n        right: auto;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width: 224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateX(calc(-100% - 12px));\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        left: 0;\n        bottom: 0px;\n        right: 0;\n        width: auto;\n        height: 340px;\n        max-width: auto;\n        max-height: 340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateY(100%);\n        }\n        .closeBtn {\n          display: block;\n        }\n      }\n    "]);return Ie=function(){return n},n}function he(){var n=je(["\n      height: 60px;\n      width: 100%;\n      @media screen and (orientation: landscape) {\n        height: 55px;\n        width: 100%;\n        color: #000;\n        font-weight: 700;\n        padding: 12px 13px 0;\n      }\n      @media screen and (orientation: portrait) {\n        width: 30%;\n        margin: 10px;\n        height: auto;\n      }\n    "]);return he=function(){return n},n}function me(){var n=je(["\n      display: none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor: pointer;\n      transition: all 0.3s ease-in-out;\n\n      @media screen and (orientation: landscape) {\n        display: block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n      }\n      &.panel_hide {\n        left: 0px;\n        display: block;\n      }\n    "]);return me=function(){return n},n}function ye(){var n=je(["\n      padding: 0 12px;\n      height: calc(100% - 60px);\n      overflow: hidden;\n    "]);return ye=function(){return n},n}function be(){var n=je(["\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 12px;\n      padding: 4px 0;\n      overflow-y: auto;\n      scrollbar-width: none;  /* Firefox */\n      -ms-overflow-style: none;  /* IE and Edge */\n      &::-webkit-scrollbar {\n        display: none;  /* Chrome, Safari and Opera */\n      }\n    "]);return be=function(){return n},n}function we(){var n=je(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 6px 4px;\n      cursor: pointer;\n      transition: all 0.3s;\n      user-select: none;\n    "]);return we=function(){return n},n}function ze(){var n=je(["\n      font-size: 50px;\n      height: 50px;\n      width: 70px;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #666;\n      background: #F5F5F5;\n      \n      img {\n        max-width: 50px;\n        max-height: 40px;\n        object-fit: contain;\n      }\n    "]);return ze=function(){return n},n}function Ae(){var n=je(["\n      font-size: 14px;\n      color: #333;\n      height: 30px;\n      line-height: 30px;\n      text-align: center;\n    "]);return Ae=function(){return n},n}var ve=(0,U.rU)(function(n){var e=n.css;return{leftPanelRoot:e(Ie()),tabBar:e(he()),collapseBtn:e(me()),popupContainer:e(ye()),itemGrid:e(be()),gridItem:e(we()),itemIcon:e(ze()),itemLabel:e(Ae())}}),Te=t(30268),Se=t(48402);function Oe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Ee(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Oe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Oe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Le=(0,a.observer)(function(n){var e=n.isCollapse,t=n.onCollapseChange,o=(0,r.B)().t,a=ve().styles,c=Ee((0,u.useState)("doors"),2),l=c[0],s=c[1],d=Ce(),g=d.doorWindowItems,f=d.structureItems,x="https://3vj-fe.3vjia.com/layoutai/figures_imgs/";(0,u.useEffect)(function(){var n=M.nb.instance;n&&n.addHouseLeftPanelEvent()},[]);var N=[{value:"doors",label:o("门窗")},{value:"structure",label:o("结构件")}],p=function(n){M.nb.DispatchEvent(M.n0.SelectedFurniture,n)},D=!e;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{id:"pad_house_left_panel",className:a.leftPanelRoot+" leftPanelRoot "+(D?"":"panel_hide"),children:[D&&(0,i.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return t(!0)}}),D&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:a.tabBar,children:(0,i.jsx)("div",{className:"tab-content",children:(0,i.jsx)($.A,{value:l,onChange:function(n){return s(n.toString())},block:!0,options:N})})}),(0,i.jsx)("div",{className:a.popupContainer,children:(0,i.jsx)("div",{className:a.itemGrid,children:"doors"===l?g.map(function(n,e){return(0,i.jsxs)("div",{className:a.gridItem,children:[(0,i.jsx)("div",{className:a.itemIcon,onPointerDown:function(e){p(n.label)},children:(0,i.jsx)(Te.A,{src:"".concat(x).concat(n.image),preview:!1,title:n.label,alt:n.label})}),(0,i.jsx)("div",{className:a.itemLabel,children:o(n.label)})]},e)}):f.map(function(n,e){return(0,i.jsxs)("div",{className:a.gridItem,children:[(0,i.jsx)("div",{className:a.itemIcon,onPointerDown:function(e){p(n.label)},onPointerUp:function(e){M.nb.DispatchEvent(M.n0.mobileAddFurniture,n.label)},children:(0,i.jsx)(Te.A,{src:"".concat(x).concat(n.image),preview:!1,title:n.label,alt:n.label})}),(0,i.jsx)("div",{className:a.itemLabel,children:o(n.label)})]},e)})})})]})]}),(0,i.jsx)("div",{className:a.collapseBtn+(D?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){return t(!e)}})]})}),Ce=function(){return(0,u.useMemo)(function(){var n=Se.V.find(function(n){return"户型"===n.label});if(!n||!n.child)return{doorWindowItems:[],structureItems:[]};var e=n.child.find(function(n){return"结构件"===n.label});if(!e||!e.figureList)return{doorWindowItems:[],structureItems:[]};var t=[],i=[];return e.figureList.forEach(function(n){var e,r={icon:n.icon||(null===(e=n.image)||void 0===e?void 0:e.split(".")[0])||"",label:n.title,type:ke(n.label)?"Door":"StructureEntity",realType:n.label,image:n.image};ke(n.label)?t.push(r):i.push(r)}),{doorWindowItems:t,structureItems:i}},[])},ke=function(n){return["Door","Window","door","window","Railing"].some(function(e){return n.includes(e)})};function Ue(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Qe(){var n=Ue(["\n      position: fixed;\n      background: #fff;\n      z-index: 10;\n      transition: all 0.3s ease-in-out;\n      .closeBtn {\n        display: none;\n        position: absolute;\n        right: 6px;\n        top: 6px;\n        font-size: 20px;\n        width: 60px;\n        height: 24px;\n        text-align: right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        left: 12px;\n        top: 52px;\n        bottom: 12px;\n        right: auto;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width: 224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateX(calc(-100% - 12px));\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        left: 0;\n        bottom: 0px;\n        right: 0;\n        width: auto;\n        height: 340px;\n        max-width: auto;\n        max-height: 340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          transform: translateY(100%);\n        }\n        .closeBtn {\n          display: block;\n        }\n      }\n    "]);return Qe=function(){return n},n}function Ye(){var n=Ue(["\n      display: none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor: pointer;\n      transition: all 0.3s ease-in-out;\n\n      @media screen and (orientation: landscape) {\n        display: block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n      }\n      &.panel_hide {\n        left: 0px;\n        display: block;\n      }\n    "]);return Ye=function(){return n},n}function Ze(){var n=Ue(["\n      padding: 12px;\n      height: calc(100% - 40px);\n      overflow-y: auto;\n      &::-webkit-scrollbar {\n        width: 4px;\n      }\n      &::-webkit-scrollbar-track {\n        background: #f1f1f1;\n      }\n      &::-webkit-scrollbar-thumb {\n        background: #888;\n        border-radius: 2px;\n      }\n    "]);return Ze=function(){return n},n}function _e(){var n=Ue(["\n      font-size: 16px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 16px;\n      padding-bottom: 8px;\n      border-bottom: 1px solid #e8e8e8;\n    "]);return _e=function(){return n},n}var Pe=(0,U.rU)(function(n){var e=n.css;return{leftPanelRoot:e(Qe()),collapseBtn:e(Ye()),propertyContainer:e(Ze()),propertyTitle:e(_e())}}),Ge=t(26769),Fe=t(32184),Be=(0,a.observer)(function(n){var e=n.isCollapse,t=n.onCollapseChange,r=(0,g.P)(),o=Pe().styles,a=!e,c=r.homeStore.selectEntity;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:o.leftPanelRoot+" leftPanelRoot "+(a?"":"panel_hide"),children:a&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return t(!0)}}),(0,i.jsx)("div",{className:o.propertyContainer,children:c&&function(n){if(!n)return!1;var e=n.type;return e===Fe.Fz.Door||e===Fe.Fz.Window||e===Fe.Fz.StructureEntity||e===Fe.Fz.Wall||e===Fe.Fz.RoomArea}(c)&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:o.propertyTitle,children:function(n){if(!n)return"";var e=n.type;return e===Fe.Fz.Door?"门信息":e===Fe.Fz.Window?"窗信息":e===Fe.Fz.StructureEntity?"结构件信息":e===Fe.Fz.Wall?"墙体信息":e===Fe.Fz.RoomArea?"空间信息":""}(c)}),(0,i.jsx)(Ge.A,{Entity:c})]})})]})}),(0,i.jsx)("div",{className:o.collapseBtn+(a?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){return t(!e)}})]})});function Re(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ve(){var n=Re(["\n        "]);return Ve=function(){return n},n}function He(){var n=Re(["  \n        position:fixed;\n        right:0;\n        z-index:9;\n        top:0px;\n        transition: all 0.2s ease;\n    "]);return He=function(){return n},n}function We(){var n=Re(["\n        position:fixed;\n        top:0;\n        z-index:9;\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        height:34px;\n        background:#fff;\n        width: 140px;\n        left: 50%;\n        transform: translateX(-50%);\n        border-radius: 6px;\n        border: 2px solid #E5E5E5;\n        font-size: 14px;\n    "]);return We=function(){return n},n}function Xe(){var n=Re(["\n        position:fixed;\n        bottom:13px;\n        z-index:9;\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        left: 50%;\n        transform: translate(-50%, 0);\n        transition: all 0.5s ease;\n        .btn {\n            border-radius: 50px;\n            background: #FFFFFF;\n            box-shadow: 0px 6px 20px 0px #00000014;\n            width : 140px;\n            border: none;\n            height : 48px;\n            color: #282828;\n            font-size: 14px;\n            line-height: 48px;\n            letter-spacing: 0px;\n            text-align: center;\n            margin-left:12px;\n            margin-right:12px;\n        }\n\n        @media screen and (orientation: landscape){\n            display:flex;\n            justify-content:center;\n            align-items:center;\n            left: 50%;\n            transform: translate(-50%, 0);\n        }\n        "]);return Xe=function(){return n},n}var Je=(0,U.rU)(function(n){var e=n.css;return{padHousePanelRoot:e(Ve()),sideToolbarContainer:e(He()),topBar:e(We()),bottomButtons:e(Xe())}}),Ke=t(23184),$e=t(6934),qe=t(73751);function nt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function et(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return nt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return nt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var tt=(0,a.observer)(function(){var n="PadHousePanel",e=(0,g.P)(),t=et((0,u.useState)(!1),2),r=t[0],o=t[1],a=Je().styles,c=et((0,u.useState)("menu"),2),l=c[0],s=c[1];return(0,u.useEffect)(function(){return M.nb.on_M(qe.$.showPopup,n,function(n){s("attribute"===n?"attribute":"menu")}),M.nb.on_M(f.U.SelectingTarget,n,function(){s("menu")}),function(){M.nb.off_M(qe.$.showPopup,n),M.nb.off_M(f.U.SelectingTarget,n)}},[]),(0,i.jsxs)("div",{className:a.padHousePanelRoot,children:[(0,i.jsx)(q.If,{condition:"menu"===l,children:(0,i.jsx)(Le,{isCollapse:r,onCollapseChange:o})}),(0,i.jsx)(q.If,{condition:"attribute"===l,children:(0,i.jsx)(Be,{isCollapse:r,onCollapseChange:o})}),(0,i.jsxs)("div",{className:a.sideToolbarContainer,children:[(0,i.jsx)(Ke.A,{}),(0,i.jsx)($e.A,{})]}),(0,i.jsx)("div",{className:a.topBar,children:"户型编辑模式"}),(0,i.jsx)("div",{className:a.bottomButtons,children:(0,i.jsx)("div",{className:"btn",onClick:function(){M.nb.instance._current_handler_mode=d.f.AiCadMode,M.nb.RunCommand(d.f.AiCadMode),e.homeStore.setDesignMode(d.f.AiCadMode)},children:"进入布局"})})]})}),it=t(65640);function rt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function ot(n,e,t,i,r,o,a){try{var c=n[o](a),l=c.value}catch(n){return void t(n)}c.done?e(l):Promise.resolve(l).then(i,r)}function at(n){return function(){var e=this,t=arguments;return new Promise(function(i,r){var o=n.apply(e,t);function a(n){ot(o,i,r,a,c,"next",n)}function c(n){ot(o,i,r,a,c,"throw",n)}a(void 0)})}}function ct(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,c=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){c=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(c)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return rt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return rt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lt(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&c[0]?i.return:c[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,c[1])).done)return r;switch(i=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,i=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=e.call(n,o)}catch(n){c=[6,n],i=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}var st=(0,a.observer)(function(){var n=(0,r.B)().t,e=(0,o.A)().styles,t=ct((0,u.useState)(""),2),a=t[0],U=t[1],Q=ct((0,u.useState)(-2),2),Y=Q[0],Z=Q[1],_=(0,g.P)(),P=c.A.confirm,G="SaveSchemeProgress",F=ct(l.A.useMessage(),2),B=F[0],R=F[1],V=ct((0,u.useState)(!1),2),H=V[0],W=(V[1],ct((0,u.useState)(!1),2)),X=W[0],J=W[1],K=ct((0,u.useState)(null),2),$=K[0],nn=K[1],en=ct((0,u.useState)(""),2),tn=en[0],rn=en[1],on=ct((0,u.useState)(-2),2),cn=(on[0],on[1]),ln=ct((0,u.useState)(0),2),sn=ln[0],un=(ln[1],(0,u.useRef)()),Mn=ct((0,u.useState)(null),2),dn=Mn[0],gn=Mn[1];M.nb.UseApp(d.e.AppName),M.nb.instance&&(M.nb.t=n);var fn=function(){M.nb.instance&&(M.nb.instance.bindCanvas(document.getElementById("cad_canvas")),M.nb.instance.update()),xn()},xn=function(){M.nb.instance&&(M.nb.instance._is_landscape=window.innerWidth>window.innerHeight);_.homeStore.IsLandscape;_.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))};return(0,u.useEffect)(function(){if(M.nb.instance&&(M.nb.instance._is_website_debug=p.iG),window.addEventListener("resize",fn),fn(),M.nb.instance){var e;if(M.nb.instance.initialized||(!(0,p.fZ)()||"HouseId"!==p.Zx&&"CopyingBase64"!==p.Zx||M.nb.emit(f.U.Initializing,{initializing:!0}),M.nb.instance.init(),M.nb.RunCommand(d.f.AiCadMode),M.nb.instance.prepare().then(function(){at(function(){var n,e,t;return lt(this,function(i){switch(i.label){case 0:return p.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:p.uN},[4,y.D.getLayoutSchemeList(n)]):[2];case 1:return e=i.sent(),t=e.layoutSchemeDataList,e.total,t&&(M.nb.DispatchEvent(M.n0.OpenMyLayoutSchemeData,t[0]),M.nb.emit(f.U.OpenHouseSearching,!1)),[2]}})})(),at(function(){var n,e,t,i;return lt(this,function(r){switch(r.label){case 0:return"HouseId"!==p.Zx?[3,1]:(at(function(){var n,e,t;return lt(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,b.ZN)("HouseId")];case 1:return n=i.sent(),it.log(n),e=n.data,(0,p.fZ)()?_.trialStore.houseData.id=e:M.nb.DispatchEvent(M.n0.PostBuildingId,{id:e,name:""}),[3,3];case 2:return t=i.sent(),it.error("Error loading file:",t),[3,3];case 3:return[2]}})})(),[3,9]);case 1:return"DwgBase64"!==p.Zx?[3,2]:(at(function(){return lt(this,function(n){try{M.nb.RunCommand(M._I.OpenDwgFilefromWork)}catch(n){it.error("Error loading file:",n)}return[2]})})(),[3,9]);case 2:return"CopyingBase64"!==p.Zx?[3,3]:(at(function(){var n,e,t;return lt(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,(0,b.ZN)("CopyingBase64")];case 1:return n=i.sent(),e=n.data,_.homeStore.setImgBase64(e),(0,p.fZ)()?it.log("fileData",e):M.nb.DispatchEvent(M.n0.LoadImitateImageFile,e),[3,3];case 2:return t=i.sent(),it.error("Error loading file:",t),[3,3];case 3:return[2]}})})(),[3,9]);case 3:return"hxcreate"!==p.Zx?[3,6]:p.fW?[4,(0,w.ON)({id:p.fW})]:[3,5];case 4:(n=r.sent()).result.contentUrl=n.result.dataUrl,M.nb.DispatchEvent(M.n0.OpenMyLayoutSchemeData,n.result),M.nb.DispatchEvent(M.n0.autoSave,null),r.label=5;case 5:return[3,9];case 6:return"hxedit"!==p.Zx?[3,9]:p.vu?[4,(0,w.ON)({id:p.vu})]:[3,8];case 7:(e=r.sent()).success&&e.result&&e.result.dataUrl&&(e.result.contentUrl=e.result.dataUrl,M.nb.DispatchEvent(M.n0.OpenMyLayoutSchemeData,e.result)),r.label=8;case 8:M.nb.instance&&"SingleRoom"==(null===(i=M.nb.instance)||void 0===i||null===(t=i.layout_container)||void 0===t?void 0:t._drawing_layer_mode)&&M.nb.DispatchEvent(M.n0.leaveSingleRoomLayout,{}),M.nb.instance._current_handler_mode=d.f.HouseDesignMode,M.nb.RunCommand(d.f.HouseDesignMode),_.homeStore.setDesignMode(d.f.HouseDesignMode),r.label=9;case 9:return[2]}})})(),M.nb.emit(f.U.Initializing,{initializing:!1});var n=M.nb.instance.scene3D;n&&n.stopRender()}),M.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(e=window)||void 0===e?void 0:e.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var i="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(i)),M.nb.instance._debug_mode=i)}}M.nb.instance.update()}M.nb.on_M("showLight3DViewer","PadMobile",function(n){n?Y<0&&(Z(2),_.homeStore.setZIndexOf3DViewer(2),M.nb.emit(L.r.UpdateScene3D,!0)):Z(-1)}),M.nb.on(f.U.ShowDreamerPopup,function(n){_.homeStore.setShowDreamerPopup(n)}),M.nb.on(f.U.LayoutSchemeOpened,function(n){U(n.name),M.nb.emit(N.$T,N.Kw.Default)}),M.nb.on(f.U.ClearLayout,function(){P({title:n("清空布局"),content:n("确定清空单空间布局？"),okText:n("确定"),cancelText:n("取消"),onOk:function(){M.nb.DispatchEvent(M.n0.ClearLayout,this)},onCancel:function(){}})}),M.nb.on(f.U.OpenMySchemeList,function(){_.homeStore.setShowMySchemeList(!0)}),M.nb.on_M(f.U.RoomList,"room_list",function(n){_.homeStore.setRoomInfos(n)}),M.nb.on(f.U.Room2SeriesSampleRoom,function(n){_.homeStore.setRoom2SeriesSampleArray(n)}),M.nb.on_M(f.U.SelectingTarget,"LeftPanelValue",function(n,e,t){J(!1)}),M.nb.on(f.U.showCustomKeyboard,function(n){(null==n?void 0:n.visible)||!1?setTimeout(function(){J(!0)},50):setTimeout(function(){J(!1)},10),n.input&&(rn(n.input.value),nn(n.input),n.onValueChange&&gn(function(){return n.onValueChange}))}),M.nb.on_M(f.U.updateAllMaterialScene3D,"padMobile",function(n){return at(function(){var e,t,i,r,o,a;return lt(this,function(c){switch(c.label){case 0:return e=M.nb.instance.layout_container,t=M.nb.instance.scene3D,n&&"3D_FirstPerson"===_.homeStore.viewMode?(M.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!0,title:"更新视角中..."}),i=[],e._room_entities.forEach(function(n){n._view_cameras.forEach(function(n){i.push(n)})}),r=i.map(function(n){return at(function(){return lt(this,function(i){switch(i.label){case 0:return n._perspective_img.src?[3,2]:(t.active_controls.bindViewEntity(n),t.update(),[4,n.updatePerspectiveViewImg(e.painter)]);case 1:i.sent(),i.label=2;case 2:return[2]}})})()}),[4,Promise.allSettled(r)]):[3,2];case 1:c.sent(),(o=e._room_entities.reduce(function(n,e){return n?e._area>n._area?e:n:e},null))?(t.active_controls.bindViewEntity(o._view_cameras[0]),_.homeStore.setCurrentViewCameras(o._view_cameras)):t.setCenter((null==o||null===(a=o._main_rect)||void 0===a?void 0:a.rect_center)||new De.Pq0(0,0,0)),M.nb.emit(f.U.ApplySeriesSample,{seriesOpening:!1,title:""}),c.label=2;case 2:return[2]}})})()}),M.nb.on(f.U.SaveProgress,function(e){"success"===e.progress?(B.open({key:G,type:"success",content:n("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),"autoExit"===_.homeStore.isAutoExit&&(k.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=p.O9),_.homeStore.setIsAutoExit("")):"fail"===e.progress?B.open({key:G,type:"error",content:n("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===e.progress&&B.open({key:G,type:"loading",content:n("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})}),M.nb.on_M(f.U.xmlSchemeLoaded,"padMobile",function(n){C.f.updateAliasName(),_.homeStore.setRoomEntites(M.nb.instance.layout_container._room_entities)})},[_.homeStore.isAutoExit]),(0,u.useEffect)(function(){4===_.homeStore.zIndexOf3DViewer?Z(2):Z(_.homeStore.zIndexOf3DViewer)},[_.homeStore.zIndexOf3DViewer]),(0,i.jsxs)("div",{className:e.root,children:[_.homeStore.designMode===d.f.HouseDesignMode?(0,i.jsx)(tt,{}):(0,i.jsx)(E.A,{updateKey:sn}),(0,i.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,i.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:Y},children:(0,i.jsx)(x.A,{defaultViewMode:4})}),(0,i.jsxs)("div",{id:"body_container",className:e.canvas_pannel+" left_panel_layout",children:[(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){_.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){_.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t);_.homeStore.setInitialDistance(i/_.homeStore.scale)}},onTouchMove:function(n){if(n.stopPropagation(),2!=n.touches[n.touches.length-1].identifier&&2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t)/_.homeStore.initialDistance;i>5?i=5:i<.001&&(i=.001),_.homeStore.setScale(i),M.nb.DispatchEvent(M.n0.scale,i)}},onTouchEnd:function(n){n.touches.length>0&&M.nb.DispatchEvent(M.n0.updateLast_pos,n),_.homeStore.setInitialDistance(null)}}),_.homeStore.designMode===d.f.MeasurScaleMode&&(0,i.jsxs)("div",{className:"canvas_btns",style:{zIndex:999999,marginBottom:"10vh",gap:"20px"},children:[(0,i.jsx)(s.A,{className:"btn",type:"primary",onClick:function(){M.nb.instance&&(M.nb.RunCommand(d.f.AiCadMode),_.homeStore.setDesignMode(d.f.AiCadMode))},children:n("取消")}),(0,i.jsx)(s.A,{className:"btn",type:"primary",onClick:function(){M.nb.instance&&(M.nb.DispatchEvent(M.n0.ConfirmScale,{img_base64:_.homeStore.img_base64}),_.homeStore.setDesignMode(d.f.AiCadMode))},children:n("确定")})]})]})]}),(0,i.jsx)(q.If,{condition:_.homeStore.showEnterPage.show,children:(0,i.jsx)(ne,{})}),(0,i.jsx)(pe,{}),(0,i.jsx)(j.A,{}),(0,i.jsx)(D.ti,{}),(0,i.jsx)(v.A,{}),_.homeStore.showDreamerPopup&&(0,i.jsx)(m.A,{}),"3D_FirstPerson"!==_.homeStore.viewMode&&"3D"!==_.homeStore.viewMode&&_.homeStore.isSingleRoom&&_.homeStore.designMode!=d.f.HouseDesignMode&&(0,i.jsx)("div",{className:e.RoomAreaBtns,children:(0,i.jsx)(A.A,{mode:1})}),_.homeStore.showSaveLayoutSchemeDialog.show&&(0,i.jsx)("div",{className:e.overlay,children:(0,i.jsx)(h.A,{schemeName:a||"",closeCb:function(){_.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:H})}),(0,i.jsx)(I.A,{schemeNameCb:function(n){U(n)}}),(0,i.jsx)(z.A,{onKeyPress:function(n){$&&($.value=$.value+n,rn($.value))},onDelete:function(){$&&($.value=$.value.slice(0,-1),rn($.value))},onConfirm:function(){if($){var n=parseFloat(tn);if(!isNaN(n)){if(dn)dn(n);else{$.value=tn;var e=new Event("change",{bubbles:!0});$.dispatchEvent(e)}J(!1)}}},onClose:function(){J(!1),rn("")},inputValue:tn,isVisible:X}),_.homeStore.showDreamerPopup&&(0,i.jsx)(m.A,{}),(0,i.jsx)(T.A,{ref:un}),(0,i.jsx)(S.A,{}),_.homeStore.showAtlas&&(0,i.jsx)("div",{className:e.mobile_atlas_container,style:{zIndex:999},children:(0,i.jsx)(O.A,{setZIndexOfMobileAtlas:cn})}),_.homeStore.showStartPage.show&&(0,i.jsx)("div",{className:e.pad_startpage_container,style:{zIndex:999},children:(0,i.jsx)(Fn,{})}),(0,i.jsx)(an.cq,{channelCode:"Helptips-006"}),R]})})}}]);