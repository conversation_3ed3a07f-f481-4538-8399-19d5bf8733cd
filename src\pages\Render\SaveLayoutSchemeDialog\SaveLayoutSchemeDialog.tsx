import React, { useEffect } from 'react';
import styles from './style/index.module.less';
import { Button, Form, FormProps, Input } from '@svg/antd';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { useTranslation } from 'react-i18next';
import { useStore } from '@/models';
import { observer } from 'mobx-react-lite';
import Icon from "@/components/Icon/icon";

interface SaveLayoutSchemeDialogProps {
    exitCb?:()=>void;
    closeCb: () => void;
    schemeName?: string;
    isSaveAs?: boolean;
    isSaveAndExit?: boolean;
}

type FieldType = {
    username?: string;
    telephone?: string;
    schemename?: string;
    address?: string;
};

const SaveLayoutSchemeDialog: React.FC<SaveLayoutSchemeDialogProps> = ({closeCb, schemeName, isSaveAs ,isSaveAndExit = false}) => {
    const { t } = useTranslation()
    const store = useStore();
    const {
        showSaveLayoutSchemeDialog
    } = store.homeStore;
    const {houseData} = store.trialStore;
    useEffect(()=> {
        let name = '未命名';
        if(houseData.buildingName && houseData.area)
        {
            name = `${houseData.buildingName} ${houseData.area}m²`;
        }
        form.setFieldValue("schemename", name);
    }, []);

    const onFinish: FormProps<FieldType>['onFinish'] = (values) => {
        LayoutAI_App.DispatchEvent(LayoutAI_Events.SaveLayoutSchemeAs, values);
        if(showSaveLayoutSchemeDialog.source == 'nextBtn') {
            // 保存方案并且进入下一步
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'saveLayoutSchemeAndNext',
                data: {
                    next: true
                }
            }, '*');
        }
        if(showSaveLayoutSchemeDialog.source == 'exitBtn') {
            // 保存方案并且退出
            console.log("保存方案并且退出");
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'closeIframe',
                data: {
                    canClose: true
                }
            }, '*');
        }
        if(showSaveLayoutSchemeDialog.source == 'aiGeneration') {
            // 保存方案并且退出
            console.log("去生图并退出");
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'aiGeneration',
                data: {
                  aiGeneration: true,
                  img: '',
                }
              }, '*');
        }
        
        // 关闭保存页面
        closeCb();
    };

    const [form] = Form.useForm();

    // 统一退出功能
    const exitCb = () => {
        if(showSaveLayoutSchemeDialog.source == 'exitBtn') {
            console.log('直接退出');
            // 向父页面发送消息，请求关闭iframe
            window.parent.postMessage({
                origin: 'layoutai.api',
                type: 'closeIframe',
                data: {
                    canClose: true
                }
            }, '*');
        }
        closeCb(); // 关闭当前保存对话框的页面
    }


    return (
      <div className={styles.dialog_root}>
        <div className={styles.dialog_header}>
            <span className={styles.dialog_tile}>{isSaveAs? t("方案另存为") : t("保存方案")}</span>
            <div onClick={closeCb} className={styles.dialog_close}>
                <Icon style={{fontSize: '20px'}} iconClass="icon-close1" />
            </div>
        </div>
        <div className={styles.dialog_content}>
            <Form
                form={form}
                name="basic"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}
                style={{ maxWidth: 500 }}
                initialValues={{ remember: true }}
                onFinish={onFinish}
                autoComplete="on"
                className={styles.dialog_form}
            >
                <Form.Item<FieldType>
                    label={t("方案名称")}
                    name="schemename"
                    wrapperCol={{ span: 24 }}
                    rules={[{ required: true, message: t('请输入方案名称')}]}
                    style={{marginBottom: '8px'}}
                >
                    <Input placeholder={t("请输入")} />
                </Form.Item>

                <Form.Item<FieldType>
                    label={t("姓名")}
                    name="username"
                    wrapperCol={{ span: 8 }}
                    style={{marginBottom: '8px'}}
                >
                    <Input placeholder={t("请输入")} />
                </Form.Item>

                <Form.Item<FieldType>
                    label={t("手机号")}
                    name="telephone"
                    wrapperCol={{ span: 8 }}
                    getValueFromEvent={event => {
                        return event.target.value.replace(/\D/g, '');
                    }}
                    style={{marginBottom: '8px'}}
                >
                    <Input placeholder={t('请输入')} />
                </Form.Item>

                <Form.Item<FieldType>
                    label={t("地址")}
                    name="address"
                    wrapperCol={{ span: 24 }}
                    style={{marginBottom: '8px'}}
                >
                    <Input placeholder={t('请输入')} />
                </Form.Item>

                <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right', marginTop: '20px', marginBottom: '0px'}}>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
                        <Button
                            type="primary"
                            htmlType="submit"
                            className={styles.dialog_save_button}
                        >
                            {showSaveLayoutSchemeDialog.source == 'exitBtn' ? t('保存退出') : t('确定')}
                        </Button>
                        <Button 
                            onClick={exitCb} 
                            className={styles.dialog_exit_button}
                        >
                            {showSaveLayoutSchemeDialog.source == 'exitBtn' ? t('直接退出') : t("取消")}
                        </Button>
                    </div>
                </Form.Item>
            </Form>
        </div>
      </div>
    );
}

export default observer(SaveLayoutSchemeDialog);
