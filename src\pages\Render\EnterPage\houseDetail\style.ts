import { createStyles } from '@svg/antd/es/theme/utils';

const useStyles = createStyles(({ css }) => ({
    container: css`
        border-radius: 12px;
        background: #FFF;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1001;
        display: flex;
        flex-direction: column;
        gap: 0;
        width: 85vw;
        height: 90vh;
        /* @media screen and (max-width: 1366px) {
            width: 85vw;
            height: 90vh;
        } */
        /* @media screen and (min-width: 1366px) {
            width: 1088px;
            height: 668px;
        } */
    `,
    header: css`
        display: flex;
        align-items: center;
        padding: 6px 20px;
        width: 100%;
        height: 40px;
        gap: 10px;
        flex-shrink: 0;
        align-self: stretch;
        .title {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1 0 0;
            .title_text {
                color: #282828;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }
        }
        .close {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    `,
    content: css`
        width: 100%;
        height: calc(100% - 52px);
        padding: 20px;
        display: flex;
        align-items:flex-start;
        gap: 28px;
        flex: 1 0 0;
        align-self: stretch;
        .content_left {
            width: 100%;
            height: 100%;
            img {
                width: 100%;
                height: auto;
                aspect-ratio: 1;
                object-fit: cover;
            }
        }
        .content_right {
            width: 500px;
            display: flex;
            flex-direction: column;
            gap: 24px;
            align-items: flex-start;
            .title {
                color: #282828;
                font-family: "PingFang SC";
                font-size: 20px;
                font-style: normal;
                font-weight: 600;
            }
            .info {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                .info_item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    .info_item_label {
                        color: #5B5E60;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 24px; /* 171.429% */
                    }
                    .info_item_value {
                        color: #282828;
                        font-family: "PingFang SC";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 24px; /* 171.429% */
                    }
                }
            }
            .btnBox {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 12px;
                .btn {
                    display: flex;
                    width: 120px;
                    height: 36px;
                    padding: 6px 16px;
                    border: none;
                    justify-content: center;
                    align-items: center;
                    gap: 8px;
                    border-radius: 20px;
                    background: linear-gradient(91deg, #BA63F0 -0.97%, #5C42FB 100%);
                    color: #FFF;
                    text-align: center;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 171.429% */
                }
            }
        }
    `,
}));

export default useStyles;
