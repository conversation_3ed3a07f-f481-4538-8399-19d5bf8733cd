"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[3704],{25781:function(n,e,t){var r=t(13274),i=t(41594),o=t(69802),a=t(90803),l=t(14181),c=t(27347),s=t(99030),u=t(9003),d=t(88934),f=t(61928),p=t(15696),h=t(62634);function x(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function m(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function g(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){m(o,r,i,a,l,"next",n)}function l(n){m(o,r,i,a,l,"throw",n)}a(void 0)})}}function b(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function v(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){b(n,e,t[e])})}return n}function y(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return x(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return x(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}e.A=(0,p.observer)(function(){(0,o.B)().t;var n=(0,a.A)().styles,e=(0,u.P)(),t=y((0,i.useState)({left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"}),2),p=t[0],x=t[1],m=c.nb.instance.layout_container,b=function(){return g(function(){var n,t,r,i,o,a,s,u;return w(this,function(d){switch(d.label){case 0:return i=c.nb.instance.scene3D,o=(null===(n=e.homeStore.guideMapCurrentRoom)||void 0===n?void 0:n.name)||(null===(t=m._selected_room)||void 0===t?void 0:t.roomname)||"",a=e.homeStore.guideMapCurrentRoom||m._selected_room||null,s=3,a&&a.furnitureList&&a.furnitureList.length>0&&(s=0),u=!1,(null===(r=i.selection_box)||void 0===r?void 0:r.visible)&&(u=i.selection_box.visible,i.selection_box.visible=!1),i.update(),[4,l.w.instance.submitAiDraw({room_name:o,roomUid:(null==a?void 0:a.uid)||"",aiModel:s},e.homeStore.aspectRatioMode)];case 1:return d.sent(),u&&(i.selection_box.visible=u),[2]}})})()};return(0,i.useEffect)(function(){c.nb.on_M(s.r.AiDrawingCapture,"AiDrawingGallery",function(){var n=c.nb.instance.scene3D,t=(c.nb.instance.layout_container,{left:"0",top:"0",width:"100vw",height:"100vh",transition:"none"}),r={left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"};if(g(function(){var n;return w(this,function(t){switch(t.label){case 0:return n=null,h.A.loading("提交渲染中...",0),m._layout_scheme_id?[3,1]:(c.nb.DispatchEvent(c.n0.autoSave,null),n&&clearInterval(n),n=setInterval(function(){return g(function(){return w(this,function(e){switch(e.label){case 0:return m._layout_scheme_id?(clearInterval(n),n=null,[4,b()]):[3,2];case 1:e.sent(),h.A.destroy(),e.label=2;case 2:return[2]}})})()},500),[2]);case 1:return[4,b()];case 2:t.sent(),t.label=3;case 3:return[4,e.homeStore.query_genCount()];case 4:return t.sent(),h.A.destroy(),h.A.success("提交AI绘图成功！"),[2]}})})(),n.parent_div){var i=n.parent_div.offsetLeft,o=n.parent_div.offsetTop,a=n.parent_div.offsetWidth,l=n.parent_div.offsetHeight;t.left=i+"px",t.top=o+"px",t.width=a+"px",t.height=l+"px"}var s=document.getElementById("aidrawing_tuku_btn");if(s&&s.getBoundingClientRect){var u=s.getBoundingClientRect();if(u){var d=u.left,f=u.top;d+=u.width/2,f+=u.height/2,r.left=d+"px",r.top=f+"px"}}x(v({},t)),setTimeout(function(){x(v({},r))},300)}),c.nb.on_M(d.U.diffuseImage,"aiDrawingGallery",function(n){var t;f.K.diffuseImageSDK(n,null===(t=e.homeStore.guideMapCurrentRoom)||void 0===t?void 0:t.roomname)})},[]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:n.photo_capture_div+" photo_capture_div",style:p})})})},53704:function(n,e,t){t.d(e,{A:function(){return kt}});var r=t(13274),i=t(69802),o=t(8268);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function l(){var n=a(["\n      position:fixed;\n      background : #fff;\n      z-index: 10;\n      .closeBtn {\n        display:none;\n        position:absolute;\n         right : 6px;\n         top : 6px;\n         font-size:20px;\n         width:60px;\n         height : 24px;\n         text-align:right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: 12px !important;\n        top: 52px !important;\n        bottom: 12px !important;\n        right: auto !important;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width:224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          display: none;\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position:fixed;\n        left:0;\n        bottom:0px;\n        right:0;\n        width : auto;\n        height:340px;\n        max-width : auto;\n        max-height:340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {\n          transform : scale(0.7);\n          transform-origin : bottom;\n          left : -15%;\n          right : -15%;\n        }\n        &.panel_hide {\n          max-width : 0px;\n        }\n        .closeBtn {\n          display : block;\n        }\n      }\n\n\n      .fade-enter {\n  opacity: 0;\n}\n\n.fade-enter-active {\n  opacity: 1;\n  transition: opacity 300ms ease-in-out;\n}\n\n.fade-exit {\n  opacity: 1;\n}\n\n.fade-exit-active {\n  opacity: 0;\n  transition: opacity 300ms ease-in-out;\n}\n    "]);return l=function(){return n},n}function c(){var n=a(["\n      background: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n    "]);return c=function(){return n},n}function s(){var n=a(["\n      height: 60px;\n      width : 100%;\n      @media screen and (orientation: landscape) {\n        height: 55px;\n        width : 100%;\n        color :#000;\n        font-weight:700;\n        padding-left: 13px;\n        padding-right: 13px;\n        padding-top: 12px;\n        display:block;\n\n      }\n      @media screen and (orientation: portrait) {\n        width: 30%;\n        margin-top: 10px;\n        margin-left: 10px;\n        height: auto;\n      }\n    "]);return s=function(){return n},n}function u(){var n=a(["\n        position: absolute;\n        left: 5px;\n        width: 34px;\n        top: 50px;\n        height:210px;\n        display: flex;\n        flex-direction: column;\n        font-size: 17px;\n        align-items: center;\n        flex-wrap: nowrap;\n        justify-content: center;\n        border-radius:8px;\n        color : #aaa;\n        text-align:center;\n        background:#eee;\n\n        @media screen and (orientation: landscape) {\n            display:none;\n        }\n        @media screen and (orientation: portrait) {\n          display: flex;\n          \n        }\n    }\n      .vTab {\n        width: 30px;\n        padding-top: 8px;\n        padding-bottom: 8px;\n        padding-left:5px;\n        padding-right:5px;\n\n        &.checked {\n          background:#fff;\n          color : #2b2b2b;\n          border-radius:8px;\n\n        }\n\n      }\n    "]);return u=function(){return n},n}function d(){var n=a(["\n      display:none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor:pointer;\n\n      @media screen and (orientation: landscape) {\n        display:block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n\n      }\n      &.panel_hide {\n        left:0px;\n        display:block;\n      }\n    "]);return d=function(){return n},n}function f(){var n=a(["\n      color : #959598;\n      .ant-tabs-tab {\n        color :#959598;\n      }\n    "]);return f=function(){return n},n}function p(){var n=a(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n\n    "]);return p=function(){return n},n}function h(){var n=a(["\n      /* position:absolute;\n      left:45px;\n      right:5px;\n      top: 0px;\n      bottom:10px;\n      overflow:hidden;\n      @media screen and (orientation: landscape) {\n        left:5px;\n        top: 0px;\n\n      } */\n    "]);return h=function(){return n},n}function x(){var n=a(["\n      height:100%;\n      width:100%;\n    "]);return x=function(){return n},n}function m(){var n=a(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return m=function(){return n},n}function g(){var n=a(["\n    /* position: fixed;\n    top: 0px;\n    background-color: #fff;\n    width: 100%;\n    height: 56px;\n\n    display: flex;\n    padding: 0 16px;\n    justify-content: space-between;\n    align-items: center;\n    z-index: 9;\n    max-width: 1174px;\n    @media screen and (max-width: 450px) { // 手机宽度\n      height: 46px;\n    } */\n  "]);return g=function(){return n},n}function b(){var n=a(["\n    position: fixed;\n    top: 12px;\n    left: 12px;\n    height: 32px;\n    width: 68px;\n    line-height:28px;\n    font-size: 14px;\n    background:#fff;\n    border-radius:6px;\n    text-align:center;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    .iconleft {\n      text-align:left;\n    }\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n      width:50px;\n\n    }\n    z-index: 9;\n  "]);return b=function(){return n},n}function v(){var n=a(["\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);\n    color: #fff;\n  "]);return v=function(){return n},n}function y(){var n=a(["\n    position: fixed;\n    top: 12px;\n    right: 12px;\n    height : 28x;\n    width:60px;\n    line-height:28px;\n    color: #fff;\n    font-size: 13px;\n    border-radius:6px;\n    text-align:center;\n    background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n    z-index:9;\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n    }\n  "]);return y=function(){return n},n}function w(){var n=a(["\n      position: fixed;\n      top: 6px;\n      right: 12px;\n      width:40px;\n      height : 40px;\n      line-height:40px;\n      border-radius:6px;\n      text-align:center;\n      color :#282828;\n      background:rgba(255,255,255,0.2);\n      z-index:9;\n      font-size: 16px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 14px;\n      }\n\n   "]);return w=function(){return n},n}function j(){var n=a(["\n    top: 50px;\n    right: 12px;\n    position: fixed;\n    z-index:999;\n    background:#fff;\n    padding:10px;\n    border-radius:6px;\n  "]);return j=function(){return n},n}function _(){var n=a(["\n      position:fixed;\n      width: 200px;\n      left: calc(50% - 100px);\n      top: 5px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width:120px;\n        left: calc(50% - 60px);\n      }\n      @media screen and (max-width: 350px) { // 手机宽度\n        top: 60px;\n        left :12px;\n      }\n      z-index: 9;\n    "]);return _=function(){return n},n}function S(){var n=a(["\n        position:fixed;\n        width: 200px;\n        right: 12px;\n        top: 5px;\n        z-index: 9;\n    "]);return S=function(){return n},n}function k(){var n=a(["\n      position:fixed;\n      bottom:13px;\n      z-index:9;\n      display:flex;\n      justify-content:center;\n      align-items:center;\n      left: 50%;\n      transform: translate(-50%, 0);\n      transition: all 0.5s ease;\n      .btn {\n        border-radius: 50px;\n        background: #FFFFFF;\n        box-shadow: 0px 6px 20px 0px #00000014;\n        width : 120px;\n        border: none;\n        height : 48px;\n        color: #282828;\n        font-size: 14px;\n        line-height: 48px;\n        letter-spacing: 0px;\n        text-align: center;\n        margin-left:12px;\n        margin-right:12px;\n      }\n      .btnForModeBar {\n        border-radius: 50px;\n        background: #FFFFFF;\n        box-shadow: 0px 6px 20px 0px #00000014;\n        width : 230px;\n        border: none;\n        height : 48px;\n        color: #282828;\n        font-size: 14px;\n        line-height: 48px;\n        letter-spacing: 0px;\n        text-align: center;\n        margin-left:12px;\n        margin-right:12px;\n        display: flex;\n        justify-content: space-around;\n        padding: 0 10px;\n\n        .active{\n          color: #1b6ef3\n        }\n      }\n      .blackColor {\n        background: rgba(0, 0, 0, 0.40) !important;\n        backdrop-filter: blur(50px) !important;\n        color: #fff !important;\n      }\n      .submit {\n        background: linear-gradient(90deg,#d07bff,#7a5bff) !important;\n        color: #fff !important;\n      }\n      @media screen and (orientation: landscape){\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        left: 50%;\n        transform: translate(-50%, 0);\n      }\n      &.showLeftPanel {\n        @media screen and (orientation: portrait) {\n          position: fixed;\n          bottom: 280px;\n          max-width : 48px;\n          top : auto;;\n          left: 44%;\n          transform: translateX(-50%);\n          right : 24px;\n          height: 120px;\n          display: block;\n\n          @media screen and (max-width: 450px) { // 手机宽度\n            right : 12px;\n          }\n          @media screen and (max-height: 700px) { \n            right : auto;\n            left : 0px;\n          }\n          .btn {\n            border-radius: 50px;\n            background: #FFFFFF;\n            box-shadow: 0px 6px 20px 0px #00000014;\n            width : 120px;\n            border: none;\n            height : 48px;\n            color: #282828;\n            font-size: 14px;\n            line-height: 48px;\n            letter-spacing: 0px;\n            text-align: center;\n            margin-left:12px;\n            margin-right:12px;\n  \n            &.hasIcon {\n              line-height:19px;\n              .iconfont {\n                  display:block;\n                  margin-top:4px;\n              }\n            }\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 40px !important;\n              height: 44px !important;\n              font-size: 10px !important;\n            }\n          }\n      }\n\n\n      }\n    "]);return k=function(){return n},n}function A(){var n=a(["\n      position:fixed;\n      right:0;\n      z-index:9;\n      top:0px;\n      transition: all 0.2s ease;\n      &.is_3d_mode {\n        top:180px;\n      }\n    "]);return A=function(){return n},n}function C(){var n=a(["\n    width:100%;\n    font-size:16px;\n    line-height:40px;\n    text-align:center;\n  "]);return C=function(){return n},n}var I=(0,o.rU)(function(n){var e=n.css;return{leftPanelRoot:e(l()),materialReplace:e(c()),tabBar:e(s()),leftTabBar:e(u()),collapseBtn:e(d()),tab_root:e(f()),topTitle:e(p()),popupContainer:e(h()),listContainer:e(x()),open:e(m()),navigation:e(g()),backBtn:e(b()),blackColor:e(v()),forwardBtn:e(y()),closeBtn:e(w()),shareBarContainer:e(j()),topTabs:e(_()),rightSceneModeTabs:e(S()),bottomButtons:e(k()),sideToolbarContainer:e(A()),schemeNameSpan:e(C())}}),z=t(15696),M=t(41594),D=t(27347),E=t(45599),F=t(50617),N=t(70060),P=t(76135),B=t(93491),O=t(9003),R=t(81074),T=t(95301),L=t(23825),U=t(88934),W=t(32184),H=t(67869),q=t(7224),V=t(73062),$=t(23184),G=t(49063),Y=t(25781),K=t(6934),Z=t(78154),X=t(22681),J=t(21491),Q=t(59525),nn=t(17365),en=t(10371),tn=t(72978),rn=t(60092);function on(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function an(){var n=on(["\n            width: 100%;\n            background-color: #fff;\n            border-radius: 4px;\n            padding: 0;\n            position: relative;\n        "]);return an=function(){return n},n}function ln(){var n=on(["\n            text-align: center;\n            font-size: 16px;\n            font-weight: 500;\n            color: #333;\n            padding: 12px 0;\n            border-bottom: 1px solid #f0f0f0;\n        "]);return ln=function(){return n},n}function cn(){var n=on(["\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            margin-bottom: 16px;\n            padding-bottom: 8px;\n            border-bottom: 1px solid #f0f0f0;\n        "]);return cn=function(){return n},n}function sn(){var n=on(["\n            font-size: 16px;\n            font-weight: 500;\n            color: #333;\n        "]);return sn=function(){return n},n}function un(){var n=on(["\n            cursor: pointer;\n            color: #999;\n            font-size: 16px;\n\n            &:hover {\n                color: #666;\n            }\n        "]);return un=function(){return n},n}function dn(){var n=on(["\n            margin-bottom: 16px;\n            padding: 0 16px;\n            &:first-of-type {\n                margin-top: 16px;\n            }\n        "]);return dn=function(){return n},n}function fn(){var n=on(["\n            display: block;\n            margin-bottom: 8px;\n            font-size: 14px;\n            color: #333;\n        "]);return fn=function(){return n},n}function pn(){var n=on(["\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            height: 36px;\n            padding: 0;\n            \n            span {\n                margin-bottom: 0;\n                flex: 1;\n            }\n        "]);return pn=function(){return n},n}function hn(){var n=on(["\n            width: 100%;\n            height: 36px;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            background-color: #fff;\n            position: relative;\n\n            &.ant-select-focused .ant-select-selector,\n            .ant-select-selector:hover,\n            .ant-select-selector:focus {\n                border-color: #40a9ff !important;\n                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;\n            }\n\n            .ant-select-selector {\n                border: none !important;\n                box-shadow: none !important;\n                height: 100% !important;\n                padding: 0 11px !important;\n            }\n        "]);return hn=function(){return n},n}function xn(){var n=on(["\n            width: 100%;\n            height: 36px;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            padding: 0 12px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n        "]);return xn=function(){return n},n}function mn(){var n=on(["\n            width: 100%;\n            padding: 8px 0;\n\n            .ant-slider-track {\n                background-color: #1890ff;\n            }\n        "]);return mn=function(){return n},n}function gn(){var n=on(["\n            display: flex;\n            align-items: center;\n            justify-content: flex-end;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            height: 36px;\n            padding: 0 12px;\n        "]);return gn=function(){return n},n}function bn(){var n=on(["\n            width: 36px;\n            height: 24px;\n            border-radius: 2px;\n            background-color: #ffa500;\n            cursor: pointer;\n            border: 1px solid #e8e8e8;\n            \n            &:hover {\n                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n            }\n        "]);return bn=function(){return n},n}function vn(){var n=on(["\n            margin-top: 0;\n            margin-bottom: 16px;\n            padding: 0 16px;\n        "]);return vn=function(){return n},n}function yn(){var n=on(["\n            width: 100%;\n            height: 36px;\n            background-color: #f5f5f5;\n            border: none;\n            border-radius: 4px;\n            color: #333;\n            cursor: pointer;\n\n            &:hover {\n                background-color: #e8e8e8;\n            }\n        "]);return yn=function(){return n},n}function wn(){var n=on(["\n            color: #999;\n            margin-left: 4px;\n        "]);return wn=function(){return n},n}var jn=(0,o.rU)(function(n){var e=n.css;return{container:e(an()),titleBar:e(ln()),header:e(cn()),title:e(sn()),closeIcon:e(un()),formItem:e(dn()),label:e(fn()),colorRow:e(pn()),select:e(hn()),inputNumber:e(xn()),slider:e(mn()),colorPicker:e(gn()),colorBlock:e(bn()),footer:e(vn()),footerButton:e(yn()),unitText:e(wn())}}),_n=t(62867),Sn=t(73751);function kn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function An(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return kn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return kn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Cn=function(n){!function(n){if(null==n)throw new TypeError("Cannot destructure "+n)}(n);var e=jn().styles,t=(0,O.P)(),i=An((0,M.useState)("客厅区"),2),o=i[0],a=i[1],l=An((0,M.useState)("#FFA500"),2),c=l[0],s=l[1],u=An((0,M.useState)([]),2),d=u[0],f=u[1],p=An((0,M.useState)(!1),2),h=p[0],x=p[1],m=An((0,M.useState)(null),2),g=m[0],b=m[1];(0,M.useEffect)(function(){var n=_n.a.getRoomSubAreaMenuData().map(function(n){return{value:n.id,label:n.text}});f(n),D.nb.on_M(Sn.$.showPopup,"SpaceAreaAttribute",function(e){var r;if("SpaceAreaAttribute"!==e&&h)x(!1);else if((null===(r=t.homeStore.selectEntity)||void 0===r?void 0:r.type)===W.Fz.RoomSubArea){x(!0);var i=t.homeStore.selectEntity;b(i);var o=i.space_area_type,l=n.find(function(n){return n.value===o});a(l),s(i.color_style)}else x(!1)})},[h]);return h&&(0,r.jsxs)("div",{className:e.container,children:[(0,r.jsxs)("div",{className:e.formItem,children:[(0,r.jsx)("div",{className:e.label,children:"分区类型"}),(0,r.jsx)(tn.A,{className:e.select,value:o,onChange:function(n){a(n),g&&n in W.fZ&&D.nb.DispatchEvent(D.n0.UpdateRoomSubAreaType,n)},options:d})]}),(0,r.jsx)("div",{className:e.formItem,children:(0,r.jsxs)("div",{className:e.colorRow,children:[(0,r.jsx)("span",{className:e.label,children:"颜色"}),(0,r.jsx)(rn.A,{value:c,onChangeComplete:function(n){s(n.toHexString()),g&&(_n.a.updateSubAreaColor(g,n.toHexString()),D.nb.instance.update())}})]})}),(0,r.jsx)("div",{className:e.footer,children:(0,r.jsx)("button",{className:e.footerButton,onClick:function(){g&&D.nb.RunCommand(D._I.DeleteFurniture)},children:"删除分区"})})]})};function In(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function zn(){var n=In(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      z-index: 8;\n      width: 100%;\n      height: 100%;\n      border-width: 40px 45px;\n      border-color: #000000;\n      border-style: solid;\n      opacity: 0.35;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      box-sizing: border-box;\n      pointer-events: none;\n    "]);return zn=function(){return n},n}function Mn(){var n=In(["\n      width: 100%;\n      height: 100%;\n      background-color: transparent; /* 中间区域透明 */\n      border: 3px solid #ffffff; /* 添加白色边框 */\n      border-radius: 3px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      pointer-events: none;\n    "]);return Mn=function(){return n},n}var Dn=(0,o.rU)(function(n){var e=n.css;return{box:e(zn()),contentBox:e(Mn())}});function En(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Fn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return En(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return En(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Nn=.83,Pn=(0,z.observer)(function(){var n=Dn().styles,e=(0,O.P)(),t=(0,M.useRef)(null),i=Fn((0,M.useState)(window.innerWidth<window.innerHeight),2),o=i[0],a=i[1],l=Fn((0,M.useState)({borderWidth:"0px",borderHeight:"0px",contentWidth:"100%",contentHeight:"100%"}),2),c=l[0],s=l[1];(0,M.useEffect)(function(){var n=function(){var n=window.innerWidth<window.innerHeight;a(n),u(e.homeStore.aspectRatioMode,n)};return window.addEventListener("resize",n),function(){return window.removeEventListener("resize",n)}},[]),(0,M.useEffect)(function(){u(e.homeStore.aspectRatioMode,o)},[e.homeStore.aspectRatioMode,o]);var u=function(n){var e,t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,i=window.innerWidth,a=window.innerHeight,l=[{width:4,height:3},{width:16,height:9},{width:3,height:4},{width:9,height:16},{width:0,height:0}][n-1];if(5==n)return e=i,t=a,void s({borderWidth:"".concat(0,"px"),borderHeight:"".concat(0,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")});r?(t=(e=i*Nn)*l.height/l.width)>a*Nn&&(e=(t=a*Nn)*l.width/l.height):(e=(t=a*Nn)*l.width/l.height)>i*Nn&&(t=(e=i*Nn)*l.height/l.width),s({borderWidth:"".concat((i-e)/2,"px"),borderHeight:"".concat((a-t)/2,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")})};return(0,M.useEffect)(function(){if(t.current){var e=t.current;e.style.borderLeftWidth=c.borderWidth,e.style.borderRightWidth=c.borderWidth,e.style.borderTopWidth=c.borderHeight,e.style.borderBottomWidth=c.borderHeight;var r=e.querySelector(".".concat(n.contentBox));r&&(r.style.width=c.contentWidth,r.style.height=c.contentHeight)}},[c,n.contentBox]),(0,r.jsx)("div",{className:n.box,ref:t,children:(0,r.jsx)("div",{className:n.contentBox})})}),Bn=t(43417),On=t(79750);function Rn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Tn(){var n=Rn(["\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n      font-size: 16px;\n      color: rgba(255, 255, 255, 0.60);\n    "]);return Tn=function(){return n},n}function Ln(){var n=Rn(["\n      position: fixed;\n      left: 50%;\n      transform: translate(-50%, 0);\n      z-index: 999;\n      bottom: 16px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      /* width: 120px; */\n      color: #fff;\n    "]);return Ln=function(){return n},n}function Un(){var n=Rn(["\n      z-index:999;\n      margin-right: 10px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 20px;\n      cursor: pointer;\n      .selectInfo {\n        z-index: 1;\n        position: absolute;\n        left: 50%;\n        bottom: 50px;\n        transform: translate(-50%);\n        max-width: 500px;\n        overflow-x: scroll;\n        transition: height 0.3;\n        display: flex;\n        scroll-behavior: smooth;\n        ::-webkit-scrollbar\n        {\n          display: none;\n        }\n        .shijiaoItem\n        {\n          width: 100%;\n          font-size: 16px;\n          text-align: center;\n          position: relative;\n          margin-right: 8px;\n          transition: all .3s;\n          border-radius: 8px;\n          width: 127px;\n          height: 95px;\n          img{\n            width: 122px;\n            height: 100%;\n            border-radius: 8px;\n          }\n          .title\n          {\n            position: absolute;\n            bottom: 0px;\n            left: 50%;\n            transform: translate(-50%);\n            color: #fff;\n            font-size: 14px;\n            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);\n            width: 123px;\n            height: 35px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 8px;\n          }\n        }\n        .shijiaoItem:hover\n        {\n          background-color: #ffffff1a;\n          transition: all 0.3;\n        }\n      }\n    "]);return Un=function(){return n},n}function Wn(){var n=Rn(["\n      border-right: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return Wn=function(){return n},n}function Hn(){var n=Rn(["\n      border-left: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return Hn=function(){return n},n}function qn(){var n=Rn(["\n      width: 80px;\n      height: 42px;\n      font-size: 16px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 100px;\n      }\n    "]);return qn=function(){return n},n}function Vn(){var n=Rn(["\n      z-index:999;\n      background: #2323234d;\n      border-radius: 20px;\n      cursor: pointer;\n      color: #eee;\n      font-size: 16px;\n      width: 120px;\n      height: 42px;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 20px;\n      align-items: center;\n      background: linear-gradient(90deg,#d07bff,#7a5bff);\n      .submit\n      {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 100%;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 120px;\n        left: 75%;\n      }\n    "]);return Vn=function(){return n},n}function $n(){var n=Rn(["\n      font-size: 14px;\n      color: rgba(255, 255, 255, 0.80);\n      font-weight: 600;\n      margin-bottom: 15px; \n    "]);return $n=function(){return n},n}function Gn(){var n=Rn(["\n    "]);return Gn=function(){return n},n}function Yn(){var n=Rn(["\n      z-index: 99;\n      position: fixed;\n      left: 12px;\n      top: 52px;\n      width: 200px;\n      height: 90%;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 8px;\n      overflow: hidden;\n      transition: height 0.3;\n      padding: 16px;\n      overflow-x: hidden;\n      .ant-tag-checkable-checked \n      {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      }\n      .ant-tag-checkable-checked:hover\n      {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      }\n      .ant-slider-track \n      {\n        background: #9156FF !important;\n      }\n      .ant-slider .ant-slider-dot-active\n      {\n        border-color: #9156FF !important;\n      }\n      .ant-tag:not(.ant-tag-checkable-checked)\n      {\n        background: rgba(0, 0, 0, 0.40);\n        color: #fff;\n      }\n      .ant-tag{\n        width: 76px;\n        text-align: center;\n        margin-bottom: 8px;\n      }\n      .ant-slider-mark-text\n      {\n        color: #ffffffbf;\n        font-size: 10px;\n      }\n    "]);return Yn=function(){return n},n}var Kn=(0,o.rU)(function(n){var e=n.css;return{titleTag:e(Tn()),submitContainer:e(Ln()),shijiaoBarContainer:e(Un()),leftArrow:e(Wn()),rightArrow:e(Hn()),shijiaoBar:e(qn()),submitBtn:e(Vn()),label:e($n()),lensContainer:e(Gn()),submitInfo:e(Yn())}}),Zn=t(96278),Xn=t(62634),Jn=t(51187),Qn=t(80277),ne=t(99030),ee=t(61535),te=t(27164),re=t(47299);function ie(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function oe(){var n=ie(["\n      z-index:999;\n      margin-right: 10px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 20px;\n      cursor: pointer;\n      .selectInfo {\n        z-index: 1;\n        position: absolute;\n        left: 50%;\n        bottom: 50px;\n        transform: translate(-50%);\n        max-width: 500px;\n        overflow-x: scroll;\n        transition: height 0.3;\n        display: flex;\n        scroll-behavior: smooth;\n        ::-webkit-scrollbar\n        {\n          display: none;\n        }\n        .shijiaoItem\n        {\n          width: 100%;\n          font-size: 16px;\n          text-align: center;\n          position: relative;\n          margin-right: 8px;\n          transition: all .3s;\n          border-radius: 8px;\n          width: 127px;\n          height: 95px;\n          img{\n            width: 122px;\n            height: 100%;\n            border-radius: 8px;\n          }\n          .title\n          {\n            position: absolute;\n            bottom: 0px;\n            left: 50%;\n            transform: translate(-50%);\n            color: #fff;\n            font-size: 14px;\n            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);\n            width: 123px;\n            height: 35px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 8px;\n          }\n        }\n        .shijiaoItem:hover\n        {\n          background-color: #ffffff1a;\n          transition: all 0.3;\n        }\n      }\n    "]);return oe=function(){return n},n}function ae(){var n=ie(["\n      border-right: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return ae=function(){return n},n}function le(){var n=ie(["\n      border-left: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return le=function(){return n},n}function ce(){var n=ie(["\n      width: 80px;\n      height: 42px;\n      font-size: 16px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      color: #fff;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 100px;\n      }\n    "]);return ce=function(){return n},n}var se=(0,o.rU)(function(n){var e=n.css;return{shijiaoBarContainer:e(oe()),leftArrow:e(ae()),rightArrow:e(le()),shijiaoBar:e(ce())}});function ue(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function de(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ue(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ue(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var fe=(0,z.observer)(function(){(0,i.B)().t;var n=se().styles,e=((0,G.Zp)(),(0,O.P)()),t=de((0,M.useState)(0),2),o=t[0],a=t[1],l=de((0,M.useState)(!1),2),c=l[0],s=l[1],u=(0,M.useRef)(null),d=(0,M.useRef)(null),f=(0,M.useRef)([]);(0,M.useEffect)(function(){var n=function(n){var e=(n.touches,n.target);u.current&&!u.current.contains(e)&&s(!1)};return c&&(document.addEventListener("mousedown",n),document.addEventListener("touchstart",n,{passive:!0})),function(){document.removeEventListener("mousedown",n),document.removeEventListener("touchstart",n)}},[c]),(0,M.useEffect)(function(){if(d.current&&f.current[o]){var n=d.current,e=f.current[o],t=n.getBoundingClientRect(),r=e.getBoundingClientRect(),i=r.left-t.left+n.scrollLeft,a=(t.width-r.width)/2;n.scrollTo({left:i-a,behavior:"smooth"})}},[o,c]);var p=function(n){a(n);var t=D.nb.instance.scene3D;t.active_controls.bindViewEntity(e.homeStore.currentViewCameras[n]),t.update()};return(0,r.jsxs)("div",{className:n.shijiaoBarContainer,ref:u,children:[(0,r.jsx)("div",{onClick:function(){s(!0),p(o>0?o-1:e.homeStore.currentViewCameras.length-1)},className:n.leftArrow,children:(0,r.jsx)(te.A,{style:{color:"#bcb9b9",fontSize:14},iconClass:"iconfill_left"})}),(0,r.jsxs)("div",{className:n.shijiaoBar,onClick:function(){s(!c)},children:["视角",o+1]}),(0,r.jsx)("div",{onClick:function(){s(!0),o<e.homeStore.currentViewCameras.length-1?p(o+1):p(0)},className:n.rightArrow,children:(0,r.jsx)(te.A,{style:{color:"#bcb9b9",fontSize:14},iconClass:"iconfill_right"})}),(0,r.jsx)("div",{className:"selectInfo",ref:d,onWheel:function(n){n.preventDefault();n.currentTarget.scrollLeft+=30*n.deltaY},style:{height:c?"auto":0},children:e.homeStore.currentViewCameras.map(function(n,e){return(0,r.jsxs)("div",{className:"shijiaoItem",ref:function(n){return f.current[e]=n},style:{border:e===o?"2px solid #147FFA":"2px solid #fff"},onClick:function(){p(e)},children:[(0,r.jsx)("img",{src:n._perspective_img.src,alt:""}),(0,r.jsxs)("div",{className:"title",children:["视角",e+1]})]},e)})})]})}),pe=t(79902),he=t(65640);function xe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function me(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function ge(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){me(o,r,i,a,l,"next",n)}function l(n){me(o,r,i,a,l,"throw",n)}a(void 0)})}}function be(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return xe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return xe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ve(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ye=void 0,we=(0,M.forwardRef)(function(n,e){var t=Kn().styles,i=(0,O.P)(),o=i.homeStore,a=o.drawPictureMode,l=o.aspectRatioMode,c=o.setAspectRatioMode,s=Zn.A.CheckableTag,u=((0,M.useRef)(null),be((0,M.useState)(1),2)),d=u[0],f=u[1],p=be((0,M.useState)(300),2),h=p[0],x=p[1],m=(i.homeStore.guideMapCurrentRoom,be((0,M.useState)(20),2)),g=(m[0],m[1],[{lable:"标清",radioMode:1,config:pe.Dx[pe.Rw.SD]},{lable:"高清",radioMode:2,config:pe.Dx[pe.Rw.HD]}]),b=function(n){return ge(function(){var e;return ve(this,function(t){switch(t.label){case 0:return ee.j.resolutionTag=n,[4,ee.j.commitOfflineRender()];case 1:if(e=t.sent(),Xn.A.destroy(),e.success)Xn.A.success("提交渲染成功！");else{if("余额不足，请充值！"===e.msg)return Xn.A.error((0,r.jsxs)(r.Fragment,{children:["提交渲染失败，余额不足，请点击",(0,r.jsx)("a",{href:"https://mall.3vjia.com/",target:"_blank",rel:"noopener noreferrer",children:"购买"})]}),5),[2];Xn.A.error((0,r.jsx)(r.Fragment,{children:e.msg}),3)}return[2]}})})()},v=function(){return ge(function(){var n,e,t,r,i;return ve(this,function(o){switch(o.label){case 0:return"aiDrawing"!==a?[3,1]:(D.nb.emit_M(ne.r.AiDrawingCapture,!0),[3,4]);case 1:return"render"!==a?[3,4]:(he.log("渲染 标清出图"),t=null===(e=g.find(function(n){return n.radioMode===d}))||void 0===e||null===(n=e.config)||void 0===n?void 0:n.resolutionTag,t?(D.nb.instance.renderSubmitObject={drawPictureMode:a,radioMode:l,resolution:d},Xn.A.loading("提交渲染中...",0),r=null,(i=D.nb.instance.layout_container)._layout_scheme_id?[3,2]:(D.nb.DispatchEvent(D.n0.autoSave,null),r&&clearInterval(r),r=setInterval(function(){return ge(function(){return ve(this,function(n){switch(n.label){case 0:return i._layout_scheme_id?(clearInterval(r),r=null,[4,b(t)]):[3,2];case 1:n.sent(),Xn.A.destroy(),n.label=2;case 2:return[2]}})})()},500),[2])):(Xn.A.error("未选择有效的分辨率！"),[2]));case 2:return[4,b(t)];case 3:o.sent(),o.label=4;case 4:return[2]}})})()};return(0,M.useImperativeHandle)(e,function(){return{clickSubmit:v}}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.submitContainer,children:[(0,r.jsx)(fe,{}),(0,r.jsx)("div",{className:t.submitBtn,children:(0,r.jsx)("div",{className:"submit",onClick:v,children:"提交渲染"})})]}),(0,r.jsx)(re.A,{in:i.homeStore.showSubmitInfo,timeout:300,classNames:{enter:"fadeEnter",enterActive:"fadeEnterActive",exit:"fadeExit",exitActive:"fadeExitActive"},unmountOnExit:!0,children:(0,r.jsxs)("div",{className:t.submitInfo,children:[(0,r.jsxs)("div",{className:t.titleTag,children:[(0,r.jsx)("div",{children:"aiDrawing"==a?"AI绘图":"标准渲染"}),(0,r.jsx)(te.A,{onClick:function(){i.homeStore.setShowSubmitInfo(!1)},style:{fontSize:14,cursor:"pointer"},iconClass:"icon-icon"})]}),(0,r.jsx)("div",{className:t.label,children:"构图"}),(0,r.jsx)("div",{style:{marginBottom:20},children:[{lable:"4:3",radioMode:1},{lable:"16:9",radioMode:2},{lable:"3:4",radioMode:3},{lable:"9:16",radioMode:4},{lable:"原图",radioMode:5}].map(function(n){return(0,r.jsx)(s,{checked:l==n.radioMode,onChange:function(e){c(n.radioMode)},children:n.lable},n.radioMode)})}),(0,r.jsx)("div",{className:t.label,children:"视角"}),(0,r.jsxs)("div",{className:t.lensContainer,children:[(0,r.jsx)("div",{style:{marginTop:12,marginRight:20,color:"rgba(255, 255, 255, 0.80)"},children:"镜头"}),(0,r.jsx)("div",{children:(0,r.jsx)(Jn.A,{style:{width:"160px"},marks:{20:{label:"特写"},40:{label:"人眼"},65:{label:"标准"},90:{label:"广角"}},defaultValue:65,step:1,min:20,max:90,onChange:function(n){var e=D.nb.instance.scene3D.active_controls.camera;e.fov=n,e.updateProjectionMatrix(),D.nb.emit_M(U.U.Scene3DCameraChanged,ye)}})})]}),(0,r.jsxs)("div",{className:t.lensContainer,children:[(0,r.jsx)("div",{style:{marginTop:12,marginRight:20,color:"rgba(255, 255, 255, 0.80)"},children:"裁剪"}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:20},children:[(0,r.jsx)(Jn.A,{style:{width:"120px"},min:300,max:2500,step:10,defaultValue:300,onChange:function(n){var e=D.nb.instance.scene3D.active_controls.camera;e.near=n,e.updateProjectionMatrix(),D.nb.emit_M(U.U.Scene3DCameraChanged,ye),x(n)}}),(0,r.jsx)(Qn.A,{min:300,max:2500,step:10,value:h,style:{width:"70px"},onChange:function(n){var e=D.nb.instance.scene3D.active_controls.camera;e.near=n,e.updateProjectionMatrix(),D.nb.emit_M(U.U.Scene3DCameraChanged,ye),x(n)}})]})]}),"render"==a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:t.label,children:"分辨率"}),(0,r.jsx)("div",{style:{marginBottom:20},children:g.map(function(n){return(0,r.jsx)(s,{checked:d==n.radioMode,onChange:function(){return ge(function(){return ve(this,function(e){return d===n.radioMode||f(n.radioMode),[2]})})()},children:n.lable},n.radioMode)})})]})]})})]})}),je=(0,z.observer)(we);function _e(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Se(){var n=_e(["\n    position: fixed;\n    top: 5px;\n    left: 50%;\n    transform: translateX(-50%);\n    max-width: 200px;\n    width: auto;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    z-index: 999;\n    border-radius: 8px;\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);;\n    justify-content: space-between;\n    @media screen and (max-width: 450px) { // 手机宽度\n      min-width: 250px;\n      height: 36px;\n    }\n  "]);return Se=function(){return n},n}function ke(){var n=_e(["\n    color: #333;\n    font-size: 14px;\n    margin-right: 12px;\n    max-width: 40%;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  "]);return ke=function(){return n},n}function Ae(){var n=_e(["\n    background-color: #1890ff;\n    color: white;\n    border: none;\n    padding: 6px 20px;\n    height: 28px;\n    line-height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    font-size: 14px;\n    margin-left: 12px;\n    &:hover {\n      background-color: #40a9ff;\n    }\n  "]);return Ae=function(){return n},n}function Ce(){var n=_e(["\n    color: #999;\n    font-size: 14px;\n    margin-left: 12px;\n    white-space: nowrap;\n  "]);return Ce=function(){return n},n}function Ie(){var n=_e(["\n    width: 100px;\n    color: #d6d1d1;\n    font-size: 14px;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    position: relative;\n    @media screen and (max-width: 450px) { // 手机宽度\n      width: 60px;\n      font-size: 12px;\n    }\n    @media screen and (max-width: 350px) { // 手机宽度\n    }\n    z-index: 9;\n  "]);return Ie=function(){return n},n}function ze(){var n=_e(["\n    color: #fff;\n    background: rgba(255, 255, 255, 0.20);\n    backdrop-filter: blur(50px);\n    border-radius: 8px;\n  "]);return ze=function(){return n},n}var Me=(0,o.rU)(function(n){var e=n.css;return{exitBarContainer:e(Se()),currentMode:e(ke()),exitButton:e(Ae()),exitHint:e(Ce()),topTabs:e(Ie()),active:e(ze())}}),De=t(57189),Ee=t(65640);function Fe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ne(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Fe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Fe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Pe=(0,z.observer)(function(){var n=(0,O.P)(),e=(0,i.B)().t,t=n.homeStore,o=t.drawPictureMode,a=t.setDrawPictureMode,l=(t.setIsdrawPicture,Me().styles),c=Ne((0,M.useState)(!1),2),s=(c[0],c[1],[{label:e("标准渲染"),value:"render"},{value:"aiDrawing",label:e("AI绘图")}]);return(0,M.useEffect)(function(){var n=D.nb.instance.scene3D;"render"===o&&(n.setLightMode(en.Ei.Night),De.p.instance.updateLighting(!0),D.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1)),"aiDrawing"===o&&(n.setLightMode(en.Ei.Day),De.p.instance.cleanLighting(),D.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),Bn.Y.cleanLight())},[o]),(0,r.jsx)("div",{className:l.exitBarContainer,children:s.map(function(e,t){return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{onClick:function(){return t=e.value,a(t),n.homeStore.setAtlasMode("aiDrawing"===t?"aidraw":"render"),void Ee.log("drawingPicMode",t);var t},className:l.topTabs+(o===e.value?" ".concat(l.active):""),children:[e.label,o===e.value&&(0,r.jsx)("span",{style:{content:'""',position:"absolute",bottom:"2px",left:"38px",width:"20px",height:"2px",backgroundColor:"#fff"}})]},t)})})})});function Be(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Oe(){var n=Be(["\n      height: 500px;\n      padding: 12px 12px;\n      border-radius: 12px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 100%;\n        padding: 12px 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return Oe=function(){return n},n}function Re(){var n=Be(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      color: rgba(255, 255, 255, 0.60);\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n      line-height: 24px;\n    "]);return Re=function(){return n},n}function Te(){var n=Be(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin-top: 12px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            border-radius: 4px;\n            border: 1px solid #EEE;\n            background: #C3C4C7;\n            @media screen and (orientation: landscape) {\n              width: 48px;\n              height: 48px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          color: rgba(255, 255, 255, 0.85);\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: rgba(255, 255, 255, 0.60);\n              margin-top: 4px;\n              user-select: text;\n              @media screen and (orientation: landscape) {\n                margin-top: 4px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return Te=function(){return n},n}function Le(){var n=Be(["\n      margin: 0px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 12px 0 8px 0px;\n      }\n    "]);return Le=function(){return n},n}function Ue(){var n=Be(["\n      display: flex;\n    "]);return Ue=function(){return n},n}function We(){var n=Be(["\n      border-radius: 4px;\n      background: rgba(0, 0, 0, 0.40);\n      height: 24px;\n      padding: 2px 8px;\n      display: flex;\n      width: 70px;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      font-size: 12px;\n      margin-right: 8px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return We=function(){return n},n}function He(){var n=Be(["\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n    "]);return He=function(){return n},n}function qe(){var n=Be(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n      @media screen and (orientation: portrait) {\n        overflow-x: scroll;\n        flex-wrap: nowrap;\n        width: 100%; \n      }\n    "]);return qe=function(){return n},n}function Ve(){var n=Be(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return Ve=function(){return n},n}function $e(){var n=Be(["\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n      @media screen and (max-width: 800px){\n         width: 112px;\n         img{\n          width: 112px;\n         }\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n\n    "]);return $e=function(){return n},n}function Ge(){var n=Be(["\n    \n    "]);return Ge=function(){return n},n}function Ye(){var n=Be(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return Ye=function(){return n},n}function Ke(){var n=Be(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      margin-top: 100%;\n      transform: translateY(-50%);\n      .emptyImg{\n        width: 60px;\n        height: 60px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #fff;\n      }\n    "]);return Ke=function(){return n},n}var Ze=(0,o.rU)(function(n){var e=n.css;return{root:e(Oe()),title:e(Re()),topInfo:e(Te()),divider:e(Le()),tabContainer:e(Ue()),tabItem:e(We()),active:e(He()),goodsInfo:e(qe()),loading:e(Ve()),goodsItem:e($e()),selectIcon:e(Ge()),sizeInfo:e(Ye()),noData:e(Ke())}}),Xe=t(76330),Je=t(37112),Qe=t(63080),nt=t(31033),et=t(63038),tt=t(44186),rt=t(46396),it=t(87961),ot=t(9455);function at(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function lt(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function ct(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){lt(o,r,i,a,l,"next",n)}function l(n){lt(o,r,i,a,l,"throw",n)}a(void 0)})}}function st(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function ut(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return at(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return at(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dt(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ft=(0,z.observer)(function(n){var e=n.selectedFigureElement,t=(0,O.P)(),o=(0,i.B)().t,a=Ze().styles,l=ut((0,M.useState)(null==e?void 0:e._candidate_materials),2),c=l[0],s=l[1],u=ut((0,M.useState)("套系素材"),2),d=u[0],f=u[1],p=ut((0,M.useState)(!1),2),h=p[0],x=p[1],m=(0,M.useRef)(null);(0,M.useEffect)(function(){e&&s(null==e?void 0:e._candidate_materials),f("套系素材")},[e]);return(0,M.useEffect)(function(){!function(n){ct(function(){var t,r,i;return dt(this,function(o){switch(o.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?s(null==e?void 0:e._candidate_materials):s([]),[3,3]);case 1:return x(!0),[4,(0,nt.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(r=o.sent()).success&&r.data?(i=r.data.materials.records.map(function(n){return{imageUrl:L.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}}),s(i)):s([]),x(!1),o.label=3;case 3:return[2]}})})()}(d)},[d]),(0,r.jsx)("div",{className:a.root,children:e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:a.title,children:[(0,r.jsx)("div",{children:o("素材替换")}),(0,r.jsx)(Xe.A,{style:{fontSize:20,color:"#5B5E60"},type:"icon-close1",onClick:function(){D.nb.emit_M(U.U.FigureElementSelected,null)}})]}),e&&(0,r.jsx)("div",{className:a.topInfo,children:(0,r.jsxs)("div",{className:"info",children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,r.jsxs)("div",{className:"sizeInfo",children:[(0,r.jsx)("div",{children:e._matched_material.name}),(0,r.jsxs)("div",{className:"size",children:[o("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,r.jsxs)("div",{className:"size",children:[o("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]}),(0,r.jsxs)("div",{className:"size",children:[o("素材ID"),"：",e._matched_material.modelId]})]})]})}),(0,r.jsx)("div",{className:a.divider,children:(0,r.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some(function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)})&&!t.userStore.aihouse&&"C00002170"!==t.userStore.userInfo.tenantId&&(0,r.jsx)(Je.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){m.current.onModal()},children:o("AI搭柜")})})}),(0,r.jsxs)("div",{className:a.tabContainer,children:[(0,r.jsx)("div",{className:"".concat(a.tabItem," ").concat("套系素材"===d?a.active:""),onClick:function(){return f("套系素材")},children:o("套系素材")}),(0,r.jsx)(rt.If,{condition:ot.x.instance.hasPermission(it.J.SERIES.CLOUD_MATERIALS),children:(0,r.jsx)("div",{className:"".concat(a.tabItem," ").concat("云素材"===d?a.active:""),onClick:function(){return f("云素材")},children:o("云素材")})})]}),(0,r.jsx)("div",{className:a.goodsInfo,children:h?(0,r.jsxs)("div",{className:a.loading,children:[(0,r.jsx)(Qe.A,{size:"large"})," "]}):c&&c.length>0?c.map(function(n,i){return(0,r.jsxs)("div",{className:a.goodsItem,onClick:function(){return ct(function(){var r,o,a,l,c,s,u,f,p;return dt(this,function(h){switch(h.label){case 0:return e.locked?[2]:(t.designStore.setSelectedIndex(i),"套系素材"!==d?[3,1]:(D.nb.DispatchEvent(D.n0.ReplaceMaterial,n),[3,4]));case 1:return a=null,[4,(0,nt.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(l=h.sent())||null===(o=l.result)||void 0===o||null===(r=o.result)||void 0===r?void 0:r[0])&&(a=null==l||null===(s=l.result)||void 0===s||null===(c=s.result)||void 0===c?void 0:c[0]),[4,(0,et.h)(n.materialId)];case 3:u=h.sent(),a&&(f={modelId:a.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:L.L4+n.imageUrl,name:a.MaterialName,originalLength:a.PICLength,originalWidth:a.PICWidth,originalHeight:a.PICHeight,length:a.PICLength,width:a.PICWidth,height:a.PICHeight,modelLoc:e.modelLoc,modelFlag:a.ModelFlag.toString(),topViewImage:u,figureElement:e},p=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){st(n,e,t[e])})}return n}({},n,f),D.nb.DispatchEvent(D.n0.ReplaceMaterial,p)),h.label=4;case 4:return[2]}})})()},children:[i===t.designStore.selectedIndex&&(0,r.jsx)("div",{className:a.selectIcon}),(0,r.jsx)("img",{src:n.imageUrl,alt:""}),(0,r.jsx)("div",{className:a.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,r.jsx)("div",{className:a.sizeInfo,style:{color:"rgba(255, 255, 255, 0.6)"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},i)}):(0,r.jsxs)("div",{className:a.noData,children:[(0,r.jsx)("img",{className:"emptyImg",src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("span",{children:o("暂无可用素材")})]})}),(0,r.jsx)(tt.A,{onParams:function(){},selectedFigureElement:e,ref:m})]})})}),pt=t(44544),ht=t(65640);function xt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function mt(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function gt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function bt(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){gt(n,e,t[e])})}return n}function vt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||wt(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yt(n){return function(n){if(Array.isArray(n))return xt(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||wt(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wt(n,e){if(n){if("string"==typeof n)return xt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?xt(n,e):void 0}}function jt(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var _t={popupType:"Layout",sceneMode:"2D",prev3DSceneMode:"3D_FirstPerson"};function St(n){return bt({icon:"",label:"",onClick:void 0,display:void 0,loading:!1,opacity:1,submit:!1},n)}var kt=(0,z.observer)(function(n){n.updateKey;var e=(0,i.B)().t,t=I().styles,o=((0,G.Zp)(),(0,O.P)()),a=(0,M.useRef)(null),l=o.homeStore,c=l.viewMode,s=(l.drawPictureMode,l.roomEntities),u=l.isdrawPicture,d=(l.guideMapCurrentRoom,l.setShowDreamerPopup,l.setIsdrawPicture),f=(l.setDrawPictureMode,l.setViewMode),p=l.setSelectEntity,h=l.setShowReplace,x=(l.setShowSaveLayoutSchemeDialog,vt((0,M.useState)(!1),2)),m=x[0],g=x[1],b=vt((0,M.useState)(!1),2),v=b[0],y=b[1],w=vt((0,M.useState)(c),2),j=w[0],_=w[1],S=vt((0,M.useState)(_t.popupType),2),k=S[0],A=S[1],C=vt((0,M.useState)(W.qB.Figure2D),2),z=(C[0],C[1]),tn=vt((0,M.useState)(null),2),rn=tn[0],on=tn[1],an=vt((0,M.useState)([]),2),ln=an[0],cn=an[1],sn=vt((0,M.useState)([{value:"Layout",label:e("推荐布局")},{value:"material",label:e("编辑布局")}]),2),un=sn[0],dn=sn[1],fn="PadLeftPanel",pn=D.nb.instance,hn=pn.layout_container,xn=(pn.scene3D,vt((0,M.useState)(!1),2)),mn=xn[0],gn=xn[1],bn=window.innerWidth<=460,vn={attribute:[{value:"attribute",label:e("属性")}],sizeEditor:[{value:"sizeEditor",label:e("尺寸")}],SpaceAreaAttribute:[{value:"SpaceAreaAttribute",label:e("属性")}]},yn=(W.qB.Figure2D,e(bn?"布局":"布局模式"),W.qB.Texture,e(bn?"风格":"风格模式"),e("漫游"),e("鸟瞰"),function(){var n,t,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(pn.layout_container)if(vn[k])un.length=0,(n=un).push.apply(n,yt(vn[k]));else if("2D"===j){var i,o;if(ht.log(pn.layout_container.drawing_figure_mode),pn.layout_container.drawing_figure_mode==W.qB.Figure2D)un.length=0,(i=un).push.apply(i,[{value:"Layout",label:e("推荐布局")},{value:"material",label:e("编辑布局")}]);else un.length=0,(o=un).push.apply(o,[{value:"Matching",label:e("推荐风格")}])}else{var a;un.length=0,(a=un).push.apply(a,[{value:"replace",label:e("换搭素材")}])}if(!rn){var l=un.findIndex(function(n){return"replace"===n.value});l>=0&&un.splice(l,1)}r&&(un.find(function(n){return n.value===k})||_n((null===(t=un[0])||void 0===t?void 0:t.value)||""));dn(yt(un))});(0,M.useEffect)(function(){wn()},[pn.layout_container.drawing_figure_mode]);var wn=function(){"2D"===j?cn([St({label:e("上一步"),display:pn.layout_container.drawing_figure_mode===W.qB.Figure2D?"none":"block",onClick:function(){var n=pn.layout_container.drawing_figure_mode;switch(n){case W.qB.Figure2D:break;case W.qB.Texture:Sn(W.qB.Figure2D);_n("Layout");break;default:ht.error("模式错误！currentMode：",n)}}}),St({label:"2D_modes"}),St({label:e("下一步"),onClick:function(){return(n=function(){var n;return jt(this,function(e){switch(e.label){case 0:switch(n=pn.layout_container.drawing_figure_mode){case W.qB.Figure2D:return[3,1];case W.qB.Texture:return[3,2]}return[3,4];case 1:return Q.y.instance.autoApplySeries(),Sn(W.qB.Texture),[3,5];case 2:return[4,Q.y.instance.autoApplySeries()];case 3:return e.sent(),_(_t.prev3DSceneMode),[3,5];case 4:ht.error("模式错误！currentMode：",n),e.label=5;case 5:return[2]}})},function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){mt(o,r,i,a,l,"next",n)}function l(n){mt(o,r,i,a,l,"throw",n)}a(void 0)})})();var n}})]):"3D"===j?cn([St({label:e("返回布局"),onClick:function(){_("2D")}})]):"3D_FirstPerson"===j&&cn([St({label:e("上一步"),onClick:function(){_("2D")}})]),u&&jn()},jn=function(){cn([])};(0,M.useEffect)(function(){wn()},[j,u]);var _n=function(n){_t.popupType=n,A(n)},Sn=function(n){pn.layout_container.drawing_figure_mode=n,pn.update(),z(n),yn(!0)};(0,M.useEffect)(function(){mn&&(ht.log("清除灯光效果"),Bn.Y.cleanLight(),gn(!1))},[mn]);var kn=function(n,e){n&&n===rn||(hn.drawing_figure_mode===W.qB.Figure2D&&"2D"===j?"Furniture"===e?"Layout"===_t.popupType&&_n("material"):_n("Layout"):"Furniture"===e||"Door"===e?n&&_n("replace"):n&&"replace"==_t.popupType?_n("replace"):_n("Matching"),on(n))};(0,M.useEffect)(function(){return yn(),wn(),D.nb.on_M(U.U.FigureElementSelected,fn,function(n){pt.C.get_polygon_type(null==n?void 0:n.rect)==W.Fz.Door?kn(n,W.Fz.Door):kn(n,W.Fz.Furniture)}),D.nb.on_M(U.U.SelectingTarget,fn,function(n,e,t){if("2D"==j){var r=n||null;if(null==r?void 0:r.figure_element)kn(r.figure_element,"Furniture");else if((null==r?void 0:r.type)==W.Fz.RoomArea){var i=r._room;(null==i?void 0:i.tile)?kn(i.tile,"RoomArea"):kn(null,"RoomArea")}else if((null==r?void 0:r.type)==W.Fz.Door){var o=null==r?void 0:r._win_figure_element;kn(o,W.Fz.Door)}else kn(null,"RoomArea");p(r),r||h(!1);var a=r;if(a&&(null==a?void 0:a._room))Q.y.instance.current_rooms=[a._room],Q.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:!0});else if(hn._rooms){var l=hn._rooms.filter(function(n){return n&&n.furnitureList.length>0});Q.y.instance.current_rooms=l,Q.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:null})}}else if((null==n?void 0:n.type)==W.Fz.RoomArea){var c=n._room;(null==c?void 0:c.tile)&&kn(c.tile,"RoomArea")}}),D.nb.on_M(Z.$.showPopup,fn,function(n){_n(n)}),D.nb.on_M(U.U.SceneModeChanged,fn,function(n){_(n)}),function(){D.nb.off_M_All({object_id:fn})}},[j]),(0,M.useEffect)(function(){"2D"===c&&_("2D")},[c]),(0,M.useEffect)(function(){!function(n){var e=D.nb.instance.layout_container,t=D.nb.instance.scene3D;if("2D"===n)t&&t.stopRender(),window.innerWidth<.8*window.innerHeight?(nn.f.focusCenterByWholeBox(e,.7),D.nb.instance.update()):(nn.f.focusCenterByWholeBox(e,.6),D.nb.instance.update()),D.nb.emit_M(q.z.showLight3DViewer,!1),f("2D");else if("3D"===n)D.nb.DispatchEvent(D.n0.Match3dPreviewMaterials,null),D.nb.emit_M(q.z.showLight3DViewer,!0),t.setCemeraMode(en.I5.Perspective),f("3D"),D.nb.DispatchEvent(D.n0.cleanSelect,null),t&&(t.startRender(),D.nb.emit_M(U.U.Scene3DUpdated,!1));else if("3D_FirstPerson"===n){if(D.nb.DispatchEvent(D.n0.Match3dPreviewMaterials,null),D.nb.emit_M(q.z.showLight3DViewer,!0),t.setCemeraMode(en.I5.FirstPerson),"2D"==c){var r=e._room_entities.reduce(function(n,e){return n?e._area>n._area?e:n:e},null);if(r){var i,a=D.nb.instance.layout_container;a?(0==r._view_cameras.length&&On.q.updateViewCameraEntities(a,null,{methods:2}),t.active_controls.bindViewEntity(r._view_cameras[0]),o.homeStore.setCurrentViewCameras(r._view_cameras)):t.setCenter((null==r||null===(i=r._main_rect)||void 0===i?void 0:i.rect_center)||new H.Pq0(0,0,0)),t.update()}else{var l,u;t.setCenter((null===(u=s[0])||void 0===u||null===(l=u._main_rect)||void 0===l?void 0:l.rect_center)||new H.Pq0(0,0,0))}}f("3D_FirstPerson"),t&&(t.startRender(),D.nb.emit_M(U.U.Scene3DUpdated,!1))}n&&"2D"!==n&&(_t.prev3DSceneMode=n),_t.sceneMode=n,yn(!0)}(j)},[j]),(0,M.useEffect)(function(){d(!1),gn(!0)},[c]);var An=!m,In=(window.innerWidth,window.innerHeight,[{key:"layout",label:"❶选布局"},{key:"series",label:"❷选套系"},{key:"effect",label:"❸看效果"}]),zn=vt((0,M.useState)("layout"),2),Mn=zn[0],Dn=zn[1];return(0,M.useEffect)(function(){pn.layout_container.drawing_figure_mode===W.qB.Figure2D?Dn("layout"):Dn("series")},[pn.layout_container.drawing_figure_mode]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.navigation+" topNavigation",children:[L.um?"true":"false",v&&(0,r.jsx)("div",{className:"",style:{top:50,right:12,position:"fixed",zIndex:"999",background:"#fff",padding:"10px"},children:(0,r.jsx)(J.A,{onClose:function(){y(!1)}})})]}),u&&(0,r.jsx)("div",{children:(0,r.jsx)(Pe,{})}),(0,r.jsx)("div",{children:(0,r.jsx)(V.A,{rightOffset:12,topOffset:20})}),(0,r.jsx)("div",{className:t.sideToolbarContainer+" sideToolbar "+("2D"!=j?"is_3d_mode":""),children:(0,r.jsx)($.A,{setSceneMode:_})}),"2D"==j&&(0,r.jsxs)("div",{id:"pad_left_panel",className:t.leftPanelRoot+" leftPanelRoot "+(An?"":"panel_hide"),children:[An&&(0,r.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return g(!0)}}),An&&un.length>1&&(0,r.jsx)("div",{className:t.tabBar,children:(0,r.jsx)(T.A,{value:k,onChange:function(n){_n(n)},block:!0,options:un})}),(0,r.jsxs)("div",{className:t.popupContainer+" side_pannel",children:[(0,r.jsx)("div",{className:t.listContainer,style:{display:"Layout"===k?"block":"none"},children:(0,r.jsx)(E.A,{width:400,showSchemeName:!1,isLightMobile:!0})}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"Matching"===k?"block":"none"},children:[" ",(0,r.jsx)(F.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"material"===k?"block":"none"},children:[" ",(0,r.jsx)(N.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"attribute"===k?"block":"none"},children:[" ",(0,r.jsx)(P.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"replace"===k?"block":"none"},children:[" ",(0,r.jsx)(B.A,{selectedFigureElement:rn})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"searchMaterial"===k?"block":"none"},children:[" ",(0,r.jsx)(R.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"sizeEditor"===k?"block":"none"},children:[" ",(0,r.jsx)(X.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"SpaceAreaAttribute"===k?"block":"none"},children:[" ",(0,r.jsx)(Cn,{})," "]})]})]}),un.length>0&&"2D"==j&&(0,r.jsx)("div",{className:t.collapseBtn+(An?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){g(!m)}}),(0,r.jsx)(re.A,{in:"2D"!=c&&!u&&rn,timeout:300,classNames:{enter:"fadeEnter",enterActive:"fadeEnterActive",exit:"fadeExit",exitActive:"fadeExitActive"},unmountOnExit:!0,children:(0,r.jsx)("div",{id:"pad_left_panel",className:"".concat(t.leftPanelRoot," ").concat("2D"!=c?t.materialReplace:""),children:(0,r.jsx)(ft,{selectedFigureElement:rn})})}),(0,r.jsx)(K.A,{}),u&&(0,r.jsx)(je,{ref:a}),(0,r.jsxs)("div",{className:t.bottomButtons+" bottomBtns"+(An?" showLeftPanel":""),children:[ln&&ln.length>0&&ln.map(function(n,e){return"2D_modes"===n.label?(0,r.jsx)("div",{className:"btnForModeBar",onClick:null==n?void 0:n.onClick,style:bt({opacity:n.opacity},void 0!==n.display?{display:n.display}:{}),children:In.map(function(n){return(0,r.jsx)("span",{className:"modeBarItem ".concat(Mn===n.key?"active":""),children:n.label},n.key)})},"bottomBtn_"+e):(0,r.jsx)("div",{className:"btn"+("2D"!==j?" blackColor":"")+(n.submit?" submit":""),onClick:null==n?void 0:n.onClick,style:bt({opacity:n.opacity},void 0!==n.display?{display:n.display}:{}),children:null==n?void 0:n.label},"bottomBtn_"+e)}),(0,r.jsx)(rt.If,{condition:"3D_FirstPerson"==c&&!o.homeStore.isdrawPicture,children:(0,r.jsx)(fe,{})})]}),(0,r.jsx)(Y.A,{}),u&&(0,r.jsx)(Pn,{})]})})}}]);