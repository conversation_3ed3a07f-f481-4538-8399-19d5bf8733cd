"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[4421],{25781:function(n,e,t){var r=t(13274),i=t(41594),o=t(69802),a=t(90803),l=t(14181),c=t(27347),s=t(99030),u=t(9003),d=t(88934),f=t(61928),p=t(15696),h=t(62634);function m(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function x(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function g(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){x(o,r,i,a,l,"next",n)}function l(n){x(o,r,i,a,l,"throw",n)}a(void 0)})}}function v(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function b(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){v(n,e,t[e])})}return n}function y(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return m(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return m(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}e.A=(0,p.observer)(function(){(0,o.B)().t;var n=(0,a.A)().styles,e=(0,u.P)(),t=y((0,i.useState)({left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"}),2),p=t[0],m=t[1],x=c.nb.instance.layout_container,v=function(){return g(function(){var n,t,r,i,o,a,s,u;return w(this,function(d){switch(d.label){case 0:return i=c.nb.instance.scene3D,o=(null===(n=e.homeStore.guideMapCurrentRoom)||void 0===n?void 0:n.name)||(null===(t=x._selected_room)||void 0===t?void 0:t.roomname)||"",a=e.homeStore.guideMapCurrentRoom||x._selected_room||null,s=3,a&&a.furnitureList&&a.furnitureList.length>0&&(s=0),u=!1,(null===(r=i.selection_box)||void 0===r?void 0:r.visible)&&(u=i.selection_box.visible,i.selection_box.visible=!1),i.update(),[4,l.w.instance.submitAiDraw({room_name:o,roomUid:(null==a?void 0:a.uid)||"",aiModel:s},e.homeStore.aspectRatioMode)];case 1:return d.sent(),u&&(i.selection_box.visible=u),[2]}})})()};return(0,i.useEffect)(function(){c.nb.on_M(s.r.AiDrawingCapture,"AiDrawingGallery",function(){var n=c.nb.instance.scene3D,t=(c.nb.instance.layout_container,{left:"0",top:"0",width:"100vw",height:"100vh",transition:"none"}),r={left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"};if(g(function(){var n;return w(this,function(t){switch(t.label){case 0:return n=null,h.A.loading("提交渲染中...",0),x._layout_scheme_id?[3,1]:(c.nb.DispatchEvent(c.n0.autoSave,null),n&&clearInterval(n),n=setInterval(function(){return g(function(){return w(this,function(e){switch(e.label){case 0:return x._layout_scheme_id?(clearInterval(n),n=null,[4,v()]):[3,2];case 1:e.sent(),h.A.destroy(),e.label=2;case 2:return[2]}})})()},500),[2]);case 1:return[4,v()];case 2:t.sent(),t.label=3;case 3:return[4,e.homeStore.query_genCount()];case 4:return t.sent(),h.A.destroy(),h.A.success("提交AI绘图成功！"),[2]}})})(),n.parent_div){var i=n.parent_div.offsetLeft,o=n.parent_div.offsetTop,a=n.parent_div.offsetWidth,l=n.parent_div.offsetHeight;t.left=i+"px",t.top=o+"px",t.width=a+"px",t.height=l+"px"}var s=document.getElementById("aidrawing_tuku_btn");if(s&&s.getBoundingClientRect){var u=s.getBoundingClientRect();if(u){var d=u.left,f=u.top;d+=u.width/2,f+=u.height/2,r.left=d+"px",r.top=f+"px"}}m(b({},t)),setTimeout(function(){m(b({},r))},300)}),c.nb.on_M(d.U.diffuseImage,"aiDrawingGallery",function(n){var t;f.K.diffuseImageSDK(n,null===(t=e.homeStore.guideMapCurrentRoom)||void 0===t?void 0:t.roomname)})},[]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:n.photo_capture_div+" photo_capture_div",style:p})})})},36906:function(n,e,t){t.d(e,{G:function(){return s}});var r=t(64186);function i(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function o(n){return function(){var e=this,t=arguments;return new Promise(function(r,o){var a=n.apply(e,t);function l(n){i(a,r,o,l,c,"next",n)}function c(n){i(a,r,o,l,c,"throw",n)}l(void 0)})}}function a(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function l(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){a(n,e,t[e])})}return n}function c(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}function s(n){return o(function(){return c(this,function(e){switch(e.label){case 0:return[4,(0,r.Ap)({method:"post",url:"api/njvr/layoutMetaImageSize/insert",data:l({},n),timeout:6e4}).catch(function(n){return null})];case 1:return[2,e.sent()]}})})()}},44421:function(n,e,t){t.r(e),t.d(e,{default:function(){return zu}});var r=t(13274),i=t(69802),o=t(23825),a=t(8268);function l(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function c(){var n=l(["\n      width:100%;\n      height:calc(var(--vh, 1vh) * 100);\n      position: relative;\n      .custom-keyboard {\n        position: fixed;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        background: #fff;\n        border-top: 1px solid #ccc;\n        padding: 10px;\n        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);\n      }\n\n      .keypad {\n          display: flex;\n          flex-wrap: wrap;\n      }\n\n      .keypad button {\n          flex: 1 0 30%; /* 控制按钮大小 */\n          margin: 5px;\n          padding: 15px;\n          font-size: 18px;\n          cursor: pointer;\n      }\n      \n    "]);return c=function(){return n},n}function s(){var n=l(["\n      position: fixed;\n      top: 0;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n      input {\n        z-index : 3;\n      }\n    "]);return s=function(){return n},n}function u(){var n=l(["\n      position: absolute;\n      top: 52px;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      padding: 12px 20px 20px 20px;\n      background-color: #fff;\n      display: flex;\n      gap: 12px;\n      flex-shrink: 0;\n      height: calc(100% - 52px);\n      width: 100%;\n      box-sizing: border-box;\n    "]);return u=function(){return n},n}function d(){var n=l(["\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n      padding: 4px 4px 12px 4px;\n      border-radius: 12px;\n      align-items: center;\n      z-index: 1;\n      background-color: #fff;\n      overflow: hidden;\n      width: 100%;\n      height: 100%;\n      box-sizing: border-box;\n      position: relative;\n      .scene3d_container{\n        width: calc(100% - 8px);\n        height: calc(100% - 48px);\n        border: none;\n        position: relative;\n        .view_select_box{\n          position: absolute;\n          bottom: 18px;\n          left: 50%;\n          max-width: 70%;\n          min-width: 300px;\n          transform: translateX(-50%);\n          overflow: hidden;\n          z-index: 100;\n          border-radius: 8px;\n          backdrop-filter: blur(10px);\n        }\n      }\n      .canvas3d_btns{\n        display: flex;\n        gap: 12px;\n        align-items: center;\n        .canvas3d_btn{\n          display: flex;\n          padding: 6px 16px;\n          justify-content: center;\n          align-items: center;\n          gap: 8px;\n          border-radius: 20px;\n          background: linear-gradient(91deg, #BA63F0 -0.97%, #5C42FB 100%);\n          color: #fff;\n          text-align: center;\n          font-size: 14px;\n          font-style: normal;\n          font-weight: 600;\n          line-height: 24px;\n        }\n      }\n      .house_match_container{\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        z-index: 999;\n      }\n    "]);return d=function(){return n},n}function f(){var n=l(['\n        position: relative;\n        overflow: hidden;\n        background-color: #eaeaea;\n        background-image:\n         -webkit-linear-gradient(180deg, #e2e2e2 1px, transparent 1px) ,\n          -webkit-linear-gradient(90deg, #e2e2e2 1px, transparent 1px);\n        background-size: 50px 50px;\n        background-position: calc(50% + 25px) 0%;\n        border-radius: 12px;\n        /* &.left_panel_layout {\n          height : calc(100%);\n        } */\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          width: 100%;\n          height: 100%;\n          touch-action: none;\n          &.canvas_drawing {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;\n          }\n        }\n        .layoutBtn {\n          position: absolute;\n          top: 12px;\n          left: 12px;\n          z-index: 99;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          height: 24px;\n          padding: 2px 11px;\n          border-radius: 4px;\n          background: rgba(0, 0, 0, 0.50);\n\n          color: #FFF;\n          font-family: "PingFang SC";\n          font-size: 12px;\n          font-style: normal;\n          font-weight: 400;\n          line-height: 166.7%; /* 20.004px */\n        }\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ',"\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n        }\n    "]);return f=function(){return n},n}function p(){var n=l(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return p=function(){return n},n}function h(){var n=l(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return h=function(){return n},n}function m(){var n=l(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return m=function(){return n},n}function x(){var n=l(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return x=function(){return n},n}function g(){var n=l(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return g=function(){return n},n}function v(){var n=l(["\n      position: fixed;\n      top: 68px;\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n      // 竖屏样式\n\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return v=function(){return n},n}function b(){var n=l(["\n      position: fixed;\n      top: 120px; /* Adjust this value to position it below the focusIcon */\n      right: 12px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #fff;\n      box-shadow: 0px 6px 20px 0px #00000014;\n      font-size: 30px;\n\n      @media screen and (max-width: 450px) {\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      @media screen and (orientation: landscape) {\n        width: 40px;\n        height: 40px;\n        font-size: 25px;\n      }\n    "]);return b=function(){return n},n}function y(){var n=l(["\n      position: absolute;\n      top: 0px;\n      left: 0;\n      width: 100%;\n      height: 45px;\n      z-index: 999;\n      background-color: #fff;\n    "]);return y=function(){return n},n}function w(){var n=l(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 999;\n    "]);return w=function(){return n},n}function j(){var n=l(["\n      padding: 20px;\n      position:absolute;\n      left: 0;\n      top: 0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n      background: #f6f7f9;\n    "]);return j=function(){return n},n}function S(){var n=l(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 99;\n    "]);return S=function(){return n},n}function _(){var n=l(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 999;\n      background: rgba(0, 0, 0, 0.50);\n    "]);return _=function(){return n},n}function k(){var n=l(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 999;\n      background: rgba(0, 0, 0, 0.5);\n    "]);return k=function(){return n},n}var I=(0,a.rU)(function(n){var e=n.css;return{root:e(c()),content:e(s()),seleceView:e(u()),canvas3d:e(d()),canvas_pannel:e(f(),(0,o.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:e(p()),backBtn:e(h()),forwardBtn:e(m()),schemeNameSpan:e(x()),overlay:e(g()),focusIcon:e(v()),multiSchemeIcon:e(b()),RoomAreaBtns:e(y()),aiDraw:e(w()),mobile_atlas_container:e(j()),selectViewContainer:e(S()),myCase:e(_()),houseDetail:e(k())}}),C=t(15696),A=t(49450),z=t(62634),N=t(37112),P=t(41594),O=t.n(P),D=t(27347),E=t(98612),M=t(9003),F=t(88934),B=t(78644);function T(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function L(){var n=T(["\n      /* position: fixed;\n      top: 0px;\n      background-color: #fff;\n      width: 100%;\n      height: 56px;\n\n      display: flex;\n      padding: 0 16px;\n      justify-content: space-between;\n      align-items: center;\n      z-index: 9;\n      max-width: 1174px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 46px;\n      } */\n    "]);return L=function(){return n},n}function R(){var n=T(["\n      position: fixed;\n      top: 12px;\n      left: 12px;\n      color: #282828;\n      font-size: 16px;\n      font-weight: 600;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 12px;\n      }\n      z-index: 9;\n    "]);return R=function(){return n},n}function U(){var n=T(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:40px;\n      color:#333;\n    "]);return U=function(){return n},n}function H(){var n=T(["\n      width:100%;\n      font-size:16px;\n      line-height:40px;\n      text-align:center;\n    "]);return H=function(){return n},n}var G=(0,a.rU)(function(n){var e=n.css;return{navigation:e(L()),backBtn:e(R()),forwardBtn:e(U()),schemeNameSpan:e(H())}});function W(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Y(){var n=W(["\n      width: 180px;\n      position: fixed;\n      right: 12px;\n      top: 12px;\n      z-index: 9;\n      .ant-segmented\n      {\n        /* background-color: #EAEBEA; */\n        /* color: #282828 !important; */\n        @media screen and (max-width: 450px) {\n          height: 28px;\n        }\n      }\n      .ant-segmented-item-label\n      {\n        @media screen and (max-width: 450px) {\n          height: 28px;\n          line-height: 28px !important;\n          font-size: 12px !important;\n        }\n      }\n    "]);return Y=function(){return n},n}function X(){var n=W(["\n      right: 50px;\n    "]);return X=function(){return n},n}function V(){var n=W(["\n      width: 50px;\n      text-align:center;\n      line-height:40px;\n      font-size:16px;\n      color : #777;\n      background-color: #fff;\n      border: 1px solid #fff;\n      &.active {\n        background: #147FFA;\n        color: #fff;\n      }\n\n    "]);return V=function(){return n},n}var Z=(0,a.rU)(function(n){var e=n.css;return{root:e(Y()),mobile_root:e(X()),state_btn:e(V())}});function q(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function J(){var n=q(["\n      width:100%;\n      height:100vh;\n    "]);return J=function(){return n},n}function K(){var n=q(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return K=function(){return n},n}function Q(){var n=q(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return Q=function(){return n},n}function $(){var n=q(["\n        position: absolute;\n        left:-100px;\n        top: -100px;\n        background-color: #EAEAEB;\n        width : calc(100% + 200px);\n        height : calc(100% + 200px);\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return $=function(){return n},n}function nn(){var n=q(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return nn=function(){return n},n}function en(){var n=q(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return en=function(){return n},n}function tn(){var n=q(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return tn=function(){return n},n}function rn(){var n=q(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return rn=function(){return n},n}var on=(0,a.rU)(function(n){var e=n.css;return{root:e(J()),content:e(K()),canvas3d:e(Q()),canvas_pannel:e($(),(0,o.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:e(nn()),backBtn:e(en()),forwardBtn:e(tn()),schemeNameSpan:e(rn())}});function an(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ln(){var n=an(["\n      position:fixed;\n      left:0;\n      bottom:0px;\n      width:100%;\n      overflow: hidden;\n      background-color: #fff;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      z-index: 99;\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: -1px !important;\n        top: 52px !important;\n        bottom: 0 !important;\n        right: auto !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        width: auto;\n        border-radius: 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n      }\n    "]);return ln=function(){return n},n}function cn(){var n=an(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n    "]);return cn=function(){return n},n}function sn(){var n=an(["\n      height:100%;\n      width:100%;\n    "]);return sn=function(){return n},n}function un(){var n=an(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return un=function(){return n},n}var dn=(0,a.rU)(function(n){var e=n.css;return{root:e(ln()),topTitle:e(cn()),listContainer:e(sn()),open:e(un())}}),fn=t(45599),pn=t(7448),hn=t(16805);function mn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function xn(){var n=mn(["\n      background: #FFF;\n      height: 100%;\n      z-index: 999;\n      padding: 0 0 0 16px;\n      /* overflow: hidden; */\n      position: fixed;\n      top: 48px;\n      left: 0;\n    "]);return xn=function(){return n},n}function gn(){var n=mn(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    "]);return gn=function(){return n},n}function vn(){var n=mn(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 16px 0px;\n    "]);return vn=function(){return n},n}function bn(){var n=mn(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      min-width: 70%;\n      height: 32px;\n      border: none;\n      margin: 16px 0 0 0px;\n      padding-left: 30px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return bn=function(){return n},n}function yn(){var n=mn(["\n      position: absolute;\n      top: 113px;\n      left: 7px;\n    "]);return yn=function(){return n},n}function wn(){var n=mn(["\n      position: absolute;\n      top: 97px;\n      right: 38%;\n      cursor: pointer;\n    "]);return wn=function(){return n},n}function jn(){var n=mn(["\n      width: 24%;\n      margin: 16px 8px 0px 0;\n      display: flex;\n      justify-items: baseline;\n      align-items: center;\n      a {\n        color: #ffffff;\n        padding: 5px;\n        line-height: 23px;\n        height: 34px;\n      }\n      a:hover {\n        color: #3D9EFF;\n        border-radius: 10px;\n        background: #BFD8FF14;\n        transition: all .3s;\n      }\n    "]);return jn=function(){return n},n}function Sn(){var n=mn(["\n      display: flex;\n      justify-content: space-between;\n      padding-right: 28px;\n      margin-bottom: 16px;\n    "]);return Sn=function(){return n},n}function _n(){var n=mn(["\n      height: auto;\n      @media screen and (orientation: landscape) {\n        padding-bottom: 16px;\n      }\n    "]);return _n=function(){return n},n}function kn(){var n=mn(["\n      @media screen and (orientation: landscape) {\n        width: 224px;\n      }\n    "]);return kn=function(){return n},n}function In(){var n=mn(["\n      width: 180px;\n      height: 28px;\n      display: flex;\n      justify-content: center;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      font-weight: 600;\n      color: #959598;\n      z-index: 9;\n      align-items: center;\n      width: 100%;\n      padding: 0px 16px;\n      margin: 16px 0px;\n      position: relative;\n      div{\n        margin-right: 8px;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: auto;\n        font-size: 16px;\n        top: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        font-size: 16px;\n        justify-content: space-between;\n      }\n      @media screen and (orientation: portrait) {\n        top : 0px;\n        left: 12px;\n        width: 50%;\n        justify-content: start;\n      }\n      .checked:after\n      {\n        content: '';\n        display: block;\n        width: 20px;\n        height: 3px;\n        border-radius: 10px;\n        background-color: #282828;\n        margin-top: 5px;\n        margin-left: 23px;\n        position: absolute;\n      }\n    "]);return In=function(){return n},n}function Cn(){var n=mn(["\n      overflow-y: hidden;\n      width:100%;\n      display:flex;\n      overflow-x: auto;\n      height: 230px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 190px);\n        width: 100%;\n        display: block;\n        overflow-y: auto;\n      }\n      @media screen and (orientation: portrait) {\n        margin-top: 10px;\n      }\n    "]);return Cn=function(){return n},n}function An(){var n=mn(["\n      margin-top: 50px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 80px) !important;\n      }\n    "]);return An=function(){return n},n}function zn(){var n=mn(["\n      float:left;\n      @media screen and (orientation: landscape) {\n        float: none;\n      }\n    "]);return zn=function(){return n},n}function Nn(){var n=mn(["\n      width: 270px;\n      height: 170px;\n      box-sizing: border-box;\n      position: relative;\n      margin:10px;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin: 0;\n        padding: 0 12px;\n        height: 137px;\n        margin-bottom: 40px;\n      }\n    "]);return Nn=function(){return n},n}function Pn(){var n=mn(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin: 10px 0 4px 0;\n      @media screen and (orientation: landscape) {\n        margin-top: 4px;\n      }\n    "]);return Pn=function(){return n},n}function On(){var n=mn(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 78%;\n    "]);return On=function(){return n},n}function Dn(){var n=mn(["\n      color: #5B5E60;\n      border-radius: 12px;\n      border: 1px solid #0000000F;\n      padding: 4px 8px;\n    "]);return Dn=function(){return n},n}function En(){var n=mn(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n    "]);return En=function(){return n},n}function Mn(){var n=mn(["\n      overflow-y: hidden !important;\n    "]);return Mn=function(){return n},n}function Fn(){var n=mn(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n    "]);return Fn=function(){return n},n}function Bn(){var n=mn(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return Bn=function(){return n},n}function Tn(){var n=mn(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return Tn=function(){return n},n}function Ln(){var n=mn(["\n      border-radius: 4px;\n      height: 100%;\n      overflow: hidden;\n      @media screen and (orientation: landscape) {\n      }\n      img {\n        transition: all .5s;\n        width: 100%;\n        border-radius: 4px;\n      }\n    "]);return Ln=function(){return n},n}function Rn(){var n=mn(["\n      border: 2px solid #9242FB !important;\n    "]);return Rn=function(){return n},n}function Un(){var n=mn(["\n      position: absolute;\n      top: 4px;\n      right: 16px;\n      border-radius: 4px;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n    "]);return Un=function(){return n},n}function Hn(){var n=mn(["\n      \n      \n    "]);return Hn=function(){return n},n}function Gn(){var n=mn(["\n      width: 560px !important;\n      @media screen and (max-width: 450px) {\n        width: 300px !important;\n      }\n      @media screen and (orientation: landscape) {\n        width: 900px !important;\n      }\n    "]);return Gn=function(){return n},n}function Wn(){var n=mn(["\n      padding: 20px;\n      @media screen and (max-width: 450px) {\n        padding: 14px;\n      }\n      @media screen and (orientation: landscape) {\n        display: flex;\n        .ant-carousel {\n          max-width: 420px;\n          margin: auto 0;\n          margin-right: 40px;\n        }\n      }\n    "]);return Wn=function(){return n},n}function Yn(){var n=mn(["\n      .swj-baseComponent-Containersbox-title{\n        background-color: #fff !important;\n      }\n      .swj-baseComponent-Containersbox-body\n      {\n        > div:first-child {\n          height: 760px !important; /* 只影响第一个子 div */\n          @media screen and (max-width: 450px) {\n            height: 500px !important;\n          }\n          @media screen and (orientation: landscape) {\n            height: 475px !important;\n            margin-top: -1px;\n          }\n        }\n       \n      }\n      \n    "]);return Yn=function(){return n},n}function Xn(){var n=mn(["\n      display: flex;\n      flex-wrap: wrap;\n      gap: 6px;\n      height: 760px;\n      overflow-y: auto;\n      max-height: 320px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 205px;\n        gap: 12px;\n      }\n      @media screen and (orientation: landscape) {\n        gap: 18px;\n      }\n      ::-webkit-scrollbar-thumb\n      {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    "]);return Xn=function(){return n},n}function Vn(){var n=mn(["\n      width: 100%;\n      \n      height: 290px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 300px;\n        height: 160px;\n      }\n      @media screen and (orientation: landscape) {\n        border-radius: 8px;\n        height: 320px;\n        min-width: 520px;\n      }\n    "]);return Vn=function(){return n},n}function Zn(){var n=mn(["\n      display: flex;\n      justify-content: space-between;\n      margin-top: 10px;\n      @media screen and (orientation: landscape) {\n        margin-top: 20px;\n      }\n      button{\n        width: 50%;\n        height: 32px;\n        margin-right: 10px;\n      }\n      .leftBtn{\n        color: #000;\n        border-radius: 10px;\n        border: none;\n        border-radius: 6px;\n        background: #EAEAEB;\n      }\n      .rightBtn{\n        border-radius: 6px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #fff;\n      }\n    "]);return Zn=function(){return n},n}var qn=(0,a.rU)(function(n){var e=n.css;return{container:e(xn()),titleContainer:e(gn()),title:e(vn()),container_input:e(bn()),Icon:e(yn()),IconDelete:e(wn()),selectInfo:e(jn()),findInfo:e(Sn()),roomListBar:e(_n()),bottomPanel:e(kn()),topSelect:e(In()),container_listInfo:e(Cn()),type:e(An()),container_box:e(zn()),container_data:e(Nn()),textInfo:e(Pn()),container_title:e(On()),container_desc:e(Dn()),seriesStyle:e(En()),noScroll:e(Mn()),side_list:e(Fn()),line:e(Bn()),emptyInfo:e(Tn()),Popover_hoverInfo:e(Ln()),Popover_hoverInfo_type:e(Rn()),tag_label:e(Un()),applyBtn:e(Hn()),panel:e(Gn()),panelContent:e(Wn()),panelContainer:e(Yn()),materialList:e(Xn()),roomImg:e(Vn()),applyBtnInfo:e(Zn())}}),Jn=t(21236),Kn=t(61307),Qn=t(13915),$n=t(37660);function ne(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ee(){var n=ne(["\n        width:100%;\n        height:100%;\n        padding: 0 16px;\n    "]);return ee=function(){return n},n}function te(){var n=ne(["\n        overflow-y: hidden;\n        height: 100%;\n        width: 100%;\n        display: flex;\n        align-items: center;\n        overflow-x: auto;\n        -ms-overflow-style: none;  /* 适用于 Internet Explorer 和 Edge */\n        scrollbar-width: none;  /* 适用于 Firefox */\n        &::-webkit-scrollbar {\n            width: 0; /* Safari 和 Chrome */\n            height: 0; /* Safari 和 Chrome */\n        }\n    "]);return te=function(){return n},n}function re(){var n=ne(["\n        // 按钮样式\n        flex: 0 0 auto;\n        width: 80px;\n        height: 28px;\n        background-color: #FFFFFF;\n        border: 1px solid #00000026;\n        border-radius: 4px;\n        overflow: hidden;\n        color: #515151;\n        font-family: PingFang SC;\n        font-weight: regular;\n        font-size: 12px;\n\n        letter-spacing: 0px;\n        text-align: center;\n        margin-right: 8px;\n\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    "]);return re=function(){return n},n}function ie(){var n=ne(["\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #FFFFFF;\n        font-weight: semibold;\n    "]);return ie=function(){return n},n}function oe(){var n=ne(["\n       height: 32px;\n       .ant-select-selector\n       {\n         border-color: #00000026 !important;\n         box-shadow: none !important;\n       }\n     "]);return oe=function(){return n},n}var ae=(0,a.rU)(function(n){var e=n.css;return{root:e(ee()),container_listInfo:e(te()),btn:e(re()),selected:e(ie()),selectListBar:e(oe())}}),le=t(72978),ce=t(46396);function se(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ue(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return se(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return se(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var de=(0,C.observer)(function(){var n,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{mode:0},t=ue((0,P.useState)([]),2),o=t[0],a=t[1],l=ae().styles,c=(0,M.P)(),s=(0,i.B)().t,u=ue((0,P.useState)([]),2),d=u[0],f=u[1],p=ue((0,P.useState)(null),2),h=p[0],m=p[1],x=ue((0,P.useState)(window.innerWidth<window.innerHeight),2);x[0],x[1],D.nb.instance.layout_container;(0,P.useEffect)(function(){a(c.homeStore.roomEntities)},[c.homeStore.roomEntities]);var g=function(n){D.nb.DispatchEvent(D.n0.selectRoomArea,n)};(0,P.useEffect)(function(){var n=c.homeStore.roomEntities.map(function(n){return{label:n.aliasName,value:n.uidN}});n.unshift({label:s("全屋"),value:"all"}),f(n)},[c.homeStore.roomEntities]),(0,P.useEffect)(function(){D.nb.on(F.U.selectRoom,function(n){n?c.homeStore.setSelectedRoom(n):c.homeStore.setSelectedRoom(null)})},[]),(0,P.useEffect)(function(){c.homeStore.selectedRoom&&m(c.homeStore.selectedRoom)},[c.homeStore.selectedRoom]);var v=1==e.mode&&c.homeStore.isSingleRoom,b=function(){return(0,r.jsxs)("div",{className:l.container_listInfo,children:[v?(0,r.jsx)("div",{className:l.btn,onClick:function(){D.nb.DispatchEvent(D.n0.leaveSingleRoomLayout,{}),c.homeStore.setIsSingleRoom(!1)},children:s("返回全屋")}):(0,r.jsx)("div",{className:"".concat(l.btn," ").concat(c.homeStore.selectedRoom?"":l.selected),onClick:function(){g(null)},children:s("全屋")}),o.map(function(n){return(0,r.jsx)("div",{className:"".concat(l.btn," ").concat((null==h?void 0:h.aliasName)===n.aliasName?l.selected:""),onClick:function(){c.homeStore.isSingleRoom?D.nb.DispatchEvent(D.n0.SingleRoomLayout,n):g(n)},children:s(n.aliasName)},n.aliasName)})]})};return(0,r.jsx)("div",{className:l.root,id:"RoomAreaBtns",children:(0,r.jsxs)(ce.If,{condition:v,children:[(0,r.jsx)(ce.al,{children:b()}),(0,r.jsx)(ce._I,{children:(0,r.jsxs)(ce.If,{condition:!c.homeStore.IsLandscape,children:[(0,r.jsx)(ce.al,{children:b()}),(0,r.jsx)(ce._I,{children:(0,r.jsx)("div",{className:l.selectListBar,children:(0,r.jsx)(le.A,{value:(null===(n=c.homeStore.selectedRoom)||void 0===n?void 0:n.uidN)||"all",style:{width:"100%"},size:"small",options:d,dropdownStyle:{zIndex:9999},onChange:function(n){"all"===n?D.nb.DispatchEvent(D.n0.selectRoomArea,null):D.nb.DispatchEvent(D.n0.selectRoomArea,c.homeStore.roomEntities.find(function(e){return e.uidN===n}))}})})})]})})]})})}),fe=t(17655),pe=t(62460),he=t(65640);function me(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function xe(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function ge(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){xe(o,r,i,a,l,"next",n)}function l(n){xe(o,r,i,a,l,"throw",n)}a(void 0)})}}function ve(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function be(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){ve(n,e,t[e])})}return n}function ye(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function we(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||Se(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function je(n){return function(n){if(Array.isArray(n))return me(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Se(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(n,e){if(n){if("string"==typeof n)return me(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?me(n,e):void 0}}function _e(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ke=(0,C.observer)(function(n){var e,t,a,l,c=n.type,s=(0,i.B)().t,u=(0,M.P)(),d=qn().styles,f=(0,P.useContext)($n.DesignContext),p=f.waitingToFurnishRemaining,h=(f.setWaitingToFurnishRemaining,Jn.A,we((0,P.useState)(!1),2)),m=(h[0],h[1]),x=we((0,P.useState)(!1),2),g=x[0],v=x[1],b=we((0,P.useState)(0),2),y=(b[0],b[1]),w=we((0,P.useState)(!1),2),j=(w[0],w[1]),S=(0,P.useRef)(null),_=((0,P.useRef)(null),(0,P.useRef)(null),(0,P.useRef)(null),we((0,P.useState)([]),2)),k=_[0],I=_[1],C=we((0,P.useState)([]),2),A=(C[0],C[1]),z=we((0,P.useState)([]),2),N=(z[0],z[1]),O=we((0,P.useState)(["全案风格"]),2),E=O[0],B=O[1],T=we((0,P.useState)((0,o.fZ)()?260:360),2),L=(T[0],T[1],we((0,P.useState)((null===(e=u.userStore.userInfo)||void 0===e?void 0:e.isFactory)?"2":"1"),2)),R=(L[0],L[1],we((0,P.useState)(1),2)),U=(R[0],R[1],we((0,P.useState)({}),2)),H=(U[0],U[1]),G=we((0,P.useState)({}),2),W=G[0],Y=G[1],X=we((0,P.useState)(p),2),V=(X[0],X[1]),Z=we((0,P.useState)(!1),2),q=Z[0],J=Z[1],K=we((0,P.useState)(null),2),Q=K[0],$=K[1],nn=we((0,P.useState)([]),2),en=nn[0],tn=nn[1],rn=we((0,P.useState)([]),2),on=rn[0],an=rn[1],ln=we((0,P.useState)([]),2),cn=ln[0],sn=ln[1],un=we((0,P.useState)([]),2),dn=(un[0],un[1]),fn=we((0,P.useState)("1"),2),mn=fn[0],xn=fn[1],gn={"成品":["1","2","3","4","15","37","38"],"定制":["10","11","12","13","17","19","20","21","22","23","24","28"],"贴图":["8","14","26","18","25","36","41"]},vn=we((0,P.useState)({orderBy:"sort asc",ruleType:(null===(t=u.userStore.userInfo)||void 0===t?void 0:t.isFactory)?2:1,pageSize:100,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),bn=vn[0],yn=vn[1];(0,P.useEffect)(function(){ge(function(){var n,e,t,r;return _e(this,function(i){switch(i.label){case 0:return v(!0),j(!0),n=bn,[4,(0,Kn.Ic)(n)];case 1:return e=i.sent(),(t=null==e?void 0:e.result)&&t.forEach(function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map(function(n){return{imgPath:n}})}),j(!1),v(!1),t?(r=(null==k?void 0:k.length)>0&&bn.pageIndex>1?je(k).concat(je(t)):t,I(r)):I([]),m(!1),y(null==e?void 0:e.recordCount),[2]}})})()},[bn]),(0,P.useEffect)(function(){var n=function(n){n.ctrlKey&&"q"===n.key.toLowerCase()&&B("全案风格"===E[0]?["风格套系","样板间"]:["全案风格"])};return window.addEventListener("keydown",n),function(){window.removeEventListener("keydown",n)}},[E]),(0,P.useEffect)(function(){V(p)},[p]);var wn=function(n,e,t,r){u.schemeStatusStore.layoutSchemeSaved=!1,u.schemeStatusStore.pendingOpenSchemeIn3D=!1,D.nb.DispatchEvent(D.n0.SeriesSampleSelected,{series:n,scope:{soft:e,hard:t,cabinet:r,remaining:!1}}),u.homeStore.selectData&&u.homeStore.selectData.rooms&&jn(u.homeStore.selectData.rooms)},jn=function(n){var e=W;e={};var t=!0,r=!1,i=void 0;try{for(var o,a=n[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var l=o.value;if(l._scope_series_map)for(var c in l._scope_series_map){var s=l._scope_series_map[c];s&&s.ruleId&&(e[s.ruleId]||(e[s.ruleId]={}),e[s.ruleId][c]=!0)}}}catch(n){r=!0,i=n}finally{try{t||null==a.return||a.return()}finally{if(r)throw i}}Y(e)};(0,P.useEffect)(function(){m(!0),ge(function(){var n,e,t;return _e(this,function(r){switch(r.label){case 0:return[4,(0,Qn.kV)()];case 1:return(e=r.sent())?(t=null===(n=Object)||void 0===n?void 0:n.keys(e).map(function(n,e){return{id:e+1,screenName:n}}),N(t),[2]):[2]}})})(),ge(function(){var n,e;return _e(this,function(t){switch(t.label){case 0:return[4,(0,Kn.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map(function(n){return{value:n.key,screenName:n.label}}),A(e),[2]):[2]}})})()},[]);var Sn="SeriesCandidateList";(0,P.useEffect)(function(){return D.nb.on_M(F.U.SelectingRoom,Sn,function(n){jn(n.current_rooms||[]),setTimeout(function(){u.homeStore.setSelectData({rooms:null==n?void 0:n.current_rooms,clickOnRoom:!0})},20)}),function(){D.nb.off_M(F.U.SelectingRoom,Sn)}},[]);var _n=function(n){var e=n.list;return(0,r.jsx)("div",{className:d.materialList,children:e.map(function(n,e){return(0,r.jsx)("img",{width:80,height:80,src:"".concat(n.imagePath,"?x-oss-process=image/resize,m_fixed,h_80,w_80"),alt:""},e)})})},kn=function(){tn([]),an([]),sn([]),dn([])},In=[{key:"1",label:s("定制模型"),children:(0,r.jsx)(_n,{list:en})},{key:"2",label:s("软装模型"),children:(0,r.jsx)(_n,{list:on})},{key:"3",label:s("硬装模型"),children:(0,r.jsx)(_n,{list:cn})}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:d.bottomPanel,children:[(0,r.jsxs)("div",{className:d.topSelect,children:[(0,r.jsx)("div",{className:"".concat(1==bn.ruleType?"checked":""),style:{color:"".concat(1==bn.ruleType?"#282828":"#959598")},onClick:function(){yn(function(n){return ye(be({},n),{ruleType:1,pageIndex:1})})},children:s("平台套系")}),(0,r.jsx)("div",{className:"".concat(2==bn.ruleType?"checked":""),style:{color:"".concat(2==bn.ruleType?"#282828":"#959598")},onClick:function(){yn(function(n){return ye(be({},n),{ruleType:2,pageIndex:1})})},children:s("企业套系")})]}),(0,r.jsx)("div",{className:d.roomListBar,children:(0,r.jsx)(de,{})}),(0,r.jsx)("div",{className:"".concat(d.container_listInfo," ").concat(g?d.noScroll:""," ").concat(c?d.type:""),ref:S,children:k&&k.length>0?(0,r.jsx)(r.Fragment,{children:null==k||null===(a=k.map)||void 0===a?void 0:a.call(k,function(n,e){return(0,r.jsx)("div",{id:"series_box"+e,className:d.container_box,children:(0,r.jsxs)("div",{className:d.container_data,onMouseEnter:function(){return H(function(n){return ye(be({},n),ve({},e,!0))})},onMouseLeave:function(){return H(function(n){return ye(be({},n),ve({},e,!1))})},children:[(0,r.jsxs)("div",{className:"".concat(d.Popover_hoverInfo," ").concat(W[n.ruleId]?d.Popover_hoverInfo_type:""),children:[(0,r.jsx)("img",{onClick:function(){!function(n,e){"软装"==n?wn(e,!0,!1,!1):"硬装"==n?wn(e,!1,!0,!1):"定制"==n&&wn(e,!1,!1,!0)}(c,n),wn(n,!0,!0,!0)},src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,r.jsx)(ce.If,{condition:W[n.ruleId],children:(0,r.jsx)("div",{className:d.tag_label,children:s("使用全部")})})]}),(0,r.jsxs)("div",{className:d.textInfo,children:[(0,r.jsx)("div",{className:d.container_title,title:n.seedSchemeName||n.ruleName,children:n.seedSchemeName||n.ruleName}),(0,r.jsx)("div",{className:d.container_desc,onClick:function(){return function(n){$(n),he.log("item",n),pe.VF.getSeriesAllMaterial(n.ruleId,function(n){tn(n.filter(function(n){return gn["定制"].includes(String(n.modelFlag))})),an(n.filter(function(n){return gn["成品"].includes(String(n.modelFlag))})),sn(n.filter(function(n){return gn["贴图"].includes(String(n.modelFlag))}))},function(){he.log("err")}),J(!0),xn("1")}(n)},children:s("详情")})]})]},e)},"record_"+e)})}):(0,r.jsx)("div",{className:d.emptyInfo,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("div",{className:"desc",children:s("暂无数据")})]})})})]}),(0,r.jsx)("div",{className:d.panelContainer,children:q&&(0,r.jsx)(fe._w,{center:!0,className:d.panel,draggable:!0,title:(null==Q?void 0:Q.seedSchemeName)||(null==Q?void 0:Q.ruleName),onClose:function(){J(!1),kn()},mask:!0,children:(0,r.jsxs)("div",{className:d.panelContent,children:[(0,r.jsx)(pn.A,{effect:"fade",autoplay:!0,children:null==Q||null===(l=Q.roomList)||void 0===l?void 0:l.map(function(n,e){return(0,r.jsx)("div",{children:(0,r.jsx)("img",{className:d.roomImg,src:"".concat(n.imgPath,"?x-oss-process=image/resize,m_fixed,h_290,w_520"),alt:""})},e)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(hn.A,{defaultActiveKey:"1",items:In,onChange:function(n){dn("1"==n?en:"2"==n?on:cn),xn(n)}}),(0,r.jsxs)("div",{className:d.applyBtnInfo,children:[(0,r.jsxs)("button",{className:"leftBtn",onClick:function(){"1"==mn?wn(Q,!1,!1,!0):"2"==mn?wn(Q,!0,!1,!1):"3"==mn&&wn(Q,!1,!0,!1),J(!1),kn()},children:["应用",s("1"==mn?"定制":"2"==mn?"软装":"硬装")]}),(0,r.jsx)("button",{className:"rightBtn",onClick:function(){wn(Q,!0,!0,!0),J(!1),kn()},children:s("应用全部")})]})]})]})})})]})}),Ie=t(48402),Ce=t(44466),Ae=t(63080),ze=t(30268),Ne=t(87927);function Pe(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Oe(){var n=Pe(["\n      display: grid;\n      max-height: 224px;\n      /* padding-top: 16px; */\n      /* transition: all .3s; */\n      overflow-y: auto;\n      overflow-x: hidden;\n      padding: 0 16px;\n      margin-bottom: 16px;\n      grid-template-columns: repeat(6, 1fr);\n      grid-template-rows: repeat(2, 1fr);\n      grid-auto-rows: 1fr;\n      gap: calc((10vw - 16px) / 6);\n      padding-bottom: 30px;\n      &::-webkit-scrollbar {\n        width: 0;\n        height: 0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        grid-template-columns: repeat(4, 1fr);\n      }\n      @media screen and (orientation: landscape) {\n        grid-template-columns: repeat(2, 1fr);\n        max-height: calc(var(--vh, 1vh) * 100 - 280px);\n      }\n      @media screen and (orientation: landscape) and (max-width: 960px) {\n        min-height: calc(var(--vh, 1vh) * 100 - 190px);\n      }\n      .fold\n      {\n        width: 100%;\n        border-radius: 4px;\n        background: #F4F5F5;\n        height: 24px;\n        line-height: 24px;\n        font-weight: 600;\n        padding: 0 5px;\n        margin: 8px 0 4px 0;\n      }\n      // .content {\n      //   display: flex;\n      //   flex-wrap: nowrap;\n      //   transition: max-height 0.3s ease-in-out; /* 这将添加过渡动画 */\n      // }\n\n      // .collapsed {\n      //   max-height: 0;\n      // }\n      .item {\n        /* max-width: 30%; */\n        margin: 1vw 0;\n        width: 100%;\n        height: 16vw;\n        cursor: pointer;\n        transition: box-shadow 0.3s ease;\n        user-select:none;\n        @media screen and (max-width: 450px) { // 手机宽度\n          height: 28vw;\n        }\n        @media screen and (orientation: landscape) {\n          height: 9vw;\n        }\n        :nth-child(6n) {\n          margin-right: 0;\n        }\n        .image {\n          height: calc(15vw * 0.9);\n          width: 90%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 6px;\n          overflow:hidden;\n          /* transition: all .3s; */\n          background-color: #f5f5f5;\n          @media screen and (max-width: 450px) { // 手机宽度\n            height: calc(25vw * 0.9);\n          }\n          @media screen and (orientation: landscape) {\n            height: 100%;\n            width: 100%;\n          }\n          .ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n          .structure-image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n            height: 60px;\n            width: 60px;\n          }\n          .group_image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n\n        }\n\n        .title {\n          color: #000000;\n          font-size: 12px;\n          padding: 5px 0;\n          height: 40px;\n          line-height: 20px;\n          text-align: center;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n      }\n      .item:hover {\n          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */\n        }\n    "]);return Oe=function(){return n},n}function De(){var n=Pe(["\n      /* height: calc(100vh - 370px) !important; */\n    "]);return De=function(){return n},n}var Ee=(0,a.rU)(function(n){var e=n.css;return{figure:e(Oe()),mobile:e(De())}});function Me(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Fe(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Me(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Me(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Be=(0,C.observer)(function(n){var e=n.data,t=n.filterName,o=(0,i.B)().t,a=Ee().styles,l=((0,M.P)(),Fe((0,P.useState)(""),2)),c=l[0],s=l[1],u=O().useRef(null),d=Fe((0,P.useState)(),2),f=(d[0],d[1]);(0,P.useRef)([]);(0,P.useEffect)(function(){var n=new fe.Jc({isShowThumbnail:!0,container:document.getElementById("side_pannel"),log:!1});return n.bindDrag(),function(){n.unbindDrag()}},[]),(0,P.useEffect)(function(){f(e.map(function(){return!1}))},[e]),(0,P.useEffect)(function(){var n=u.current,e=function(){c&&(D.nb.RunCommand(D._I.LeaveSubHandler),s(""))},t=function(n){var e=u.current.getBoundingClientRect();n.touches[0].clientY<e.bottom-e.height&&u.current&&(u.current.style.overflow="hidden")},r=function(n){u.current&&(u.current.style.overflow="scroll")};return n.addEventListener("mouseup",e),n.addEventListener("touchmove",t),n.addEventListener("touchend",r),function(){n.removeEventListener("mouseup",e),n.removeEventListener("touchmove",t),n.removeEventListener("touchend",r)}},[c]);var p=O().memo(ze.A);return(0,r.jsx)("div",{className:"".concat(a.figure),ref:u,id:"scrollContainerRef",children:e.map(function(n,e){return(0,r.jsx)(O().Fragment,{children:n.figureList.map(function(n,e){return(0,r.jsxs)("div",{className:"item",children:[(0,r.jsx)("div",{className:"image",onPointerDown:function(e){var t=n.title;n.group_code&&(t="GroupTemplate:"+n.group_code),n.title.includes("单开门")||n.title.includes("推拉门")||n.title.includes("一字窗")||n.title.includes("飘窗"),D.nb.DispatchEvent(D.n0.SelectedFurniture,t)},onPointerUp:function(e){var t=n.title;if(n.group_code)return t="GroupTemplate:"+n.group_code,void D.nb.DispatchEvent(D.n0.mobileAddFurniture,t);D.nb.DispatchEvent(D.n0.mobileAddFurniture,t)},children:(0,r.jsx)(p,{src:"https://3vj-fe.3vjia.com/layoutai/figures_imgs/".concat(n.png),preview:!1,alt:n.title,className:"结构件"===t?"structure-image":""})}),(0,r.jsx)(Ne.A,{placement:"rightBottom",title:o(n.title),children:(0,r.jsx)("div",{className:"title",children:o(n.title)})})]},e)})},"figure_menu_key"+e)})})});function Te(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Le(){var n=Te(["\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return Le=function(){return n},n}function Re(){var n=Te(["\n\n    "]);return Re=function(){return n},n}function Ue(){var n=Te(["\n      display: flex;\n      flex-wrap: nowrap;\n      margin-top: 10px;\n      overflow-x: scroll;\n      padding: 0 16px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        flex-wrap: wrap;\n        padding: 0 12px;\n      }\n      .item\n      {\n        width: 66px;\n        height: 28px;\n        padding: 2px 10px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        margin: 2px 5px 2px 0;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        @media screen and (orientation: landscape) {\n          margin: 2px 4px 2px 0px;\n          width: 61px;\n        }\n      }\n      img{\n        width: 16px;\n        height: 16px;\n      }\n      .active\n      {\n        border-radius: 4px;\n        background: #EAEAEB;\n      }\n    "]);return Ue=function(){return n},n}function He(){var n=Te(["\n      width: 100%;\n      height: 1px;\n      background: #EAEAEB;\n      margin: 10px 0;\n    "]);return He=function(){return n},n}function Ge(){var n=Te(["\n      display: flex;\n      margin-bottom: 4px;\n      overflow-x: scroll;\n      width: auto;\n      margin-left: 20px;\n      .item\n      {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: regular;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        margin-right: 16px;\n        white-space: nowrap;\n        cursor: pointer;\n      }\n      .active\n      {\n        color: #147FFA;\n      }\n    "]);return Ge=function(){return n},n}function We(){var n=Te(["\n      box-sizing: border-box;\n      padding: 12px 12px 0 0;\n      transition: all .3s;\n      min-width: 156px;\n      ul {\n        padding: 0;\n      }\n      li {\n        padding: 0;\n        margin: 0;\n        list-style: none;\n      }\n      .menu {\n        > li {\n          margin-bottom: 16px;\n          transition: all .3s;\n        }\n        li:hover{\n          color: #5B5E60;\n        }\n        &_columns {\n          display: flex;\n        }\n\n        &_item {\n          /* background: #f2f2f2; */\n          padding: 8px 0;\n\n          :first-child {\n            margin-right: 12px;\n          }\n          \n          :last-child li:first-child {\n            width: 72px;\n          }\n          li {\n            // padding: 0 16px 0 22px;\n            margin: 8px 0;\n            color: #25282D;\n            font-family: PingFang SC;\n            font-weight: regular;\n            font-size: 14px;\n            line-height: 20px;\n            height: 20px;\n            width: 60px;\n            letter-spacing: 0px;\n            text-align: left;\n            cursor: pointer;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            user-select:none;\n          }\n        }\n      }\n      .icon {\n        color: red;\n      }\n      .label {\n        color: #25282D;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        user-select:none;\n\n        &_name {\n          // margin-left: 5px;\n          height: 100%;\n        }\n\n        &_name::after {\n          content: '';\n          height: 8px;\n          display: block;\n          position: relative;\n          top: -3px;\n          opacity: 0.5;\n          background: linear-gradient(90deg, #66B8FF 0%, #147FFA00 100%);\n        }\n\n        &.active {\n          color: rgba(20, 127, 250, 1);\n        }\n      }\n    "]);return We=function(){return n},n}function Ye(){var n=Te(["\n      height: 100%;\n      /* min-width: 256px; */\n      .layout_btns {\n        display: flex;\n        align-items: center;\n        margin-top:10px;\n        padding-left:16px;\n\n        .btn {\n          border-radius: 2px;\n          background: rgba(20, 127, 250, 0.1);\n          color: rgba(20,127,250,1);\n          font-weight: bold;\n          font-size: 14px;\n          margin-bottom:8px;\n          width: 100px;\n          text-align: center;\n          &:first-child{\n            margin-right: 8px;\n          }\n          &:hover{\n            background: rgba(20, 127, 250, 0.25);\n            color: rgba(20,127,250,1);\n          }\n        }\n      }\n    "]);return Ye=function(){return n},n}function Xe(){var n=Te(["\n      border-radius: 4px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 32px;\n      letter-spacing: 0px;\n      text-align: left;\n      // min-width: 326px;\n      width: 100%;\n      height: 32px;\n      border: none;\n      padding-left: 36px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return Xe=function(){return n},n}function Ve(){var n=Te(["\n      position: absolute;\n      top: 50%;\n      translate: 16px -50%;\n    "]);return Ve=function(){return n},n}function Ze(){var n=Te(["\n      position: absolute;\n      top: 8px;\n      right: 10px;\n      cursor: pointer;\n    "]);return Ze=function(){return n},n}function qe(){var n=Te(["\n      display: flex;\n    "]);return qe=function(){return n},n}function Je(){var n=Te(["\n      display: flex;\n      align-items: center;\n    "]);return Je=function(){return n},n}function Ke(){var n=Te(["\n      display: flex;\n      flex-grow: 1;\n      position: relative;\n    "]);return Ke=function(){return n},n}function Qe(){var n=Te(["\n      color: #147FFA !important;\n    "]);return Qe=function(){return n},n}function $e(){var n=Te(["\n      position: absolute;\n      top: 32px;\n      left: 0;\n      right: 0;\n      background: #fff;\n      border-radius: 4px;\n      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);\n      z-index: 100;\n      padding: 8px 0;\n      max-height: 300px;\n      overflow: auto;\n    "]);return $e=function(){return n},n}var nt=(0,a.rU)(function(n){var e=n.css;return{root:e(Le()),menu_container:e(Re()),tab_box:e(Ue()),line:e(He()),filterlist:e(Ge()),menu_box:e(We()),figure_box:e(Ye()),container_input:e(Xe()),Icon:e(Ve()),deleteIcon:e(Ze()),searchInfo:e(qe()),closeInfo:e(Je()),inputInfo:e(Ke()),select:e(Qe()),searchList:e($e())}});function et(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function tt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return et(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return et(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var rt=(0,C.observer)(function(){var n=(0,M.P)(),e=(0,i.B)().t,t=nt().styles,o=tt((0,P.useState)([]),2),a=o[0],l=o[1],c=tt((0,P.useState)(!1),2),s=c[0],u=c[1],d=tt((0,P.useState)(""),2),f=d[0],p=d[1],h=tt((0,P.useState)(""),2),m=h[0],x=h[1],g=tt((0,P.useState)([]),2),v=(g[0],g[1]),b=tt((0,P.useState)(!1),2),y=b[0],w=(b[1],tt((0,P.useState)([]),2)),j=w[0],S=(w[1],D.nb.IsDebug||n.userStore.beta?Ie.V:Ie.V.filter(function(n){return"视角"!==n.label&&"户型"!==n.label&&"定制"!==n.label})),_=(0,r.jsx)(Jn.A,{style:{fontSize:24},spin:!0}),k=[],I=[];S.map(function(n){n.child.map(function(n){k=k.concat(n.figureList)})}),S.filter(function(n){return"户型"!==n.label}).map(function(n,e){n.child.map(function(n){I=I.concat(n.figureList)})});var C=function(n){l(n.child)},A=function(){if(D.nb.instance._current_handler_mode===E.f.HouseDesignMode){var n,e,t=S.filter(function(n){return n.label.includes("户型")});v(t),l(null===(e=t[0])||void 0===e||null===(n=e.child[0])||void 0===n?void 0:n.figureList)}else{var r=S.filter(function(n){return!0});v(r)}};return(0,P.useEffect)(function(){C(S[0]),p(S[0].label),x(S[0].child[0].label),D.nb.on_M(F.U.AIDesignModeChanged,"FiguresMenu",function(){A()}),A()},[]),(0,P.useEffect)(function(){u(!0),setTimeout(function(){u(!1)},200)},[a]),(0,P.useEffect)(function(){D.nb.instance._current_handler_mode,E.f.HouseDesignMode},[n.homeStore.selectedRoom]),(0,r.jsx)("div",{className:t.root,children:y?(0,r.jsx)(Ce.xS,{data:j}):(0,r.jsxs)("div",{className:t.menu_container,style:{opacity:y?0:1},children:[(0,r.jsx)("div",{className:t.tab_box,children:S.map(function(n){return(0,r.jsxs)("div",{className:"item ".concat((t=n.label,f===t?"active":"")),onClick:function(){C(n),p(n.label),x(n.child[0].label)},children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/figures_imgs/".concat(n.png),alt:n.title}),(0,r.jsx)("span",{className:"label_name",children:e(n.label)})]},n.label);var t})}),(0,r.jsx)("div",{className:t.figure_box,children:(0,r.jsx)(Ae.A,{indicator:_,spinning:s,children:(0,r.jsx)(Be,{data:a,filterName:m})})})]})})});function it(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ot(){var n=it(["\n      height: 300px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 85);\n        width: 224px;\n        margin-top: -40px;\n        background: #fff;\n        position: absolute;\n      }\n    "]);return ot=function(){return n},n}function at(){var n=it(["\n      height: 40px;\n    "]);return at=function(){return n},n}function lt(){var n=it(["\n      position: fixed;\n      top: 50px;\n      right: 20px;\n      z-index: 1200\n    "]);return lt=function(){return n},n}function ct(){var n=it(["\n      padding: 0 20px;\n      display: flex;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        flex-direction: column;\n      }\n      .svg-input-number\n      {\n        padding-right: 4px;\n        @media screen and (max-width: 450px) { // 手机宽度\n          width: 65px;\n          font-size: 12px;\n        }\n      }\n      .left\n      {\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .houseInfo\n        {\n          .input\n          {\n            width: 50px;\n            margin-right: 4px;\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 45px;\n              margin-top: 10px;\n            }\n          }\n\n          span{\n            margin-right: 13px;\n            font-size: 14px;\n          }\n        }\n        .leftInfo\n        {\n          display: flex;\n        }\n\n      }\n      .right{\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .rightInfo\n        {\n          display: flex;\n          \n        }\n      }\n      .title{\n        @media screen and (orientation: landscape) {\n          font-size: 14px;\n          margin: 16px 0 16px 0px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n      }\n      .houseInfo{\n        @media screen and (orientation: landscape) {\n          display: flex;\n          flex: 0 0 50%;\n          font-size: 14px;\n          flex-wrap: wrap;\n          align-items: center;\n          div{\n            display: flex;\n            align-items: center;\n            margin-right: 8px;\n            margin-bottom: 12px;\n            align-items: center;\n            .input{\n              width: 64px;\n            }\n            span{\n              margin-left: 4px;\n            }\n          }\n        }\n      }\n    "]);return ct=function(){return n},n}var st=(0,a.rU)(function(n){var e=n.css;return{root:e(ot()),roomListBar:e(at()),name:e(lt()),attributeInfo:e(ct())}}),ut=t(62954),dt=t(51187),ft=t(80277),pt=t(63286),ht=t(17365);function mt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function xt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return mt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return mt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var gt=(0,C.observer)(function(){var n=(0,M.P)(),e=(0,i.B)().t,t=st().styles,o=xt((0,P.useState)(0),2),a=(o[0],o[1],xt((0,P.useState)(-1),2)),l=(a[0],a[1],xt((0,P.useState)(""),2)),c=(l[0],l[1],xt((0,P.useState)({}),2)),s=(c[0],c[1],xt((0,P.useState)(0),2)),u=(s[0],s[1],xt((0,P.useState)(!1),2)),d=(u[0],u[1],xt((0,P.useState)({}),2)),f=d[0],p=d[1],h=xt((0,P.useState)(),2),m=h[0],x=h[1],g=xt((0,P.useState)(),2),v=g[0],b=g[1],y=xt((0,P.useState)(),2),w=y[0],j=y[1],S=xt((0,P.useState)(),2),_=S[0],k=S[1],I=xt((0,P.useState)(),2),C=I[0],A=I[1],z=xt((0,P.useState)(0),2),N=z[0],O=z[1],E=xt((0,P.useState)(0),2),F=E[0],B=E[1],T=xt((0,P.useState)(0),2),L=T[0],R=T[1],U=xt((0,P.useState)(0),2),H=U[0],G=U[1],W=xt((0,P.useState)(0),2),Y=W[0],X=W[1],V=xt((0,P.useState)(1),2);V[0],V[1];(0,P.useEffect)(function(){var t,r,i,o,a,l,c,s,u,d,f,h,m,g,v,y,w;"空间信息"!=(null===(t=n.homeStore.attribute)||void 0===t?void 0:t.title)&&(null===(r=n.homeStore.attribute)||void 0===r?void 0:r.tile)!=e("空间信息")||(p(n.homeStore.attribute.properties),x(null===(a=n.homeStore.attribute)||void 0===a||null===(o=a.properties)||void 0===o||null===(i=o.name)||void 0===i?void 0:i.defaultValue),j(null===(s=n.homeStore.attribute)||void 0===s||null===(c=s.properties)||void 0===c||null===(l=c.storey_height)||void 0===l?void 0:l.defaultValue),b(null===(f=n.homeStore.attribute)||void 0===f||null===(d=f.properties)||void 0===d||null===(u=d.ceiling_height)||void 0===u?void 0:u.defaultValue),k(null===(g=n.homeStore.attribute)||void 0===g||null===(m=g.properties)||void 0===m||null===(h=m.floor_thickness)||void 0===h?void 0:h.defaultValue),A(null===(w=n.homeStore.attribute)||void 0===w||null===(y=w.properties)||void 0===y||null===(v=y.max_cabinet_height)||void 0===v?void 0:v.defaultValue))},[n.homeStore.attribute]),(0,P.useEffect)(function(){if(n.homeStore.roomInfos){var e=0,t=0,r=0,i=0,o=0;n.homeStore.roomInfos.forEach(function(n){e+=n.area,(n.name.includes("室")||n.name.includes("卧"))&&t++,n.name.includes("厅")&&r++,(n.name.includes("厕所")||n.name.includes("卫生间"))&&i++,n.name.includes("厨房")&&o++}),G(parseFloat(e.toFixed(2))),O(t),B(r),R(i),X(o),j(D.nb.instance.layout_container._storey_height)}},[n.homeStore.roomInfos]);var Z=(0,P.useCallback)(function(e,t){return function(r){var i;e(r),null==f||null===(i=f[t])||void 0===i||i.onChange(r),"name"===t&&(ht.f.updateAliasName(D.nb.instance.layout_container),n.homeStore.setRoomEntites(D.nb.instance.layout_container._room_entities))}},[f]);return(0,r.jsx)("div",{className:t.root,children:(0,r.jsx)("div",{className:t.attributeInfo,children:n.homeStore.selectedRoom?(0,r.jsx)(r.Fragment,{children:n.homeStore.IsLandscape?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"title",children:e("空间类型")}),(0,r.jsx)("div",{children:(0,r.jsx)(le.A,{value:m,style:{width:"100%"},onChange:Z(x,"name"),options:pt.H.getRoomNameOptions(),getPopupContainer:function(n){return n.parentNode}})}),(0,r.jsxs)("div",{className:"title",children:[e("当前层高"),(0,r.jsx)(ft.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:w,onChange:function(n){return Z(j,"storey_height")(Number(n))},suffix:"mm"})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"left",children:[(0,r.jsx)("div",{className:"title",children:e("空间类型")}),(0,r.jsx)("div",{children:(0,r.jsx)(le.A,{value:m,style:{width:"100%"},onChange:Z(x,"name"),options:pt.H.getRoomNameOptions()})}),(0,r.jsx)("div",{className:"title",children:e("当前层高")}),(0,r.jsxs)("div",{className:"leftInfo",children:[(0,r.jsx)(dt.A,{min:0,max:2800,onChange:Z(j,"storey_height"),style:{width:"100%"},value:w,disabled:!0}),(0,r.jsx)(ft.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:w,onChange:function(n){return Z(j,"storey_height")(Number(n))},suffix:"mm"})]}),(0,r.jsx)("div",{className:"title",children:e("地铺厚度")}),(0,r.jsxs)("div",{className:"leftInfo",children:[(0,r.jsx)(dt.A,{min:0,max:100,onChange:Z(k,"floor_thickness"),style:{width:"100%"},value:_}),(0,r.jsx)(ft.A,{min:0,max:100,style:{width:"100px"},value:_,onChange:function(n){return Z(k,"floor_thickness")(Number(n))},suffix:"mm"})]})]}),(0,r.jsxs)("div",{className:"right",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"title",children:e("最高柜顶高")}),(0,r.jsx)("div",{children:(0,r.jsx)(ut.A,{disabled:!0,style:{width:"100%"},value:C,suffix:"mm"})})]}),(0,r.jsx)("div",{className:"title",children:e("吊顶下吊")}),(0,r.jsxs)("div",{className:"rightInfo",children:[(0,r.jsx)(dt.A,{min:200,max:400,onChange:Z(b,"ceiling_height"),style:{width:"100%"},value:v}),(0,r.jsx)(ft.A,{min:200,max:400,style:{width:"100px"},value:v,onChange:function(n){return Z(b,"ceiling_height")(Number(n))},suffix:"mm"})]})]})]})}):(0,r.jsx)(r.Fragment,{children:n.homeStore.IsLandscape?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,r.jsx)("div",{children:(0,r.jsx)(ut.A,{disabled:!0,style:{width:"100%"},value:H,suffix:"m²"})})]}),(0,r.jsx)("div",{className:"title",children:e("户型")}),(0,r.jsxs)("div",{className:"houseInfo",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:N}),(0,r.jsx)("span",{children:e("室")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:F}),(0,r.jsx)("span",{children:e("厅")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:L})," ",(0,r.jsx)("span",{children:e("卫")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:Y})," ",(0,r.jsx)("span",{children:e("厨")})]})]}),(0,r.jsxs)("div",{className:"title",children:[e("当前层高"),(0,r.jsx)(ft.A,{min:2e3,max:3500,style:{width:"100px"},value:w,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(D.nb.instance.layout_container._storey_height=e),j(n)},suffix:"mm"})]}),(0,r.jsx)("div",{className:"rightInfo",children:(0,r.jsx)(dt.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(D.nb.instance.layout_container._storey_height=e),j(n)},style:{width:"100%"},value:w})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"left",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,r.jsx)("div",{children:(0,r.jsx)(ut.A,{disabled:!0,style:{width:"100%"},value:H,suffix:"m²"})})]}),(0,r.jsx)("div",{className:"title",children:e("户型")}),(0,r.jsxs)("div",{className:"houseInfo",children:[(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:N}),(0,r.jsx)("span",{children:e("室")}),(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:F}),(0,r.jsx)("span",{children:e("厅")}),(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:L}),(0,r.jsx)("span",{children:e("卫")}),(0,r.jsx)(ut.A,{className:"input",disabled:!0,value:Y}),(0,r.jsx)("span",{children:e("厨")})]})]}),(0,r.jsxs)("div",{className:"right",children:[(0,r.jsx)("div",{className:"title",children:e("当前层高")}),(0,r.jsxs)("div",{className:"rightInfo",children:[(0,r.jsx)(dt.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(D.nb.instance.layout_container._storey_height=e),j(n)},style:{width:"50%"},value:w}),(0,r.jsx)(ft.A,{min:2e3,max:3500,style:{width:"100px"},value:w,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(D.nb.instance.layout_container._storey_height=e),j(n)},suffix:"mm"})]})]})]})})})})}),vt=t(76330),bt=t(93491);function yt(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function wt(){var n=yt(["\n      height: 510px;\n      @media screen and (max-width: 450px) {\n        height: 380px;\n      }\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return wt=function(){return n},n}function jt(){var n=yt(["\n      display: flex;\n      padding: 0 24px;\n      margin-top: 20px;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        display: block;\n      }\n    "]);return jt=function(){return n},n}function St(){var n=yt(["\n      width: 32%;\n      height: 80px;\n      border-radius: 8px;\n      background: #F4F5F5;\n      position: relative;\n      overflow: hidden;\n      border: 1px solid #0000000F;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin-bottom: 8px;\n      }\n      .add\n      {\n        left: 50%;\n        top: 50%;\n        position: absolute;\n        transform: translate(-50%, -50%);\n        color: #282828;\n        font-size: 12px;\n        width: 100%;\n        text-align: center;\n      }\n      img{\n        width: 50%;\n        height: 80px;\n        margin-right: 8px;\n        @media screen and (orientation: landscape) {\n          width: 30%;\n          height: 80px;\n        }\n      }\n      .item\n      {\n        display: flex;\n        justify-content: space-between;\n        position: relative;\n        @media screen and (orientation: landscape) {\n          justify-content: start;\n        }\n        .title\n        {\n          width: 50%;\n          position: absolute;\n          text-align: center;\n          bottom: 0px;\n          height: 20px;\n          color: #FFF;\n          border-radius: 0px 0px 4px 4px;\n          background: #00000066;\n          padding-top: 2px;\n          @media screen and (orientation: landscape) {\n            width: 30%;\n          }\n        }\n      }\n      .rightitem\n      {\n\n        width: 50%;\n        display: flex;\n        flex-direction: column;\n        padding: 8px 4px;\n        justify-content: space-between;\n        position: relative;\n        color: #282828;\n        .icon\n        {\n          position: absolute;\n          right: 10px;\n          bottom: 10px;\n          font-size: 18px;\n          color: #5B5E60;\n        }\n        .seriesStyle\n        {\n          border-radius: 4px;\n          background: #FFFFFF;\n          padding: 4px 8px;\n          font-size: 12px;\n          color: #5B5E60;\n          width: 40px;\n          text-align: center;\n          height: 24px;\n        }\n      }\n    "]);return St=function(){return n},n}function _t(){var n=yt(["\n     overflow-y: scroll;\n     height: calc(100% - 100px);\n     margin-top: 24px;\n     padding: 0 24px;\n     @media screen and (orientation: landscape) {\n      height: calc(var(--vh, 1vh) * 100 - 350px);\n     }\n    .itemInfo {\n        margin-bottom: 16px; /* 每个 item 之间的间距 */\n        overflow: hidden; /* 隐藏溢出内容 */\n\n        .itemList\n        {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 16px;\n          .item{\n            text-align: center;\n            overflow: hidden;\n            width: 104px;\n            position: relative;\n            .redIcon\n            {\n              position: absolute;\n              top: 5px;\n              left: 5px;\n              color: #FF4D4F;\n            }\n            @media screen and (max-width: 450px){\n              width: 82px;\n            }\n            @media screen and (max-width: 400px){\n              width: 69px;\n            }\n            @media screen and (orientation: landscape) {\n              width: 69px;\n            }\n            img{\n              aspect-ratio: 1 / 1;\n              margin-bottom: 4px;\n            }\n            div{\n              text-overflow: ellipsis;\n              white-space: nowrap;\n              overflow: hidden;\n            }\n          }\n          \n        }\n    }\n\n    .header {\n        display: flex;\n        justify-content: space-between; /* 使 label 和 icon 分开 */\n        padding: 12px 0px; /* 内边距 */\n        .title\n        {\n          font-weight: 600;\n          font-size: 14px;\n        }\n    }\n\n    .item {\n        \n    }\n\n    .item img {\n        width: 100%; /* 图片宽度占满 */\n        height: auto; /* 高度自适应 */\n        background-color: #F4F5F5;\n        border-radius: 4px;\n    }\n    &::-webkit-scrollbar {\n        display: none;\n    }\n  \n    scrollbar-width: none;\n    -ms-overflow-style: none;\n    "]);return _t=function(){return n},n}function kt(){var n=yt(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: flex-end;\n      z-index: 10002;\n      @media screen and (orientation: landscape) {\n        justify-content: end;\n        position: fixed;\n\n      }\n    "]);return kt=function(){return n},n}function It(){var n=yt(["\n      background-color: white;\n      width: 100%;\n      padding: 20px;\n      height: 300px;\n      padding-top: 56px;\n      border-radius: 16px 16px 0px 0px;\n      background: #FFFFFF;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      position: relative;\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        bottom: 12px;\n        right: 0;\n        top: 45px;\n        left: auto;\n        padding: 0;\n        width: 224px;\n        border-radius: 0px;\n        overflow: hidden;\n        height: auto;\n        border-radius: 6px;\n      }\n    "]);return It=function(){return n},n}function Ct(){var n=yt(["\n      position: absolute;\n      bottom: 0;\n      right: 0;\n      width: 100%;\n      height: 586px;\n      background-color: white;\n      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);\n      transition: transform 0.2s ease;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      @media screen and (orientation: landscape) {\n        height: calc(100%);\n        border-radius: 0px;\n        width: 224px;\n        left : 0px;\n        right : auto;\n      }\n      .sideTopInfo\n      {\n        padding: 24px;\n        display: flex;\n        justify-content: space-between;\n        div{\n          font-size: 20px;\n          font-weight: 600;\n          color: #282828;\n          margin-left: 6px;\n        }\n      }\n    "]);return Ct=function(){return n},n}function At(){var n=yt(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n            }\n         } \n      "]);return At=function(){return n},n}function zt(){var n=yt(["\n      transform: translateX(0);\n      @media screen and (orientation: landscape) {\n        transform: translateX(0);        \n      }\n    "]);return zt=function(){return n},n}function Nt(){var n=yt(["\n      transform: translateX(100%);\n      @media screen and (orientation: landscape) {\n        transform: translateX(-100%);\n      }\n    "]);return Nt=function(){return n},n}function Pt(){var n=yt(["\n      position: absolute;\n      right: 7px;\n      top: 57%;\n      font-size: 16px;\n      color: #5B5E60;\n    "]);return Pt=function(){return n},n}function Ot(){var n=yt(["\n      position: absolute;\n      left: 5px;\n      top: 5px;\n      font-size: 16px;\n      color: #FFAA00;\n    "]);return Ot=function(){return n},n}var Dt=(0,a.rU)(function(n){var e=n.css;return{root:e(wt()),styleInfo:e(jt()),styleItem:e(St()),materialInfo:e(_t()),visible:e(kt()),serialsInfo:e(It()),sideVisible:e(Ct()),topInfo:e(At()),slideIn:e(zt()),slideOut:e(Nt()),lock_icon:e(Pt()),warn_icon:e(Ot())}});function Et(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Mt(){var n=Et(["\n      background: #fff;\n      height: 500px;\n      padding: 0 16px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        padding: 0 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return Mt=function(){return n},n}function Ft(){var n=Et(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin: 12px 0px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            @media screen and (orientation: landscape) {\n              width: 80px;\n              height: 80px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n              @media screen and (orientation: landscape) {\n                margin-top: 8px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return Ft=function(){return n},n}function Bt(){var n=Et(["\n      margin: 20px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 0px 0 8px 0px;\n      }\n    "]);return Bt=function(){return n},n}function Tt(){var n=Et(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n\n      \n    "]);return Tt=function(){return n},n}function Lt(){var n=Et(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return Lt=function(){return n},n}function Rt(){var n=Et(["\n      overflow: hidden;\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      @media screen and (max-width: 800px){\n         width: 122px;\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n    "]);return Rt=function(){return n},n}function Ut(){var n=Et(["\n    \n    "]);return Ut=function(){return n},n}function Ht(){var n=Et(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n    "]);return Ht=function(){return n},n}function Gt(){var n=Et(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      .emptyImg{\n        width: 120px;\n        height: 120px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #5B5E60;\n      }\n    "]);return Gt=function(){return n},n}var Wt=(0,a.rU)(function(n){var e=n.css;return{root:e(Mt()),topInfo:e(Ft()),divider:e(Bt()),goodsInfo:e(Tt()),loading:e(Lt()),goodsItem:e(Rt()),selectIcon:e(Ut()),sizeInfo:e(Ht()),noData:e(Gt())}}),Yt=t(95301),Xt=t(31033),Vt=t(63038),Zt=t(44186);function qt(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Jt(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Kt(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Jt(o,r,i,a,l,"next",n)}function l(n){Jt(o,r,i,a,l,"throw",n)}a(void 0)})}}function Qt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function $t(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return qt(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return qt(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nr(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var er=(0,C.observer)(function(n){var e=n.selectedFigureElement,t=(0,M.P)(),a=(0,i.B)().t,l=Wt().styles,c=$t((0,P.useState)(null==e?void 0:e._candidate_materials),2),s=c[0],u=c[1],d=$t((0,P.useState)("套系素材"),2),f=d[0],p=d[1],h=$t((0,P.useState)(!1),2),m=h[0],x=h[1],g=(0,P.useRef)(null);(0,P.useEffect)(function(){e&&u(null==e?void 0:e._candidate_materials),p("套系素材")},[e]);return(0,P.useEffect)(function(){!function(n){Kt(function(){var t,r,i;return nr(this,function(a){switch(a.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?u(null==e?void 0:e._candidate_materials):u([]),[3,3]);case 1:return x(!0),[4,(0,Xt.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(r=a.sent()).success&&r.data?(i=r.data.materials.records.map(function(n){return{imageUrl:o.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}}),u(i)):u([]),x(!1),a.label=3;case 3:return[2]}})})()}(f)},[f]),(0,r.jsx)("div",{className:l.root,children:e&&(0,r.jsxs)(r.Fragment,{children:[e&&(0,r.jsx)("div",{className:l.topInfo,children:(0,r.jsxs)("div",{className:"info",children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,r.jsxs)("div",{className:"sizeInfo",children:[(0,r.jsx)("div",{children:e._matched_material.name}),(0,r.jsxs)("div",{className:"size",children:[a("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,r.jsxs)("div",{className:"size",children:[a("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]})]})]})}),(0,r.jsxs)("div",{className:l.divider,children:[(0,r.jsx)("div",{children:a("可用素材")}),(0,r.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some(function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)})&&!t.userStore.aihouse&&"C00002170"!==t.userStore.userInfo.tenantId&&(0,r.jsx)(N.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){g.current.onModal()},children:a("AI搭柜")})})]}),(0,r.jsx)(Yt.A,{block:!0,value:f,options:[a("套系素材"),a("云素材")],onChange:function(n){p(n)}}),(0,r.jsx)("div",{className:l.goodsInfo,children:m?(0,r.jsxs)("div",{className:l.loading,children:[(0,r.jsx)(Ae.A,{size:"large"})," "]}):s&&s.length>0?s.map(function(n,i){return(0,r.jsxs)("div",{className:l.goodsItem,onClick:function(){return Kt(function(){var r,a,l,c,s,u,d,p,h;return nr(this,function(m){switch(m.label){case 0:return e.locked?[2]:(t.designStore.setSelectedIndex(i),"套系素材"!==f?[3,1]:(D.nb.DispatchEvent(D.n0.ReplaceMaterial,n),[3,4]));case 1:return l=null,[4,(0,Xt.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(c=m.sent())||null===(a=c.result)||void 0===a||null===(r=a.result)||void 0===r?void 0:r[0])&&(l=null==c||null===(u=c.result)||void 0===u||null===(s=u.result)||void 0===s?void 0:s[0]),[4,(0,Vt.h)(n.materialId)];case 3:d=m.sent(),l&&(p={modelId:l.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:o.L4+n.imageUrl,name:l.MaterialName,originalLength:l.PICLength,originalWidth:l.PICWidth,originalHeight:l.PICHeight,length:l.PICLength,width:l.PICWidth,height:l.PICHeight,modelLoc:e.modelLoc,modelFlag:l.ModelFlag.toString(),topViewImage:d,figureElement:e},h=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Qt(n,e,t[e])})}return n}({},n,p),D.nb.DispatchEvent(D.n0.ReplaceMaterial,h)),m.label=4;case 4:return[2]}})})()},children:[i===t.designStore.selectedIndex&&(0,r.jsx)("div",{className:l.selectIcon}),(0,r.jsx)("img",{src:n.imageUrl,alt:""}),(0,r.jsx)("div",{className:l.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,r.jsx)("div",{className:l.sizeInfo,style:{color:"#959598"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},i)}):(0,r.jsxs)("div",{className:l.noData,children:[(0,r.jsx)("img",{className:"emptyImg",src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("span",{children:a("暂无可用素材")})]})}),(0,r.jsx)(Zt.A,{onParams:function(){},selectedFigureElement:e,ref:g})]})})});function tr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function rr(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function ir(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function or(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||lr(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ar(n){return function(n){if(Array.isArray(n))return tr(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||lr(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lr(n,e){if(n){if("string"==typeof n)return tr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?tr(n,e):void 0}}var cr=(0,C.observer)(function(){var n,e,t,o,a,l,c,s,u,d,f,p,h,m,x,g=(0,i.B)().t,v=Dt().styles,b=(0,M.P)(),y=or((0,P.useState)([]),2),w=(y[0],y[1]),j=or((0,P.useState)([]),2),S=j[0],_=j[1],k=or((0,P.useState)([]),2),I=k[0],C=k[1],A=or((0,P.useState)(""),2),N=A[0],O=A[1],E=or((0,P.useState)(!1),2),B=E[0],T=E[1],L=or((0,P.useState)(0),2),R=L[0],U=L[1],H=or((0,P.useState)(null),2),G=H[0],W=H[1],Y=or((0,P.useState)({"定制素材":!0,"软装素材":!0,"硬装素材":!0}),2),X=Y[0],V=Y[1],Z=or((0,P.useState)(!1),2),q=Z[0],J=Z[1],K=function(n){V(function(e){return ir(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){rr(n,e,t[e])})}return n}({},e),rr({},n,!e[n]))})},Q=[],$=[],nn=[],en=[];return D.nb.instance&&D.nb.on(F.U.RoomMaterialsUpdated,function(){U(R+1)}),(0,P.useEffect)(function(){!function(){var n,e,t,r,i,o,a,l,c,s,u,d,f,p,h,m,x,v,y,j,S,k,I,A=[];if(b.homeStore.selectEntity){var z=null===(t=b.homeStore.selectedRoom)||void 0===t?void 0:t._room;if(z){var N=ar(z._furniture_list);z._furniture_list.forEach(function(e){return(n=N).push.apply(n,ar(e.getAlternativeFigureElements()))}),N.sort(function(n,e){return e.default_drawing_order-n.default_drawing_order}),N.forEach(function(n){var e,t,r,i,o,a;if(D.nb.IsDebug||!n._is_decoration&&!n._is_sub_board)if(n.haveMatchedMaterial()||n.haveDeletedMaterial()||n.sub_category.includes("组合")){if("Electricity"!==n._decoration_type&&(n.haveMatchedMaterial()||n.haveDeletedMaterial()||!n.sub_category.includes("组合"))){var l=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",c=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无",s={image_path:l,title:g(n.sub_category)+" "+g("素材ID:")+c,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.length),"*").concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n,room:z};n.haveMatchedCustomCabinet()?en.push(s):$.push(s)}}else Q.push({image_path:(null===(a=Ie.eW[n.sub_category])||void 0===a?void 0:a.png)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",title:g(n.modelLoc)+" | "+g(n.sub_category),centerTitle:g(n.modelLoc),bottomTitle:"".concat(Math.round(n.length),"*").concat(Math.round(n.depth),"*").concat(Math.round(n.height)),figure_element:n,room:z})}),(e=$).unshift.apply(e,ar(Q)),z.getHardDecorationList().forEach(function(n){var e,t,r,i,o;if(n.haveMatchedMaterial()){var a=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",l=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无";nn.push({image_path:a,title:g(n.sub_category)+" "+g("素材ID:")+l,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.length),"*").concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n})}}),A.push({title1:(null===(i=z._scope_series_map)||void 0===i||null===(r=i.soft)||void 0===r?void 0:r.ruleName)||null,title2:(null===(a=z._scope_series_map)||void 0===a||null===(o=a.cabinet)||void 0===o?void 0:o.ruleName)||null,title3:(null===(c=z._scope_series_map)||void 0===c||null===(l=c.hard)||void 0===l?void 0:l.ruleName)||null,img1:(null===(u=z._scope_series_map)||void 0===u||null===(s=u.soft)||void 0===s?void 0:s.thumbnail)||null,img2:(null===(f=z._scope_series_map)||void 0===f||null===(d=f.cabinet)||void 0===d?void 0:d.thumbnail)||null,img3:(null===(h=z._scope_series_map)||void 0===h||null===(p=h.hard)||void 0===p?void 0:p.thumbnail)||null,softseriesStyle:null===(x=z._scope_series_map)||void 0===x||null===(m=x.soft)||void 0===m?void 0:m.seriesName,cabinetseriesStyle:null===(y=z._scope_series_map)||void 0===y||null===(v=y.cabinet)||void 0===v?void 0:v.seriesName,hardseriesStyle:null===(S=z._scope_series_map)||void 0===S||null===(j=S.hard)||void 0===j?void 0:j.seriesName,bottomTitle:z.roomname,area:null===(k=z.area)||void 0===k?void 0:k.toFixed(2),room:z});var P=[{label:g("风格"),figureList:A},{label:g("定制素材"),figureList:en},{label:g("软装素材"),figureList:$},{label:g("硬装素材"),figureList:nn}];w(P),_(null===(I=P[0])||void 0===I?void 0:I.figureList),C(P.filter(function(n,e){return 0!==e}))}}}()},[b.homeStore.selectEntity,b.homeStore.room2SeriesSampleArray,R,B]),(0,r.jsxs)("div",{className:v.root,children:[(0,r.jsxs)("div",{className:v.styleInfo,children:[(0,r.jsx)("div",{className:v.styleItem,onClick:function(){J(!0),O("软装")},children:(null===(n=S[0])||void 0===n?void 0:n.title1)&&(null===(e=S[0])||void 0===e?void 0:e.img1)?(0,r.jsxs)("div",{className:"item",children:[(0,r.jsx)("img",{src:null===(t=S[0])||void 0===t?void 0:t.img1,alt:""}),(0,r.jsx)("div",{className:"title",children:g("软装")}),(0,r.jsxs)("div",{className:"rightitem",children:[(0,r.jsx)("span",{children:null===(o=S[0])||void 0===o?void 0:o.title1}),(0,r.jsx)("span",{className:"seriesStyle",children:null===(a=S[0])||void 0===a?void 0:a.softseriesStyle})]})]}):(0,r.jsxs)("span",{className:"add",children:[(0,r.jsx)(vt.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),g("添加软装风格")]})}),(0,r.jsx)("div",{className:v.styleItem,onClick:function(){J(!0),O("定制")},children:(null===(l=S[0])||void 0===l?void 0:l.title2)&&(null===(c=S[0])||void 0===c?void 0:c.img2)?(0,r.jsxs)("div",{className:"item",children:[(0,r.jsx)("img",{src:null===(s=S[0])||void 0===s?void 0:s.img2,alt:""}),(0,r.jsx)("div",{className:"title",children:g("定制")}),(0,r.jsxs)("div",{className:"rightitem",children:[(0,r.jsx)("span",{children:null===(u=S[0])||void 0===u?void 0:u.title2}),(0,r.jsx)("span",{className:"seriesStyle",children:null===(d=S[0])||void 0===d?void 0:d.cabinetseriesStyle})]})]}):(0,r.jsxs)("span",{className:"add",children:[(0,r.jsx)(vt.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),g("添加定制风格")]})}),(0,r.jsx)("div",{className:v.styleItem,onClick:function(){J(!0),O("硬装")},children:(null===(f=S[0])||void 0===f?void 0:f.title3)&&(null===(p=S[0])||void 0===p?void 0:p.img3)?(0,r.jsxs)("div",{className:"item",children:[(0,r.jsx)("img",{src:null===(h=S[0])||void 0===h?void 0:h.img3,alt:""}),(0,r.jsx)("div",{className:"title",children:g("硬装")}),(0,r.jsxs)("div",{className:"rightitem",children:[(0,r.jsx)("span",{children:null===(m=S[0])||void 0===m?void 0:m.title3}),(0,r.jsx)("span",{className:"seriesStyle",children:null===(x=S[0])||void 0===x?void 0:x.hardseriesStyle})]})]}):(0,r.jsxs)("span",{className:"add",children:[(0,r.jsx)(vt.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),g("添加硬装风格")]})})]}),(0,r.jsx)("div",{className:v.materialInfo,children:I.map(function(n){return(0,r.jsxs)("div",{className:"itemInfo",children:[(0,r.jsxs)("div",{className:"header",children:[(0,r.jsxs)("span",{className:"title",onClick:function(){return K(n.label)},children:[n.label,X[n.label]]}),X[n.label]?(0,r.jsx)(vt.A,{type:"icon-a-fangxiangxia",style:{fontSize:16,color:"#959598"},onClick:function(){return K(n.label)}}):(0,r.jsx)(vt.A,{type:"icon-a-fangxiangyou",style:{fontSize:16,color:"#959598"},onClick:function(){return K(n.label)}})]}),X[n.label]&&(0,r.jsx)("div",{className:"itemList",children:n.figureList.map(function(n,e){var t,i,o;return(0,r.jsxs)("div",{className:"item",onClick:function(){var e;T(!0),b.homeStore.setSelectEntity(n.figure_element.furnitureEntity),W(n.figure_element),(null==n||null===(e=n.figure_element)||void 0===e?void 0:e.haveMatchedMaterial())||z.A.error(g("当前套系缺失".concat(null==n?void 0:n.figure_element.sub_category,"素材，请联系管理员补全"))),(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&z.A.warning(g("超出目标尺寸范围"))},children:[(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&(0,r.jsx)(vt.A,{className:v.warn_icon,type:"icon-a-tianchongFace"}),!(null==n||null===(t=n.figure_element)||void 0===t?void 0:t.haveMatchedMaterial())&&(0,r.jsx)(vt.A,{type:"icon-a-tianchongFace-1",className:"redIcon"}),(0,r.jsx)("img",{src:n.image_path,alt:""}),(0,r.jsx)("div",{children:n.centerTitle}),(0,r.jsx)("div",{style:{color:"#959598",marginTop:"4px"},children:n.bottomTitle}),(null==n||null===(i=n.figure_element)||void 0===i?void 0:i.haveMatchedMaterial())&&(0,r.jsx)(vt.A,{className:v.lock_icon,type:(null===(o=n.figure_element)||void 0===o?void 0:o.locked)?"icon-suoding1":"icon-jiesuo1",onClick:function(e){var t;n.room&&(null===(t=n.room)||void 0===t?void 0:t.locked)||n.figure_element&&(n.figure_element.locked=!n.figure_element.locked,D.nb.emit(F.U.RoomMaterialsUpdated,!0),D.nb.instance.update(),e.stopPropagation(),W(n.figure_element))}})]},e)})})]},n.label)})}),q&&(0,r.jsx)("div",{className:v.visible,onClick:function(){J(!1)},children:(0,r.jsx)("div",{className:"".concat(v.serialsInfo," ").concat(q?"show":""),onClick:function(n){return n.stopPropagation()},children:(0,r.jsx)(ke,{type:N})})}),(0,r.jsxs)("div",{className:"".concat(v.sideVisible," ").concat(B?v.slideIn:v.slideOut),children:[(0,r.jsxs)("div",{className:"sideTopInfo",children:[(0,r.jsx)("div",{children:g("模型位信息")}),(0,r.jsx)("div",{children:(0,r.jsx)(vt.A,{type:"icon-icon",style:{color:"#5B5E60"},onClick:function(){T(!1)}})})]}),(0,r.jsx)(er,{selectedFigureElement:G})]})]})});function sr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ur(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return sr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return sr(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var dr=function(n){return n.setIsVisible="setIsVisible",n.showPopup="showPopup",n}({}),fr=(0,C.observer)(function(){var n,e=(0,i.B)().t,t=dn().styles,o=ur((0,P.useState)(!0),2),a=(o[0],o[1],ur((0,P.useState)(""),2)),l=a[0],c=a[1],s=ur((0,P.useState)(!1),2),u=s[0],d=s[1],f=(0,M.P)();(0,P.useEffect)(function(){D.nb.on("showPopup",function(n){n?(d(!0),c(n)):d(!1)})},[]);return(0,r.jsxs)("div",{className:t.root,style:{zIndex:10,maxHeight:f.homeStore.IsLandscape&&(u?"800px":"0px"),maxWidth:!f.homeStore.IsLandscape&&(u?"376px":"0px"),transition:"all 0.3s ease"},children:["view"!=l&&(0,r.jsxs)("div",{className:t.topTitle,children:[(0,r.jsx)("div",{children:function(n){switch(n){case"Layout":return e("布局");case"Matching":return e("");case"view":return e("视角");case"material":return e("素材");case"attribute":return e("属性");case"replace":return e("替换素材");case"searchMaterial":return e("空间素材");default:return""}}(l)}),(0,r.jsx)("div",{children:(0,r.jsx)(vt.A,{style:{color:"#959598"},type:"icon-icon",onClick:function(){var n;d(!1),"3D_FirstPerson"===f.homeStore.viewMode&&"Furniture"===(null===(n=f.homeStore.selectEntity)||void 0===n?void 0:n.type)&&D.nb.DispatchEvent(D.n0.cleanSelect,null)}})})]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"Layout"===l?"block":"none"},children:[" ",(0,r.jsx)(fn.A,{width:400,showSchemeName:!1,isLightMobile:!0})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"Matching"===l?"block":"none"},children:[" ",(0,r.jsx)(ke,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"material"===l?"block":"none"},children:[" ",(0,r.jsx)(rt,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"attribute"===l?"block":"none"},children:[" ",(0,r.jsx)(gt,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"replace"===l?"block":"none"},children:[" ",(0,r.jsx)(bt.A,{selectedFigureElement:null===(n=f.homeStore.selectEntity)||void 0===n?void 0:n.figure_element})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"searchMaterial"===l?"block":"none"},children:[" ",(0,r.jsx)(cr,{})," "]})]})}),pr=t(99030),hr=t(32184);function mr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function xr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return mr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return mr(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var gr=function(n){return n.showLight3DViewer="showLight3DViewer",n}({}),vr=((0,C.observer)(function(){var n=(0,i.B)().t,e=on().styles,t=xr((0,P.useState)(""),2),a=(t[0],t[1]),l=xr((0,P.useState)(-2),2),c=l[0],s=l[1],u=(0,M.P)();(0,P.useRef)(null);D.nb.UseApp(E.e.AppName),D.nb.instance&&(D.nb.t=n);var d=function(){D.nb.instance&&(D.nb.instance.bindCanvas(document.getElementById("cad_canvas")),D.nb.instance.update())};return(0,P.useEffect)(function(){D.nb.instance&&(D.nb.instance._is_website_debug=o.iG),window.addEventListener("resize",d),d(),D.nb.instance&&(D.nb.instance.initialized||(D.nb.instance.init(),D.nb.RunCommand(E.f.AiCadMode),D.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,D.nb.instance.layout_container.drawing_figure_mode=hr.qB.Texture,D.nb.instance.prepare().then(function(){}),D.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),D.nb.instance.update()),D.nb.on_M("showLight3DViewer","LightMain",function(n){n?(s(2),D.nb.emit(pr.r.UpdateScene3D,!1)):s(-1)}),D.nb.on(F.U.LayoutSchemeOpened,function(n){a(n.name),D.nb.emit(Cr,Ir.Default)})},[]),(0,r.jsxs)("div",{className:e.root,children:[(0,r.jsx)(Ar,{}),(0,r.jsx)(fr,{}),(0,r.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,r.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:c},children:(0,r.jsx)(B.A,{defaultViewMode:4})}),(0,r.jsx)("div",{id:"body_container",className:e.canvas_pannel,children:(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){u.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){u.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t);u.homeStore.setInitialDistance(r/u.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t)/u.homeStore.initialDistance;r>5?r=5:r<.05&&(r=.05),u.homeStore.setScale(r),D.nb.DispatchEvent(D.n0.scale,r)}},onTouchEnd:function(){u.homeStore.setInitialDistance(null)}})})]})]})}),t(67869)),br=t(10371);function yr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function wr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return yr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return yr(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var jr=(0,C.observer)(function(){var n=(0,i.B)().t,e=Z().styles,t=(0,M.P)(),o=wr((0,P.useState)(t.homeStore.viewMode||"2D"),2),a=o[0],l=o[1],c=D.nb.instance.layout_container,s=D.nb.instance.scene3D;(0,P.useEffect)(function(){if(D.nb.emit_M(gr.showLight3DViewer,!0),"2D"===a)s&&s.stopRender(),D.nb.emit_M(gr.showLight3DViewer,!1),t.homeStore.setViewMode("2D");else if("3D"===a)D.nb.emit_M(gr.showLight3DViewer,!0),s.setCemeraMode(br.I5.Perspective),t.homeStore.setViewMode("3D"),"SingleRoom"===c._drawing_layer_mode&&D.nb.DispatchEvent(D.n0.leaveSingleRoomLayout,{}),s&&s.startRender();else if("3D_FirstPerson"===a){s.setCemeraMode(br.I5.FirstPerson);var n,e,r,i=t.homeStore.roomEntities.reduce(function(n,e){return n?e._area>n._area?e:n:e},null);if(i)s.setCenter((null==i||null===(n=i._main_rect)||void 0===n?void 0:n.rect_center)||new vr.Pq0(0,0,0)),s.update();else s.setCenter((null===(r=t.homeStore.roomEntities[0])||void 0===r||null===(e=r._main_rect)||void 0===e?void 0:e.rect_center)||new vr.Pq0(0,0,0));t.homeStore.setViewMode(a),s&&s.startRender()}D.nb.emit_M(F.U.Scene3DUpdated,!0)},[a]);var u=[{value:"2D",label:n("2D")},{value:"3D_FirstPerson",label:n("漫游")},{value:"3D",label:n("鸟瞰")}];return(0,r.jsx)("div",{className:e.root,children:(0,r.jsx)(Yt.A,{value:a,onChange:function(n){l(n)},block:!0,size:"middle",options:u})})}),Sr=t(57235);function _r(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function kr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return _r(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return _r(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ir=function(n){return n.Default="Default",n.HouseSearch="HouseSearch",n.HuaweiDemo="HuaweiDemo",n}({}),Cr="NavigationEvent",Ar=(0,C.observer)(function(n){var e=(0,i.B)().t,t=G().styles,a=D.nb.instance.layout_container,l=kr((0,P.useState)(!0),2),c=(l[0],l[1]),s=kr((0,P.useState)(!0),2),u=(s[0],s[1]);return(0,P.useEffect)(function(){D.nb.instance&&(D.nb.instance.updateSlot("TopMenu_RedoableSlot",{ui_name:"RedoDisabled",target:D.nb.instance,callback:function(n){c(!n)}}),D.nb.instance.updateSlot("TopMenu_UndoableSlot",{ui_name:"UndoDisabled",target:D.nb.instance,callback:function(n){u(!n)}})),D.nb.instance.connect_obj(Sr.n.signalRedoable,D.nb.instance,"TopMenu_RedoableSlot"),D.nb.instance.connect_obj(Sr.n.signalUndoable,D.nb.instance,"TopMenu_UndoableSlot"),D.nb.on(Cr,function(n){n||(n="Default")})},[]),(0,r.jsxs)("div",{className:t.navigation,children:[!o.Ic&&(0,r.jsx)("div",{className:t.backBtn,onClick:function(){0==a._room_entities.length?window.location.href=o.O9:(D.nb.DispatchEvent(D.n0.autoSave,null),z.A.loading(e("保存中...")),setTimeout(function(){z.A.destroy(),window.location.href=o.O9},1500))},children:(0,r.jsx)(vt.A,{type:"icon-zhuye",style:{fontSize:"18px",marginRight:"4px"}})}),(0,r.jsx)(jr,{})]})});function zr(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Nr(){var n=zr(["\n      width: 600px;\n      height: 650px;\n      background:#fff;\n      position:fixed;\n      overflow: hidden;\n      z-index:10;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      padding: 0 20px;\n      .iconicon\n      {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n      }\n      .houseSearch {\n        border-radius: 8px;\n        width: 80%;\n        max-width:520px;\n        height: 40px;\n        line-height: 40px;\n        border: 1px solid #e5e7eb;\n        margin: 40px auto;\n        display: flex;\n        .search-btn {\n          width: 60px;\n          background: #147FFA;\n          border-radius: 0 8px 8px 0;\n          text-align: center;\n          font-family: PingFangSC-Semibold;\n          font-size: 15px;\n          color: #fff;\n          font-weight: 550;\n          cursor: pointer;\n        }\n        .search-input {\n          width: calc(100% - 120px);\n          padding-left: 22px;\n          font-family: PingFangSC-Regular;\n          font-size: 13px;\n          color: #2e3238 !important;\n          box-sizing: border-box;\n          border: none;\n          background: none;\n          box-shadow: none;\n        }\n        .candidate_buiding_names {\n          width: calc(100vw - 200px);\n          background:#fff;\n          left:70px;\n          height : 250px;\n          position:absolute;\n          top:60px;\n          padding: 20px 0 10px;\n          z-index: 2;\n          box-shadow: 0 3px 12px rgba(0, 0, 0, .2);\n          border-radius: 0 0 2px 2px;\n        }\n        .select-operate {\n          display: flex;\n          justify-content: space-between;\n          line-height: 22px;\n          height: 30px;\n          position: relative;\n          margin-bottom: 4px;\n          padding: 0 20px;\n          font-size: 14px;\n          color: #747b81;\n        }\n        .select_item {\n          padding: 0 20px;\n          height: 38px;\n          line-height: 38px;\n          font-family: PingFangSC-Medium;\n          font-size: 14px;\n          color: #2e3238;\n          font-weight: 600;\n          display: block;\n          cursor: pointer;\n          &:hover {\n            background:rgba(0,0,0,.02)\n          }\n          .select-item_left {\n            font-family: PingFangSC-Regular;\n            font-size: 12px;\n            color: #2e3238;\n            font-weight: 400;\n          }\n          .select-item_left {\n            display: inline-block;\n            float:left;\n          }\n          .select-item_right {\n            font-family: PingFangSC-Regular;\n            font-size: 12px;\n            color: #2e3238;\n            font-weight: 400;\n            display: inline-block;\n            float:right;\n          }\n        }\n      }\n      .style_trigger__1c72413f {\n        width: 60px;\n        cursor: pointer;\n        text-align: center;\n        display: flex;\n        justify-content: center;\n      }\n      .style_city_label__1c72413f {\n        font-size: 15px;\n        font-weight: 550;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        display: block;\n        max-width: 108px;\n      }\n      .line {\n        width: 1px;\n        height: 20px;\n        left: 94px;\n        top: 10px;\n      }\n      .anticon {\n        display: inline-block;\n        color: inherit;\n        font-style: normal;\n        line-height: 40px;\n        text-align: center;\n        text-transform: none;\n        vertical-align: -.125em;\n        text-rendering: optimizelegibility;\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n      }\n      .buildingContent {\n         display:flex;\n         flex-flow:row wrap;\n         /* margin:auto; */\n         height: calc(100% - 200px);\n         overflow-y:auto;\n         justify-content: space-between;\n      }\n\n      .house-card {\n          width: 175px;\n          height: 278px;\n          padding: 8px 8px 16px;\n          border-radius: 8px;\n          box-shadow: 0 2px 24px rgba(0, 0, 0, .1);\n          transition: all .3s;\n          text-align:center;\n          cursor:pointer;\n          &:hover {\n            box-shadow: 0 2px 24px rgba(0, 0, 0, .5);\n\n          }\n          img {\n            width:100%;\n          }\n          .house-card__info {\n            font-size: 14px;\n            line-height: 22px;\n            text-align:left;\n            .name {\n              color: #2e3238;\n              line-height: 24px;\n              font-weight: 600;\n              margin-top:5px;\n            }\n            .detail-wrap {\n              margin-top: 2px;\n              display: block;\n            }\n            .houseType{\n              color: #2e3238;\n              margin-top:2px;\n            }\n            .location {\n              color: #a5adb6;\n              margin-top:2px;\n\n            }\n          }\n      }\n\n    }\n    "]);return Nr=function(){return n},n}function Pr(){var n=zr(["\n      width:100%;\n      min-height:70px;\n    "]);return Pr=function(){return n},n}function Or(){var n=zr(["\n      width: 140px;\n      height: 46px;\n      position:absolute;\n      top:50px;\n      left : calc(50% - 70px);\n      background: url(data:image/png;base64,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);\n      background-size: cover;\n    "]);return Or=function(){return n},n}function Dr(){var n=zr(["\n      position:absolute;\n      width: calc(100% - 60px);\n      height: 400px;\n      top:160px;\n      left:40px;\n      text-align:left;\n      overflow-y:auto;\n      background:#fff;\n      border:1px solid #eee;\n      box-shadow: 0 2px 24px rgba(0, 0, 0, .5);\n\n      .row {\n          width:100%;\n          line-heght:20px;\n      }\n      .provice {\n        float:left;\n        margin-right:20px;\n        margin-left:10px;\n        padding:2px;\n        cursor:pointer;\n      }\n      .cities {\n        float:left;\n        width:calc(100% - 90px);\n      }\n      .city {\n        float:left;\n        margin-right:10px;\n        padding:2px;\n        cursor:pointer;\n        &:hover {\n          background:#aaaaaa33;\n        }\n      }\n    "]);return Dr=function(){return n},n}var Er=(0,a.rU)(function(n){var e=n.css;return{root:e(Nr()),row:e(Pr()),hx_logo:e(Or()),districts:e(Dr())}}),Mr=t(69391);function Fr(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Br(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Fr(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Fr(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Tr=[],Lr=function(n){var e=Er().styles,t=Br((0,P.useState)([]),2),i=(t[0],t[1]),o=Br((0,P.useState)([]),2);o[0],o[1];return(0,P.useEffect)(function(){0==Tr.length?fetch("https://3vj-fe.3vjia.com/fe_oss_prod/static/swj/district/prod/district-v3.json").then(function(n){return n.json()}).then(function(n){(Tr=n).sort(function(n,e){return n.firstLetter.localeCompare(e.firstLetter)}),i(Tr)}):i(Tr)},[]),n.is_visible?(0,r.jsx)("div",{className:e.districts,children:Tr.map(function(e,t){return(0,r.jsxs)("div",{className:"row",children:[(0,r.jsx)("div",{className:"provice",children:e.label}),(0,r.jsx)("div",{className:"cities",children:e.children.map(function(e,t){return(0,r.jsx)("div",{className:"city",onClick:function(){n.onSelected&&n.onSelected({name:e.label,code:e.value})},children:e.label},"city_sub_"+t)})})]},"Distric_0_"+t)})}):(0,r.jsx)(r.Fragment,{})},Rr=t(27164);function Ur(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Hr(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Gr(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Hr(o,r,i,a,l,"next",n)}function l(n){Hr(o,r,i,a,l,"throw",n)}a(void 0)})}}function Wr(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Ur(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ur(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yr(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Xr=(0,C.observer)(function(){var n=(0,i.B)().t,e=Er().styles,t=(0,P.useRef)(null),o=Wr((0,P.useState)([]),2),a=o[0],l=o[1],c=Wr((0,P.useState)([]),2),s=c[0],u=c[1],d=Wr((0,P.useState)(""),2),f=d[0],p=(d[1],Wr((0,P.useState)(!1),2)),h=p[0],m=p[1],x=Wr((0,P.useState)(!1),2),g=x[0],v=x[1],b=Wr((0,P.useState)({name:"广州市",code:"440100"}),2),y=b[0],w=b[1],j=function(n){return Gr(function(){var e,t;return Yr(this,function(r){switch(r.label){case 0:return[4,Mr.Q.search("",n.code)];case 1:return e=r.sent(),t=(null==e?void 0:e.records)||[],l(t||[]),[2]}})})()};return(0,P.useEffect)(function(){D.nb.on("setIsVisible",function(n){m(n)}),Gr(function(){var n;return Yr(this,function(e){switch(e.label){case 0:return[4,Mr.Q.ipParse()];case 1:return(n=e.sent())&&n.city&&(w(n.city),j(n.city)),[2]}})})()},[]),h?(0,r.jsxs)("div",{className:e.root,children:[(0,r.jsx)(Rr.A,{iconClass:"iconclose1",className:"iconicon",onClick:function(){m(!1)}}),(0,r.jsx)("div",{className:e.row,children:(0,r.jsx)("div",{className:e.hx_logo})}),(0,r.jsx)("div",{className:e.row,children:(0,r.jsxs)("div",{className:"houseSearch",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"style_trigger__1c72413f",children:[(0,r.jsx)(Lr,{is_visible:g,onSelected:function(n){w(n),j(n),v(!1)}}),(0,r.jsx)("span",{className:"style_city_label__1c72413f",onClick:function(){return v(!g)},children:y.name}),(0,r.jsx)("span",{role:"img","aria-label":"caret-down",className:"anticon anticon-caret-down",children:(0,r.jsx)("svg",{viewBox:"0 0 1024 1024",focusable:"false","data-icon":"caret-down",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"})})})]})}),(0,r.jsx)("div",{className:"line"}),s&&s.length>0&&(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsxs)("div",{className:"candidate_buiding_names",children:[(0,r.jsx)("div",{className:"select-operate",children:(0,r.jsx)("div",{children:"你是不是想找"})}),s.map(function(e,i){return(0,r.jsxs)("div",{className:"select_item",onClick:function(){return Gr(function(){var n,r;return Yr(this,function(i){switch(i.label){case 0:return t&&t.current?(t.current.value=e.buildingName,[4,Mr.Q.search(e.buildingName,y.code)]):[3,2];case 1:n=i.sent(),r=(null==n?void 0:n.records)||[],l(r||[]),i.label=2;case 2:return u([]),[2]}})})()},children:[(0,r.jsx)("div",{className:"select-item_left",children:(0,r.jsx)("b",{children:e.buildingName})}),(0,r.jsx)("div",{className:"select-item_right",children:(0,r.jsxs)("span",{children:[e.roomCount,n("个结果")]})})]},"candidate"+i)})]})}),(0,r.jsx)("input",{placeholder:n("输入楼盘、小区名称查找户型"),className:"ant-input search-input",type:"text",defaultValue:f,ref:t,onChange:function(n){return Gr(function(){var e,t;return Yr(this,function(r){switch(r.label){case 0:return e=n.target.value,[4,Mr.Q.getBuildingList(e,y.code)];case 1:return t=r.sent(),u(t||[]),[2]}})})()}}),(0,r.jsx)("div",{className:"search-btn",onClick:function(){return Gr(function(){var n,e,r;return Yr(this,function(i){switch(i.label){case 0:return t.current?(n=t.current.value,[4,Mr.Q.search(n,y.code)]):[3,2];case 1:e=i.sent(),r=(null==e?void 0:e.records)||[],l(r||[]),i.label=2;case 2:return[2]}})})()},children:n("搜户型")})]})}),(0,r.jsx)("div",{className:e.row+" buildingContent",children:a.map(function(n,e){return(0,r.jsxs)("div",{className:"house-card",onClick:function(){D.nb.instance&&(D.nb.DispatchEvent(D.n0.PostBuildingId,{id:n.id,name:n.buildingName}),m(!1))},children:[(0,r.jsx)("img",{src:n.imagePath,alt:n.buildingName}),(0,r.jsxs)("div",{className:"house-card__info",children:[(0,r.jsx)("div",{className:"name",children:n.buildingName}),(0,r.jsxs)("div",{className:"detail-wrap",children:[(0,r.jsxs)("div",{className:"houseType",children:[n.roomTypeName," | ",n.area+"m²","  "]}),(0,r.jsxs)("div",{className:"location",children:[n.provinceName," / ",n.cityName]})]})]})]},"build_result_"+e)})})]}):(0,r.jsx)(r.Fragment,{})}),Vr="dialog_root-zX7eS",Zr="dialog_header-OU7id",qr="dialog_tile-n_RJq",Jr="dialog_close-fjBI6",Kr="dialog_content-a16Z8",Qr="dialog_form-OOY6z",$r="dialog_save_button-BVZyR",ni="dialog_exit_button-FIAP4",ei=t(88341),ti=t(65640);function ri(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ii(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ri(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ri(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var oi=(0,C.observer)(function(n){var e=n.closeCb,t=(n.schemeName,n.isSaveAs),o=(n.isSaveAndExit,(0,i.B)().t),a=(0,M.P)(),l=a.homeStore.showSaveLayoutSchemeDialog,c=a.trialStore.houseData;(0,P.useEffect)(function(){var n="未命名";c.buildingName&&c.area&&(n="".concat(c.buildingName," ").concat(c.area,"m²")),s.setFieldValue("schemename",n)},[]);var s=ii(ei.A.useForm(),1)[0];return(0,r.jsxs)("div",{className:Vr,children:[(0,r.jsxs)("div",{className:Zr,children:[(0,r.jsx)("span",{className:qr,children:o(t?"方案另存为":"保存方案")}),(0,r.jsx)("div",{onClick:e,className:Jr,children:(0,r.jsx)(Rr.A,{style:{fontSize:"20px"},iconClass:"icon-close1"})})]}),(0,r.jsx)("div",{className:Kr,children:(0,r.jsxs)(ei.A,{form:s,name:"basic",labelCol:{span:6},wrapperCol:{span:16},style:{maxWidth:500},initialValues:{remember:!0},onFinish:function(n){D.nb.DispatchEvent(D.n0.SaveLayoutSchemeAs,n),"nextBtn"==l.source&&window.parent.postMessage({origin:"layoutai.api",type:"saveLayoutSchemeAndNext",data:{next:!0}},"*"),"exitBtn"==l.source&&(ti.log("保存方案并且退出"),window.parent.postMessage({origin:"layoutai.api",type:"closeIframe",data:{canClose:!0}},"*")),"aiGeneration"==l.source&&(ti.log("去生图并退出"),window.parent.postMessage({origin:"layoutai.api",type:"aiGeneration",data:{aiGeneration:!0,img:""}},"*")),e()},autoComplete:"on",className:Qr,children:[(0,r.jsx)(ei.A.Item,{label:o("方案名称"),name:"schemename",wrapperCol:{span:24},rules:[{required:!0,message:o("请输入方案名称")}],style:{marginBottom:"8px"},children:(0,r.jsx)(ut.A,{placeholder:o("请输入")})}),(0,r.jsx)(ei.A.Item,{label:o("姓名"),name:"username",wrapperCol:{span:8},style:{marginBottom:"8px"},children:(0,r.jsx)(ut.A,{placeholder:o("请输入")})}),(0,r.jsx)(ei.A.Item,{label:o("手机号"),name:"telephone",wrapperCol:{span:8},getValueFromEvent:function(n){return n.target.value.replace(/\D/g,"")},style:{marginBottom:"8px"},children:(0,r.jsx)(ut.A,{placeholder:o("请输入")})}),(0,r.jsx)(ei.A.Item,{label:o("地址"),name:"address",wrapperCol:{span:24},style:{marginBottom:"8px"},children:(0,r.jsx)(ut.A,{placeholder:o("请输入")})}),(0,r.jsx)(ei.A.Item,{wrapperCol:{span:24},style:{textAlign:"right",marginTop:"20px",marginBottom:"0px"},children:(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",gap:"12px"},children:[(0,r.jsx)(N.A,{type:"primary",htmlType:"submit",className:$r,children:"exitBtn"==l.source?o("保存退出"):o("确定")}),(0,r.jsx)(N.A,{onClick:function(){"exitBtn"==l.source&&(ti.log("直接退出"),window.parent.postMessage({origin:"layoutai.api",type:"closeIframe",data:{canClose:!0}},"*")),e()},className:ni,children:"exitBtn"==l.source?o("直接退出"):o("取消")})]})})]})})]})}),ai=t(22640),li=t(84872),ci=t(58567),si=t(20995),ui=t(56697),di=t(88863),fi=t(69283),pi=t(41140),hi=t(64186),mi=t(65640);function xi(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function gi(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){xi(o,r,i,a,l,"next",n)}function l(n){xi(o,r,i,a,l,"throw",n)}a(void 0)})}}function vi(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function bi(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var yi=new function n(){var e=this;!function(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),vi(this,"_GetSchemeListByPagePath","/api/njvr/layoutSchemeMult/listByPage"),vi(this,"_InsertSchemePath","/api/njvr/layoutSchemeMult/insert"),vi(this,"_DeleteSchemePath","/api/njvr/layoutSchemeMult/delete"),vi(this,"_GetLayoutSchemeMultDetailPath","/api/njvr/layoutSchemeMult/get"),vi(this,"getLayoutSchemeMultDetail",function(n){return gi(function(){var e;return bi(this,function(t){switch(t.label){case 0:return[4,(0,hi.Ap)({method:"post",url:this._GetLayoutSchemeMultDetailPath,data:{id:n}})];case 1:return(e=t.sent())&&e.success?(mi.log(e),[2,e.result]):(mi.warn("getLayoutSchemeMultDetail response is null"),[2,null])}})}).call(e)}),vi(this,"loadSchemeList",function(n){return gi(function(){var e,t,r;return bi(this,function(i){switch(i.label){case 0:return[4,(0,hi.Ap)({method:"post",url:this._GetSchemeListByPagePath,data:{originSchemeId:n,page:1,pageSize:10,orderBy:"create_date desc"},timeout:6e4}).catch(function(n){mi.error(n)})];case 1:return(e=i.sent())&&e.success?(null==e||null===(r=e.result)||void 0===r||null===(t=r.result)||void 0===t?void 0:t.length)>0?[2,e.result.result]:[2,[]]:(mi.warn("getSchemeListByPage response is null"),[2,[]])}})}).call(e)}),vi(this,"insertScheme",function(n,t,r){return gi(function(){var e;return bi(this,function(i){switch(i.label){case 0:return[4,(0,hi.Ap)({method:"post",url:this._InsertSchemePath,data:{originSchemeId:n,refSchemeId:t,refSchemeName:r}})];case 1:return(e=i.sent())&&e.success?[2,!0]:[2,!1]}})}).call(e)}),vi(this,"deleteScheme",function(n){return gi(function(){var e;return bi(this,function(t){switch(t.label){case 0:return[4,(0,hi.Ap)({method:"post",url:this._DeleteSchemePath,data:{id:n}})];case 1:return(e=t.sent())&&e.success?[2,!0]:[2,!1]}})}).call(e)})};function wi(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ji(){var n=wi(["\n            position: absolute;\n            top: ",";\n            right: ",";\n            bottom: ",";\n            width: 218px;\n            background: ",";\n            overflow: hidden;\n            ",";\n            padding: 16px;\n            display: flex;\n            flex-direction: column;\n        "]);return ji=function(){return n},n}function Si(){var n=wi(["\n            font-size: 16px;\n            font-weight: 600;\n            margin-bottom: 16px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            background: ",";\n        "]);return Si=function(){return n},n}function _i(){var n=wi(["\n            font-size: 18px;\n            color: ",";\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            line-height: 1;\n            height: 18px;\n            &:hover {\n                color: ",";\n            }\n        "]);return _i=function(){return n},n}function ki(){var n=wi(["\n            display: flex;\n            flex-direction: column;\n            overflow-y: auto;\n            overflow-x: hidden;\n            flex: 1;\n            width: 100%;\n            align-items: center;\n        "]);return ki=function(){return n},n}function Ii(){var n=wi(["\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            width: 100%;\n            cursor: pointer;\n        "]);return Ii=function(){return n},n}function Ci(){var n=wi(["\n            width: 180px;\n            height: 135px;\n            background-color: #38295710;\n            border-radius: 8px;\n            border: 1px solid ",";\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        "]);return Ci=function(){return n},n}function Ai(){var n=wi(["\n            width: 180px;\n            height: 135px;\n            background-color: #3829570A;;\n            border-radius: 8px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        "]);return Ai=function(){return n},n}function zi(){var n=wi(["\n            width: 180px;\n            height: 135px;\n            background-color: #3829570A;\n            border-radius: 8px;\n            border: 1px dashed ",";\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            cursor: pointer;\n            flex-direction: column;\n            justify-content: center;\n            text-align: center; \n        "]);return zi=function(){return n},n}function Ni(){var n=wi(["\n            font-size: 20px;\n        "]);return Ni=function(){return n},n}function Pi(){var n=wi(["\n            font-size: 12px;\n            margin-top: 8px;\n            display: block;\n            text-align: center;\n            color: gray;\n        "]);return Pi=function(){return n},n}function Oi(){var n=wi(["\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            width: 180px;\n            height: 36px;\n            padding: 0 6px;\n        "]);return Oi=function(){return n},n}function Di(){var n=wi(["\n            font-size: 14px;\n            font-weight: 600;\n            color: ",";\n            padding-left: 6px;\n        "]);return Di=function(){return n},n}function Ei(){var n=wi(["\n            color: ",";\n            cursor: pointer;\n            font-size: 14px;\n            display: flex;\n            align-items: center;\n            padding-right: 6px;\n            &:hover {\n                color: ",";\n            }\n        "]);return Ei=function(){return n},n}function Mi(){var n=wi(["\n            position: absolute;\n            left: 0;\n            right: 0;\n            top: 0;\n            bottom: 0;\n            z-index: 1;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            background: rgba(255, 255, 255, 0.6);\n            .ant-spin {\n                pointer-events: auto;\n            }\n        "]);return Mi=function(){return n},n}var Fi=(0,a.rU)(function(n){var e=n.token,t=n.css;return{schemeListContainer:t(ji(),(null===o.fZ||void 0===o.fZ?void 0:(0,o.fZ)())?"64px":"1px",(null===o.fZ||void 0===o.fZ?void 0:(0,o.fZ)())?"68px":"244px",(null===o.fZ||void 0===o.fZ?void 0:(0,o.fZ)())?"12px":"0px",e.colorBgContainer,(null===o.fZ||void 0===o.fZ?void 0:(0,o.fZ)())&&"border-radius: 12px;"),title:t(Si(),e.colorBgContainer),closeIcon:t(_i(),e.colorTextSecondary,e.colorText),schemeList:t(ki()),schemeItem:t(Ii()),selectedSchemeImage:t(Ci(),e.colorPrimary),schemeImage:t(Ai()),addSchemeImage:t(zi(),e.colorBorder),addIcon:t(Ni()),addSchemeText:t(Pi()),schemeBottom:t(Oi()),schemeName:t(Di(),e.colorText),deleteIcon:t(Ei(),e.colorTextSecondary,e.colorError),spinContainer:t(Mi()),textEllipsis:{maxWidth:"140px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"inline-block"}}});function Bi(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ti(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Li(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Ti(o,r,i,a,l,"next",n)}function l(n){Ti(o,r,i,a,l,"throw",n)}a(void 0)})}}function Ri(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Bi(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Bi(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ui(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Hi=(0,pi.observer)(function(){var n=(0,M.P)(),e=(0,i.B)().t,t=Fi(),o=t.styles,a=t.cx,l=Ri((0,P.useState)(!1),2),c=l[0],s=l[1],u=Ri((0,P.useState)(!1),2),d=u[0],f=u[1],p=Ri((0,P.useState)(""),2),h=p[0],m=p[1],x=Ri((0,P.useState)(""),2),g=x[0],v=x[1],b=Ri((0,P.useState)(""),2),y=b[0],w=b[1],j=D.nb.instance.layout_container,S=Ri((0,P.useState)([]),2),_=S[0],k=S[1];(0,P.useEffect)(function(){D.nb.on(F.U.setMultiSchemeListVisible,function(n){return Li(function(){var e;return Ui(this,function(t){return null==n||n==c||(s(!!n),e=j._layout_scheme_id,m(e)),[2]})})()});return D.nb.on(F.U.RoomList,function(){return Li(function(){var n;return Ui(this,function(e){return(n=j._layout_scheme_id)?(n!=h&&m(n),[2]):(s(!1),[2])})})()}),function(){D.nb.off(F.U.setMultiSchemeListVisible),D.nb.off(F.U.RoomList)}}),(0,P.useEffect)(function(){c?I(h):(k([]),v(""),w(""),m(""))},[h,c]),(0,P.useEffect)(function(){n.homeStore.showWelcomePage&&s(!1)},[n.homeStore.showWelcomePage]);var I=function(n){return Li(function(){var e;return Ui(this,function(t){switch(t.label){case 0:return[4,li.D.getLayoutSchemeById(n)];case 1:return(null==(e=t.sent())?void 0:e.projectId)?(v(e.projectId),[4,C(e.projectId)]):[3,3];case 2:return t.sent(),[3,4];case 3:k([]),t.label=4;case 4:return[2]}})})()},C=function(n){return Li(function(){var e,t,r;return Ui(this,function(i){switch(i.label){case 0:f(!0),i.label=1;case 1:return i.trys.push([1,,4,5]),[4,yi.loadSchemeList(n)];case 2:return e=i.sent(),[4,li.D.getLayoutSchemeById(n)];case 3:return(t=i.sent())&&(r={id:t.id,originSchemeId:t.projectId,refSchemeId:t.projectId,refSchemeName:t.layoutSchemeName,imageUrl:new URL(t.coverImage).pathname.replace(/^\/+/,"")},w(t.layoutSchemeName),e.push(r)),k(e),[3,5];case 4:return f(!1),[7];case 5:return[2]}})})()},A=function(){var n=y||j._layout_scheme_name,t=_.length>0?_.length:1;return e("副本")+t+"-"+n};return c?(0,r.jsxs)("div",{className:o.schemeListContainer,children:[(0,r.jsxs)("div",{className:o.title,children:[e("布局方案"),(0,r.jsx)(Rr.A,{iconClass:"iconclose1",onClick:function(){s(!1)},className:o.closeIcon})]}),(0,r.jsx)("div",{className:o.schemeList,children:(0,r.jsxs)(Ae.A,{spinning:d,size:"large",children:[(0,r.jsx)("div",{className:o.schemeItem,children:(0,r.jsxs)("div",{className:o.addSchemeImage,onClick:function(){return Li(function(){var n,t,r;return Ui(this,function(i){switch(i.label){case 0:return _.length>=6?(z.A.warning({content:e("最多只能关联5个方案").toString(),style:{marginTop:"4vh"}}),[2]):j._layout_scheme_id?0==j._room_entities.length?(z.A.warning({content:e("当前方案为空，无法保存！").toString(),style:{marginTop:"4vh"}}),[2]):(n=g||j._layout_scheme_id,t=A(),D.nb.on(F.U.SaveProgress,function(t){return Li(function(){var r;return Ui(this,function(i){switch(i.label){case 0:return"ongoing"==(null==t?void 0:t.progress)?[2]:"fail"==(null==t?void 0:t.progress)?(z.A.error({content:e("方案保存失败").toString(),style:{marginTop:"4vh"}}),[2]):[4,yi.insertScheme(n,t.id,t.name)];case 1:return i.sent()?z.A.success({content:e("方案关联成功").toString(),style:{marginTop:"4vh"}}):z.A.error({content:e("方案关联失败").toString(),style:{marginTop:"4vh"}}),r=j._layout_scheme_id,m(r),[2]}})})()}),r={schemename:t,username:"",telephone:"",address:""},[4,j.saveSchemeLayout2JsonAs(r)]):(z.A.warning({content:e("请先保存方案").toString(),style:{marginTop:"4vh"}}),[2]);case 1:return i.sent(),D.nb.off(F.U.SaveProgress),[2]}})})()},children:[(0,r.jsx)(vt.A,{className:o.addIcon,type:"icon-tianjiasucai"}),(0,r.jsx)("span",{className:o.addSchemeText,children:e("新增方案")})]})}),_.length>0?_.map(function(n){return(0,r.jsxs)("div",{className:o.schemeItem,children:[(0,r.jsx)("div",{className:a(o.schemeImage,(t={},i=o.selectedSchemeImage,l=n.refSchemeId===j._layout_scheme_id,i in t?Object.defineProperty(t,i,{value:l,enumerable:!0,configurable:!0,writable:!0}):t[i]=l,t)),onClick:function(){return function(n){return Li(function(){return Ui(this,function(t){switch(t.label){case 0:return D.nb.on(F.U.SaveProgress,function(t){return Li(function(){var r;return Ui(this,function(i){switch(i.label){case 0:return"ongoing"==(null==t?void 0:t.progress)?[2]:"fail"==(null==t?void 0:t.progress)?(z.A.error({content:e("方案保存失败").toString(),style:{marginTop:"4vh"}}),D.nb.off(F.U.SaveProgress),[2]):(z.A.success({content:e("方案自动保存成功").toString(),style:{marginTop:"4vh"},duration:1}),[4,li.D.getLayoutSchemeById(n.refSchemeId)]);case 1:return r=i.sent(),D.nb.DispatchEvent(D.n0.OpenMyLayoutSchemeData,r),D.nb.off(F.U.SaveProgress),[2]}})})()}),[4,j.saveSchemeLayout2Json()];case 1:return t.sent(),[2]}})})()}(n)},style:{cursor:"pointer"},children:n.imageUrl?(0,r.jsx)(ze.A,{preview:!1,src:new URL(n.imageUrl,di.hl).href,width:180,height:140,style:{objectFit:"contain",padding:"10px",userSelect:"none",pointerEvents:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none"}}):(0,r.jsx)(ze.A,{width:180,height:140,src:"error",style:{objectFit:"contain",padding:"10px",userSelect:"none",pointerEvents:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none"}})}),(0,r.jsxs)("div",{className:o.schemeBottom,children:[(0,r.jsx)(Ne.A,{title:n.refSchemeName,children:(0,r.jsx)("span",{className:"".concat(o.schemeName," ").concat(o.textEllipsis),children:n.refSchemeId==g?e("原始方案"):n.refSchemeName})}),n.refSchemeId!=g&&(0,r.jsx)(Rr.A,{iconClass:"icondelete",onClick:function(t){var r;t.stopPropagation(),r=n.id,Li(function(){var n;return Ui(this,function(t){switch(t.label){case 0:return[4,yi.deleteScheme(r)];case 1:return t.sent()?(z.A.success({content:e("删除成功").toString(),style:{marginTop:"4vh"}}),n=j._layout_scheme_id,I(n)):z.A.error({content:e("删除失败").toString(),style:{marginTop:"4vh"}}),[2]}})})()},className:o.deleteIcon})]})]},n.id);var t,i,l}):(0,r.jsx)(fi.A,{image:fi.A.PRESENTED_IMAGE_SIMPLE,description:e("暂无关联方案")})]})})]}):null}),Gi=t(90112),Wi=t(51010);function Yi(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Xi(){var n=Yi(["\n      position:fixed;\n      background : #fff;\n      z-index: 10;\n      .closeBtn {\n        display:none;\n        position:absolute;\n         right : 6px;\n         top : 6px;\n         font-size:20px;\n         width:60px;\n         height : 24px;\n         text-align:right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: 12px !important;\n        top: 52px !important;\n        bottom: 12px !important;\n        right: auto !important;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width:224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          display: none;\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position:fixed;\n        left:0;\n        bottom:0px;\n        right:0;\n        width : auto;\n        height:340px;\n        max-width : auto;\n        max-height:340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {\n          transform : scale(0.7);\n          transform-origin : bottom;\n          left : -15%;\n          right : -15%;\n        }\n        &.panel_hide {\n          max-width : 0px;\n        }\n        .closeBtn {\n          display : block;\n        }\n      }\n\n\n      .fade-enter {\n        opacity: 0;\n      }\n\n      .fade-enter-active {\n        opacity: 1;\n        transition: opacity 300ms ease-in-out;\n      }\n\n      .fade-exit {\n        opacity: 1;\n      }\n\n      .fade-exit-active {\n        opacity: 0;\n        transition: opacity 300ms ease-in-out;\n      }\n    "]);return Xi=function(){return n},n}function Vi(){var n=Yi(["\n      background: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n    "]);return Vi=function(){return n},n}function Zi(){var n=Yi(['\n      .tabItem {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        gap: 12px;\n        flex: 1 0 0;\n        padding: 16px 12px 0 12px;\n        .item {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          gap: 2px;\n          flex: 1 0 0;\n          .label {\n            align-self: stretch;\n            color: #959598;\n            text-align: center;\n            font-family: "PingFang SC";\n            font-size: 16px;\n            font-style: normal;\n            font-weight: 600;\n            line-height: 150%; /* 24px */\n          }\n          .line {\n            width: 20px;\n            height: 3px;\n            border-radius: 3px;\n            background: #ffffff;\n          }\n          &.active {\n            .line {\n              width: 20px;\n              height: 3px;\n              border-radius: 3px;\n              background: #282828;\n            }\n            .label {\n              color: #282828;\n              font-weight: 600;\n              line-height: 150%; /* 24px */\n            }\n          }\n        }\n      }\n    ']);return Zi=function(){return n},n}function qi(){var n=Yi(["\n      position: absolute;\n      left: 5px;\n      width: 34px;\n      top: 50px;\n      height:210px;\n      display: flex;\n      flex-direction: column;\n      font-size: 17px;\n      align-items: center;\n      flex-wrap: nowrap;\n      justify-content: center;\n      border-radius:8px;\n      color : #aaa;\n      text-align:center;\n      background:#eee;\n\n      @media screen and (orientation: landscape) {\n          display:none;\n      }\n      @media screen and (orientation: portrait) {\n        display: flex;\n        \n      }\n      .vTab {\n        width: 30px;\n        padding-top: 8px;\n        padding-bottom: 8px;\n        padding-left:5px;\n        padding-right:5px;\n\n        &.checked {\n          background:#fff;\n          color : #2b2b2b;\n          border-radius:8px;\n\n        }\n\n      }\n    "]);return qi=function(){return n},n}function Ji(){var n=Yi(["\n      display:none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor:pointer;\n\n      @media screen and (orientation: landscape) {\n        display:block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n\n      }\n      &.panel_hide {\n        left:0px;\n        display:block;\n      }\n    "]);return Ji=function(){return n},n}function Ki(){var n=Yi(["\n      color : #959598;\n      .ant-tabs-tab {\n        color :#959598;\n      }\n    "]);return Ki=function(){return n},n}function Qi(){var n=Yi(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n\n    "]);return Qi=function(){return n},n}function $i(){var n=Yi(["\n      /* position:absolute;\n      left:45px;\n      right:5px;\n      top: 0px;\n      bottom:10px;\n      overflow:hidden;\n      @media screen and (orientation: landscape) {\n        left:5px;\n        top: 0px;\n\n      } */\n    "]);return $i=function(){return n},n}function no(){var n=Yi(["\n      height:100%;\n      width:100%;\n      .dropdown {\n        width: calc(100% - 24px);\n        height: 26px;\n        box-sizing: border-box;\n        padding: 0 12px;\n        margin: 16px 12px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 4px;\n        border: 1px solid rgba(0, 0, 0, 0.15);\n        background: #FFFFFF;\n      }\n    "]);return no=function(){return n},n}function eo(){var n=Yi(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return eo=function(){return n},n}function to(){var n=Yi(["\n  "]);return to=function(){return n},n}function ro(){var n=Yi(["\n    position: fixed;\n    top: 12px;\n    left: 12px;\n    height: 32px;\n    width: 68px;\n    line-height:28px;\n    font-size: 14px;\n    background:#fff;\n    border-radius:6px;\n    text-align:center;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    .iconleft {\n      text-align:left;\n    }\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n      width:50px;\n\n    }\n    z-index: 9;\n  "]);return ro=function(){return n},n}function io(){var n=Yi(['\n    position: fixed;\n    top: 0;\n    right: 0;\n    display: flex;\n    width: 100%;\n    height: 52px;\n    padding: 6px 20px;\n    box-sizing: border-box;\n    align-items: center;\n    gap: 16px;\n    align-self: stretch;\n    z-index: 9;\n    .icon {\n      width: 20px;\n      height: 20px;\n      cursor: pointer;\n    }\n    .center {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 38px;\n      .segmented {\n        display: flex;\n        padding: 2px;\n        align-items: center;\n        border-radius: 100px;\n        background: #E9EBEB;\n        border: none;\n        height: 38px;\n        \n        .ant-segmented-item {\n          display: flex;\n          padding: 6px 16px;\n          justify-content: center;\n          align-items: center;\n          gap: 10px;\n          border-radius: 100px;\n          border: none;\n          height: 34px;\n          &-input {\n            display: none;\n          }\n\n          .ant-segmented-item-label {\n            color: #5B5E60;\n            font-family: "PingFang SC";\n            font-size: 16px;\n            font-style: normal;\n            font-weight: 400;\n            line-height: normal;\n            text-align: center;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            padding: 0;\n          }\n          \n          &.ant-segmented-item-selected {\n            background: #FFF;\n            \n            .ant-segmented-item-label {\n              color: #282828;\n              font-weight: 600;\n              padding: 0;\n            }\n          }\n        }\n      }\n    }\n  ']);return io=function(){return n},n}function oo(){var n=Yi(["\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);\n    color: #fff;\n  "]);return oo=function(){return n},n}function ao(){var n=Yi(["\n    position: fixed;\n    top: 12px;\n    right: 12px;\n    height : 28x;\n    width:60px;\n    line-height:28px;\n    color: #fff;\n    font-size: 13px;\n    border-radius:6px;\n    text-align:center;\n    background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n    z-index:9;\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n    }\n  "]);return ao=function(){return n},n}function lo(){var n=Yi(["\n      position: fixed;\n      top: 6px;\n      right: 12px;\n      width:40px;\n      height : 40px;\n      line-height:40px;\n      border-radius:6px;\n      text-align:center;\n      color :#282828;\n      background:rgba(255,255,255,0.2);\n      z-index:9;\n      font-size: 16px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 14px;\n      }\n\n   "]);return lo=function(){return n},n}function co(){var n=Yi(["\n    top: 50px;\n    right: 12px;\n    position: fixed;\n    z-index:999;\n    background:#fff;\n    padding:10px;\n    border-radius:6px;\n  "]);return co=function(){return n},n}function so(){var n=Yi(["\n      position:fixed;\n      width: 200px;\n      left: calc(50% - 100px);\n      top: 0px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width:120px;\n        left: calc(50% - 60px);\n      }\n      @media screen and (max-width: 350px) { // 手机宽度\n        top: 60px;\n        left :12px;\n      }\n      z-index: 9;\n    "]);return so=function(){return n},n}function uo(){var n=Yi(["\n        position:fixed;\n        width: 200px;\n        right: 12px;\n        top: 5px;\n        z-index: 9;\n    "]);return uo=function(){return n},n}function fo(){var n=Yi(["\n      position:fixed;\n      bottom: 20px;\n      z-index: 9;\n      display:flex;\n      justify-content:center;\n      align-items:center;\n      gap: 16px;\n      left: 50%;\n      transform: translate(-50%, 0);\n      transition: all 0.5s ease;\n      .btn {\n        display: flex;\n        width: 102px;\n        height: 36px;\n        box-sizing: border-box;\n        justify-content: center;\n        align-items: center;\n        flex: 1 0 0;\n        align-self: stretch;\n        border-radius: 50px;\n        border-top: 1px solid rgba(0, 0, 0, 0.06);\n        background: #FFF;\n        box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.08);\n\n        color: #282828;\n        font-size: 14px;\n        font-weight: 600;\n        line-height: 22px;\n        text-align: center;\n      }\n      .blackColor {\n        background: rgba(0, 0, 0, 0.40) !important;\n        backdrop-filter: blur(50px) !important;\n        color: #fff !important;\n      }\n      @media screen and (orientation: landscape){\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        left: 50%;\n        transform: translate(-50%, 0);\n      }\n      &.showLeftPanel {\n        @media screen and (orientation: portrait) {\n          position: fixed;\n          bottom: 280px;\n          max-width : 48px;\n          top : auto;;\n          left: 44%;\n          transform: translateX(-50%);\n          right : 24px;\n          height: 120px;\n          display: block;\n\n          @media screen and (max-width: 450px) { // 手机宽度\n            right : 12px;\n          }\n          @media screen and (max-height: 700px) { \n            right : auto;\n            left : 0px;\n          }\n          .btn {\n            border-radius: 50px;\n            background: #FFFFFF;\n            box-shadow: 0px 6px 20px 0px #00000014;\n            width : 140px;\n            border: none;\n            height : 48px;\n            color: #282828;\n            font-size: 14px;\n            line-height: 48px;\n            letter-spacing: 0px;\n            text-align: center;\n            margin-left:12px;\n            margin-right:12px;\n  \n            &.hasIcon {\n              line-height:19px;\n              .iconfont {\n                  display:block;\n                  margin-top:4px;\n              }\n            }\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 40px !important;\n              height: 44px !important;\n              font-size: 10px !important;\n            }\n          }\n      }\n\n\n      }\n    "]);return fo=function(){return n},n}function po(){var n=Yi(["\n      position:fixed;\n      right:0;\n      z-index:9;\n      top:0px;\n      transition: all 0.2s ease;\n      &.is_3d_mode {\n        top:180px;\n      }\n    "]);return po=function(){return n},n}function ho(){var n=Yi(["\n    width:100%;\n    font-size:16px;\n    line-height:40px;\n    text-align:center;\n  "]);return ho=function(){return n},n}function mo(){var n=Yi(["\n      position: fixed;\n      bottom: 0;\n      right: 30%;\n      transform: translateX(-70%);\n      z-index: 9;\n      .renderBtn {\n        position: absolute;\n        right: 50%;\n        bottom: 10px;\n        transform: translateX(50%);\n      }\n    "]);return mo=function(){return n},n}function xo(){var n=Yi(["\n      position: absolute;\n      right: 25%;\n      top: 5px;\n      display: flex;\n      gap: 10px;\n      z-index: 9;\n    "]);return xo=function(){return n},n}function go(){var n=Yi(['\n      position: absolute;\n      bottom: 5px;\n      right: 5px;\n      display: flex;\n      padding: 4px 12px;\n      align-items: center;\n      gap: 4px;\n      border-radius: 50px;\n      background: linear-gradient(90deg, #EDE5FF 0%, #EAF0FF 100%);\n      border: none;\n      height: 30px;\n      z-index: 9;\n      \n      .text {\n        color: #5C42FB !important;\n        text-align: center;\n        font-family: "PingFang SC";\n        font-size: 13px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 22px;\n      }\n    ']);return go=function(){return n},n}var vo=(0,a.rU)(function(n){var e=n.css;return{leftPanelRoot:e(Xi()),materialReplace:e(Vi()),tabBar:e(Zi()),leftTabBar:e(qi()),collapseBtn:e(Ji()),tab_root:e(Ki()),topTitle:e(Qi()),popupContainer:e($i()),listContainer:e(no()),open:e(eo()),navigation:e(to()),backBtn:e(ro()),header:e(io()),blackColor:e(oo()),forwardBtn:e(ao()),closeBtn:e(lo()),shareBarContainer:e(co()),topTabs:e(so()),rightSceneModeTabs:e(uo()),bottomButtons:e(fo()),sideToolbarContainer:e(po()),schemeNameSpan:e(ho()),viewSelectContainer:e(mo()),selectBtns:e(xo()),layoutPlusButton:e(go())}});function bo(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function yo(){var n=bo(["\n    @keyframes fade-in {\n      0% { opacity: 0; }\n      100% { opacity: 1; }\n    }\n  "]);return yo=function(){return n},n}function wo(){var n=bo(["\n      left: 0;\n      position: fixed;\n      color: #6c7175;\n      background-color: #FFF;\n      width: 288px;\n      z-index: 999;\n      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.16);\n      height: 100%;\n    "]);return wo=function(){return n},n}function jo(){var n=bo(["\n      color: #000;\n      font-weight: bold;\n      font-size: 20px;\n      line-height: 1.67;\n      padding: 14px 0 16px 16px;\n      height: 60px;\n      background-color: #fff;\n      width: 100%;\n    "]);return jo=function(){return n},n}function So(){var n=bo(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 850px;\n      color: #6c7175;\n      text-align: center;\n      flex-direction: column;\n      img{\n        width: 80px;\n        height: 80px;\n      }\n    "]);return So=function(){return n},n}function _o(){var n=bo(["\n      margin-bottom: '12px';\n      img {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 300px;\n      }\n    "]);return _o=function(){return n},n}function ko(){var n=bo(["    \n      margin-top: 15px;\n      font-size: 12px;\n      line-height: 18px;\n      color: #A2A2A5;\n    "]);return ko=function(){return n},n}function Io(){var n=bo(["\n      position:absolute;\n    "]);return Io=function(){return n},n}function Co(){var n=bo(["\n      height: calc(100vh - 165px) !important;\n      min-width: 220px !important;\n      img{\n        height: 216px !important;\n      }\n    "]);return Co=function(){return n},n}function Ao(){var n=bo(["\n      width: 100%;\n      min-width: 300px;\n      background-color: #FFF;\n      height: calc(100vh - 130px - 38px);\n      overflow-y: scroll;\n      text-align: center;\n      padding: 2px 12px 2px 2px;\n      canvas {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 100%;\n        padding: 10px;\n        background-color: #F2F3F5; \n      }\n      canvas:hover {\n        outline: 2px solid #9242FB;\n      }\n      .iconfont {\n        width:20px;\n        height:20px;\n        line-height:20px;\n        float:right;\n        color:#1790ff;\n        font-weight: 100;\n        cursor:pointer;\n\n      }\n      .mobile\n      {\n        height: 216px !important;\n      }\n      img {\n        cursor : pointer;\n        box-sizing: border-box;\n        width: 100%;\n        height: 310px;\n        background-color: #F2F3F5; \n      }\n      img:hover {\n        outline: 2px solid #9242FB;\n      }\n      .emptyImg\n      {\n        height: 80px !important;\n        width: 80px !important;\n        display: flex;\n        justify-content: center;\n        border-radius: 50%;\n      }\n      > div {\n          position: relative;\n      }\n\n\n    "]);return Ao=function(){return n},n}function zo(){var n=bo(["\n      height: 300px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 250px;\n      }\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 300px);\n        min-width: 224px;\n      }\n    "]);return zo=function(){return n},n}function No(){var n=bo(["\n      height: 56px;\n      padding-top: 16px;\n      @media screen and (orientation: landscape) {\n        height: 38px;\n        padding-top: 0px;\n      }\n    "]);return No=function(){return n},n}function Po(){var n=bo(["\n        width: 100%;\n        height:100%;\n        background-color: #FFF;\n        overflow-x: scroll;\n        overflow-y:hidden;\n        text-align: center;\n        display:flex;\n        @media screen and (orientation: landscape) {\n          overflow-y:scroll !important;\n          /* width: 200px !important; */\n          height: calc(var(--vh, 1vh) * 100 - 135px - 50px);\n          display: block !important;\n          padding: 0 4px;\n          ::-webkit-scrollbar {\n            display: none; /* 隐藏滚动条 */\n          }\n        }\n        .scheme_div {\n          width: 200px;\n          margin: 8px 8px;\n          position: relative;\n          flex: 0 0 auto;\n          @media screen and (max-width: 450px) { // 手机宽度\n            width: 150px;\n          }\n        }\n        .scheme_name {\n          margin: 4px 0px;\n          .ant-rate\n          {\n            font-size: 16px !important;\n          }\n          @media screen and (max-width: 450px) {\n            .ant-rate\n            {\n              font-size: 12px !important;\n              .ant-rate-star:not(:last-child)\n              {\n                margin-inline-end: 3px;\n              }\n            }\n          }\n        }\n        .iconfont {\n          width:20px;\n          height:20px;\n          line-height:20px;\n          float:right;\n          color:#1790ff;\n          font-weight: 100;\n          cursor:pointer;\n  \n        }\n\n        img {\n          cursor : pointer;\n          box-sizing: border-box;\n          background-color: #F2F3F5; \n          width: 100%;\n          border-radius: 2px;\n          padding: 15px;\n        }\n        .emptyImg\n        {\n          height: 80px !important;\n          width: 80px !important;\n          display: flex;\n          justify-content: center;\n          border-radius: 50%;\n        }\n    "]);return Po=function(){return n},n}function Oo(){var n=bo(["\n      outline: 2px solid #9242FB;\n    "]);return Oo=function(){return n},n}function Do(){var n=bo(["\n      position: absolute;\n      top: 4px;\n      right: 4px;\n      border-radius: 4px;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n      padding: 5px 4px;\n    "]);return Do=function(){return n},n}function Eo(){var n=bo(["\n      display: none;\n    "]);return Eo=function(){return n},n}function Mo(){var n=bo(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-size: 14px;\n      line-height: 1.57;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 8px 0px;\n      text-align: center;\n      user-select:text;\n    "]);return Mo=function(){return n},n}function Fo(){var n=bo(["\n      display: flex;\n      padding: 0px 10px;\n      justify-content: space-between;\n    "]);return Fo=function(){return n},n}function Bo(){var n=bo(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      position: absolute;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return Bo=function(){return n},n}function To(){var n=bo(["\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      img{\n        width: 80px;\n        height: 80px;\n        border-radius: 50%;\n        margin: auto;\n      }\n    "]);return To=function(){return n},n}function Lo(){var n=bo(["\n      text-align: center;\n    "]);return Lo=function(){return n},n}function Ro(){var n=bo(["\n      font-size: 12px;\n      color: #6c7175;\n      margin-top: 10px;\n      color: #A2A2A5;\n      line-height: 18px;\n    "]);return Ro=function(){return n},n}function Uo(){var n=bo(["\n      position: absolute;\n      top: 0px;\n      left: 0px;\n      background-color: red;\n      color: #fff;\n      width: 25px;\n      height: 25px;\n      line-height: 25px;\n      border-bottom-right-radius: 45%;\n    "]);return Uo=function(){return n},n}var Ho=(0,a.rU)(function(n,e){n.token;var t=n.css;t(yo());return{left_panel:t(wo()),left_panel_title:t(jo()),left_panel_placeholder:t(So()),left_panel_placeholder_image:t(_o()),left_panel_placeholder_text:t(ko()),left_panel_container:t(Io()),mobile:t(Co()),left_panel_layout_list:t(Ao()),bottom_panel_container:t(zo()),roomListBar:t(No()),bottom_panel_layout_list:t(Po()),active:t(Oo()),activeTitle:t(Do()),activeTitleNone:t(Eo()),_scheme_name:t(Mo()),star_name:t(Fo()),line:t(Bo()),letfEmpty:t(To()),letfEmptyItem:t(Lo()),text:t(Ro()),difference:t(Uo())}}),Go=t(33313),Wo=t(94499),Yo=t(62837);function Xo(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Vo(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Xo(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Xo(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Zo=function(){var n=(0,Go.A)().styles,e=D.nb.t,t=Vo((0,P.useState)(0),2),i=t[0],o=t[1],a=Vo((0,P.useState)([]),2),l=a[0],c=a[1];return(0,P.useEffect)(function(){D.nb.on(F.U.ShowPopUpLayoutScore,function(n){o(n.top);var t=n.scheme;if(null==t?void 0:t.layout_score_dict){var r=Object.keys(t.layout_score_dict).map(function(n){return t.layout_score_dict[n]}),i=0;r.forEach(function(n){n.children,i+=n.score}),D.nb.IsDebug&&r.push({name:e("总分"),score:i,ui_format:[Wo.td.Name,Wo.td.Percentage,Wo.td.Grade]}),c(r)}else c([])})},[]),(0,r.jsx)("div",{className:n.schemeLayoutScorePopUp,style:{top:i+"px",display:l.length>0?"block":"none",minWidth:"300px",minHeight:"400px"},children:(0,r.jsx)(Yo.A,{layoutScoreList:l,style:D.nb.IsDebug?0:1})})},qo=t(75206),Jo=t.n(qo),Ko=t(67194),Qo=t(79489),$o=t(42751),na=t(59525),ea=t(62867);function ta(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ra(n){return function(n){if(Array.isArray(n))return ta(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||function(n,e){if(!n)return;if("string"==typeof n)return ta(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ta(n,e)}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ia=function(n,e,t){n&&n.drawOnCanvas(t,e,576,576)},oa={},aa=(0,C.observer)(function(n){var e=(0,M.P)(),t=(0,P.useRef)(null),a=Ho(n.width).styles,l=n.showSchemeName||!1,c=(0,i.B)().t,s=function(n){var e=t.current;e&&(e.querySelectorAll("img").forEach(function(e,t){e.className=t==n?a.active:""}),e.querySelectorAll("#active_div").forEach(function(e,t){e.className=t==n?a.activeTitle:a.activeTitleNone}))},u=function(n,e){var i,o;if(t.current){t.current.scrollTop=0;var u=t.current;u.innerHTML="",oa={room_scheme_list:n,room_scheme_index:e};var d=D.nb.instance.painter;n.forEach(function(n){var e=0;n._layout_scores.forEach(function(n){e+=n.score}),n.totalScore=e});var f=null===(o=n[0])||void 0===o||null===(i=o._scheme_name)||void 0===i?void 0:i.includes("DIY"),p=f?n.shift():null;if(f&&p&&n.unshift(p),n.length>0){var h=function(t){var i=n[t],o=document.createElement("canvas");i._drawn_image||(ia(i,o,d),i._drawn_image=new Image,i._drawn_image.src=o.toDataURL(),i._drawn_image.crossOrigin="anonymous");var f=i._drawn_image;f.className=~~t==e?a.active:"",f.onclick=function(){var n,e;D.nb.DispatchEvent(D.n0.ClickLayoutScheme,{value:i,index:~~t}),D.nb.emit_M(F.U.OnAILayoutSchemeSelected,{value:i,index:~~t}),s(~~t),1==(null===(n=na.y.instance.current_rooms)||void 0===n?void 0:n.length)&&(null===(e=na.y.instance.current_rooms[0])||void 0===e?void 0:e._series_sample_info)&&D.nb.instance.layout_container.drawing_figure_mode!==hr.qB.Figure2D&&D.nb.DispatchEvent(D.n0.SeriesSampleSelected,{series:na.y.instance.current_rooms[0]._series_sample_info,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}})},f.onpointermove=function(n){n.stopPropagation()},o=null;var p=document.createElement("div");p.className="scheme_div",p.appendChild(i._drawn_image);var h=i._drawn_image;h.id="layout_scheme_cavas_"+t,h.className=~~t==e?a.active:"";var m=document.createElement("div");m.innerHTML=c("正在使用"),m.className=~~t==e?a.activeTitle:a.activeTitleNone,m.id="active_div",u.appendChild(p);var x=document.createElement("div");x.className=a._scheme_name+" scheme_name",x.innerHTML="[".concat(c(i.room.name),"] ").concat(i._scheme_name.includes("DIY")?c(i._scheme_name):c("方案")+(1+~~t)," ")+"".concat(l&&D.nb.IsDebug&&i._scheme_name||"");var g=null,v=document.createElement("div");if((0,Qo.MP)([i.room.roomname],["卧室","厨房","卫生间","客餐厅","入户花园"])){var b=0,y=0;i._layout_scores.forEach(function(n){b+=n.grade});var w=i._layout_scores.some(function(n){return n.children&&n.children.length>0?n.children.some(function(n){return n.score<=-100}):n.score<=-100});b&&(y=Math.round(b/i._layout_scores.length)),(Wo.fT[y]<2||w)&&(v.className=a.difference,v.innerHTML=c("差")),x.classList.add(a.star_name),g=document.createElement("div"),Jo().createRoot(g).render((0,r.jsx)(Ko.A,{disabled:!0,allowHalf:!0,value:Wo.fT[y]}));var j="".concat(i._scheme_name.includes("DIY")?c(i._scheme_name):c("方案")+(1+~~t)," ")+"".concat(l&&D.nb.IsDebug&&i._scheme_name||"");x.innerHTML="";var S=document.createElement("div");Jo().createRoot(S).render((0,r.jsx)(Ne.A,{title:j,children:(0,r.jsx)("span",{onDoubleClick:function(){var n;if(null===(n=navigator)||void 0===n?void 0:n.clipboard){var e=j,t=e.indexOf("相似");t>=0&&(e=e.substring(t+2)),navigator.clipboard.writeText(j)}},children:j})})),g.onclick=function(n){var e=n.currentTarget.getBoundingClientRect(),t=e.top+e.height/2-425,r=document.documentElement.clientHeight-680,o=Math.max(-55,Math.min(t,r));D.nb.emit(F.U.ShowPopUpLayoutScore,{scheme:i,top:o})},g.onmouseenter=function(n){var e=n.currentTarget.getBoundingClientRect(),t=e.top+e.height/2-425,r=document.documentElement.clientHeight-680,o=Math.max(-55,Math.min(t,r));D.nb.emit(F.U.ShowPopUpLayoutScore,{scheme:i,top:o})},g.onpointerleave=function(){D.nb.emit(F.U.ShowPopUpLayoutScore,{scheme:null,top:Math.max(p.offsetTop-u.scrollTop,0)})},x.appendChild(S),x.appendChild(g)}p.appendChild(x),p.appendChild(v),p.appendChild(m)};for(var m in n)h(m);D.nb.emit(F.U.ShowPopUpLayoutScore,{scheme:null,top:0})}else u.innerHTML="\n      <div class=".concat(a.letfEmpty,">\n          <div class=").concat(a.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(a.text,">\n              ").concat(c("暂无内容"),"，").concat(c("请选择其他空间"),"\n            </div>\n          </div>\n      </div>\n  ")}},d=function(n,e){if(t.current){t.current.scrollTop=0,oa={whole_scheme_list:n,whole_scheme_index:e};var r=t.current;r.innerHTML="";var i=D.nb.instance.painter;if(n.length>0){var u=function(e){var t=n[e];if(!t._drawn_image){var u=document.createElement("canvas");ia(t,u,i),t._drawn_image=new Image,t._drawn_image.src=u.toDataURL(),t._drawn_image.crossOrigin="anonymous",t._drawn_image.onclick=function(){var n,r;D.nb.DispatchEvent(D.n0.ClickWholeLayoutScheme,{value:t,index:~~e}),D.nb.emit_M(F.U.OnAILayoutSchemeSelected,{value:t,index:~~e}),s(~~e),(null===(n=na.y.instance.current_rooms)||void 0===n?void 0:n.length)>1&&(null===(r=na.y.instance.current_rooms[0])||void 0===r?void 0:r._series_sample_info)&&D.nb.instance.layout_container.drawing_figure_mode!==hr.qB.Figure2D&&D.nb.DispatchEvent(D.n0.SeriesSampleSelected,{series:na.y.instance.current_rooms[0]._series_sample_info,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}})},u=null}var d=document.createElement("div");d.className="scheme_div",d.appendChild(t._drawn_image);var f=t._drawn_image;f.id="layout_whole_scheme_cavans_"+e,f.className=(0,o.fZ)()?"mobile":"",r.appendChild(d);var p=document.createElement("div");p.className=a._scheme_name+" scheme_name",p.innerHTML="".concat(c("全屋")).concat(t._scheme_name.includes("DIY")?c(t._scheme_name):"".concat(c("方案"))+(1+~~e))+"".concat(l&&D.nb.IsDebug&&t._scheme_name||""),d.appendChild(p)};for(var d in n)u(d)}else r.innerHTML="\n      <div class=".concat(a.letfEmpty,">\n          <div class=").concat(a.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(a.text,">\n              ").concat(c("暂无全屋")).concat(c("推荐内容"),"\n            </div>\n          </div>\n      </div>\n  ")}},f=function(n,e){ea.a.applySubAreaScheme(n,e)},p=function(n,e){if(t.current){t.current.scrollTop=0,oa={subarea_scheme_list:n,subarea_scheme_index:e};var r=t.current;r.innerHTML="";var i=D.nb.instance.painter;if(n.length>0){var u=function(t){var u=n[t];if(!u._drawn_image){var d=document.createElement("canvas");ia(u,d,i),u._drawn_image=new Image,u._drawn_image.src=d.toDataURL(),u._drawn_image.crossOrigin="anonymous",u._drawn_image.onclick=function(){f(u,e),s(~~t)},d=null}var p=document.createElement("div");p.className="scheme_div",p.appendChild(u._drawn_image);var h=u._drawn_image;h.id="layout_subarea_scheme_cavans_"+t,h.className=(0,o.fZ)()?"mobile":"",r.appendChild(p);var m=document.createElement("div");m.className=a._scheme_name+" scheme_name",m.innerHTML="".concat(c("分区")).concat(u._scheme_name.includes("DIY")?c(u._scheme_name):"".concat(c("布局"))+(1+~~t))+"".concat(l&&D.nb.IsDebug&&u._scheme_name||""),p.appendChild(m)};for(var d in n)u(d)}else r.innerHTML="\n      <div class=".concat(a.letfEmpty,">\n          <div class=").concat(a.letfEmptyItem,">\n            <img class='emptyImg' src=","https://3vj-fe.3vjia.com/layoutai/image/Empty.png",' alt="" />\n            <div class=').concat(a.text,">\n              ").concat(c("暂无分区")).concat(c("推荐内容"),"\n            </div>\n          </div>\n      </div>\n  ")}};return(0,P.useEffect)(function(){D.nb.on(F.U.LayoutSchemeList,function(n){(null==n?void 0:n.schemeList)?u(ra(n.schemeList),n.index):u([],0)}),D.nb.on_M(F.U.WholeLayoutSchemeList,"schemeList",function(n){(null==n?void 0:n.schemeList)?d(ra((null==n?void 0:n.schemeList)||[]),n.index):d([],0)}),D.nb.on(F.U.SubAreaLayoutSchemeList,function(n){(null==n?void 0:n.schemeList)?(p(ra(n.schemeList),n.index),n.auto_layout&&n.schemeList[0]&&f(n.schemeList[0],0)):p([],0)}),oa&&(oa.room_scheme_list?u(oa.room_scheme_list,oa.room_scheme_index):oa.whole_scheme_list&&d(oa.whole_scheme_list,oa.whole_scheme_index))},[]),n.isLightMobile?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:a.bottom_panel_container,children:[!e.homeStore.isSingleRoom&&(0,r.jsx)("div",{className:a.roomListBar,children:(0,r.jsx)($o.A,{})}),(0,r.jsx)("div",{className:"".concat(a.bottom_panel_layout_list),id:"side_list_div",ref:t})]}),(0,r.jsx)(Zo,{})]}):(0,r.jsxs)("div",{className:a.left_panel_container,style:{marginTop:"16px"},children:[(0,r.jsx)("div",{className:"".concat(a.left_panel_layout_list," ").concat((0,o.fZ)()?a.mobile:""),id:"side_list_div",ref:t}),(0,r.jsx)(Zo,{})]})});function la(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ca(){var n=la(["\n      display: flex;\n      position: fixed;\n      top: calc(var(--vh, 1vh) * 50);\n      transform: translateY(-50%);\n      right: 12px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n      /* position: relative; */\n    "]);return ca=function(){return n},n}function sa(){var n=la(["\n      background-color: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n      color: #fff !important;\n      .iconButtonText\n      {\n        color: #fff !important;\n      }\n      .icon\n      {\n        color: #fff !important;\n      }\n    "]);return sa=function(){return n},n}function ua(){var n=la(["\n      display: flex;\n      position: absolute;\n      bottom: 0px;\n      right: 62px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n    "]);return ua=function(){return n},n}function da(){var n=la(["\n      position : fixed;\n      top : 40px;\n      left : 40px;\n      right : 40px;\n      bottom : 40px;\n      z-index:101\n    "]);return da=function(){return n},n}function fa(){var n=la(["\n      width: 48px;\n      height: auto;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 30px;\n      flex-direction: column;\n      position: relative;\n      /* margin-bottom: 12px; */\n      .iconButtonText\n      {\n        font-size: 12px;\n        color: #282828;\n        margin-top: 4px;\n      }\n      .iconLabel {\n        font-size: 13px;\n        position: absolute;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      .divider\n      {\n        margin-top: 12px;\n        width: 100%;\n        height: 1px;\n        background-color: #E0E0E0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        /* min-height: 40px; */\n      }\n      \n      @keyframes flashEffect {\n        0% {\n          background-color: rgba(255, 255, 255, 0.7); /* 原始背景色 */\n        }\n        50% {\n          background-color: rgba(200, 200, 255, 0.7); /* 高亮颜色 */\n        }\n        100% {\n          background-color: rgba(255, 255, 255, 0.7); /* 回到原始背景色 */\n        }\n      }\n    "]);return fa=function(){return n},n}function pa(){var n=la(["\n      font-size: 12px;\n      color: #000;\n      margin-top: 4px;\n    "]);return pa=function(){return n},n}function ha(){var n=la(["\n      position: fixed;\n      background: #fff;\n      z-index: 20;\n      right: 64px;\n      top: 50vh;\n      transform: translateY(-50%);\n      font-size:15px;\n      padding: 12px;\n      line-height: 28px;\n      border-radius: 8px;\n    "]);return ha=function(){return n},n}function ma(){var n=la(["\n      position: fixed;\n      background-color: #ffffff;\n      top: 50%;\n      right: 60px;\n      border-radius: 8px;\n      width: 210px;\n      font-size: 13px;\n      .camera_container {\n        padding: 8px;\n        .content {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n          .slider {\n            flex: 1;\n          }\n          .slider-camera {\n            flex: 1;\n            padding-bottom: 5px;\n            .camera-state {\n              display: flex;\n              justify-content: space-between;\n            }\n          }\n        }\n      }\n    "]);return ma=function(){return n},n}function xa(){var n=la(["\n      position: fixed;\n      background-color: #ffffff;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 10;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .viewGrid {\n        border-radius: 10px;\n        overflow: hidden;\n      }\n    "]);return xa=function(){return n},n}function ga(){var n=la(["\n      background-color: #ffffff;\n      position: fixed;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 11;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .loading-item {\n        border-radius: 10px;\n        position:relative;\n        overflow: hidden;\n        display: flex; /* 使用flex布局 */\n        flex-direction: column; /* 垂直排列 */\n        justify-content: center; /* 水平居中 */\n        align-items: center; /* 垂直居中 */\n        gap: 8px;\n        height: 106.9px; /* 保持高度 */\n        width: 142.5px; /* 保持宽度 */\n        span {\n          font-size: 12px;\n          color: #C0C0C0;\n        }\n      }\n    "]);return ga=function(){return n},n}function va(){var n=la(["\n      display: flex;\n      gap: 10px;\n      position: fixed;\n      flex-direction: column;\n      top: 40%;\n      right: 65px;\n      .ratioBtn {\n        background-color:rgb(218, 218, 218);\n        height: 30px;\n        width: 45px;\n        border-radius: 5px;\n        font-size: 16px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n      }\n    "]);return va=function(){return n},n}var ba=(0,a.rU)(function(n){var e=n.css;return{container:e(ca()),blackColor:e(sa()),morebtns_container:e(ua()),center_container:e(da()),iconButton:e(fa()),name:e(pa()),checkBoxes:e(ha()),camera:e(ma()),viewCamera:e(xa()),loading:e(ga()),ratioContainer:e(va())}}),ya=t(23664),wa=t(11180),ja=t(86014),Sa=t(61600),_a=t(54282),ka=t(63616);function Ia(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ca(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Aa(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Ca(n,e,t[e])})}return n}function za(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Ia(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ia(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Na={};Na[_a.uW.CadEzdxfDrawing]=!1,Na[_a.uW.CadRoomStrucure]=!0,Na[_a.uW.CadFurniture]=!0,Na[_a.uW.CadCabinet]=!0,Na[_a.uW.CadOutLine]=!0,Na[_a.uW.CadLighting]=!1,Na[_a.uW.CadCeiling]=!1,Na[_a.uW.CadDecorates]=!1,Na[_a.uW.CadSubRoomAreaDrawing]=!1,Na[_a.uW.CadDimensionWallElement]=!1,Na[_a.uW.CadDimensionOutterWallElement]=!1,Na[_a.uW.CadRoomName]=!0,Na[_a.uW.RulerDrawing]=!0;var Pa=function(n){var e=n.isVisible,t=ba().styles,i=D.nb.t,o=(D.nb.instance.layout_container,za((0,P.useState)(Na),2)),a=o[0],l=o[1],c=[{id:_a.uW.CadRoomStrucure,title:i("墙体结构"),titleCn:"墙体结构",type:"checkbox",checked:a[_a.uW.CadRoomStrucure]},{id:_a.uW.CadFurniture,title:i("家具"),titleCn:"家具",type:"checkbox",checked:a[_a.uW.CadFurniture]},{id:_a.uW.CadCabinet,title:i("定制柜"),titleCn:"定制柜",type:"checkbox",checked:a[_a.uW.CadCabinet]},{id:_a.uW.CadSubRoomAreaDrawing,title:i("区域"),titleCn:"区域",type:"checkbox",checked:a[_a.uW.CadSubRoomAreaDrawing]},{id:_a.uW.CadCeiling,title:i("吊顶"),titleCn:"吊顶",type:"checkbox",checked:a[_a.uW.CadCeiling]},{id:_a.uW.CadRoomName,title:i("空间名称"),titleCn:"空间名称",type:"checkbox",checked:a[_a.uW.CadRoomName]},{id:_a.uW.RulerDrawing,title:i("量尺"),titleCn:"量尺",type:"checkbox",checked:a[_a.uW.RulerDrawing]}];c=c.filter(function(n){return n});return(0,P.useEffect)(function(){return D.nb.DispatchEvent(D.n0.HandleSwitchDrawingLayer,Na),D.nb.on_M(F.U.SwitchDrawingLayer,"display-check-box",function(n){var e=Aa({},a,n);l(e)}),function(){}},[]),(0,r.jsx)("div",{className:t.checkBoxes,style:{display:e?"block":"none"},children:c.map(function(n,e){return(0,r.jsx)("div",{children:(0,r.jsx)(ka.A,{checked:a[n.id],onChange:function(e){!function(n){if(void 0===a[n.id]&&(a[n.id]=!1),void 0!==a[n.id]){var e=Aa({},a);e[n.id]=!e[n.id],D.nb.DispatchEvent(D.n0.HandleSwitchDrawingLayer,e)}}(n)},children:n.title})},"display_check_"+e)})})},Oa=t(49063),Da=t(21491),Ea=t(43417),Ma=t(57189),Fa=t(65640);function Ba(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ta(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function La(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Ta(o,r,i,a,l,"next",n)}function l(n){Ta(o,r,i,a,l,"throw",n)}a(void 0)})}}function Ra(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Ua(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Ra(n,e,t[e])})}return n}function Ha(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function Ga(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Ba(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ba(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wa(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Ya=(0,C.observer)(function(n){var e=n.setSceneMode,t=ba().styles,i=(0,M.P)(),a=i.homeStore,l=a.setShowDreamerPopup,c=a.setIsdrawPicture,s=D.nb.instance.scene3D,u=D.nb.instance.layout_container,d=Ga(z.A.useMessage(),2),f=d[0],p=d[1],h=D.nb.t,m=Ga((0,P.useState)(null),2),x=m[0],g=m[1],v=((0,Oa.Zp)(),Ga((0,P.useState)(!1),2)),b=v[0],y=v[1],w=[{iconType:"icon-chexiao",onClick:function(){D.nb.RunCommand(D._I.Undo)},name:h("撤销"),isHidden:"2D"!==i.homeStore.viewMode},{iconType:"icon-huifu",onClick:function(){D.nb.RunCommand(D._I.Redo)},name:h("恢复"),isHidden:"2D"!==i.homeStore.viewMode,divider:!0},{iconType:"icon-change_logo",onClick:function(){return La(function(){return Wa(this,function(n){return"3D"==i.homeStore.viewMode?e("3D_FirstPerson"):e("3D"),[2]})})()},name:"3D"==i.homeStore.viewMode?h("漫游"):h("鸟瞰"),isHidden:"2D"==i.homeStore.viewMode||i.homeStore.isdrawPicture},{iconType:"icon-save",onClick:function(){0==u._room_entities.length?z.A.error(h("当前方案为空，无法保存！")):null==u._layout_scheme_id?i.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"}):D.nb.DispatchEvent(D.n0.SaveLayoutScheme,null)},name:h("保存"),isHidden:i.homeStore.isdrawPicture},{iconType:"icon-tuku",onClick:function(){return La(function(){return Wa(this,function(n){switch(n.label){case 0:return[4,i.homeStore.query_genCount()];case 1:return n.sent(),0==u._room_entities.length?z.A.warning(h("请先创建方案")):i.homeStore.setShowAtlas(!0),[2]}})})()},name:h("图册"),id:"renderingTukuBtnId",isHidden:!0},{iconType:"icon-xiangjishezhi",onClick:function(){i.homeStore.setShowSubmitInfo(!0)},name:h("相机"),isHidden:!i.homeStore.isdrawPicture},{iconType:"icon-lishibanben",onClick:function(){D.nb.emit(F.U.setMultiSchemeListVisible,!0)},name:h("多方案"),isHidden:!0},{iconType:"icondisplay",onClick:function(){},name:h("显隐"),isHidden:"2D"!==i.homeStore.viewMode},{iconType:"iconhuizhong",onClick:function(){if("2D"===i.homeStore.viewMode)window.innerWidth<.8*window.innerHeight?ht.f.focusCenterByWholeBox(u,.7):ht.f.focusCenterByWholeBox(u,.5),D.nb.instance.update();else{u.updateWholeBox();var n=u._whole_bbox.getCenter(new vr.Pq0);s.setCenter(n)}},name:h("居中"),isHidden:"2D"!==i.homeStore.viewMode},{iconType:"icon-baojia",onClick:function(){return La(function(){var n,e,t,r,i,o,a;return Wa(this,function(l){switch(l.label){case 0:return{},e=D.nb.instance,t=ya.K.instance,r={seriesKgId:null,ruleId:null,seedSchemeId:null,ruleName:null,seedSchemeName:null,roomName:null,seriesName:null,status:null,thumbnail:null,roomList:null,ruleImageList:null,layoutTemplates:null},(i=t.makeQuoteData(e.layout_container,new wa._(r)))?(n={data:JSON.stringify(i)},[4,(0,ja.fS)(n)]):[3,2];case 1:(o=l.sent()).success&&(z.A.success(h("报价成功")),(a=document.createElement("a")).href=o.data,document.body.appendChild(a),a.click(),document.body.removeChild(a)),l.label=2;case 2:return[2]}})})()},name:h("装修"),isHidden:!0},{iconType:"icon-baojia",onClick:function(){return La(function(){return Wa(this,function(n){return i.homeStore.setShowCabinetCompute(!0),[2]})})()},name:h("算量"),isHidden:"2D"!==i.homeStore.viewMode},{iconType:"icon-search",onClick:function(){return La(function(){return Wa(this,function(n){return l(!0),[2]})})()},name:h("找相似"),isHidden:"2D"!==i.homeStore.viewMode||!o.um},{iconType:(null==s?void 0:s.outlineMaterialMode)==br.Gf.WhiteModelOutline?"iconShowmaterial_Nor":"iconShowoutline_Nor",name:(null==s?void 0:s.outlineMaterialMode)==br.Gf.WhiteModelOutline?h("材质"):h("轮廓"),onClick:function(){(null==s?void 0:s.outlineMaterialMode)==br.Gf.WhiteModelOutline?(s.outlineMaterialMode=br.Gf.MaterialOnly,_(function(n){return n.map(function(n){return n.name===D.nb.t("材质")?Ha(Ua({},n),{name:D.nb.t("轮廓"),iconType:"iconShowoutline_Nor"}):n})})):(s.outlineMaterialMode=br.Gf.WhiteModelOutline,_(function(n){return n.map(function(n){return n.name===D.nb.t("轮廓")?Ha(Ua({},n),{name:D.nb.t("材质"),iconType:"iconShowmaterial_Nor"}):n})})),f.open({type:null,content:(null==s?void 0:s.outlineMaterialMode)==br.Gf.WhiteModelOutline?h("显示轮廓"):h("显示材质"),className:"custom-class"})},isHidden:"2D"===i.homeStore.viewMode,isChecked:!0},{iconType:D.nb.instance.Configs.isClickDrawPic?"icon-chanpinzhiru":"icon-maikefeng1",onClick:function(){D.nb.instance.Configs.isClickDrawPic=!D.nb.instance.Configs.isClickDrawPic,D.nb.emit_M(F.U.FigureElementSelected,null),D.nb.instance.scene3D.setSelectionBox(null),f.open({type:null,content:D.nb.instance.Configs.isClickDrawPic?h("进入演讲模式"):h("进入换搭模式"),className:"custom-class"})},name:D.nb.instance.Configs.isClickDrawPic?h("换搭"):h("演讲"),isHidden:"3D_FirstPerson"!==i.homeStore.viewMode||i.homeStore.isdrawPicture},{iconType:"icon-Frame",onClick:function(){D.nb.DispatchEvent(D.n0.autoSave,null),c(!0),D.nb.instance.Configs.isClickDrawPic=!0,s.raycasteControls.onSelectedFigure(null)},name:h("出图"),isHidden:"3D_FirstPerson"!==i.homeStore.viewMode||i.homeStore.isdrawPicture,isChecked:!0},{iconType:"icon-fenxiang",onClick:function(){return La(function(){return Wa(this,function(n){return y(!b),[2]})})()},name:h("分享"),isHidden:i.homeStore.isdrawPicture},{iconType:"iconmore",onClick:function(){},name:null,isHidden:!0,isChecked:!0},{iconType:"icon-icon",onClick:function(){c(!1),D.nb.instance.Configs.isClickDrawPic=!1,D.nb.instance.renderSubmitObject={drawPictureMode:null,radioMode:0,resolution:0},s.setLightMode(br.Ei.Day),Ma.p.instance.cleanLighting(),D.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),Ea.Y.cleanLight()},name:h("取消"),isHidden:!i.homeStore.isdrawPicture,isChecked:!0}],j=Ga((0,P.useState)(),2),S=j[0],_=j[1];(0,P.useEffect)(function(){D.nb.on_M(F.U.Scene3DUpdated,"Statusbars",function(){D.nb.instance.layout_container;s=D.nb.instance.scene3D})},[]),(0,P.useEffect)(function(){var n=function(n){null!==n.target.closest("#ipad-sideToolbar")||g(null)};return document.addEventListener("mousedown",n),function(){document.removeEventListener("mousedown",n)}},[]);var k=[{iconType:"iconfile",onClick:function(){i.homeStore.setShowEnterPage({show:!0,source:"sideToolbar"})},name:h("新建")},{iconType:"iconbuzhisucai",onClick:function(){i.homeStore.setShowMySchemeList(!0)},name:h("方案")}];k=k.filter(function(n){return n&&!n.isHidden}),(0,P.useEffect)(function(){_(w.filter(function(n){return null!==n&&!n.isHidden})),Fa.log("store.homeStore.viewMode",i.homeStore.viewMode)},[i.homeStore.viewMode,i.homeStore.drawPictureMode,i.homeStore.isdrawPicture,D.nb.instance.Configs.isClickDrawPic]);var I="2D"===i.homeStore.viewMode,C=Ga((0,P.useState)(0),2),A=C[0],N=C[1];return(0,P.useEffect)(function(){N(i.homeStore.genCount)},[i.homeStore.genCount]),(0,r.jsxs)("div",{id:"ipad-sideToolbar",children:[(0,r.jsxs)("div",{className:"".concat(t.container," ").concat("2D"!==i.homeStore.viewMode?t.blackColor:""),children:[S&&S.map(function(n,e){return(0,r.jsxs)("div",{className:"".concat(t.iconButton," ").concat(x===n.iconType?n.isChecked?"checked":"notChecked":""),onClick:function(){return e=n.iconType,t=n.onClick,g(x===e?null:e),void t();var e,t},id:n.id,children:["图册"===n.name?(0,r.jsx)(Sa.A,{count:null===A?"?":A,size:"small",children:(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==i.homeStore.viewMode?"#fff":"#595959",children:(0,r.jsx)("use",{xlinkHref:"#".concat(n.iconType)})})}):(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==i.homeStore.viewMode?"#fff":"#595959",children:(0,r.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,r.jsx)(ce.If,{condition:n.name,children:(0,r.jsx)("div",{className:"iconButtonText",children:n.name})}),(0,r.jsx)(ce.If,{condition:n.divider,children:(0,r.jsx)("span",{className:"divider"})})]},e)}),"iconmore"===x&&(0,r.jsx)("div",{className:t.morebtns_container,children:k.map(function(n,e){return(0,r.jsxs)("div",{className:t.iconButton,onClick:function(){g(null),n.onClick()},id:n.id,children:[(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==i.homeStore.viewMode?"#fff":"#595959",children:(0,r.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,r.jsx)("div",{className:"iconButtonText",children:n.name}),(0,r.jsx)(ce.If,{condition:n.divider,children:(0,r.jsx)("span",{className:"divider"})})]},e)})})]}),(0,r.jsx)(Pa,{isVisible:"icondisplay"===x&&I}),b&&(0,r.jsx)("div",{className:"",style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:"999",background:"#fff",padding:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)",borderRadius:"4px"},children:(0,r.jsx)(Da.A,{onClose:function(){y(!1)}})}),p]})}),Xa=t(25781);function Va(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Za(){var n=Va(["\n      display: flex;\n      justify-content: start;\n      align-items: center;\n      border-radius: 50px;\n      background: #fff;\n      border: 1px solid #FFFFFF;\n      box-shadow: 0px 6px 20px 0px #0000001E;\n      position: fixed;\n      left: 50%;\n      z-index: 9;\n      transform: translateX(-50%);\n      overflow:hidden;\n      top: 60px; \n      left: 50%; \n       flex-direction: row; \n      .topLine{\n        width: 40px;\n        height: 2px;\n        background: #E0E1E1;\n        position: absolute;\n        top: 5px;\n        left: 50%;\n        transform: translateX(-50%);\n        border-radius: 10px;\n      }\n    "]);return Za=function(){return n},n}function qa(){var n=Va(["\n      opacity: 1;\n    "]);return qa=function(){return n},n}function Ja(){var n=Va(["\n      opacity: 0;\n    "]);return Ja=function(){return n},n}function Ka(){var n=Va(["\n      width: 56px;\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      color: #282828;\n      font-size: 12px;\n      padding: 4px 0px;\n      margin: 0 8px;\n      position: relative;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 30px !important;\n        height: 44px !important;\n        margin: 0 4px !important;\n        font-size: 10px !important;\n        .label {\n          width:20px;\n        }\n      }\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        font-size: 11px;\n      }\n      div{\n        margin: 0px 0px;\n      }\n      .divider\n      {\n        position: absolute;\n        left: -5px;\n        height: 100%;\n        border-left: 1px #E0E1E1 solid;\n        width: 1px;\n      }\n    "]);return Ka=function(){return n},n}var Qa=(0,a.rU)(function(n){var e=n.css;return{root:e(Za()),show:e(qa()),hide:e(Ja()),btnInfo:e(Ka())}}),$a=t(5640),nl=t(99940),el=t(36906),tl=t(38008),rl=t(65640);function il(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ol(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function al(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){ol(o,r,i,a,l,"next",n)}function l(n){ol(o,r,i,a,l,"throw",n)}a(void 0)})}}function ll(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||sl(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cl(n){return function(n){if(Array.isArray(n))return il(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||sl(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sl(n,e){if(n){if("string"==typeof n)return il(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?il(n,e):void 0}}function ul(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}A.A.confirm;var dl=(0,C.observer)(function(){var n,e,t,a=(0,i.B)().t,l=Qa().styles,c=(0,M.P)(),s=ll((0,P.useState)([]),2),u=(s[0],s[1]),d=(0,P.useRef)(null),f=ll((0,P.useState)(!1),2),p=f[0],h=f[1],m=ll((0,P.useState)("null"),2),x=m[0],g=m[1],v=(D.nb.instance.layout_container,ll((0,P.useState)([]),2)),b=v[0],y=v[1],w=ll((0,P.useState)(0),2),j=w[0],S=w[1],_=ll((0,P.useState)(""),2),k=(_[0],_[1],ll((0,P.useState)(!1),2)),I=(k[0],k[1],ll((0,P.useState)(!1),2)),C=I[0],A=I[1],N=ll((0,P.useState)(!1),2),O=N[0],E=(N[1],ll((0,P.useState)(!1),2)),B=E[0],T=E[1],L=ll((0,P.useState)({designStyle:[],rooms:[]}),2),R=(L[0],L[1]),U=((0,Oa.Zp)(),p?36:45),H=[{id:"Layout",label:a("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:a("风格"),icon:"icon-a-TypefenggeStateDefault"},"2D"===c.homeStore.viewMode?{id:"material",label:a("素材"),icon:"icon-a-TypesucaiStateDefault"}:null,{id:"attribute",label:a("属性"),icon:"icon-a-TypeshuxingStateDefault"},"3D_FirstPerson"===c.homeStore.viewMode?{id:"view",label:a("视角"),icon:"icon-smarttemplate"}:null,"2D"===c.homeStore.viewMode?{id:"aidraw",label:a("AI绘图"),icon:"icon-AIchutu"}:null,o.Ic?{id:"similar",label:a("相似匹配"),icon:"icona-Typexuanzebujian"}:null,o.Ic?{id:"create",label:a("新建"),icon:"iconfile"}:null].filter(Boolean),G=[{id:"Layout",label:a("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:a("风格"),icon:"icon-a-TypefenggeStateDefault"},{id:"submitDrawing",label:a("提交绘图"),icon:"icon-xuanranRender"},{id:"atlas",label:a("图册"),icon:"icon-tuku"}].filter(Boolean),W=[{id:"size",icon:"icon-chicun",label:a("尺寸"),onClick:function(){c.homeStore.setSizeInfo({type:"size",visible:!0}),D.nb.emit_M(dr.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"rotate",icon:"icon-rotate",label:a("旋转"),disabled:!1,divider:!1},{id:"flip",icon:"icon-horizontalflip_line",label:a("左右镜像"),disabled:!1,divider:!1},{id:"copy",icon:"icon-niantie",label:a("复制"),disabled:!1,divider:!1},{id:"autoRuler",icon:"icon-chicun",label:a("开启标尺"),disabled:!1,divider:!1},"Furniture"===(null===(n=c.homeStore.selectEntity)||void 0===n?void 0:n.type)&&!(null===(e=c.homeStore.selectEntity)||void 0===e?void 0:e.figure_element.haveMatchedMaterial())&&{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}];W=W.filter(Boolean);var Y=[{id:"rotate",icon:"icon-rotate",label:a("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}],X=[{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}],V=[{id:"splitWall",icon:"iconsplit",label:a("拆分墙")},{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}],Z=[{id:"size",icon:"icon-chicun",label:a("尺寸"),onClick:function(){c.homeStore.setSizeInfo({type:"size",visible:!0}),D.nb.emit_M(dr.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"rotate",icon:"icon-rotate",label:a("旋转"),disabled:!1,divider:!1},{id:"ungroupTemplate",icon:"icon-jiezu-2",label:a("解组"),disabled:!1,divider:!1},{id:"combinationStorage",icon:"icon-anzhuangInstall",label:a("组合入库"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}],q=[{id:"attribute",label:a("属性"),icon:"icon-a-TypeshuxingStateDefault",onClick:function(){D.nb.emit_M(dr.showPopup,"attribute")}},{id:"material",label:a("清除布局"),icon:"icon-shanchubuju",EventName:"ClearLayout"},c.homeStore.isSingleRoom||"2D"!==c.homeStore.viewMode?null:{id:"focusSpace",label:a("专注空间"),icon:"icon-zhuanzhukongjian",EventName:"SingleRoomLayout",onClick:function(){D.nb.DispatchEvent(D.n0.SingleRoomLayout,c.homeStore.selectEntity),c.homeStore.setIsSingleRoom(!0)}},c.homeStore.isSingleRoom&&"2D"===c.homeStore.viewMode?{id:"exit",label:"",icon:"icon-a-tianchongFace-1",onClick:function(){D.nb.DispatchEvent(D.n0.leaveSingleRoomLayout,{}),c.homeStore.setIsSingleRoom(!1)}}:null];q=q.filter(function(n){return null!==n}),(0,P.useEffect)(function(){D.nb.on_M(F.U.SelectingTarget,"PadStatusBar",function(n){var e,t=n||null;c.homeStore.setSelectEntity(n),n||c.homeStore.setShowReplace(!1);var r=(null==t?void 0:t.type)||null,i=(null==n||null===(e=n.ex_prop)||void 0===e?void 0:e.label)||null;if("Furniture"===r){var o;o=cl(W),y(o)}else if("Group"===r){var l=[{id:"size",icon:"icon-chicun",label:a("尺寸"),onClick:function(){c.homeStore.setSizeInfo({type:"size",visible:!0}),D.nb.emit_M(dr.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"combination",label:a("组合"),icon:"iconcombination",tips:a("组合"),disabled:!1,divider:!1}];y(l)}else if("BaseGroup"===r){var s=[];s=cl(Z),(null==t?void 0:t.matched_rect)&&s.splice(s.length-1,0,{id:"replace",icon:"icon-change_logo",label:a("替换"),disabled:!1,divider:!1}),y(Z)}else if("Door"===r||"Window"===r){if("baywindow"===i)return void y(X);y(Y)}else"Wall"===r?y(V):"StructureEntity"===r?y([{id:"delete",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}]):r===hr.Fz.RoomSubArea&&y([{id:"roomSubAreaAttribute",icon:"icon-a-TypeshuxingStateDefault",label:a("属性"),disabled:!1,divider:!1,onClick:function(){D.nb.emit_M(dr.showPopup,"SpaceAreaAttribute")}},{id:"copyRoomSubArea",icon:"iconcopy",label:a("复制"),disabled:!1,divider:!0},{id:"deleteRoomSubArea",icon:"icon-delete",label:a("删除"),disabled:!1,divider:!0}])}),wn()},[]),(0,P.useEffect)(function(){"3D_FirstPerson"===c.homeStore.viewMode?u(G):u(H)},[c.homeStore.viewMode]),(0,P.useEffect)(function(){var n,e,t;if("3D_FirstPerson"===c.homeStore.viewMode&&"Furniture"!==(null===(n=c.homeStore.selectEntity)||void 0===n?void 0:n.type))return $(null),void D.nb.instance.layout_container.cleanDimension();if("3D_FirstPerson"===c.homeStore.viewMode&&"Furniture"===(null===(e=c.homeStore.selectEntity)||void 0===e?void 0:e.type))return D.nb.emit_M(dr.showPopup,"replace"),D.nb.instance.layout_container.cleanDimension(),Q(!1),void $(null);var r=(null===(t=c.homeStore.selectEntity)||void 0===t?void 0:t.type)||null;$(r)},[null===(t=c.homeStore.selectEntity)||void 0===t?void 0:t.type,c.homeStore.viewMode]);var J=ll((0,P.useState)(!0),2),K=J[0],Q=J[1],$=function(n){Q(!1),setTimeout(function(){g(n||"null"),Q(!0),"3D"===c.homeStore.viewMode&&Q(!1)},30)},nn=window.innerWidth,en=(window.innerHeight,ll((0,P.useState)({top:80,left:nn/2}),2)),tn=en[0],rn=en[1],on=ll((0,P.useState)(!1),2),an=on[0],ln=on[1],cn=(0,P.useRef)(null),sn=(0,P.useRef)({top:0,left:0}),un=(0,P.useRef)({x:0,y:0}),dn=ll((0,P.useState)(!1),2),fn=dn[0],pn=dn[1],hn=function(n){ln(!0),sn.current={top:tn.top,left:tn.left},un.current={x:n.touches[0].clientX,y:n.touches[0].clientY}},mn=function(n){n.preventDefault()},xn=function(n){},gn=function(){return{position:"fixed",top:tn.top,left:"50%",maxWidth:C||fn?U+"px":"550px",maxHeight:C?U+"px":fn?"550px":U+"px",minWidth:U+"px",minHeight:U+"px",flexDirection:fn?"column":"row"}},vn=function(){return(0,r.jsx)("div",{onClick:function(){return h(!p)},style:{borderTopLeftRadius:"50%",width:fn?64:24,height:fn?24:64,backgroundColor:"#fff"}})},bn=function(){return C&&(0,r.jsx)(vt.A,{type:"icon-a-TypegongjuStateDefault",style:{fontSize:"31px",color:"#282828",margin:"-3px 14px 0px 17px"},onClick:yn})},yn=function(){!function(n,e,t){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];rn({top:n,left:e}),A(t),pn(r)}(60,nn/2,!1,!1)};(0,P.useEffect)(function(){var n=cn.current;return n&&n.addEventListener("touchend",xn),function(){n&&n.removeEventListener("touchend",xn)}},[an,O]),(0,P.useEffect)(function(){yn()},[c.homeStore.IsLandscape]),(0,P.useEffect)(function(){4===c.homeStore.zIndexOf3DViewer&&jn()},[c.homeStore.zIndexOf3DViewer]),(0,P.useEffect)(function(){D.nb.DispatchEvent(D.n0.AutoRuler,{AutoRuler:B}),y(b.map(function(n){return"autoRuler"===n.id&&(n.label=a(B?"关闭标尺":"开启标尺")),n}))},[B]);var wn=function(){return al(function(){var n,e;return ul(this,function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return n=t.sent(),R(n),[3,4];case 3:return e=t.sent(),rl.error("获取配置失败:",e),[3,4];case 4:return[2]}})})()},jn=function(){switch(x){case"null":return(0,r.jsx)(r.Fragment,{});case"Furniture":case"BaseGroup":case"Door":case"Window":case"Group":return(0,r.jsxs)("div",{ref:cn,onTouchStart:hn,onTouchMove:mn,style:gn(),className:"".concat(l.root," ").concat(K?l.show:l.hide),children:[vn(),bn(),"                    ",b.map(function(n,e){return(0,r.jsxs)("div",{className:l.btnInfo,onClick:function(){if(n.onClick)n.onClick();else switch(n.id){case"rotate":D.nb.RunCommand(D._I.RotateFurniture);break;case"flip":D.nb.RunCommand(D._I.FlipFurniture);break;case"flip_vertical":D.nb.RunCommand(D._I.FlipFurnitureVertical);break;case"delete":case"deleteRoomSubArea":D.nb.RunCommand(D._I.DeleteFurniture);break;case"deleteRuler":D.nb.RunCommand(D._I.DeleteRuler);break;case"copy":D.nb.RunCommand(D._I.CopyFurniture);break;case"combination":d.current.onModal();break;case"ungroupTemplate":D.nb.DispatchEvent(D.n0.HandleUnGroupTemplate,{}),c.homeStore.setKey(Date.now());break;case"replace":D.nb.emit_M(dr.showPopup,n.id),Q(!1);break;case"size":c.homeStore.setSizeInfo({type:"size",visible:!0});break;case"pos_z":c.homeStore.setSizeInfo({type:"pos_z",visible:!0});break;case"copyRoomSubArea":D.nb.DispatchEvent(D.n0.CopyRoomSubArea,null);break;case"combinationStorage":al(function(){var n,e,t,r;return ul(this,function(i){switch(i.label){case 0:return n=c.addGroupStore.addGroupData,e=c.homeStore.selectEntity,[4,(0,$a.nA)(tl.x.saveEntityImage(e),"snapShot"+Math.floor(1e4*Math.random())+".png")];case 1:return t=i.sent(),"addsizeGroup"!==n.type?[3,3]:(r={metaData:e.exportData(),metaHeight:e.rect.rect_center_3d.z,metaLength:e.rect.w,metaWidth:e.rect.h,metaImage:t,metaName:e.name,metaImageId:n.sizeObj.id},[4,(0,el.G)(r)]);case 2:i.sent().success&&(z.A.success(a("尺寸链组合入库成功")),window.parent.postMessage({origin:"layoutai.api",type:"GroupMessage",data:{success:!0}},"*")),i.label=3;case 3:return[2]}})})();break;case"autoRuler":T(!B)}},children:[n.divider&&(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:(0,r.jsx)(vt.A,{type:n.icon,style:{fontSize:"20px",color:"#282828"}})}),!p&&(0,r.jsx)("div",{className:"label",children:n.label})]})]},e)}),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{children:(0,r.jsx)(vt.A,{type:"icon-a-tianchongFace-1",onClick:function(){D.nb.DispatchEvent(D.n0.cleanSelect,null)},style:{fontSize:"20px",color:"#BCBEC2"}})})}),vn()]});case"RoomArea":return(0,r.jsxs)("div",{ref:cn,onTouchStart:hn,onTouchMove:mn,className:"".concat(l.root," ").concat(K?l.show:l.hide),style:gn(),children:[bn(),vn(),(0,r.jsx)(r.Fragment,{children:q.map(function(n,e){return(0,r.jsxs)("div",{className:l.btnInfo,style:{margin:fn?"4px 0":"0 8px"},onClick:function(){n.onClick?(n.onClick(),S(Math.floor(1e4*Math.random()))):(null==n?void 0:n.EventName)&&(S(Math.floor(1e4*Math.random())),D.nb.DispatchEvent(null==n?void 0:n.EventName,c.homeStore.selectEntity))},children:[(0,r.jsx)("div",{children:(0,r.jsx)(vt.A,{type:n.icon,style:{fontSize:"20px",color:"#282828"}})}),!p&&(0,r.jsx)("div",{className:"label",children:n.label})]},e)})}),vn()]});default:return null}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:jn()},j),(0,r.jsx)(nl.A,{ref:d})]})});function fl(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function pl(){var n=fl(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      z-index: 8;\n      width: 100%;\n      height: 100%;\n      border-width: 40px 45px;\n      border-color: #000000;\n      border-style: solid;\n      opacity: 0.35;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      box-sizing: border-box;\n      pointer-events: none;\n    "]);return pl=function(){return n},n}function hl(){var n=fl(["\n      width: 100%;\n      height: 100%;\n      background-color: transparent; /* 中间区域透明 */\n      border: 3px solid #ffffff; /* 添加白色边框 */\n      border-radius: 3px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      pointer-events: none;\n    "]);return hl=function(){return n},n}var ml=(0,a.rU)(function(n){var e=n.css;return{box:e(pl()),contentBox:e(hl())}});function xl(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function gl(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return xl(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return xl(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var vl=.83,bl=(0,C.observer)(function(){var n=ml().styles,e=(0,M.P)(),t=(0,P.useRef)(null),i=gl((0,P.useState)(window.innerWidth<window.innerHeight),2),o=i[0],a=i[1],l=gl((0,P.useState)({borderWidth:"0px",borderHeight:"0px",contentWidth:"100%",contentHeight:"100%"}),2),c=l[0],s=l[1];(0,P.useEffect)(function(){var n=function(){var n=window.innerWidth<window.innerHeight;a(n),u(e.homeStore.aspectRatioMode,n)};return window.addEventListener("resize",n),function(){return window.removeEventListener("resize",n)}},[]),(0,P.useEffect)(function(){u(e.homeStore.aspectRatioMode,o)},[e.homeStore.aspectRatioMode,o]);var u=function(n){var e,t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,i=window.innerWidth,a=window.innerHeight,l=[{width:4,height:3},{width:16,height:9},{width:3,height:4},{width:9,height:16},{width:0,height:0}][n-1];if(5==n)return e=i,t=a,void s({borderWidth:"".concat(0,"px"),borderHeight:"".concat(0,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")});r?(t=(e=i*vl)*l.height/l.width)>a*vl&&(e=(t=a*vl)*l.width/l.height):(e=(t=a*vl)*l.width/l.height)>i*vl&&(t=(e=i*vl)*l.height/l.width),s({borderWidth:"".concat((i-e)/2,"px"),borderHeight:"".concat((a-t)/2,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")})};return(0,P.useEffect)(function(){if(t.current){var e=t.current;e.style.borderLeftWidth=c.borderWidth,e.style.borderRightWidth=c.borderWidth,e.style.borderTopWidth=c.borderHeight,e.style.borderBottomWidth=c.borderHeight;var r=e.querySelector(".".concat(n.contentBox));r&&(r.style.width=c.contentWidth,r.style.height=c.contentHeight)}},[c,n.contentBox]),(0,r.jsx)("div",{className:n.box,ref:t,children:(0,r.jsx)("div",{className:n.contentBox})})}),yl=t(79750);function wl(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function jl(){var n=wl(["\n    position: fixed;\n    top: 5px;\n    left: 50%;\n    transform: translateX(-50%);\n    max-width: 200px;\n    width: auto;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    z-index: 999;\n    border-radius: 8px;\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);;\n    justify-content: space-between;\n    @media screen and (max-width: 450px) { // 手机宽度\n      min-width: 250px;\n      height: 36px;\n    }\n  "]);return jl=function(){return n},n}function Sl(){var n=wl(["\n    color: #333;\n    font-size: 14px;\n    margin-right: 12px;\n    max-width: 40%;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  "]);return Sl=function(){return n},n}function _l(){var n=wl(["\n    background-color: #1890ff;\n    color: white;\n    border: none;\n    padding: 6px 20px;\n    height: 28px;\n    line-height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    font-size: 14px;\n    margin-left: 12px;\n    &:hover {\n      background-color: #40a9ff;\n    }\n  "]);return _l=function(){return n},n}function kl(){var n=wl(["\n    color: #999;\n    font-size: 14px;\n    margin-left: 12px;\n    white-space: nowrap;\n  "]);return kl=function(){return n},n}function Il(){var n=wl(["\n    width: 100px;\n    color: #d6d1d1;\n    font-size: 14px;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    position: relative;\n    @media screen and (max-width: 450px) { // 手机宽度\n      width: 60px;\n      font-size: 12px;\n    }\n    @media screen and (max-width: 350px) { // 手机宽度\n    }\n    z-index: 9;\n  "]);return Il=function(){return n},n}function Cl(){var n=wl(["\n    color: #fff;\n    background: rgba(255, 255, 255, 0.20);\n    backdrop-filter: blur(50px);\n    border-radius: 8px;\n  "]);return Cl=function(){return n},n}var Al=(0,a.rU)(function(n){var e=n.css;return{exitBarContainer:e(jl()),currentMode:e(Sl()),exitButton:e(_l()),exitHint:e(kl()),topTabs:e(Il()),active:e(Cl())}}),zl=t(65640);function Nl(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Pl(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Nl(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Nl(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ol=(0,C.observer)(function(){var n=(0,M.P)(),e=(0,i.B)().t,t=n.homeStore,o=t.drawPictureMode,a=t.setDrawPictureMode,l=(t.setIsdrawPicture,Al().styles),c=Pl((0,P.useState)(!1),2),s=(c[0],c[1],[{label:e("标准渲染"),value:"render"},{value:"aiDrawing",label:e("AI绘图")}]);return(0,P.useEffect)(function(){var n=D.nb.instance.scene3D;"render"===o&&(n.setLightMode(br.Ei.Night),Ma.p.instance.updateLighting(!0),D.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1)),"aiDrawing"===o&&(n.setLightMode(br.Ei.Day),Ma.p.instance.cleanLighting(),D.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),Ea.Y.cleanLight())},[o]),(0,r.jsx)("div",{className:l.exitBarContainer,children:s.map(function(e,t){return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{onClick:function(){return t=e.value,a(t),n.homeStore.setAtlasMode("aiDrawing"===t?"aidraw":"render"),void zl.log("drawingPicMode",t);var t},className:l.topTabs+(o===e.value?" ".concat(l.active):""),children:[e.label,o===e.value&&(0,r.jsx)("span",{style:{content:'""',position:"absolute",bottom:"2px",left:"38px",width:"20px",height:"2px",backgroundColor:"#fff"}})]},t)})})})}),Dl=t(47299);function El(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ml(){var n=El(["\n      height: 500px;\n      padding: 12px 12px;\n      border-radius: 12px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 100%;\n        padding: 12px 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return Ml=function(){return n},n}function Fl(){var n=El(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      color: rgba(255, 255, 255, 0.60);\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n      line-height: 24px;\n    "]);return Fl=function(){return n},n}function Bl(){var n=El(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin-top: 12px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            border-radius: 4px;\n            border: 1px solid #EEE;\n            background: #C3C4C7;\n            @media screen and (orientation: landscape) {\n              width: 48px;\n              height: 48px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          color: rgba(255, 255, 255, 0.85);\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: rgba(255, 255, 255, 0.60);\n              margin-top: 4px;\n              user-select: text;\n              @media screen and (orientation: landscape) {\n                margin-top: 4px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return Bl=function(){return n},n}function Tl(){var n=El(["\n      margin: 0px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 12px 0 8px 0px;\n      }\n    "]);return Tl=function(){return n},n}function Ll(){var n=El(["\n      display: flex;\n    "]);return Ll=function(){return n},n}function Rl(){var n=El(["\n      border-radius: 4px;\n      background: rgba(0, 0, 0, 0.40);\n      height: 24px;\n      padding: 2px 8px;\n      display: flex;\n      width: 70px;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      font-size: 12px;\n      margin-right: 8px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return Rl=function(){return n},n}function Ul(){var n=El(["\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n    "]);return Ul=function(){return n},n}function Hl(){var n=El(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n      @media screen and (orientation: portrait) {\n        overflow-x: scroll;\n        flex-wrap: nowrap;\n        width: 100%; \n      }\n    "]);return Hl=function(){return n},n}function Gl(){var n=El(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return Gl=function(){return n},n}function Wl(){var n=El(["\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n      @media screen and (max-width: 800px){\n         width: 112px;\n         img{\n          width: 112px;\n         }\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n\n    "]);return Wl=function(){return n},n}function Yl(){var n=El(["\n    \n    "]);return Yl=function(){return n},n}function Xl(){var n=El(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return Xl=function(){return n},n}function Vl(){var n=El(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      margin-top: 100%;\n      transform: translateY(-50%);\n      .emptyImg{\n        width: 60px;\n        height: 60px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #fff;\n      }\n    "]);return Vl=function(){return n},n}var Zl=(0,a.rU)(function(n){var e=n.css;return{root:e(Ml()),title:e(Fl()),topInfo:e(Bl()),divider:e(Tl()),tabContainer:e(Ll()),tabItem:e(Rl()),active:e(Ul()),goodsInfo:e(Hl()),loading:e(Gl()),goodsItem:e(Wl()),selectIcon:e(Yl()),sizeInfo:e(Xl()),noData:e(Vl())}});function ql(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Jl(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Kl(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Jl(o,r,i,a,l,"next",n)}function l(n){Jl(o,r,i,a,l,"throw",n)}a(void 0)})}}function Ql(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function $l(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ql(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ql(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nc(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ec=(0,C.observer)(function(n){var e=n.selectedFigureElement,t=(0,M.P)(),a=(0,i.B)().t,l=Zl().styles,c=$l((0,P.useState)(null==e?void 0:e._candidate_materials),2),s=c[0],u=c[1],d=$l((0,P.useState)("套系素材"),2),f=d[0],p=d[1],h=$l((0,P.useState)(!1),2),m=h[0],x=h[1],g=(0,P.useRef)(null);(0,P.useEffect)(function(){e&&u(null==e?void 0:e._candidate_materials),p("套系素材")},[e]);return(0,P.useEffect)(function(){!function(n){Kl(function(){var t,r,i;return nc(this,function(a){switch(a.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?u(null==e?void 0:e._candidate_materials):u([]),[3,3]);case 1:return x(!0),[4,(0,Xt.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(r=a.sent()).success&&r.data?(i=r.data.materials.records.map(function(n){return{imageUrl:o.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}}),u(i)):u([]),x(!1),a.label=3;case 3:return[2]}})})()}(f)},[f]),(0,r.jsx)("div",{className:l.root,children:e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:l.title,children:[(0,r.jsx)("div",{children:a("素材替换")}),(0,r.jsx)(vt.A,{style:{fontSize:20,color:"#5B5E60"},type:"icon-close1",onClick:function(){D.nb.emit_M(F.U.FigureElementSelected,null)}})]}),e&&(0,r.jsx)("div",{className:l.topInfo,children:(0,r.jsxs)("div",{className:"info",children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,r.jsxs)("div",{className:"sizeInfo",children:[(0,r.jsx)("div",{children:e._matched_material.name}),(0,r.jsxs)("div",{className:"size",children:[a("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,r.jsxs)("div",{className:"size",children:[a("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]}),(0,r.jsxs)("div",{className:"size",children:[a("素材ID"),"：",e._matched_material.modelId]})]})]})}),(0,r.jsx)("div",{className:l.divider,children:(0,r.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some(function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)})&&!t.userStore.aihouse&&"C00002170"!==t.userStore.userInfo.tenantId&&(0,r.jsx)(N.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){g.current.onModal()},children:a("AI搭柜")})})}),(0,r.jsxs)("div",{className:l.tabContainer,children:[(0,r.jsx)("div",{className:"".concat(l.tabItem," ").concat("套系素材"===f?l.active:""),onClick:function(){return p("套系素材")},children:a("套系素材")}),(0,r.jsx)("div",{className:"".concat(l.tabItem," ").concat("云素材"===f?l.active:""),onClick:function(){return p("云素材")},children:a("云素材")})]}),(0,r.jsx)("div",{className:l.goodsInfo,children:m?(0,r.jsxs)("div",{className:l.loading,children:[(0,r.jsx)(Ae.A,{size:"large"})," "]}):s&&s.length>0?s.map(function(n,i){return(0,r.jsxs)("div",{className:l.goodsItem,onClick:function(){return Kl(function(){var r,a,l,c,s,u,d,p,h;return nc(this,function(m){switch(m.label){case 0:return e.locked?[2]:(t.designStore.setSelectedIndex(i),"套系素材"!==f?[3,1]:(D.nb.DispatchEvent(D.n0.ReplaceMaterial,n),[3,4]));case 1:return l=null,[4,(0,Xt.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(c=m.sent())||null===(a=c.result)||void 0===a||null===(r=a.result)||void 0===r?void 0:r[0])&&(l=null==c||null===(u=c.result)||void 0===u||null===(s=u.result)||void 0===s?void 0:s[0]),[4,(0,Vt.h)(n.materialId)];case 3:d=m.sent(),l&&(p={modelId:l.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:o.L4+n.imageUrl,name:l.MaterialName,originalLength:l.PICLength,originalWidth:l.PICWidth,originalHeight:l.PICHeight,length:l.PICLength,width:l.PICWidth,height:l.PICHeight,modelLoc:e.modelLoc,modelFlag:l.ModelFlag.toString(),topViewImage:d,figureElement:e},h=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Ql(n,e,t[e])})}return n}({},n,p),D.nb.DispatchEvent(D.n0.ReplaceMaterial,h)),m.label=4;case 4:return[2]}})})()},children:[i===t.designStore.selectedIndex&&(0,r.jsx)("div",{className:l.selectIcon}),(0,r.jsx)("img",{src:n.imageUrl,alt:""}),(0,r.jsx)("div",{className:l.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,r.jsx)("div",{className:l.sizeInfo,style:{color:"rgba(255, 255, 255, 0.6)"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},i)}):(0,r.jsxs)("div",{className:l.noData,children:[(0,r.jsx)("img",{className:"emptyImg",src:"https://3vj-fe.3vjia.com/layoutai/image/Empty.png",alt:""}),(0,r.jsx)("span",{children:a("暂无可用素材")})]})}),(0,r.jsx)(Zt.A,{onParams:function(){},selectedFigureElement:e,ref:g})]})})}),tc=t(44544);function rc(){var n,e,t=(n=["\n        width: 100%;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        .ant-dropdown-trigger {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            .ant-space {\n                width: 100%;\n                height: 100%;\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                gap: 4px;\n                .ant-space-item {\n                    color: #282828;\n                    font-family: PingFang SC;\n                    font-size: 12px;\n                    font-weight: regular;\n                    line-height: 166.7%;\n                    text-align: center;\n                }\n            }\n        }\n    "],e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}})));return rc=function(){return t},t}var ic=(0,a.rU)(function(n){return{root:(0,n.css)(rc())}}),oc=t(62428),ac=t(88098);function lc(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function cc(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return lc(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return lc(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var sc=(0,C.observer)(function(){var n=ic().styles,e=(0,M.P)(),t=(0,i.B)().t,o=cc((0,P.useState)(null),2),a=o[0],l=o[1],c=cc((0,P.useState)([]),2),s=c[0],u=c[1],d=cc((0,P.useState)([]),2),f=d[0],p=d[1],h=cc((0,P.useState)(!0),2),m=h[0],x=h[1],g=function(n){D.nb.DispatchEvent(D.n0.selectRoomArea,n),e.homeStore.setSelectedRoom(n),l(n),x(null===n)};(0,P.useEffect)(function(){p(e.homeStore.roomEntities)},[e.homeStore.roomEntities]),(0,P.useEffect)(function(){D.nb.on(F.U.selectRoom,function(n){n?(e.homeStore.setSelectedRoom(n),l(n),x(!1)):(e.homeStore.setSelectedRoom(null),l(null),x(!0))})},[]),(0,P.useEffect)(function(){var n=[{key:"all",label:t("全屋"),onClick:function(){g(null)}}];f.forEach(function(e){n.push({key:e.uidN,label:t(e.aliasName),onClick:function(){g(e)}})}),u(n)},[f,t]),(0,P.useEffect)(function(){e.homeStore.selectedRoom!==a&&(l(e.homeStore.selectedRoom),x(null===e.homeStore.selectedRoom))},[e.homeStore.selectedRoom]);return(0,r.jsx)("div",{className:n.root,children:(0,r.jsx)(oc.A,{menu:{items:s},placement:"bottomLeft",trigger:["click"],children:(0,r.jsx)("a",{onClick:function(n){return n.preventDefault()},children:(0,r.jsxs)(ac.A,{children:[t(a&&!m?a.aliasName:"全屋"),(0,r.jsx)(Rr.A,{iconClass:"icon-line_down",style:{fontSize:"16px",color:"#BCBEC2"}})]})})})})}),uc=t(65640);function dc(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function fc(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function pc(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){fc(o,r,i,a,l,"next",n)}function l(n){fc(o,r,i,a,l,"throw",n)}a(void 0)})}}function hc(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||xc(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mc(n){return function(n){if(Array.isArray(n))return dc(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||xc(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xc(n,e){if(n){if("string"==typeof n)return dc(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?dc(n,e):void 0}}function gc(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var vc={popupType:"Layout",sceneMode:"2D",prev3DSceneMode:"3D_FirstPerson"},bc=(0,C.observer)(function(n){n.updateKey;var e=n.isPanelOpen,t=n.layoutType,o=n.setChoosedMode,a=n.updateCanvasForViewMode,l=(0,i.B)().t,c=vo().styles,s=((0,Oa.Zp)(),(0,M.P)()),u=s.homeStore,d=u.viewMode,f=u.roomEntities,p=u.isdrawPicture,h=u.setIsdrawPicture,m=u.setViewMode,x=u.setSelectEntity,g=u.setShowReplace,v=u.setShowSaveLayoutSchemeDialog,b=hc((0,P.useState)(!1),2),y=b[0],w=b[1],j=hc((0,P.useState)(!1),2),S=j[0],_=j[1],k=hc((0,P.useState)(d),2),I=k[0],C=k[1],A=hc((0,P.useState)(vc.popupType),2),z=A[0],N=A[1],O=hc((0,P.useState)(null),2),E=O[0],B=O[1],T=hc((0,P.useState)([]),2),L=T[0],R=T[1],U=hc((0,P.useState)([{value:"Layout",label:l("推荐布局")},{value:"material",label:l("编辑布局")}]),2),H=U[0],G=U[1],W="PadLeftPanel",Y=D.nb.instance,X=Y.layout_container,V=hc((0,P.useState)(!1),2),Z=V[0],q=V[1],J=hc((0,P.useState)("AI生成"),2),K=J[0],Q=J[1],$={attribute:[{value:"attribute",label:l("属性")}],sizeEditor:[{value:"sizeEditor",label:l("尺寸")}],SpaceAreaAttribute:[{value:"SpaceAreaAttribute",label:l("属性")}]},nn=function(){var n,e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(Y.layout_container)if($[z])H.length=0,(n=H).push.apply(n,mc($[z]));else if("2D"===I){var r,i;if(uc.log(Y.layout_container.drawing_figure_mode),Y.layout_container.drawing_figure_mode==hr.qB.Figure2D)H.length=0,(r=H).push.apply(r,[{value:"Layout",label:l("推荐布局")},{value:"material",label:l("编辑布局")}]);else H.length=0,(i=H).push.apply(i,[{value:"Matching",label:l("推荐风格")}])}else{var o;H.length=0,(o=H).push.apply(o,[{value:"replace",label:l("换搭素材")}])}if(!E){var a=H.findIndex(function(n){return"replace"===n.value});a>=0&&H.splice(a,1)}t&&(H.find(function(n){return n.value===z})||on((null===(e=H[0])||void 0===e?void 0:e.value)||""));G(mc(H))},en=function(){return pc(function(){var n;return gc(this,function(e){switch(e.label){case 0:return[4,na.y.instance.autoApplySeries()];case 1:return e.sent(),C(vc.prev3DSceneMode),(n=D.nb.instance.scene3D)&&(n.outlineMaterialMode=br.Gf.WhiteModelOutline),[2]}})})()},tn=function(){if("2D"===I){var n=[{label:l("下一步"),onClick:function(){return pc(function(){return gc(this,function(n){return!X._layout_scheme_id&&X._room_entities.length>0?v({show:!0,source:"nextBtn"}):X._layout_scheme_id&&en(),[2]})})()}}].filter(function(n){return n});R(n)}else if("3D"===I){var e=[{label:l("返回布局"),onClick:function(){C("2D")}}];R(e)}else if("3D_FirstPerson"===I){var t=[{label:l("上一步"),onClick:function(){C("2D")}}];R(t)}p&&rn()},rn=function(){R([])};(0,P.useEffect)(function(){tn()},[I,p]);var on=function(n){vc.popupType=n,N(n)};(0,P.useEffect)(function(){Z&&(Ea.Y.cleanLight(),q(!1))},[Z]);var an=function(n,e){n&&n===E||(X.drawing_figure_mode===hr.qB.Figure2D&&"2D"===I?"Furniture"===e?"Layout"===vc.popupType&&on("material"):on("Layout"):"Furniture"===e||"Door"===e?n&&on("replace"):n&&"replace"==vc.popupType?on("replace"):on("Matching"),B(n))};(0,P.useEffect)(function(){var n=function(n){"saveLayoutSchemeAndNext"===n.data.type&&en()};return window.addEventListener("message",n),function(){window.removeEventListener("message",n)}},[]),(0,P.useEffect)(function(){return nn(),tn(),D.nb.on_M(F.U.FigureElementSelected,W,function(n){tc.C.get_polygon_type(null==n?void 0:n.rect)==hr.Fz.Door?an(n,hr.Fz.Door):an(n,hr.Fz.Furniture)}),D.nb.on_M(F.U.SelectingTarget,W,function(n,e,t){if("2D"==I){var r=n||null;if(null==r?void 0:r.figure_element)an(r.figure_element,"Furniture");else if((null==r?void 0:r.type)==hr.Fz.RoomArea){var i=r._room;(null==i?void 0:i.tile)?an(i.tile,"RoomArea"):an(null,"RoomArea")}else if((null==r?void 0:r.type)==hr.Fz.Door){var o=null==r?void 0:r._win_figure_element;an(o,hr.Fz.Door)}else an(null,"RoomArea");x(r),r||g(!1);var a=r;if(a&&(null==a?void 0:a._room))na.y.instance.current_rooms=[a._room],na.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:!0});else if(X._rooms){var l=X._rooms.filter(function(n){return n&&n.furnitureList.length>0});na.y.instance.current_rooms=l,na.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:null})}}}),D.nb.on_M(dr.showPopup,W,function(n){on(n)}),D.nb.on_M(F.U.SceneModeChanged,W,function(n){C(n)}),function(){D.nb.off_M_All({object_id:W})}},[I]),(0,P.useEffect)(function(){"2D"===d?C("2D"):"3D_FirstPerson"===d&&C("3D_FirstPerson")},[d]),(0,P.useEffect)(function(){!function(n){var e=D.nb.instance.layout_container,t=D.nb.instance.scene3D;if("2D"===n)t&&t.stopRender(),window.innerWidth<.8*window.innerHeight?(ht.f.focusCenterByWholeBox(e,.7),D.nb.instance.update()):(ht.f.focusCenterByWholeBox(e,.6),D.nb.instance.update()),D.nb.emit_M(gr.showLight3DViewer,!1),m("2D");else if("3D"===n)D.nb.DispatchEvent(D.n0.Match3dPreviewMaterials,null),D.nb.emit_M(gr.showLight3DViewer,!0),t.setCemeraMode(br.I5.Perspective),m("3D"),D.nb.DispatchEvent(D.n0.cleanSelect,null),t&&(t.startRender(),D.nb.emit_M(F.U.Scene3DUpdated,!1));else if("3D_FirstPerson"===n){if(D.nb.DispatchEvent(D.n0.Match3dPreviewMaterials,null),D.nb.emit_M(gr.showLight3DViewer,!0),t.setCemeraMode(br.I5.FirstPerson),"2D"==d){var r,i,o,a=e._room_entities.reduce(function(n,e){return n?e._area>n._area?e:n:e},null);if(a)e?(0==a._view_cameras.length&&(yl.q.updateViewCameraEntities(e,null,{methods:2}),uc.log("room._view_cameras",a._view_cameras)),t.active_controls.bindViewEntity(a._view_cameras[0]),s.homeStore.setCurrentViewCameras(a._view_cameras)):t.setCenter((null==a||null===(r=a._main_rect)||void 0===r?void 0:r.rect_center)||new vr.Pq0(0,0,0)),t.update();else t.setCenter((null===(o=f[0])||void 0===o||null===(i=o._main_rect)||void 0===i?void 0:i.rect_center)||new vr.Pq0(0,0,0))}m("3D_FirstPerson"),t&&(t.startRender(),D.nb.emit_M(F.U.Scene3DUpdated,!1))}n&&"2D"!==n&&(vc.prev3DSceneMode=n),vc.sceneMode=n,nn(!0)}(I)},[I]),(0,P.useEffect)(function(){h(!1),q(!0)},[d]),(0,P.useEffect)(function(){o(K)},[K]);var ln=!y;window.innerWidth,window.innerHeight;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:c.navigation+" topNavigation",children:[!s.homeStore.isdrawPicture&&(0,r.jsxs)("div",{className:c.header,children:[(0,r.jsx)("div",{className:"icon",onClick:function(){"view"===t&&"3D_FirstPerson"===d||"2D"===d?s.homeStore.setShowEnterPage({show:!0,source:"backBtn"}):(C("2D"),s.homeStore.setViewMode("2D"),s.homeStore.setZIndexOf3DViewer(-2),a&&a("2D",400))},children:(0,r.jsx)(Rr.A,{style:{fontSize:"20px"},iconClass:"icon-a-fangxiangzuo"})}),(0,r.jsx)("div",{className:"center",children:!e&&(0,r.jsx)(Yt.A,{options:["AI生成","户型匹配"],shape:"round",value:K,defaultValue:"AI生成",className:"segmented",onChange:function(n){Q(n)}})}),(0,r.jsx)("div",{className:"icon",onClick:function(){uc.log("点击关闭"),!X._layout_scheme_id&&X._room_entities.length>0?v({show:!0,source:"exitBtn"}):(D.nb.DispatchEvent(D.n0.SaveLayoutScheme,null),window.parent.postMessage({origin:"layoutai.api",type:"closeIframe",data:{canClose:!0}},"*"))},children:(0,r.jsx)(Rr.A,{style:{fontSize:"20px"},iconClass:"icon-close1"})})]}),S&&(0,r.jsx)("div",{className:"",style:{top:50,right:12,position:"fixed",zIndex:"999",background:"#fff",padding:"10px"},children:(0,r.jsx)(Da.A,{onClose:function(){_(!1)}})})]}),p&&(0,r.jsx)(Ol,{}),e&&(0,r.jsx)("div",{className:c.sideToolbarContainer+" sideToolbar "+("2D"!=I?"is_3d_mode":""),children:(0,r.jsx)(Ya,{setSceneMode:C})}),"2D"===I&&e&&(0,r.jsxs)("div",{id:"pad_left_panel",className:c.leftPanelRoot+" leftPanelRoot "+(ln?"":"panel_hide"),children:[ln&&(0,r.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return w(!0)}}),ln&&H.length>1&&(0,r.jsx)("div",{className:c.tabBar,children:(0,r.jsx)("div",{className:"tabItem",children:H.map(function(n,e){return(0,r.jsxs)("div",{className:"item"+(z===n.value?" active":""),onClick:function(){on(n.value)},children:[(0,r.jsx)("div",{className:"label",children:n.label}),(0,r.jsx)("div",{className:"line"})]},e)})})}),(0,r.jsxs)("div",{className:c.popupContainer+" side_pannel",children:[(0,r.jsxs)("div",{className:c.listContainer,style:{display:"Layout"===z?"block":"none"},children:[(0,r.jsx)("div",{className:"dropdown",children:(0,r.jsx)(sc,{})}),(0,r.jsx)(aa,{width:400,showSchemeName:!1,isLightMobile:!0})]}),(0,r.jsxs)("div",{className:c.listContainer,style:{display:"material"===z?"block":"none"},children:[" ",(0,r.jsx)(rt,{})," "]})]})]}),H.length>0&&e&&"2D"==I&&(0,r.jsx)("div",{className:c.collapseBtn+(ln?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){w(!y)}}),(0,r.jsx)(Dl.A,{in:"2D"!=d&&!p&&E&&e,timeout:300,classNames:{enter:"fadeEnter",enterActive:"fadeEnterActive",exit:"fadeExit",exitActive:"fadeExitActive"},unmountOnExit:!0,children:(0,r.jsx)("div",{id:"pad_left_panel",className:"".concat(c.leftPanelRoot," ").concat("2D"!=d?c.materialReplace:""),children:(0,r.jsx)(ec,{selectedFigureElement:E})})}),(0,r.jsx)(dl,{}),e&&(0,r.jsx)("div",{className:c.bottomButtons+" bottomBtns"+(ln?" showLeftPanel":""),children:L&&L.length>0&&L.map(function(n,e){return(0,r.jsx)("div",{className:"btn"+("2D"!==I?" blackColor":""),onClick:null==n?void 0:n.onClick,children:null==n?void 0:n.label},"bottomBtn_"+e)})}),e&&(0,r.jsxs)("div",{className:c.layoutPlusButton,onClick:function(){uc.log("跳转AI布局plus")},children:[(0,r.jsx)(Rr.A,{iconClass:"icongallery1",style:{fontSize:"16px",color:"#5C42FB"}}),(0,r.jsx)("div",{className:"text",children:"跳转AI布局plus"})]}),(0,r.jsx)(Xa.A,{}),p&&(0,r.jsx)(bl,{})]})}),yc=t(61928);function wc(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function jc(){var n=wc(['\n      position:fixed;\n      left:0;\n      bottom:0;\n      height: calc(var(--vh, 1vh) * 100);\n      width:100%;\n      z-index: 999;\n      background: #fff;\n      .slide-enter {\n        transform: translateX(-100%);\n        opacity: 0;\n      }\n\n      .slide-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-exit-active {\n        transform: translateX(100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n\n\n      .slide-reverse-enter {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n\n      .slide-reverse-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-reverse-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-reverse-exit-active {\n        transform: translateX(-100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .upload_hx\n      {\n        position: absolute !important;\n        right: 30px !important;\n        bottom: 30px !important;\n        top: auto !important;\n        transform: none !important;\n        z-index: 1000;\n        display: flex;\n        width: 48px;\n        height: 90px;\n        padding: 24px 2px;\n        flex-direction: column;\n        align-items: center;\n        gap: 8px;\n        flex-shrink: 0;\n        .upload_title {\n          color: #333;\n          text-align: center;\n          font-family: "PingFang SC";\n          font-size: 10px;\n          font-style: normal;\n          font-weight: 400;\n          line-height: 10px; /* 100% */\n        }\n      }\n    ']);return jc=function(){return n},n}function Sc(){var n=wc(["\n      padding: 5px 10px 0 10px;\n      height: 100%;\n      /* .right_btns\n      {\n        position: fixed;\n        right: 25px;\n        top: 25px;\n      } */\n\n    "]);return Sc=function(){return n},n}function _c(){var n=wc(["\n      width: 100%;\n      height: 100%;\n    "]);return _c=function(){return n},n}function kc(){var n=wc(["\n      padding: 0px 40px;\n      height: calc(var(--vh, 1vh) * 100 - 170px);\n      margin-top: 16px;\n      overflow-y: scroll;\n      ::-webkit-scrollbar\n      {\n        display: none;\n      }\n      .demandLabel\n        {\n          font-weight: 600;\n          font-size: 16px;\n          color: #000;\n          margin-bottom: 8px;\n          margin-top: 20px;\n        }\n      .demandItem\n      {\n       \n        .tabRoot\n        {\n          display: flex;\n          flex-wrap: wrap;\n        }\n      }\n      .demandtab\n      {\n        display: flex;\n        width: 100px;\n        height: 32px;\n        padding: 4px 16px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n        background: #F2F3F4;\n        margin-right: 12px;\n        margin-bottom: 12px;\n      }\n      .selected\n      {\n        border-radius: 6px;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);\n        color: #fff;\n      }\n    "]);return kc=function(){return n},n}function Ic(){var n=wc(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    "]);return Ic=function(){return n},n}function Cc(){var n=wc(["\n      width: 100%;\n      height: 100%;\n    "]);return Cc=function(){return n},n}function Ac(){var n=wc(['\n      display: flex;\n      padding: 6px 20px;\n      height: 40px;\n      align-items: center;\n      gap: 10px;\n      align-self: stretch;\n\n      .title {\n        display: flex;\n        align-items: center;\n        gap: 16px;\n        flex: 1 0 0;\n        .title_text {\n          color: #282828;\n          font-family: "PingFang SC";\n          font-size: 16px;\n          font-style: normal;\n          font-weight: 600;\n          line-height: normal;\n        }\n      }\n\n      .layoutPlusButton{\n        display: flex;\n        padding: 4px 12px;\n        align-items: center;\n        gap: 4px;\n        border-radius: 50px;\n        background: linear-gradient(90deg, #EDE5FF 0%, #EAF0FF 100%);\n        border: none;\n        height: 30px;\n        \n        .text {\n          color: #5C42FB !important;\n          text-align: center;\n          font-family: "PingFang SC";\n          font-size: 13px;\n          font-style: normal;\n          font-weight: 400;\n          line-height: 22px;\n        }\n      }\n      .mySchemeButton{\n        display: flex;\n        padding: 4px 12px;\n        justify-content: center;\n        align-items: center;\n        gap: 4px;\n        border-radius: 20px;\n        background: #E9EBEB;\n        border: none;\n        height: 30px;\n        .text {\n          color: #282828 !important;\n          text-align: center;\n          font-family: "PingFang SC";\n          font-size: 13px;\n          font-style: normal;\n          font-weight: 400;\n          line-height: 22px;\n        }\n      }\n    ']);return Ac=function(){return n},n}function zc(){var n=wc(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: 88px;\n      background: #fff;\n      display: flex;\n      align-items: center;\n      padding: 20px 60px;\n      justify-content: space-between;\n      .ant-btn\n      {\n        width: 160px;\n        height: 48px;\n        border-radius: 24px;\n      }\n      .rotate\n      {\n        font-size: 16px;\n        color: #5B5E60;\n      }\n    "]);return zc=function(){return n},n}function Nc(){var n=wc(["\n      display: flex;\n      flex-wrap: wrap;\n      box-sizing: border-box;\n      margin-top: 20px;\n    "]);return Nc=function(){return n},n}function Pc(){var n=wc(["\n      width: calc(20% - 10px);\n      height: auto;\n      padding: 2px;\n      box-sizing: border-box;\n      position: relative;\n      margin-right: 10px;\n      @media (max-width: 800px) {\n        width: calc(33.33% - 10px);\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 5/3;\n      }\n    "]);return Pc=function(){return n},n}function Oc(){var n=wc(["\n      padding: 0 5px;\n      "]);return Oc=function(){return n},n}function Dc(){var n=wc(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n      margin-top: 5px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 10px;\n      .ant-rate\n      {\n        color: #FFAA00;\n        font-size: 16px !important;\n        .ant-rate-star:not(:last-child)\n        {\n          margin-inline-end: 3px;\n        }\n      }\n    "]);return Dc=function(){return n},n}function Ec(){var n=wc(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      display: flex;\n      margin-top: 5px;\n    "]);return Ec=function(){return n},n}var Mc=(0,a.rU)(function(n){n.token;var e=n.css;return{enterPage:e(jc()),selectHx:e(Sc()),hxRoot:e(_c()),selectDemand:e(kc()),styleTitle:e(Ic()),demandRoot:e(Cc()),hxHeader:e(Ac()),bottom:e(zc()),container_listInfo:e(Nc()),container_list:e(Pc()),textInfo:e(Oc()),container_title:e(Dc()),container_desc:e(Ec())}});function Fc(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Bc(){var n=Fc(["\n        width: 100%;\n        height: calc(100vh - 52px);\n        display: flex;\n        flex-direction: column;\n        position: relative;\n        overflow: hidden;\n    "]);return Bc=function(){return n},n}function Tc(){var n=Fc(["\n        font-size: 24px;\n        font-weight: 600;\n        color: #000000;\n        margin: 0;\n    "]);return Tc=function(){return n},n}function Lc(){var n=Fc(["\n        width: 100%;\n        height: 100%;\n        position: relative;\n        iframe {\n            width: 100%;\n            height: 100%;\n            border: none;\n        }\n    "]);return Lc=function(){return n},n}function Rc(){var n=Fc(["\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.50);\n        z-index: 1000;\n    "]);return Rc=function(){return n},n}function Uc(){var n=Fc(["\n        /* position: fixed;\n        bottom: 0;\n        left: 0;\n        right: 0; */\n        width: 100%;\n        flex: 1;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        gap: 200px;\n        padding: 10px 0;\n        background-color: #ffffff;\n        /* border-top: 1px solid #e0e0e0; */\n        /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */\n    "]);return Uc=function(){return n},n}function Hc(){var n=Fc(["\n        width: 150px;\n        height: 40px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        font-size: 16px;\n        text-align: center;\n        color: #282828;\n        background-color: #f4f4f4;\n        border-radius: 10px;\n        cursor: pointer;\n        transition: background-color 0.3s;\n        border: 1px solid #d0d0d0;\n        font-weight: 500;\n        \n        &:hover {\n            background-color: #e8e8e8;\n        }\n    "]);return Hc=function(){return n},n}var Gc=(0,a.rU)(function(n){var e=n.css;return{container:e(Bc()),title:e(Tc()),iframe:e(Lc()),dialog:e(Rc()),bottom:e(Uc()),bottomButton:e(Hc())}}),Wc=t(56070),Yc=t(65640);function Xc(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Vc(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Zc(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function qc(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function Jc(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Xc(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Xc(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kc(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Qc=function(){var n=(0,i.B)().t,e=Gc().styles,t=Jc((0,P.useState)(""),2),a=t[0],l=t[1],c=(0,P.useRef)(null),s=(0,M.P)(),u=function(){return(e=function(){var e,t,r,i,a,c,u,d;return Kc(this,function(f){switch(f.label){case 0:e="20345075692",t="b8e9032f984a45dba5fe85b9168543f4",f.label=1;case 1:return f.trys.push([1,6,,7]),Yc.log("sendRequest"),[4,(0,Wc.pY)({url:"https://graph.3vjia.com/oauth/token",method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:new URLSearchParams({grant_type:"client_credentials",client_id:e,client_secret:t}).toString()})];case 2:return r=f.sent(),(i=r.data.access_token)?[4,(0,Wc.pY)({url:"https://open-gateway.3vjia.com/common/api/joinAuth/authorize",method:"POST",headers:{"Access-Token":i,"Content-Type":"application/json"},data:{svjUserId:"",enterpriseUserId:"xxx",tokenExpiresIn:3600,bizExtParams:'{"enterpriseUserName":"张三","mobile":"18666628697","limitCount":"5"}'}})]:[3,4];case 3:return a=f.sent(),Yc.log("新请求响应：",a.data),c=a.data.data.code,u="".concat(o.xH,"/hx-select-ui/?appId=").concat(e,"&code=").concat(c),l(u),s.trialStore.setHouseData({}),[3,5];case 4:z.A.error(n("无法获取 access_token")),f.label=5;case 5:return[3,7];case 6:return d=f.sent(),Yc.error(n("请求错误")+":",d),z.A.error(n("请求错误")),[3,7];case 7:return[2]}})},function(){var n=this,t=arguments;return new Promise(function(r,i){var o=e.apply(n,t);function a(n){Vc(o,r,i,a,l,"next",n)}function l(n){Vc(o,r,i,a,l,"throw",n)}a(void 0)})})();var e};return(0,P.useEffect)(function(){u();var n=0;window.addEventListener("message",function(e){"layoutSelected"===e.data.type&&(s.trialStore.setHouseData(qc(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){Zc(n,e,t[e])})}return n}({},e.data.data),{num:n++})),e.data.data.id&&s.homeStore.setIsShowHouseDetail({show:!0,source:"selectHouse"}))})},[]),(0,r.jsx)("div",{className:e.container,ref:c,children:(0,r.jsx)("div",{className:e.iframe,children:(0,r.jsx)("iframe",{src:a,title:n("户型选择")})})})},$c=t(65640);function ns(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function es(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function ts(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ns(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ns(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rs(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var is=(0,C.observer)(function(){(0,P.useRef)(null);var n=(0,i.B)().t,e=Mc().styles,t=(0,M.P)(),o=ts((0,P.useState)(0),2),a=o[0];o[1];(0,P.useEffect)(function(){t.homeStore.setViewMode("2D")},[]);var l=function(){window.parent.postMessage({origin:"layoutai.api",type:"closeIframe",data:{canClose:!0}},"*")};return(0,r.jsxs)("div",{className:e.enterPage,children:[(0,r.jsxs)("div",{className:e.hxHeader,children:[(0,r.jsx)("div",{className:"title",children:(0,r.jsx)("div",{className:"title_text",children:"搜户型"})}),(0,r.jsxs)("div",{className:"layoutPlusButton",onClick:function(){$c.log("跳转AI布局plus")},children:[(0,r.jsx)(fe.In,{iconClass:"icongallery1",style:{fontSize:"16px",color:"#5C42FB"}}),(0,r.jsx)("div",{className:"text",children:"跳转AI布局plus"})]}),(0,r.jsxs)("div",{className:"mySchemeButton",onClick:function(){t.homeStore.setShowMySchemeList(!0)},children:[(0,r.jsx)(fe.In,{iconClass:"icongallery1",style:{fontSize:"16px",color:"#282828"}}),(0,r.jsx)("div",{className:"text",children:"我的方案"})]}),(0,r.jsx)("div",{className:"closePage",onClick:l,children:(0,r.jsx)(fe.In,{iconClass:"icon-close1",style:{fontSize:"20px",color:"#282828"}})})]}),(0,r.jsxs)("div",{className:"upload_hx",onClick:function(){return(e=function(){var e;return rs(this,function(r){switch(r.label){case 0:return[4,(0,$a.L7)("image/*").catch(function(n){return null})];case 1:return(e=r.sent()).content?(t.homeStore.setImgBase64(e.content),z.A.success(n("上传户型图成功"))):z.A.warning(n("上传户型图失败")),[2]}})},function(){var n=this,t=arguments;return new Promise(function(r,i){var o=e.apply(n,t);function a(n){es(o,r,i,a,l,"next",n)}function l(n){es(o,r,i,a,l,"throw",n)}a(void 0)})})();var e},children:[(0,r.jsx)("img",{style:{width:"24px",height:"24px"},src:"https://3vj-fe.3vjia.com/layoutai/icons/upload.svg",alt:""}),(0,r.jsx)("div",{className:"upload_title",children:"上传户型"})]}),(0,r.jsx)(Dl.A,{in:0===a,timeout:300,classNames:1===a?"slide-reverse":"slide",mountOnEnter:!0,appear:!0,style:{display:0===a?"block":"none"},children:(0,r.jsx)("div",{className:e.selectHx,children:(0,r.jsx)("div",{className:e.hxRoot,children:(0,r.jsx)(Qc,{})})})})]})});function os(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function as(){var n=os(["\n      background-color: #fff;\n      border-radius: 8px !important;\n      position: relative;\n      .swj-baseComponent-Containersbox-title\n      {\n        background-color: #fff !important;\n      }\n    "]);return as=function(){return n},n}function ls(){var n=os(["\n      .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell\n      {\n        background-color: #fff !important;\n      }\n    "]);return ls=function(){return n},n}function cs(){var n=os(['\n      color: #282828;\n      font-family: "PingFang SC";\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n    ']);return cs=function(){return n},n}function ss(){var n=os(["\n      display: flex;\n      flex-direction: row;\n      gap: 10px;\n      margin: 16px 0px;\n      color: #282828;\n      .tabItem\n      {\n        display: flex;\n        width: 64px;\n        height: 28px;\n        padding: 0px 0px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n      }\n      .selected\n      {\n        background: #F2F3F4;\n        font-weight: 600;\n      }\n    "]);return ss=function(){return n},n}function us(){var n=os(["\n      .ant-table-thead .ant-table-selection-column .ant-checkbox-wrapper\n      {\n         display: none;\n      }\n      .ant-table-thead .ant-table-cell\n      {\n        background: #F2F3F4;\n      }\n      .ant-table-container\n      {\n        border: 2px solid #F2F3F4;\n      }\n    "]);return us=function(){return n},n}function ds(){var n=os(["\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      position: absolute;\n      bottom: 0;\n      width: 100%;\n      padding: 16px;\n      background-color: #fff;\n      width: 95%;\n      font-size: 16px;\n    "]);return ds=function(){return n},n}function fs(){var n=os(["\n      font-size: 16px;\n      font-weight: 600;\n    "]);return fs=function(){return n},n}var ps=(0,a.rU)(function(n){var e=n.css;return{panelContainer:e(as()),content:e(ls()),title:e(cs()),tab:e(ss()),table:e(us()),bottom:e(ds()),bottomRight:e(fs())}}),hs=t(29686);function ms(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function xs(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function gs(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){xs(n,e,t[e])})}return n}function vs(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function bs(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ms(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ms(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ys=(0,C.observer)(function(){var n,e,t,o,a=(0,M.P)(),l=((0,i.B)().t,ps().styles),c=bs((0,P.useState)("checkbox"),2),s=c[0],u=(c[1],bs((0,P.useState)([]),2)),d=u[0],f=u[1],p=bs((0,P.useState)([]),2),h=p[0],m=p[1],x=bs((0,P.useState)("延米"),2),g=x[0],v=x[1],b=bs((0,P.useState)(0),2),y=b[0],w=b[1],j=bs((0,P.useState)([{name:"定制柜",selected:!1},{name:"橱柜",selected:!1},{name:"卫阳",selected:!1}]),2),S=j[0],_=j[1],k=[{title:"类型",dataIndex:"name"},{title:"尺寸",dataIndex:"size"},{title:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},onClick:function(){v("延米"===g?"投影面积":"延米")},children:[(0,r.jsx)(ce.If,{condition:null===(n=S.find(function(n){return"定制柜"===n.name}))||void 0===n?void 0:n.selected,children:(0,r.jsx)(ce.al,{children:"投影面积"})}),(0,r.jsx)(ce.If,{condition:null===(e=S.find(function(n){return"橱柜"===n.name}))||void 0===e?void 0:e.selected,children:(0,r.jsx)(ce.al,{children:"延米"})}),(0,r.jsx)(ce.If,{condition:null===(t=S.find(function(n){return"卫阳"===n.name}))||void 0===t?void 0:t.selected,children:(0,r.jsxs)(ce.al,{children:[(0,r.jsx)(vt.A,{type:"icon-change_logo"}),g]})})]}),dataIndex:"meter",render:function(n,e){return(0,r.jsx)("span",{children:"延米"===g?e.meter:e.area})}},{title:"空间",dataIndex:"space",hidden:null===(o=S.find(function(n){return"橱柜"===n.name}))||void 0===o?void 0:o.selected}],I={onChange:function(n,e){f(n)},getCheckboxProps:function(n){return{disabled:"Disabled User"===n.name,name:n.name}}};return(0,P.useEffect)(function(){var n,e=S.find(function(n){return n.selected}),t=D.nb.instance.layout_container;if(m([]),0!=(null==t||null===(n=t._room_entities)||void 0===n?void 0:n.length)){if("橱柜"===(null==e?void 0:e.name)){var r;v("延米");var i=t._room_entities.find(function(n){return"厨房"===n.name})._room;if(i&&(null===(r=i._furniture_list)||void 0===r?void 0:r.length)>0){var o=[];i._furniture_list.forEach(function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;o.push({key:n.uuid,name:n.category,size:"".concat(e._w,"*").concat(e._h,"*").concat(e.rect_center_3d.z||n.height),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:i.name,area:"".concat((e._w*(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}}),m(o)}}if("卫阳"===(null==e?void 0:e.name)){var a=t._room_entities.filter(function(n){if(n.name.includes("卫生间")||n.name.includes("阳台"))return n});if(a&&a.length>0){var l=[];a.forEach(function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach(function(n){if(n.category.endsWith("柜")){var e=n.matched_rect?n.matched_rect:n.rect;l.push({key:n.uuid,name:n.category,size:"".concat(Math.round(e._w),"*").concat(Math.round(e._h),"*").concat(Math.round(e.rect_center_3d.z||n.height)),meter:"".concat((e._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((e._w*Math.round(e.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})}})}),m(l)}}if("定制柜"===(null==e?void 0:e.name)){v("投影面积");var c=t._room_entities.filter(function(n){if(!n.name.includes("卫生间")&&!n.name.includes("阳台")&&!n.name.includes("厨房"))return n});if(c&&c.length>0){var s=[];c.forEach(function(n){var e;(null===(e=n._room._furniture_list)||void 0===e?void 0:e.length)>0&&n._room._furniture_list.forEach(function(n){n.category.endsWith("柜")&&s.push({key:n.uuid,name:n.category,size:"".concat(Math.round(n.rect._w),"*").concat(Math.round(n.rect._h),"*").concat(Math.round(n.rect.rect_center_3d.z||n.height)),meter:"".concat((n.rect._w/1e3).toFixed(2),"m"),space:n._room.name,area:"".concat((n.rect._w*Math.round(n.rect.rect_center_3d.z||n.height)/1e6).toFixed(2),"m²")})})}),m(s)}}f([]),w(y+1)}},[S]),(0,P.useEffect)(function(){a.homeStore.showCabinetCompute&&_(S.map(function(n,e){return vs(gs({},n),{selected:0===e})}))},[a.homeStore.showCabinetCompute]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(ce.If,{condition:a.homeStore.showCabinetCompute,children:(0,r.jsx)(fe._w,{center:!0,height:446,width:600,showHeader:!0,showCloseInContainerbox:!1,title:"基本算量",className:l.panelContainer,onClose:function(){a.homeStore.setShowCabinetCompute(!1)},draggable:!0,children:(0,r.jsxs)("div",{style:{padding:"0 20px 20px 20px"},className:l.content,children:[(0,r.jsx)("div",{className:l.tab,children:S.map(function(n,e){return(0,r.jsx)("div",{onClick:function(){_(S.map(function(n,t){return vs(gs({},n),{selected:t===e})}))},className:"tabItem"+(n.selected?" selected":""),children:n.name},e)})}),(0,r.jsx)(hs.A,{rowSelection:gs({type:s,columnTitle:"",selectedRowKeys:d},I),columns:k,dataSource:h,pagination:!1,scroll:{y:240},className:l.table},y),(0,r.jsxs)("div",{className:l.bottom,children:[(0,r.jsx)("div",{children:(0,r.jsx)(ka.A,{checked:d.length===h.length,indeterminate:d.length>0&&d.length<h.length,onChange:function(n){n.target.checked?f(h.map(function(n){return n.key})):f([])},children:"全选"})}),(0,r.jsx)("div",{className:l.bottomRight,children:(0,r.jsxs)(ce.If,{condition:"延米"===g,children:[(0,r.jsxs)(ce.al,{children:["共 ",h.filter(function(n){return d.includes(n.key)}).reduce(function(n,e){return n+parseFloat(e.meter)},0).toFixed(2),"m"]}),(0,r.jsxs)(ce._I,{children:["共 ",h.filter(function(n){return d.includes(n.key)}).reduce(function(n,e){return n+parseFloat(e.area)},0).toFixed(2),"m²"]})]})})]})]})})})})});function ws(){var n,e,t=(n=["\n      z-index: 99;\n      display: flex;\n      justify-content: center;\n      backdrop-filter: blur(50px);\n      width: 100%;\n      max-width: 100%;\n      overflow: hidden;\n      \n      .selectInfo {\n        display: flex;\n        gap: 8px;\n        height: 64px;\n        overflow-x: auto;\n        overflow-y: hidden;\n        scroll-behavior: smooth;\n        padding: 0 4px;\n        width: 100%;\n        max-width: 100%;\n        \n        /* 隐藏滚动条 */\n        ::-webkit-scrollbar {\n          display: none;\n        }\n        \n        /* Firefox 隐藏滚动条 */\n        scrollbar-width: none;\n        -ms-overflow-style: none;\n        \n        /* 确保内容可以横向滚动 */\n        flex-wrap: nowrap;\n        align-items: center;\n        \n        .shijiaoItem {\n          width: 80px;\n          height: 60px;\n          position: relative;\n          border: 2px solid #FFFFFF;\n          border-radius: 4px;\n          flex-shrink: 0; /* 防止项目被压缩 */\n          cursor: pointer;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          overflow: hidden;\n          \n          img {\n            width: 80px;\n            height: 60px;\n            border-radius: 4px;\n            object-fit: cover;\n            display: block;\n          }\n          \n          .title {\n            position: absolute;\n            bottom: 0px;\n            left: 0;\n            width: 100%;\n            height: 20px;\n            border-radius: 0 0 4px 4px;\n            background: rgba(0, 0, 0, 0.40);\n            backdrop-filter: blur(2px);\n            padding: 0 11px;\n            color: #fff;\n            font-size: 12px;\n            font-weight: 400;\n            font-family: PingFang SC;\n            font-style: normal;\n            line-height: 166.7%;\n            text-align: center;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n        }\n        \n        .shijiaoItem:hover {\n          background-color: #ffffff1a;\n          transform: scale(1.02);\n          transition: all 0.3s ease;\n        }\n        \n        .shijiaoItem:active {\n          transform: scale(0.98);\n        }\n      }\n    "],e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}})));return ws=function(){return t},t}var js=(0,a.rU)(function(n){return{shijiaoBarContainer:(0,n.css)(ws())}}),Ss=t(65640);function _s(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ks(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return _s(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return _s(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Is=(0,C.observer)(function(){var n=js().styles,e=(0,M.P)(),t=ks((0,P.useState)(0),2),i=t[0],o=t[1],a=(0,P.useRef)(null),l=(0,P.useRef)(null),c=(0,P.useRef)([]),s=ks((0,P.useState)(!1),2),u=s[0],d=s[1],f=ks((0,P.useState)(0),2),p=f[0],h=f[1],m=ks((0,P.useState)(0),2),x=m[0],g=m[1],v=function(n){if(l.current&&c.current[n]){var e=l.current,t=c.current[n],r=e.getBoundingClientRect(),i=t.getBoundingClientRect(),o=i.left-r.left+e.scrollLeft,a=(r.width-i.width)/2;e.scrollTo({left:o-a,behavior:"smooth"})}};(0,P.useEffect)(function(){v(i)},[i]),(0,P.useEffect)(function(){Ss.log("store.homeStore.currentViewCameras",e.homeStore.currentViewCameras)},[e.homeStore.currentViewCameras]);return(0,r.jsx)("div",{className:n.shijiaoBarContainer,ref:a,children:(0,r.jsx)("div",{className:"selectInfo",ref:l,onWheel:function(n){n.preventDefault();n.currentTarget.scrollLeft+=30*n.deltaY},onTouchStart:function(n){var e;d(!0),h(n.touches[0].clientX),g((null===(e=l.current)||void 0===e?void 0:e.scrollLeft)||0)},onTouchMove:function(n){if(u&&l.current){n.preventDefault();var e=l.current,t=n.touches[0].clientX,r=p-t;e.scrollLeft=x+r}},onTouchEnd:function(){d(!1)},children:e.homeStore.currentViewCameras.map(function(n,t){return(0,r.jsxs)("div",{className:"shijiaoItem",ref:function(n){return c.current[t]=n},style:{border:t===i?"2px solid #147FFA":"2px solid #FFFFFF"},onClick:function(){!function(n){o(n);var t=D.nb.instance.scene3D;t.active_controls.bindViewEntity(e.homeStore.currentViewCameras[n]),t.update(),v(n)}(t)},children:[(0,r.jsx)("img",{src:n._perspective_img.src,alt:""}),(0,r.jsxs)("div",{className:"title",children:["视角",t+1]})]},t)})})})});function Cs(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function As(){var n=Cs(["\n            width: 100%;\n            height: 100%;\n            background-color: #fff;\n            border-radius: 12px;\n            padding: 12px;\n            box-sizing: border-box;\n            display: flex;\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 12px;\n            align-self: stretch;\n            flex: 1 0 0;\n        "]);return As=function(){return n},n}function zs(){var n=Cs(['\n            display: flex;\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 12px;\n            .choose_tag_row {\n                display: grid;\n                grid-template-columns: 28px 1fr;\n                gap: 24px;\n                .taglabel {\n                    width: 28px;\n                    height: 24px;\n                    color: #2E3238;\n                    font-family: "PingFang SC";\n                    font-size: 14px;\n                    font-style: normal;\n                    font-weight: 600;\n                    line-height: 24px; /* 171.429% */\n                }\n                .tagbtns {\n                    display: flex;\n                    align-items: center;\n                    gap: 4px 16px;\n                    flex-wrap: wrap;\n                    height: 24px;\n                    .tag {\n                        display: flex;\n                        height: 24px;\n                        box-sizing: border-box;\n                        padding: 2px 4px;\n                        justify-content: center;\n                        align-items: center;\n                        border-radius: 4px;\n                        background: #F2F3F4;\n                        border-radius: 4px;\n                        background: #F2F3F4;\n\n                        color: #282828;\n                        text-align: center;\n                        font-family: "PingFang SC";\n                        font-size: 13px;\n                        font-style: normal;\n                        font-weight: 400;\n                        line-height: 22px; /* 169.231% */\n                        &.checked {\n                            border-radius: 4px;\n                            border: 1px solid #BA63F0;\n                            background: linear-gradient(90deg, #EDE5FF 0%, #EAF0FF 100%);\n                            color: #5C42FB;\n                        }\n                    }\n                }\n            }\n        ']);return zs=function(){return n},n}function Ns(){var n=Cs(["\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            scrollbar-width: none; /* Firefox */\n            -ms-overflow-style: none; /* IE and Edge */\n            &::-webkit-scrollbar {\n                display: none; /* Chrome, Safari and Opera */\n            }\n        "]);return Ns=function(){return n},n}function Ps(){var n=Cs(["\n            position:relative;\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            scrollbar-width: none; /* Firefox */\n            -ms-overflow-style: none; /* IE and Edge */\n            &::-webkit-scrollbar {\n                display: none; /* Chrome, Safari and Opera */\n            }\n        "]);return Ps=function(){return n},n}function Os(){var n=Cs(["\n            position:absolute;\n            top:0;\n            left:0;\n            width:100%;\n            height:100%;\n            background: #fff;\n            overflow:hidden;\n            z-index: 9;\n\n            display: grid;\n            grid-template-columns: repeat(3, 1fr);\n            gap: 20px;\n            justify-items: start;\n            align-content: start;\n            .Skeleton_content {\n                width: 100%;\n                display: flex;\n                flex-direction: column;\n                align-items: flex-start;\n                position: relative;\n                \n                .ant-skeleton {\n                    width: 100% !important;\n                    height: auto !important;\n                    \n                    .ant-skeleton-button {\n                        width: 100% !important;\n                        height: 100% !important;\n                    }\n                    \n                    &:nth-child(1) {\n                        height: 134px !important;\n                    }\n                    &:nth-child(2) {\n                        height: 22px !important;\n                        margin-top: 4px;\n                    }\n                    &:nth-child(3) {\n                        height: 16px !important;\n                        margin-top: 4px;\n                    }\n                }\n            }\n        "]);return Os=function(){return n},n}function Ds(){var n=Cs(['\n            width: 100%;\n            height: 100%;\n            display: grid;\n            grid-template-columns: repeat(3, 1fr);\n            gap: 20px;\n            justify-items: start;\n            align-content: start;\n            .result_list_item {\n                display: flex;\n                flex-direction: column;\n                align-items: flex-start;\n                position: relative;\n                cursor: pointer;\n                min-width: 0;\n                width: 100%;\n                \n                .hximg {\n                    position: absolute;\n                    top: 4px;\n                    left: 4px;\n                    width: 80px;\n                    height: 60px;\n                    z-index: 2;\n                    .img_element {\n                        position: relative;\n                        width: 80px;\n                        height: 60px;\n                        border-radius: 4px;\n                        padding: 4px;\n                        box-sizing: border-box;\n                        .layout2d_img {\n                            position: absolute;\n                            top: 0;\n                            left: 0;\n                            width: 100%;\n                            height: 100%;\n                            object-fit: contain;\n                            border-radius: 2px;\n                        }\n                    }\n                }\n                .result_img {\n                    width: 100%;\n                    height: 133.875px;\n                    flex-shrink: 0;\n                    align-self: stretch;\n                    border-radius: 4px;\n                    object-fit: cover;\n                }\n                .result_info {\n                    width: 100%;\n                    padding: 8px 0;\n                    display: flex;\n                    flex-direction: column;\n                    align-items: flex-start;\n                    .result_title {\n                        color: #2E3238;\n                        font-family: "PingFang SC";\n                        font-size: 14px;\n                        font-weight: 500;\n                        line-height: 20px;\n                        margin-bottom: 4px;\n                        overflow: hidden;\n                        text-overflow: ellipsis;\n                        white-space: nowrap;\n                        width: 100%;\n                    }\n                    .result_tags {\n                        height: 22px;\n                        width: auto;\n                        padding: 0 4px;\n                        border-radius: 4px;\n                        border: 1px solid #EAEAEB;\n                        background: linear-gradient(90deg, #FAFAFA 0%, #EAEAEB 100%);\n                        display: flex;\n                        justify-content: flex-end;\n                        align-self: flex-end;\n                        margin-left: auto;\n                        &.goodFit {\n                            border: 1px solid #D9F7BE;\n                            background: linear-gradient(90deg, #F6FFED 0%, #D9F7BE 100%);\n                        }\n                        .bestFit, .defaultFit {\n                            padding: 0 4px;\n                            border-radius: 4px;\n                            font-family: "PingFang SC";\n                            font-size: 12px;\n                            font-style: normal;\n                            font-weight: 600;\n                            line-height: 20px;\n                            white-space: nowrap;\n                        }\n                        .bestFit {\n                            color: #52C41A;\n                        }\n                        .defaultFit {\n                            color: #5B5E60;\n                        }\n                    }\n                }\n            }\n        ']);return Ds=function(){return n},n}var Es=(0,a.rU)(function(n){var e=n.css;return{houseMatch:e(As()),tagListBox:e(zs()),result_container:e(Ns()),resultprogress:e(Ps()),houseMatch_skeleton:e(Os()),result_list:e(Ds())}}),Ms=t(39907),Fs=t(93783),Bs=t(65640);function Ts(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Ls(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Rs(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){Ls(o,r,i,a,l,"next",n)}function l(n){Ls(o,r,i,a,l,"throw",n)}a(void 0)})}}function Us(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||Gs(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hs(n){return function(n){if(Array.isArray(n))return Ts(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Gs(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gs(n,e){if(n){if("string"==typeof n)return Ts(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Ts(n,e):void 0}}function Ws(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Ys=function(){var n=function(){var n=Array.from({length:9}).map(function(n,e){return(0,r.jsxs)("div",{className:"Skeleton_content",children:[(0,r.jsx)(Ms.A.Button,{active:!0},"skeleton_button_1_".concat(e)),(0,r.jsx)(Ms.A.Button,{active:!0},"skeleton_button_2_".concat(e)),(0,r.jsx)(Ms.A.Button,{active:!0},"skeleton_button_3_".concat(e))]},"skeleton_".concat(e))});return(0,r.jsx)("div",{className:e.houseMatch_skeleton,children:n})},e=Es().styles,t=(0,i.B)().t,o=Us((0,P.useState)([{title:t("归属"),tags:[{name:t("门店"),checked:!1},{name:t("企业"),checked:!1},{name:t("平台"),checked:!0}]},{title:t("空间"),tags:[{name:"客餐厅",checked:!1},{name:"厨房",checked:!1},{name:"卧室",checked:!1}]}]),2),a=o[0],l=o[1],c=Us((0,P.useState)(!1),2),s=c[0],u=c[1],d=Us((0,P.useState)([]),2),f=d[0],p=d[1],h="平台",m=!1,x=function(){return Rs(function(){var n;return Ws(this,function(e){switch(e.label){case 0:return n=D.nb.instance,"平台"!==h?[3,2]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!0)];case 1:return e.sent(),[3,6];case 2:return"企业"!=h?[3,4]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer",!0,!1)];case 3:return e.sent(),[3,6];case 4:return"门店"!=h?[3,6]:[4,n.layout_graph_solver.queryDreamerRoomsFromServer(n.layout_container._rooms,"dreamer_store",!0,!1)];case 5:e.sent(),e.label=6;case 6:return m=!0,[2]}})})()},g=function(){return Rs(function(){var n,e,t,r,i,o,l;return Ws(this,function(c){switch(c.label){case 0:return D.nb.instance?(n=D.nb.instance,u(!0),m?[3,2]:[4,x()]):[3,4];case 1:c.sent(),c.label=2;case 2:return e=a[1].tags.find(function(n){return n.checked}),t=Hs(n.layout_container._rooms),e&&(t=t.filter(function(n){return(0,Qo.MP)([(n.name||n.roomname)+n._t_id],[e.name])}))[0]&&(n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=1),n.layer_CadRoomNameLayer&&(n.layer_CadRoomNameLayer._name_mode=0)),r=n.layout_graph_solver.queryDreamerRoomsWithLayout(t),i=[],o=function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:90;return Rs(function(){var t;return Ws(this,function(r){switch(r.label){case 0:return[4,Fs.H.instance.getSchemeInfo(n)];case 1:return(t=r.sent())&&(t.fit_score=e,i.push(t)),[2]}})})()},l=r.map(function(n){return o(n.scheme_id,n.score)}),[4,Promise.all(l)];case 3:c.sent(),i.sort(function(n,e){return(e.fit_score||0)-(n.fit_score||0)}),p(i),u(!1),c.label=4;case 4:return[2]}})})()};return(0,P.useEffect)(function(){!function(){var n=D.nb.instance.layout_container,e=!0,t=!1,r=void 0;try{for(var i,o=n._furniture_entities[Symbol.iterator]();!(e=(i=o.next()).done);e=!0)i.value.is_selected=!1}catch(n){t=!0,r=n}finally{try{e||null==o.return||o.return()}finally{if(t)throw r}}n.updateRoomsFromEntities()}(),function(){if(D.nb.instance){var n=D.nb.instance;if(n.layout_container){n.layout_container.updateWholeBox();var e=a[1];e.tags=[];var t=Hs(n.layout_container._rooms),r=["入户花园","阳台","厨房","卧室","卫生间","客餐厅"];t.sort(function(n,e){return r.indexOf(e.roomname)+e.area/1e3-(r.indexOf(n.roomname)+n.area/1e3)}),t.forEach(function(n,e){return n._t_id=e+1}),e.tags=t.map(function(n,e){return{name:(n.name||n.roomname)+n._t_id,checked:0==e}}),l(Hs(a))}}}(),g()},[]),(0,P.useEffect)(function(){Bs.log("dreamHouseResultList",f)},[f]),(0,r.jsxs)("div",{className:e.houseMatch,children:[(0,r.jsx)("div",{className:e.tagListBox,children:a.map(function(n,e){return(0,r.jsxs)("div",{className:"choose_tag_row",children:[(0,r.jsx)("div",{className:"taglabel",children:n.title}),(0,r.jsx)("div",{className:"tagbtns",children:n.tags.map(function(n,e){return(0,r.jsx)("div",{className:"tag"+(n.checked?" checked":""),onClick:function(){return function(n){return Rs(function(){var e,r,i,o,c,s,u,d,f,m,v,b,y,w;return Ws(this,function(j){switch(j.label){case 0:if(!1===n.enabled)return z.A.info(t("暂未开放")),[2];if(n.checked)return[2];if(p([]),n.checked=!n.checked,n.checked){r=!0,i=!1,o=void 0;try{for(c=a[Symbol.iterator]();!(r=(s=c.next()).done);r=!0)if((u=s.value).tags.includes(n)){d=!0,f=!1,m=void 0;try{for(v=u.tags[Symbol.iterator]();!(d=(b=v.next()).done);d=!0)(y=b.value).checked=y===n}catch(n){f=!0,m=n}finally{try{d||null==v.return||v.return()}finally{if(f)throw m}}}}catch(n){i=!0,o=n}finally{try{r||null==c.return||c.return()}finally{if(i)throw o}}}return w=(null===(e=a[0].tags.find(function(n){return n.checked}))||void 0===e?void 0:e.name)||"",w&&w!==h?(h=w,[4,x()]):[3,2];case 1:j.sent(),j.label=2;case 2:return l(Hs(a)),g(),[2]}})})()}(n)},children:n.name},"tag_ele_"+e)})})]},"tags_row_"+e)})}),(0,r.jsxs)("div",{className:e.result_container,id:"dreamhouse_result_container",children:[s&&(0,r.jsx)("div",{className:e.resultprogress,children:(0,r.jsx)(n,{})}),(0,r.jsx)("div",{className:e.result_list,children:f.map(function(n,e){return(0,r.jsxs)("div",{className:"result_list_item",onClick:function(){return function(n){var e={origin:"layoutai.api",data:{schemeId:n.schemeId}};window.parent.postMessage(e,"*"),Bs.log("postMessage",JSON.stringify(e))}(n)},children:[(0,r.jsx)("div",{className:"hximg",children:(0,r.jsx)("div",{className:"img_element",children:(0,r.jsx)("img",{src:n.layout2d,className:"layout2d_img"})})}),(0,r.jsx)("img",{src:n.imagePath+"?x-oss-process=image/resize,w_600",className:"result_img"}),(0,r.jsxs)("div",{className:"result_info",children:[(0,r.jsx)("div",{className:"result_title",title:n.schemeName,children:n.schemeName}),(0,r.jsx)("div",{className:"result_tags"+(n.fit_score>80?" goodFit":""),children:n.fit_score>80?(0,r.jsx)("div",{className:"bestFit",children:Math.min(Math.round(n.fit_score),95)+"%"+t("匹配")}):(0,r.jsx)("div",{className:"defaultFit",children:Math.min(Math.round(n.fit_score),80)+"%"+t("匹配")})})]})]},"dreamhouse_result_"+e)})})]})]})};function Xs(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Vs(){var n=Xs(["\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            z-index: 999;\n            background: #fff;\n            border-radius: 16px;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            flex-shrink: 0;\n            background: #fff;\n            width: 85vw;\n            height: 90vh;\n        "]);return Vs=function(){return n},n}function Zs(){var n=Xs(['\n            display: flex;\n            height: 52px;\n            padding: 6px 20px;\n            align-items: center;\n            gap: 10px;\n            flex-shrink: 0;\n            align-self: stretch;\n            width: 100%;\n            box-sizing: border-box;\n            .title {\n                display: flex;\n                align-items: center;\n                gap: 16px;\n                flex: 1 0 0;\n                .title_text {\n                    color: #282828;\n                    font-family: "PingFang SC";\n                    font-size: 16px;\n                    font-style: normal;\n                    font-weight: 600;\n                    line-height: normal;\n                }\n            }\n            .close {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 20px;\n                height: 20px;\n            }\n        ']);return Zs=function(){return n},n}function qs(){var n=Xs(["\n            display: flex;\n            padding: 0 20px 20px 20px;\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 12px;\n            flex: 1 0 0;\n            height: calc(100% - 52px);\n            align-self: stretch;\n        "]);return qs=function(){return n},n}function Js(){var n=Xs(['\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            align-self: stretch;\n            height: 30px;\n            .search_input {\n                width: 340px;\n                padding-left: 16px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                border-radius: 50px;\n                border: 1px solid rgba(0, 0, 0, 0.15);\n                background: #FFF;\n                .ant-input-outlined {\n                    border: none;\n                }\n                .ant-input-group-addon {\n                    z-index: 1;\n                    border-radius: 50px !important;\n                    .ant-btn {\n                        border: none !important;\n                        padding: 0 16px;\n                        gap: 8px;\n                        align-self: stretch;\n                        border-radius: 50px !important;\n                        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n                        border-top-left-radius: 50px !important;\n                        border-bottom-left-radius: 50px !important;\n                        border-top-right-radius: 50px !important;\n                        border-bottom-right-radius: 50px !important;\n                    }\n                }\n                \n                /* 更具体的选择器 */\n                .ant-input-group-wrapper .ant-input-group-addon .ant-btn {\n                    border-radius: 50px !important;\n                    border-top-left-radius: 50px !important;\n                    border-bottom-left-radius: 50px !important;\n                    border-top-right-radius: 50px !important;\n                    border-bottom-right-radius: 50px !important;\n                }\n                \n                /* 针对搜索按钮的特殊样式 */\n                .ant-input-search-button {\n                    border-radius: 50px !important;\n                    border-top-left-radius: 50px !important;\n                    border-bottom-left-radius: 50px !important;\n                    border-top-right-radius: 50px !important;\n                    border-bottom-right-radius: 50px !important;\n                }\n            }\n            .searchChoice {\n                display: flex;\n                align-items: flex-start;\n                gap: 12px;\n                .searchChoiceItem {\n                    display: flex;\n                    align-items: center;\n                    .ant-space {\n                        display: flex;\n                        align-items: center;\n                        gap: 2px;\n                        .ant-space-item {\n                            color: var(--map-colorText, #282828);\n                            font-family: "PingFang SC";\n                            font-size: 13px;\n                            font-style: normal;\n                            font-weight: 400;\n                            line-height: 22px;\n                        }\n                    }\n                }\n            }\n        ']);return Js=function(){return n},n}function Ks(){var n=Xs(["\n            display: grid;\n            grid-template-columns: repeat(5, 1fr);\n            gap: 16px 20px;\n            max-height: calc(100vh - 200px);\n            overflow-y: auto;\n            width: 100%;\n            height: calc(100% - 30px);\n            \n            /* 隐藏滚动条 */\n            scrollbar-width: none; /* Firefox */\n            -ms-overflow-style: none; /* IE and Edge */\n            \n            &::-webkit-scrollbar {\n                display: none; /* Chrome, Safari and Opera */\n            }\n            \n            .listItem {\n                display: flex;\n                flex-direction: column;\n                align-items: flex-start;\n                flex: 1 0 0;\n                border-radius: 8px;\n                border: 1px solid rgba(0, 0, 0, 0.06);\n                background: #FFF;\n                .listItemImg {\n                    display: flex;\n                    padding: 8px;\n                    justify-content: center;\n                    align-items: center;\n                    gap: 10px;\n                    align-self: stretch;\n                    background: #F4F5F5;\n                    img {\n                        width: 100%;\n                        height: 100%;\n                        object-fit: cover;\n                    }\n                }\n                .itemInfo {\n                    display: flex;\n                    padding: 8px 16px 16px 16px;\n                    flex-direction: column;\n                    align-items: flex-start;\n                    align-self: stretch;\n                }\n            }\n            \n            .no-more {\n                grid-column: 1 / -1;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                padding: 20px;\n                .no-more-text {\n                    color: #999;\n                    font-size: 13px;\n                }\n            }\n        "]);return Ks=function(){return n},n}var Qs=(0,a.rU)(function(n){var e=n.css;return{myCaseRoot:e(Vs()),header:e(Zs()),content:e(qs()),search:e(Js()),list:e(Ks())}}),$s=t(65640);function nu(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function eu(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function tu(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function ru(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function iu(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||au(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ou(n){return function(n){if(Array.isArray(n))return nu(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||au(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function au(n,e){if(n){if("string"==typeof n)return nu(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?nu(n,e):void 0}}function lu(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var cu=ut.A.Search,su=(0,C.observer)(function(n){var e=n.getHxId,t=Qs().styles,i=(0,P.useRef)(!0),o=(0,P.useRef)(null),a=(0,M.P)(),l=iu((0,P.useState)({orderBy:"update_date desc",pageIndex:1,pageSize:25}),2),c=l[0],s=l[1],u=iu((0,P.useState)([]),2),d=u[0],f=u[1],p=iu((0,P.useState)("修改时间"),2),h=p[0],m=p[1],x=iu((0,P.useState)("全部"),2),g=x[0],v=x[1],b=iu((0,P.useState)(""),2),y=b[0],w=b[1],j=iu((0,P.useState)(!1),2),S=j[0],_=j[1],k=iu((0,P.useState)(!0),2),I=k[0],C=k[1],A=[{key:"update_date desc",label:(0,r.jsx)("div",{className:"timeOrderItem",onClick:function(n){n.stopPropagation(),m("修改时间")},children:(0,r.jsx)("span",{className:"timeOrderItemText",children:"修改时间"})})},{key:"create_date desc",label:(0,r.jsx)("div",{className:"timeOrderItem",onClick:function(n){n.stopPropagation(),m("创建时间")},children:(0,r.jsx)("span",{className:"timeOrderItemText",children:"创建时间"})})}],z=[{key:"updateTimeRangeAll",label:(0,r.jsx)("div",{className:"timeRangeItem",onClick:function(n){n.stopPropagation(),v("全部")},children:(0,r.jsx)("span",{className:"timeRangeItemText",children:"全部"})})},{key:"updateTimeRange",label:(0,r.jsx)("div",{className:"timeRangeItem",onClick:function(n){n.stopPropagation(),v("近一个月")},children:(0,r.jsx)("span",{className:"timeRangeItemText",children:"近一个月"})})},{key:"updateTimeRange3",label:(0,r.jsx)("div",{className:"timeRangeItem",onClick:function(n){n.stopPropagation(),v("近三个月")},children:(0,r.jsx)("span",{className:"timeRangeItemText",children:"近三个月"})})},{key:"updateTimeRange6",label:(0,r.jsx)("div",{className:"timeRangeItem",onClick:function(n){n.stopPropagation(),v("近半年")},children:(0,r.jsx)("span",{className:"timeRangeItemText",children:"近半年"})})}],N=function(){var n,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(n=function(){var n,t;return lu(this,function(r){switch(r.label){case 0:return S?[2]:(_(!0),[4,(0,si.F2)(c).catch(function(n){return $s.log(n),e||f([]),_(!1),null})]);case 1:return(null==(n=r.sent())?void 0:n.success)?(t=n.result.result||[],f(e?function(n){return ou(n).concat(ou(t))}:t),C(t.length===c.pageSize)):(e||f([]),C(!1)),_(!1),[2]}})},function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){eu(o,r,i,a,l,"next",n)}function l(n){eu(o,r,i,a,l,"throw",n)}a(void 0)})})()},O=(0,P.useCallback)(function(n){var e=n.currentTarget,t=e.scrollTop;if(e.scrollHeight-t-e.clientHeight<100&&!S&&I){var r=ru(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){tu(n,e,t[e])})}return n}({},c),{pageIndex:c.pageIndex+1});s(r)}},[S,I,c]);return(0,P.useEffect)(function(){N()},[]),(0,P.useEffect)(function(){if(i.current)i.current=!1;else{var n=c.pageIndex>1;N(n)}},[c]),(0,P.useEffect)(function(){if(i.current)i.current=!1;else{var n={orderBy:"update_date desc",pageIndex:1,pageSize:25};delete n.updateTimeRange,delete n.updateTimeRange3,delete n.updateTimeRange6,delete n.keyword,"近一个月"===g?n.updateTimeRange=1:"近三个月"===g?n.updateTimeRange3=1:"近半年"===g&&(n.updateTimeRange6=1),y&&(n.keyword=y),"修改时间"===h?n.orderBy="update_date desc":"创建时间"===h&&(n.orderBy="create_date desc"),s(n),C(!0)}},[y,g,h]),(0,P.useEffect)(function(){$s.log(d)},[d]),(0,r.jsxs)("div",{className:t.myCaseRoot,onClick:function(n){n.stopPropagation()},children:[(0,r.jsxs)("div",{className:t.header,children:[(0,r.jsx)("div",{className:"title",children:(0,r.jsx)("span",{className:"title_text",children:"我的方案"})}),(0,r.jsx)("div",{className:"close",onClick:function(){a.homeStore.setShowMySchemeList(!1)},children:(0,r.jsx)(Rr.A,{iconClass:"icon-close1",style:{fontSize:"20px",color:"#282828"}})})]}),(0,r.jsxs)("div",{className:t.content,children:[(0,r.jsxs)("div",{className:t.search,children:[(0,r.jsx)(cu,{placeholder:"搜索全部方案",allowClear:!0,enterButton:"搜索",onSearch:function(n){w(n)},className:"search_input"}),(0,r.jsxs)("div",{className:"searchChoice",children:[(0,r.jsx)("div",{className:"searchChoiceItem",children:(0,r.jsx)(oc.A,{menu:{items:A},trigger:["click"],children:(0,r.jsx)("a",{onClick:function(n){return n.preventDefault()},children:(0,r.jsxs)(ac.A,{children:[h,(0,r.jsx)(Rr.A,{iconClass:"icon-line_down",style:{fontSize:"12px",color:"#595959"}})]})})})}),(0,r.jsx)("div",{className:"searchChoiceItem",children:(0,r.jsx)(oc.A,{menu:{items:z},trigger:["click"],children:(0,r.jsx)("a",{onClick:function(n){return n.preventDefault()},children:(0,r.jsxs)(ac.A,{children:[g,(0,r.jsx)(Rr.A,{iconClass:"icon-line_down",style:{fontSize:"12px",color:"#595959"}})]})})})})]})]}),(0,r.jsxs)("div",{className:t.list,onScroll:O,ref:o,children:[d.map(function(n,t){return(0,r.jsxs)("div",{className:"listItem",onClick:function(){$s.log(n.id),e(n.id),a.homeStore.setShowMySchemeList(!1)},children:[(0,r.jsx)("div",{className:"listItemImg",children:(0,r.jsx)("img",{src:n.coverImage,alt:""})}),(0,r.jsx)("div",{className:"itemInfo",children:(0,r.jsx)("div",{className:"itemame",children:n.layoutSchemeName})})]},"".concat(n.id,"-").concat(t))}),!I&&d.length>0&&(0,r.jsx)("div",{className:"no-more",children:(0,r.jsx)("div",{className:"no-more-text",children:"没有更多数据了"})})]})]})]})});function uu(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function du(){var n=uu(["\n        border-radius: 12px;\n        background: #FFF;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        z-index: 1001;\n        display: flex;\n        flex-direction: column;\n        gap: 0;\n        width: 85vw;\n        height: 90vh;\n        /* @media screen and (max-width: 1366px) {\n            width: 85vw;\n            height: 90vh;\n        } */\n        /* @media screen and (min-width: 1366px) {\n            width: 1088px;\n            height: 668px;\n        } */\n    "]);return du=function(){return n},n}function fu(){var n=uu(['\n        display: flex;\n        align-items: center;\n        padding: 6px 20px;\n        width: 100%;\n        height: 40px;\n        gap: 10px;\n        flex-shrink: 0;\n        align-self: stretch;\n        .title {\n            display: flex;\n            align-items: center;\n            gap: 16px;\n            flex: 1 0 0;\n            .title_text {\n                color: #282828;\n                font-family: "PingFang SC";\n                font-size: 16px;\n                font-style: normal;\n                font-weight: 600;\n                line-height: normal;\n            }\n        }\n        .close {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 20px;\n            height: 20px;\n            cursor: pointer;\n        }\n    ']);return fu=function(){return n},n}function pu(){var n=uu(['\n        width: 100%;\n        height: calc(100% - 52px);\n        padding: 20px;\n        display: flex;\n        align-items:flex-start;\n        gap: 28px;\n        flex: 1 0 0;\n        align-self: stretch;\n        .content_left {\n            width: 100%;\n            height: 100%;\n            img {\n                width: 100%;\n                height: auto;\n                aspect-ratio: 1;\n                object-fit: cover;\n            }\n        }\n        .content_right {\n            width: 500px;\n            display: flex;\n            flex-direction: column;\n            gap: 24px;\n            align-items: flex-start;\n            .title {\n                color: #282828;\n                font-family: "PingFang SC";\n                font-size: 20px;\n                font-style: normal;\n                font-weight: 600;\n            }\n            .info {\n                display: flex;\n                flex-direction: column;\n                align-items: flex-start;\n                gap: 8px;\n                .info_item {\n                    display: flex;\n                    align-items: center;\n                    gap: 10px;\n                    .info_item_label {\n                        color: #5B5E60;\n                        font-family: "PingFang SC";\n                        font-size: 14px;\n                        font-style: normal;\n                        font-weight: 400;\n                        line-height: 24px; /* 171.429% */\n                    }\n                    .info_item_value {\n                        color: #282828;\n                        font-family: "PingFang SC";\n                        font-size: 14px;\n                        font-style: normal;\n                        font-weight: 400;\n                        line-height: 24px; /* 171.429% */\n                    }\n                }\n            }\n            .btnBox {\n                display: flex;\n                align-items: center;\n                justify-content: flex-start;\n                gap: 12px;\n                .btn {\n                    display: flex;\n                    width: 120px;\n                    height: 36px;\n                    padding: 6px 16px;\n                    border: none;\n                    justify-content: center;\n                    align-items: center;\n                    gap: 8px;\n                    border-radius: 20px;\n                    background: linear-gradient(91deg, #BA63F0 -0.97%, #5C42FB 100%);\n                    color: #FFF;\n                    text-align: center;\n                    font-family: "PingFang SC";\n                    font-size: 14px;\n                    font-style: normal;\n                    font-weight: 600;\n                    line-height: 24px; /* 171.429% */\n                }\n            }\n        }\n    ']);return pu=function(){return n},n}var hu=(0,a.rU)(function(n){var e=n.css;return{container:e(du()),header:e(fu()),content:e(pu())}}),mu=t(65640);function xu(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function gu(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function vu(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function bu(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}),n}function yu(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return xu(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return xu(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wu(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var ju=function(n){var e=n.closeHouseDetail,t=n.toLayout,o=n.hxId,a=hu().styles,l=(0,i.B)().t,c=(0,M.P)(),s=c.trialStore.houseData,u=yu((0,P.useState)({}),2),d=u[0],f=u[1],p=yu((0,P.useState)(!1),2),h=p[0],m=p[1],x=function(n){return(e=function(){var e;return wu(this,function(t){switch(t.label){case 0:return[4,(0,si.rP)({id:n}).catch(function(n){return mu.log(n),f({}),null})];case 1:return(null==(e=t.sent())?void 0:e.success)&&f(e.result),[2]}})},function(){var n=this,t=arguments;return new Promise(function(r,i){var o=e.apply(n,t);function a(n){gu(o,r,i,a,l,"next",n)}function l(n){gu(o,r,i,a,l,"throw",n)}a(void 0)})})();var e},g=function(n){var e="layout"===n,t=bu(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),r.forEach(function(e){vu(n,e,t[e])})}return n}({},d),{auto_layout:e});D.nb.DispatchEvent(D.n0.OpenMyLayoutSchemeData,t),D.nb.emit(F.U.OpenHouseSearching,!1),c.homeStore.setIsShowHouseDetail({show:!1,source:""}),c.homeStore.setShowEnterPage({show:!1,source:""})},v=function(n){h?(g(n),t(n)):(t(n),b(n),e())},b=function(n){c.trialStore.houseData.id||c.homeStore.img_base64?("layout"===n?c.homeStore.img_base64?D.nb.DispatchEvent(D.n0.LoadImitateImageFile,c.homeStore.img_base64):c.trialStore.houseData.id&&D.nb.DispatchEvent(D.n0.PostBuildingId,{id:c.trialStore.houseData.id,name:""}):"view"===n&&(c.homeStore.img_base64?D.nb.DispatchEvent(D.n0.LoadImitateImageFile,c.homeStore.img_base64):c.trialStore.houseData.id&&D.nb.DispatchEvent(D.n0.PostBuildingId,{id:c.trialStore.houseData.id,name:"",auto_layout:!1})),c.homeStore.setShowEnterPage({show:!1,source:""})):z.A.error(l("请先选择户型"))};return(0,P.useEffect)(function(){"myCase"===c.homeStore.isShowHouseDetail.source?m(!0):m(!1)},[]),(0,P.useEffect)(function(){o&&x(o)},[o]),(0,r.jsxs)("div",{className:a.container,onClick:function(n){return n.stopPropagation()},children:[(0,r.jsxs)("div",{className:a.header,children:[(0,r.jsx)("div",{className:"title",children:(0,r.jsx)("span",{className:"title_text",children:"户型详情"})}),(0,r.jsx)("div",{className:"close",onClick:function(n){n.stopPropagation(),e()},children:(0,r.jsx)(fe.In,{iconClass:"icon-close1",style:{fontSize:"20px",color:"#282828"}})})]}),(0,r.jsxs)("div",{className:a.content,children:[(0,r.jsx)("div",{className:"content_left",children:h?(0,r.jsx)("img",{src:d.coverImage,alt:"户型图"}):(0,r.jsx)("img",{src:s.imagePath,alt:"户型图"})}),(0,r.jsxs)("div",{className:"content_right",children:[(0,r.jsx)("div",{className:"title",children:s.buildingName}),!h&&(0,r.jsxs)("div",{className:"info",children:[(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"建筑面积"}),(0,r.jsx)("span",{className:"info_item_value",children:"".concat(s.area,"平方米")})]}),(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"户型信息"}),(0,r.jsx)("span",{className:"info_item_value",children:s.roomTypeName})]}),(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"户型朝向"}),(0,r.jsx)("span",{className:"info_item_value",children:s.area})]}),(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"所在地区"}),(0,r.jsx)("span",{className:"info_item_value",children:"".concat(s.provinceName).concat(s.cityName)})]}),(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"户型ID"}),(0,r.jsx)("span",{className:"info_item_value",children:s.id})]})]}),h&&(0,r.jsxs)("div",{className:"info",children:[(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"建筑面积"}),(0,r.jsx)("span",{className:"info_item_value",children:"".concat(d.area,"平方米")})]}),(0,r.jsxs)("div",{className:"info_item",children:[(0,r.jsx)("span",{className:"info_item_label",children:"户型ID"}),(0,r.jsx)("span",{className:"info_item_value",children:d.id})]})]}),(0,r.jsxs)("div",{className:"btnBox",children:[(0,r.jsx)(N.A,{className:"btn",onClick:function(){v("layout")},children:"去布局"}),(0,r.jsx)(N.A,{className:"btn",onClick:function(){v("view")},children:"看效果"})]})]})]})]})},Su=t(65640);function _u(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function ku(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Iu(n){return function(){var e=this,t=arguments;return new Promise(function(r,i){var o=n.apply(e,t);function a(n){ku(o,r,i,a,l,"next",n)}function l(n){ku(o,r,i,a,l,"throw",n)}a(void 0)})}}function Cu(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return _u(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return _u(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Au(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var zu=(0,C.observer)(function(){var n=(0,i.B)().t,e=I().styles,t=Cu((0,P.useState)(""),2),a=t[0],l=t[1],c=Cu((0,P.useState)(-2),2),s=c[0],u=c[1],d=(0,M.P)(),f=A.A.confirm,p="SaveSchemeProgress",h=Cu(z.A.useMessage(),2),m=h[0],x=h[1],g=Cu((0,P.useState)(!1),2),v=g[0],b=(g[1],Cu((0,P.useState)(!1),2)),y=b[0],w=b[1],j=Cu((0,P.useState)(null),2),S=j[0],_=j[1],k=Cu((0,P.useState)(""),2),C=k[0],O=k[1],T=Cu((0,P.useState)(0),2),L=T[0],R=(T[1],(0,P.useRef)()),U=Cu((0,P.useState)(!0),2),H=U[0],G=U[1],W=Cu((0,P.useState)(""),2),Y=W[0],X=W[1],V=Cu((0,P.useState)(""),2),Z=V[0],q=V[1];D.nb.UseApp(E.e.AppName),D.nb.instance&&(D.nb.t=n);var J=function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;if(D.nb.instance){var t=D.nb.instance.scene3D;t&&t.stopRender(),setTimeout(function(){var e=document.getElementById("cad_canvas"),r=document.getElementById("body_container");if(e){if(D.nb.instance.bindCanvas(e),"2D"===n)r&&(r.style.width="100%",r.style.height="100%"),e.style.width="100%",e.style.height="100%",e.width=window.innerWidth,e.height=window.innerHeight;else{r&&(r.style.width="40%",r.style.height="100%"),e.style.width="100%",e.style.height="100%";var i=.4*window.innerWidth;e.width=i,e.height=window.innerHeight}D.nb.instance.update(),"2D"!==n&&t&&t.startRender()}},e)}},K=function(){D.nb.instance&&(D.nb.instance.bindCanvas(document.getElementById("cad_canvas")),D.nb.instance.update()),Q()},Q=function(){D.nb.instance&&(D.nb.instance._is_landscape=window.innerWidth>window.innerHeight);d.homeStore.IsLandscape;d.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))},$=Cu((0,P.useState)("layout"),2),nn=$[0],en=$[1];return(0,P.useEffect)(function(){d.homeStore.setShowEnterPage({show:!0,source:""}),d.homeStore.setViewMode("2D"),u(-2),d.homeStore.setZIndexOf3DViewer(-2)},[]),(0,P.useEffect)(function(){ht.f.updateAliasName(),d.homeStore.setRoomEntites(D.nb.instance.layout_container._room_entities)},[D.nb.instance.layout_container._room_entities]),(0,P.useEffect)(function(){if(D.nb.instance&&(D.nb.instance._is_website_debug=o.iG),window.addEventListener("resize",K),K(),D.nb.instance){var e;if(D.nb.instance.initialized||(!(0,o.fZ)()||"HouseId"!==o.Zx&&"CopyingBase64"!==o.Zx||D.nb.emit(F.U.Initializing,{initializing:!0}),D.nb.instance.init(),D.nb.RunCommand(E.f.AiCadMode),D.nb.instance.prepare().then(function(){if(Iu(function(){var n,e,t;return Au(this,function(r){switch(r.label){case 0:return o.uN?(n={isDelete:0,pageIndex:1,pageSize:9,keyword:o.uN},[4,li.D.getLayoutSchemeList(n)]):[2];case 1:return e=r.sent(),t=e.layoutSchemeDataList,e.total,t&&(D.nb.DispatchEvent(D.n0.OpenMyLayoutSchemeData,t[0]),D.nb.emit(F.U.OpenHouseSearching,!1)),[2]}})})(),Iu(function(){var n,e,t,r;return Au(this,function(i){switch(i.label){case 0:return"HouseId"!==o.Zx?[3,1]:(Iu(function(){var n,e,t;return Au(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,ci.ZN)("HouseId")];case 1:return n=r.sent(),Su.log(n),e=n.data,(0,o.fZ)()?d.trialStore.houseData.id=e:D.nb.DispatchEvent(D.n0.PostBuildingId,{id:e,name:""}),[3,3];case 2:return t=r.sent(),Su.error("Error loading file:",t),[3,3];case 3:return[2]}})})(),[3,9]);case 1:return"DwgBase64"!==o.Zx?[3,2]:(Iu(function(){return Au(this,function(n){try{D.nb.RunCommand(D._I.OpenDwgFilefromWork)}catch(n){Su.error("Error loading file:",n)}return[2]})})(),[3,9]);case 2:return"CopyingBase64"!==o.Zx?[3,3]:(Iu(function(){var n,e,t;return Au(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,ci.ZN)("CopyingBase64")];case 1:return n=r.sent(),e=n.data,d.homeStore.setImgBase64(e),(0,o.fZ)()?Su.log("fileData",e):D.nb.DispatchEvent(D.n0.LoadImitateImageFile,e),[3,3];case 2:return t=r.sent(),Su.error("Error loading file:",t),[3,3];case 3:return[2]}})})(),[3,9]);case 3:return"hxcreate"!==o.Zx?[3,6]:o.fW?[4,(0,si.ON)({id:o.fW})]:[3,5];case 4:(n=i.sent()).result.contentUrl=n.result.dataUrl,D.nb.DispatchEvent(D.n0.OpenMyLayoutSchemeData,n.result),D.nb.DispatchEvent(D.n0.autoSave,null),i.label=5;case 5:return[3,9];case 6:return"hxedit"!==o.Zx?[3,9]:o.vu?[4,(0,si.ON)({id:o.vu})]:[3,8];case 7:(e=i.sent()).success&&e.result&&e.result.dataUrl&&(e.result.contentUrl=e.result.dataUrl,D.nb.DispatchEvent(D.n0.OpenMyLayoutSchemeData,e.result)),i.label=8;case 8:D.nb.instance&&"SingleRoom"==(null===(r=D.nb.instance)||void 0===r||null===(t=r.layout_container)||void 0===t?void 0:t._drawing_layer_mode)&&D.nb.DispatchEvent(D.n0.leaveSingleRoomLayout,{}),D.nb.instance._current_handler_mode=E.f.HouseDesignMode,D.nb.RunCommand(E.f.HouseDesignMode),d.homeStore.setDesignMode(E.f.HouseDesignMode),i.label=9;case 9:return[2]}})})(),D.nb.emit(F.U.Initializing,{initializing:!1}),D.nb.instance&&D.nb.instance.scene3D){var n=D.nb.instance.scene3D;n&&n.stopRender()}}),D.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(e=window)||void 0===e?void 0:e.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var r="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(r)),D.nb.instance._debug_mode=r)}}D.nb.instance.update()}D.nb.on_M("showLight3DViewer","PadMobile",function(n){n?s<0&&(u(2),d.homeStore.setZIndexOf3DViewer(2),D.nb.emit(pr.r.UpdateScene3D,!0)):(u(-2),d.homeStore.setZIndexOf3DViewer(-2))}),D.nb.on(F.U.ShowDreamerPopup,function(n){d.homeStore.setShowDreamerPopup(n)}),D.nb.on(F.U.LayoutSchemeOpened,function(n){l(n.name),D.nb.emit(Cr,Ir.Default)}),D.nb.on(F.U.ClearLayout,function(){f({title:n("清空布局"),content:n("确定清空单空间布局？"),okText:n("确定"),cancelText:n("取消"),onOk:function(){D.nb.DispatchEvent(D.n0.ClearLayout,this)},onCancel:function(){}})}),D.nb.on(F.U.OpenMySchemeList,function(){d.homeStore.setShowMySchemeList(!0)}),D.nb.on_M(F.U.RoomList,"room_list",function(n){d.homeStore.setRoomInfos(n)}),D.nb.on(F.U.Room2SeriesSampleRoom,function(n){d.homeStore.setRoom2SeriesSampleArray(n)}),D.nb.on(F.U.showCustomKeyboard,function(n){setTimeout(function(){w(!0)},50),n.input&&(O(n.input.value),_(n.input))}),D.nb.on_M(F.U.updateAllMaterialScene3D,"padMobile",function(n){return Iu(function(){var e,t,r,i,o,a;return Au(this,function(l){switch(l.label){case 0:return e=D.nb.instance.layout_container,D.nb.instance&&D.nb.instance.scene3D?(t=D.nb.instance.scene3D,n&&"3D_FirstPerson"===d.homeStore.viewMode?(D.nb.emit(F.U.ApplySeriesSample,{seriesOpening:!0,title:"更新视角中..."}),r=[],e._room_entities.forEach(function(n){n._view_cameras.forEach(function(n){r.push(n)})}),i=r.map(function(n){return Iu(function(){return Au(this,function(r){switch(r.label){case 0:return n._perspective_img.src?[3,2]:(t.active_controls.bindViewEntity(n),t.update(),[4,n.updatePerspectiveViewImg(e.painter)]);case 1:r.sent(),r.label=2;case 2:return[2]}})})()}),[4,Promise.allSettled(i)]):[3,2]):[2];case 1:l.sent(),(o=e._room_entities.reduce(function(n,e){return n?e._area>n._area?e:n:e},null))?(t.active_controls.bindViewEntity(o._view_cameras[0]),d.homeStore.setCurrentViewCameras(o._view_cameras)):t.setCenter((null==o||null===(a=o._main_rect)||void 0===a?void 0:a.rect_center)||new vr.Pq0(0,0,0)),D.nb.emit(F.U.ApplySeriesSample,{seriesOpening:!1,title:""}),l.label=2;case 2:return[2]}})})()}),D.nb.on(F.U.SaveProgress,function(e){"success"===e.progress?(m.open({key:p,type:"success",content:n("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),"autoExit"===d.homeStore.isAutoExit&&(yc.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=o.O9),d.homeStore.setIsAutoExit("")):"fail"===e.progress?m.open({key:p,type:"error",content:n("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===e.progress&&m.open({key:p,type:"loading",content:n("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})}),D.nb.on_M(F.U.xmlSchemeLoaded,"padMobile",function(n){var e=D.nb.instance.layout_container;e&&yl.q.updateViewCameraEntities(e,null,{methods:2})})},[d.homeStore.isAutoExit]),(0,P.useEffect)(function(){4===d.homeStore.zIndexOf3DViewer?u(2):u(d.homeStore.zIndexOf3DViewer)},[d.homeStore.zIndexOf3DViewer]),(0,P.useEffect)(function(){"2D"===d.homeStore.viewMode?G(!0):G(!1)},[d.homeStore.viewMode]),(0,P.useEffect)(function(){D.nb.instance&&J(d.homeStore.viewMode)},[d.homeStore.viewMode]),(0,P.useEffect)(function(){"2D"===d.homeStore.viewMode&&D.nb.instance&&J("2D",400)},[d.homeStore.viewMode]),(0,r.jsxs)("div",{className:e.root,style:{backgroundColor:"3D_FirstPerson"===d.homeStore.viewMode?"#fff":"transparent"},children:[(0,r.jsx)(bc,{updateKey:L,isPanelOpen:H,layoutType:nn,setChoosedMode:function(n){X(n)},updateCanvasForViewMode:J}),(0,r.jsx)(ce.If,{condition:d.homeStore.showEnterPage.show,children:(0,r.jsx)(is,{})}),(0,r.jsx)(ys,{}),(0,r.jsx)(Xr,{}),(0,r.jsx)(Ce.ti,{}),(0,r.jsx)(Hi,{}),d.homeStore.showDreamerPopup&&(0,r.jsx)(ai.A,{}),d.homeStore.showSaveLayoutSchemeDialog.show&&(0,r.jsx)("div",{className:e.overlay,children:(0,r.jsx)(oi,{schemeName:a||"",closeCb:function(){d.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:v})}),d.homeStore.showMySchemeList&&(0,r.jsx)("div",{className:e.myCase,onClick:function(){return d.homeStore.setShowMySchemeList(!1)},children:(0,r.jsx)(su,{getHxId:function(n){d.homeStore.setIsShowHouseDetail({show:!0,source:"myCase"}),q(n)}})}),d.homeStore.isShowHouseDetail.show&&(0,r.jsx)("div",{className:e.houseDetail,onClick:function(){return d.homeStore.setIsShowHouseDetail({show:!1,source:""})},children:(0,r.jsx)(ju,{closeHouseDetail:function(){return d.homeStore.setIsShowHouseDetail({show:!1,source:""})},toLayout:function(n){en(n),"view"===n?Iu(function(){return Au(this,function(n){return d.homeStore.setViewMode("2D"),setTimeout(function(){return Iu(function(){var n;return Au(this,function(e){switch(e.label){case 0:return[4,na.y.instance.autoApplySeries()];case 1:return e.sent(),d.homeStore.setViewMode("3D_FirstPerson"),u(2),d.homeStore.setZIndexOf3DViewer(2),J("3D_FirstPerson"),(n=D.nb.instance.scene3D)&&(n.outlineMaterialMode=br.Gf.MaterialOnly,n.update()),[2]}})})()},300),[2]})})():"layout"===n&&Iu(function(){return Au(this,function(n){return u(-2),d.homeStore.setZIndexOf3DViewer(-2),d.homeStore.setViewMode("2D"),J("2D",300),[2]})})()},hxId:Z})}),(0,r.jsx)(ui.A,{onKeyPress:function(n){S&&(S.value=S.value+n,O(S.value))},onDelete:function(){S&&(S.value=S.value.slice(0,-1),O(S.value))},onConfirm:function(){S&&(D.nb.DispatchEvent(D.n0.DimensionInput,S),w(!1),O(""))},onClose:function(){w(!1),O("")},inputValue:C,isVisible:y}),d.homeStore.showDreamerPopup&&(0,r.jsx)(ai.A,{}),(0,r.jsx)(Gi.A,{ref:R}),(0,r.jsx)(Wi.A,{}),(0,r.jsx)(fe.cq,{channelCode:"Helptips-006"}),x,(0,r.jsxs)("div",{id:"Canvascontent",className:"3D_FirstPerson"===d.homeStore.viewMode?e.seleceView:e.content,children:[(0,r.jsxs)("div",{id:"body_container",className:e.canvas_pannel+" left_panel_layout",style:{flex:"2D"===d.homeStore.viewMode?"1":"0 0 40%",height:"100%",transition:"flex 0.3s ease-in-out"},children:[(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){d.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){d.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t);d.homeStore.setInitialDistance(r/d.homeStore.scale)}},onTouchMove:function(n){if(n.stopPropagation(),2!=n.touches[n.touches.length-1].identifier&&2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,r=Math.sqrt(e*e+t*t)/d.homeStore.initialDistance;r>5?r=5:r<.001&&(r=.001),d.homeStore.setScale(r),D.nb.DispatchEvent(D.n0.scale,r)}},onTouchEnd:function(n){n.touches.length>0&&D.nb.DispatchEvent(D.n0.updateLast_pos,n),d.homeStore.setInitialDistance(null)}}),!H&&(0,r.jsx)("div",{className:"layoutBtn",children:(0,r.jsx)("div",{onClick:function(){d.homeStore.setViewMode("2D"),u(-2),d.homeStore.setZIndexOf3DViewer(-2),setTimeout(function(){J("2D")},300)},children:"去布局设计"})}),d.homeStore.designMode===E.f.MeasurScaleMode&&H&&(0,r.jsxs)("div",{className:"canvas_btns",style:{zIndex:999999,marginBottom:"10vh",gap:"20px"},children:[(0,r.jsx)(N.A,{className:"btn",type:"primary",onClick:function(){D.nb.instance&&(D.nb.RunCommand(E.f.AiCadMode),d.homeStore.setDesignMode(E.f.AiCadMode))},children:n("取消")}),(0,r.jsx)(N.A,{className:"btn",type:"primary",onClick:function(){D.nb.instance&&(D.nb.DispatchEvent(D.n0.ConfirmScale,{img_base64:d.homeStore.img_base64}),d.homeStore.setDesignMode(E.f.AiCadMode))},children:n("确定")})]})]}),"2D"!==d.homeStore.viewMode&&(0,r.jsxs)("div",{className:e.canvas3d,style:{width:"calc(60% - 12px)",height:"100%"},children:[(0,r.jsxs)("div",{className:"scene3d_container",children:[(0,r.jsx)(B.A,{defaultViewMode:4}),(0,r.jsx)("div",{className:"view_select_box",children:(0,r.jsx)(Is,{})})]}),(0,r.jsx)("div",{className:"canvas3d_btns",children:(0,r.jsx)("div",{className:"canvas3d_btn",onClick:function(){return Iu(function(){var e,t,r;return Au(this,function(i){switch(i.label){case 0:if(Su.log("去生图"),0==(e=D.nb.instance.layout_container)._room_entities.length)return z.A.error(n("当前方案为空，无法保存！")),[2];if(null==e._layout_scheme_id)return d.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:""}),[2];i.label=1;case 1:return i.trys.push([1,3,,4]),D.nb.DispatchEvent(D.n0.SaveLayoutScheme,null),[4,new Promise(function(n,e){var t=D.nb.instance.scene3D;if(!t||!t.isValid())return Su.warn("3D场景不可用，返回空图片"),void n("");try{var r=t.sreenShot();Su.log("screenshotDataUrl1111",r);var i=document.createElement("canvas"),o=i.getContext("2d"),a=new Image;a.onload=function(){try{i.width=a.width,i.height=a.height,null==o||o.drawImage(a,0,0);var t=i.toDataURL("image/jpeg",.9);n(t)}catch(n){Su.error("JPG转换失败:",n),e(n)}},a.onerror=function(n){Su.error("图片加载失败:",n),e(n)},a.src=r}catch(n){Su.error("3D截图失败:",n),e(n)}})];case 2:return t=i.sent(),window.parent.postMessage({origin:"layoutai.api",type:"aiGeneration",data:{aiGeneration:!0,img:t}},"*"),[3,4];case 3:return r=i.sent(),Su.error("生图过程出错:",r),z.A.error(n("生图失败，请重试")),[3,4];case 4:return[2]}})})()},children:"去生图"})}),(0,r.jsx)("div",{className:"house_match_container",style:{display:"户型匹配"===Y?"block":"none"},children:(0,r.jsx)(Ys,{})})]})]})]})})},56697:function(n,e,t){t.d(e,{A:function(){return b}});var r=t(13274),i=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n        position: fixed;\n        bottom: -100%;\n        left: 0;\n        right: 0;\n        background: #fff;\n        border-top: 1px solid #ccc;\n        padding: 10px;\n        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);\n        z-index: 99;\n        transition: bottom 0.3s ease; /* 添加过渡效果 */\n\n        @media screen and (orientation: landscape) {\n            left : auto;\n            width : 420px;\n        }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n         bottom: 0 !important;\n    "]);return l=function(){return n},n}function c(){var n=o(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n    "]);return c=function(){return n},n}function s(){var n=o(["\n        display: flex;\n        align-items: center;\n        width: 50%;\n        position: relative;\n        \n    "]);return s=function(){return n},n}function u(){var n=o(["\n        width: 100%;\n        height: 50px;\n        background: #eee;\n        border: none;\n        border-radius: 5px;\n        padding: 5px 30px;\n        margin-left: 5px;\n        /* margin-bottom: 10px; */\n        font-size: 20px;\n    "]);return u=function(){return n},n}function d(){var n=o(["\n        margin-left: 5px;\n        font-size: 20px;\n        position: absolute;\n        right: 10px;\n    "]);return d=function(){return n},n}function f(){var n=o(["\n        display: flex;\n        flex-wrap: wrap;\n        margin-top: 10px;\n        button {\n            flex: 1 0 30%; /* 控制按钮大小 */\n            margin: 5px;\n            padding: 15px;\n            font-size: 20px;\n            cursor: pointer;\n            background: #eee;\n            border: none;\n            border-radius: 5px;\n        }\n    "]);return f=function(){return n},n}function p(){var n=o(["\n        font-size: 30px;\n        margin-right: 10px;\n    "]);return p=function(){return n},n}function h(){var n=o(["\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        flex: 1 0 30%; /* 控制按钮大小 */\n        .confirm_button{\n            background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n            color: #FFFFFF;\n        }\n    "]);return h=function(){return n},n}var m=(0,t(8268).rU)(function(n){var e=n.css;return{custom_keyboard:e(a()),custom_keyboard_visible:e(l()),top_container:e(c()),top_input_container:e(s()),top_input:e(u()),unit:e(d()),keypad:e(f()),close_icon:e(p()),bottom_row:e(h())}}),x=t(76330);function g(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function v(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return g(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return g(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var b=function(n){var e=n.onKeyPress,t=n.onDelete,o=n.onConfirm,a=n.onClose,l=n.inputValue,c=n.isVisible,s=m().styles,u=v((0,i.useState)(l),2),d=u[0],f=u[1];return(0,i.useEffect)(function(){f(l)},[l]),(0,r.jsxs)("div",{className:"".concat(s.custom_keyboard," ").concat(c?s.custom_keyboard_visible:""),children:[(0,r.jsxs)("div",{className:s.top_container,children:[(0,r.jsxs)("div",{className:s.top_input_container,children:[(0,r.jsx)("input",{type:"text",className:s.top_input,value:d,readOnly:!0}),(0,r.jsx)("span",{className:s.unit,children:"mm"})," "]}),(0,r.jsx)(x.A,{type:"icon-a-fangxiangxia",className:s.close_icon,onClick:a})]}),(0,r.jsxs)("div",{className:s.keypad,children:[["1","2","3","4","5","6","7","8","9"].map(function(n){return(0,r.jsx)("button",{onClick:function(){return e(n)},children:n},n)}),(0,r.jsxs)("div",{className:s.bottom_row,children:[(0,r.jsx)("button",{onClick:t,children:(0,r.jsx)(x.A,{type:"icon-a-tianchongFace-1",style:{color:"#585858"}})}),(0,r.jsx)("button",{onClick:function(){return e("0")},children:"0"})," ",(0,r.jsx)("button",{className:"confirm_button",onClick:o,children:"确定"})]})]})]})}},69283:function(n,e,t){t.d(e,{A:function(){return r}});var r=t(49582).A}}]);