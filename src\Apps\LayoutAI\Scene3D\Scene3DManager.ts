import { Color, Group, Mesh, MeshBasicMaterial } from "three";
import { TFigureElement } from "../Layout/TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../Layout/TLayoutEntities/TBaseGroupEntity";
import { TLayoutEntityContainer } from "../Layout/TLayoutEntities/TLayoutEntityContainter";
import { compareNames } from "@layoutai/z_polygon";
import { Scene3D } from "./Scene3D";
import { UserDataKey } from "./NodeName";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { I_Scene3DManager, LayoutAI_App } from "@/Apps/LayoutAI_App";
import { Model3dApi } from "../Api/Model3dApi";
import { Model3dWorkerPool } from "../Api/Model3dWorkerPool";
import { TextureManager } from "./TextureManager";
import { EventName } from "@/Apps/EventSystem";
import { TDesignMaterialUpdater } from "../Services/MaterialMatching/TDesignMaterialUpdater";
import { Utils3D } from "./Utils3D";


/**
 *   3D场景管理器---用它跟LayoutContainer 与 Scene3D 进行关联管理
 */
export class Scene3DManager implements I_Scene3DManager {
    protected _manager: TAppManagerBase;
    protected _scene3D: Scene3D;
    constructor(manager: TAppManagerBase) {
        this._manager = manager;
    }

    initScene3D(force: boolean): void {
        this._scene3D = new Scene3D();
    }
    get container() {
        return this._manager.layout_container;
    }

    get scene3D() {
        return this._scene3D as Scene3D;
    }
    bindMainDiv(div: HTMLDivElement) {
        if (this.scene3D) {
            this.scene3D.bindMainDiv(div);
        }
    }
    onElementUpdate(ele: any, options: { isWhite?: boolean, attached_mesh?: Mesh, [key: string]: any } = {}) {
        if (ele instanceof TFigureElement) {
            if (compareNames([ele.category, ele.sub_category], ["墙面", "地面"])) {
                let matched_info = ele._matched_material;
                let mesh = ele._solid_mesh3D as Mesh;
                if (mesh && mesh instanceof Mesh) {
                    if (matched_info.imageUrl) {
                        TextureManager.updateMeshTextureWithImg(mesh, matched_info.imageUrl, matched_info.modelId);
                    }
                    if (compareNames([ele.sub_category, ele.category], ["地面"])) {
                        let fill_color = ele.fill_color || "#aaa";
                        (mesh.material as MeshBasicMaterial).color = new Color(fill_color);;
                    }

                }

            }
            else {
                let solid_mesh = ele._solid_mesh3D;
                let group_node = solid_mesh as Group;
                if (group_node) {
                    let design_material_info = group_node.userData[UserDataKey.MaterialInfo];
                    if (ele.pictureViewImg && design_material_info && design_material_info.ContentUrl) {
                        if (!options.isWhite) {
                        }
                        ele.updateRenderedTopViewImg();
                    } else if (ele.shouldUseRenderedTopViewImg()) {
                        ele.updateRenderedTopViewImg();
                    }
                    let scene3d = this.scene3D;
                    if (scene3d) {
                        scene3d.outlinePostProcessing.makeOutlineDirty();
                        let selEle = scene3d.selection_box?.userData[UserDataKey.EntityOfMesh];
                        if (selEle == ele) {
                            scene3d.setSelectionBox(ele);
                        }
                    }


                }

            }


        }
    }
    UpdateScene3DWithMaterials = async (force: boolean = false, options: { needs_centered?: boolean } = {}) => {
        let container = this.container;
        LayoutAI_App.emit_M(EventName.SceneContentStateChanged, { state: 0 });

        this.updateScene3D(force);

        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: "更新3D场景中..." });

        let validCabinetStyleIds: string[] = [];
        container._rooms.forEach((room) => {
            if (room._cabinetStyleId) {
                if (!validCabinetStyleIds.includes(room._cabinetStyleId)) validCabinetStyleIds.push(room._cabinetStyleId);
            }
        });
        TextureManager.instance.clearCache(validCabinetStyleIds);

        // 并行处理所有房间的材质更新
        const roomPromises = force ? container._rooms.map(room =>
            TDesignMaterialUpdater.instance.updateRoomFurnitureDesignMaterials(room, null, force)
        ) : [];

        // 并行处理所有窗户的材质更新
        const windowPromises = TDesignMaterialUpdater.instance.updateWindowsFurnitureDesignMaterials(
            container._window_entities.filter((win) => !win?._win_figure_element?._solid_mesh3D)
        );

        // 等待所有材质更新完成
        await Promise.allSettled([...roomPromises, windowPromises]);

        let scene3d = (LayoutAI_App.instance).scene3D as Scene3D;
        scene3d.setLightMode(scene3d.getLightMode());
        scene3d.entity_mesh_mode = scene3d.entity_mesh_mode;
        LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: "" });

        scene3d.update();

        // 通知给padMobile, 所有材质更新完成， 更新视角
        LayoutAI_App.emit_M(EventName.updateAllMaterialScene3D, { state: 1 });
    }

    bindOnSelectFigure(func: (...args: any) => void): void {
        if (this.scene3D?.raycasteControls) {
            this.scene3D.raycasteControls.onSelectedFigure = func;
        }
    }
    bindOnSelectRoom(func: (...args: any) => void): void {
        if (this.scene3D?.raycasteControls) {
            this.scene3D.raycasteControls.onSelectedRoom = func;
        }
    }

    updateScene3D(force: boolean = false) {
        if (!this.scene3D) return;
        let container = this.container;

        if (force) {
            this.scene3D.reset();
            this.scene3D.cleanWalls();
            this.scene3D.cleanWindows();
            this.scene3D.cleanFurnitures();
            this.scene3D.cleanRoomEntities();
            this.scene3D.updateShadowTexture();
            // this.scene3D.createOrbitControls();

            this.updateFillLights(true);

            for (let entity of container._window_entities)  // 先更新 门窗
            {
                let mesh = entity.updateMesh3D();
                if (mesh) {
                    this.scene3D.windows_group.add(mesh);
                }
            }

            for (let wall of container._wall_entities) {

                let wall_mesh = wall.updateMesh3D();
                if (wall_mesh) {
                    this.scene3D.walls_group.add(wall_mesh);
                }
            }
            for (let entity of container._room_entities) {
                this.scene3D.rooms_group.add(entity.updateMesh3D());
            }


            for (let entity of container._furniture_entities) {
                if (entity instanceof TBaseGroupEntity) {
                    let groupEntity = entity as TBaseGroupEntity;
                    if (groupEntity.figure_element.haveMatchedGroupMaterial() && !groupEntity.figure_element.isMaterialMarkAsInvisible()) {
                        this.scene3D.furniture_group.add(groupEntity.updateMesh3D());
                    } else {
                        groupEntity.combination_entitys.forEach((entity) => {
                            if (entity.figure_element.isMaterialMarkAsInvisible()) {
                                return;
                            }
                            this.scene3D.furniture_group.add(entity.updateMesh3D());
                        });
                    }
                } else {
                    let figure_mesh = entity.updateMesh3D();
                    if (figure_mesh && !entity.figure_element.isMaterialMarkAsInvisible()) {
                        this.scene3D.furniture_group.add(figure_mesh);
                    }
                }
            }

            if (container._rooms) {
                let lights: TFigureElement[] = [];
                container._rooms.forEach((room) => {
                    lights.push(...room._furniture_list.filter(light => compareNames([light.category], ["主灯"])))
                });

                this.scene3D.addNightLights(lights);
            }


            // this.scene3D.setCenter(this.painter.p_center);
            /**
             *   用自身等于自身的方法, 触发一次更新
             */
            const temp = this.scene3D.entity_mesh_mode;
            this.scene3D.entity_mesh_mode = temp;

            if (this.scene3D.active_controls) {
                this.scene3D.active_controls.updateMeshesVisible();
            }
        }
        else {
            let mesh_groups = [this.scene3D.furniture_group, this.scene3D.walls_group, this.scene3D.windows_group];
            for (let group of mesh_groups) {
                let to_remove_meshes: Mesh[] = [];

                for (let mesh of group.children) {
                    let entity = mesh.userData[UserDataKey.EntityOfMesh];
                    if (!entity || !this.container.containsEntity(entity)) {
                        to_remove_meshes.push(mesh as any);
                    }
                }
                to_remove_meshes.forEach(mesh => Utils3D.disposeObject(mesh));
                group.remove(...to_remove_meshes);
            }

            this.scene3D.updateShadowTexture();
            // this.scene3D.updateLightingTexture();
            // this.scene3D.createOrbitControls();
            for (let entity of container._window_entities)  // 先更新 门窗
            {
                let mesh = entity.updateMesh3D();
                if (mesh) {
                    this.scene3D.windows_group.add(mesh);
                }
            }

            for (let wall of container._wall_entities) {

                let wall_mesh = wall.updateMesh3D();
                if (wall_mesh) {
                    this.scene3D.walls_group.add(wall_mesh);
                }
            }
            for (let entity of container._room_entities) {
                this.scene3D.walls_group.add(entity.updateMesh3D());
            }


            for (let entity of container._furniture_entities) {
                let figure_mesh = entity.updateMesh3D();
                if (figure_mesh) {
                    this.scene3D.furniture_group.add(figure_mesh);
                }

            }




            // this.scene3D.setCenter(this.painter.p_center);
            /**
             *   用自身等于自身的方法, 触发一次更新， 用变量的形式去修改
             */
            const temp = this.scene3D.entity_mesh_mode;
            this.scene3D.entity_mesh_mode = temp;

            if (this.scene3D.active_controls) {
                this.scene3D.active_controls.updateMeshesVisible();
            }
        }

    }
    public updateFillLights(force: boolean = false) {
        this.scene3D.cleanFillLights();
        let fillLightsEntities = this.container.getFillLightEntities();
        for (let entity of fillLightsEntities) {
            this.scene3D.aiLightsGroupOffline.add(entity.update3D());
            this.scene3D.aiLightsGroupTest.add(entity.lightMesh);
        }
    }
}