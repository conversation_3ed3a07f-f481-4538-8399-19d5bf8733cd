"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[158],{158:function(e,t,n){n.r(t),n.d(t,{default:function(){return Jr}});var r=n(13274),a=n(41594),i=n(46562),o=n(62634),l=n(37112),s=n(63080),c=n(44466),u=n(27347),d=n(88934),f=n(39454),h=n(16805),m=n(17567),p=n(31281),g=n(23825),y=n(40955),b=n(9003);function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var w=p.A.Platform,S=function(e){var t,n,i=e.updateIssueReportVisible,o=e.updateLayoutBatchTestVisible,s=e.updateModelLightConfigVisible,c=_((0,a.useState)("testFigureGroupBtn"),2),f=c[0],p=c[1],v=(0,y.A)().styles,S=(0,b.P)(),k={left:(0,r.jsx)("span",{className:"tabs-extra-left-title",style:{marginLeft:"10px",fontSize:"18px"},children:"训练后台"}),right:(0,r.jsx)(l.A,{type:"dashed",icon:(0,r.jsx)(m.A,{}),style:{marginRight:"10px"},className:"tabs-extra-right-button",onClick:function(){w.Application.closeApp({appId:g.sZ})}})},I=[{key:"LayoutGraphTesting",label:"单空间调试",command_name:"LayoutGraphTesting"},{key:"LayoutTaskTesting",label:"布局任务测试",command_name:"LayoutTaskTesting"},{key:"LayoutSchemeTesting",label:"全屋测试(旧)",command_name:"LayoutSchemeTesting"},{key:"ModelRoomList",label:"布局模板",command_name:"ModelRoomList"},{key:"FigureTemplateTesting",label:"组合模板",command_name:"FigureTemplateTesting"},{key:"LayoutScoreConfig",label:"布局评分配置",command_name:"LayoutScoreConfig"},{key:"ModelLightConfig",label:"灯光模板",command_name:"ModelLightConfig"}];null===(n=S.userStore)||void 0===n||null===(t=n.userInfo)||void 0===t||t.tenantId;var A=_((0,a.useState)(["left","right"]),2),T=A[0],N=(A[1],(0,a.useMemo)(function(){return 0===T.length?null:T.reduce(function(e,t){return j(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){x(e,t,n[t])})}return e}({},e),x({},t,k[t]))},{})},[T])),C=function(e){p(e);var t=I.find(function(t){return t.key===e});t&&u.nb.RunCommand(t.command_name),i("LayoutIssueReport"===e),o("LayoutBatchTest"===e),s("ModelLightConfig"===e),u.nb.instance.update()};return u.nb.on(d.U.TrainingTabChanged,function(e){C(e)}),(0,a.useEffect)(function(){u.nb.instance._current_handler&&u.nb.instance._current_handler.enter()},[]),(0,r.jsx)("div",{className:v.title_bar,children:(0,r.jsx)(h.A,{rootClassName:v.tab_root,size:"middle",centered:!0,defaultActiveKey:f,activeKey:f,tabBarExtraContent:N,items:I,onTabClick:C})})},k=n(45599),I=n(93646),A=n(88880),T=n(15696),N=n(65640);function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function O(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function L(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){O(i,r,a,o,l,"next",e)}function l(e){O(i,r,a,o,l,"throw",e)}o(void 0)})}}function R(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var P=[{dataset_name:"基础空间-20",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/houseSchemeListA.json"},{dataset_name:"回归测试集-100",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/houseSchemeListB.json"},{dataset_name:"种子库-厨房",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/kitchenSeeds.json"},{dataset_name:"种子库-厨房(烟道)",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/kitchenFlueSeeds.json"},{dataset_name:"种子库-客餐厅",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/LivingRoomSeeds.json"},{dataset_name:"种子库-卫生间",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/WashroomSeeds.json"},{dataset_name:"种子库-卧室",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/BedroomSeeds.json"},{dataset_name:"种子库-书房",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/StudySeeds.json"},{dataset_name:"种子库-阳台",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/BalconySeeds.json"},{dataset_name:"测试集-卫生间-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/WashroomTesting.json"},{dataset_name:"测试集-客餐厅-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/LivingRoomTesting.json"},{dataset_name:"测试集-厨房(烟道)-2000",dataset_url:"https://3vj-fe.oss-cn-shenzhen.aliyuncs.com/layoutai/KitchenTesting.json"}],E=null,F=0,z="",M=(0,T.observer)(function(){var e=(0,I.A)().styles,t=R((0,a.useState)(0),2),n=t[0],i=t[1],o=R((0,a.useState)(z),2),l=o[0],s=o[1],c=R((0,a.useState)(!1),2),f=c[0],h=c[1],m=R((0,a.useState)({}),2),p=m[0],g=m[1],y=(0,a.useRef)(null),b=function(e){return L(function(){var t,r,a;return D(this,function(o){switch(o.label){case 0:return!(E=e)||E.data_list?[3,4]:E.dataset_url||!E._query_by_hx_search?[3,2]:(t=prompt("输入 城市,关键字, 举例: 广州,云湖花城","广州"),[4,A.h.instance.getDatasetByHxSearch(t)]);case 1:return(r=o.sent())&&(E.data_list=r.data_list,r=null),[3,4];case 2:return[4,A.h.instance.getBasicDataset(E.dataset_url)];case 3:(a=o.sent())&&(E.data_list=a.data_list,a=null),o.label=4;case 4:return A.h.instance.loadLocalStorage_DatasetCounters(E),u.nb.DispatchEvent(u.n0.ShowCurrentTestingDataset,E),i(n+1),[2]}})})()},v=function(e,t){s((null==e?void 0:e.buildingRoomId)||""),z=l,u.nb.DispatchEvent(u.n0.OnClickTestingHouseSchemeInfo,e)},x="__houseSchemeList";return u.nb.on_M(d.U.SetStartRunningState,x,function(e){0==e&&A.h.instance.saveLocalStorage_DatasetCounters(E),h(e)}),u.nb.on_M(d.U.SetHighlightSchemeIds,x,function(e){if(g(e),e&&E){var t=E.data_list.findIndex(function(e){return e.buildingRoomId===l}),n=E.data_list.findIndex(function(n,r){return!!(e[n.schemeId]&&r>t)});if(n<0&&(n=E.data_list.findIndex(function(t){return e[t.schemeId]})),n>=0){var r=E.data_list[n];v(r)}}}),u.nb.on_M(d.U.ShowCurrentHouseSchemeInfo,x,function(e){e&&(F=E.data_list.findIndex(function(t){return t.buildingRoomId===(null==e?void 0:e.buildingRoomId)}),s((null==e?void 0:e.buildingRoomId)||""),function(){if(localStorage){var e={dataset_name:E.dataset_name,build_room_id:l};localStorage.setItem("houseSchemeListData",JSON.stringify(e))}}(),z=l)}),E||P&&P[0]&&b(P[0]),(0,a.useEffect)(function(){L(function(){var e,t,n,r,a,i,o;return D(this,function(l){switch(l.label){case 0:if(!localStorage)return[3,5];if(!(e=localStorage.getItem("houseSchemeListData")))return[3,5];l.label=1;case 1:return l.trys.push([1,4,,5]),t=JSON.parse(e),n=t.dataset_name,r=t.build_room_id,n&&r&&(a=P.find(function(e){return e.dataset_name===n}))?[4,b(a)]:[3,3];case 2:l.sent(),r&&(s(r||""),(i=a.data_list.find(function(e){return e.buildingRoomId===r}))&&u.nb.DispatchEvent(u.n0.OnClickTestingHouseSchemeInfo,i)),l.label=3;case 3:return[3,5];case 4:return o=l.sent(),N.log(o),[3,5];case 5:return[2]}})})()},[]),(0,a.useEffect)(function(){if(y){var e=y.current;e.scrollTo({top:45*(F-(e.clientHeight-45)/45/2),behavior:"smooth"})}},[l]),(0,r.jsxs)("div",{className:e.houseSchemeList,id:"house_scheme_list_div",children:[(0,r.jsxs)("div",{className:"dataset_title",children:["测试集：",P.length>0&&(0,r.jsx)("select",{onChange:function(e){var t,n=(null==e||null===(t=e.target)||void 0===t?void 0:t.selectedIndex)||0;b(P[n]||P[0])},value:(null==E?void 0:E.dataset_name)||"",children:P.map(function(e,t){return(0,r.jsx)("option",{value:e.dataset_name,children:e.dataset_name},t)})}),(0,r.jsx)("div",{className:e.startRunningBtn+" "+(f?"isRunning":""),onClick:function(){var e=!f;u.nb.DispatchEvent(u.n0.OnClickStartRuningTesting,e)},children:f?"测试中...":"开始测试"})]}),(0,r.jsx)("div",{ref:y,className:"listDiv",style:{overflow:"auto"},children:E&&E.data_list&&E.data_list.map(function(e,t){return(0,r.jsxs)("div",{className:"".concat(p[e.schemeId]?"highlight":""," ").concat(l===e.buildingRoomId?"active":"","  scheme_row_div"),id:"house_scheme-list-"+t,"data-room_name":e.buildingRoomId,onClick:function(){return v(e)},children:[t+1,":  ",e.buildingRoomId," ",(0,r.jsx)("br",{}),"     ",e.buildingName||""]},t)})})]})});function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return B(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return B(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var H=function(){var e=(0,I.A)().styles,t=U((0,a.useState)(!1),2),n=t[0],i=t[1],o=U((0,a.useState)(!0),2),l=o[0],s=o[1];return u.nb.on(d.U.ShowSchemeTestingLeftPanel,function(e){i(e)}),u.nb.on_M(d.U.ShowCurrentTestingRoomInfo,"SchemeTestingLeftPanel",function(e){s(!e)}),(0,a.useEffect)(function(){},[]),(0,r.jsx)("div",{className:e.leftPanel,id:"SchemeTestingLeftPanel",style:{display:n?"block":"none"},children:n&&(0,r.jsxs)("div",{className:"leftPanel",children:[(0,r.jsx)("div",{style:{marginTop:"60px",borderTopWidth:"1px"},children:(0,r.jsx)(k.A,{width:410,showSchemeName:!0})}),l&&(0,r.jsx)(M,{})]})})},J=n(48402),V=n(33421);function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||K(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e){return function(e){if(Array.isArray(e))return G(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||K(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(e,t){if(e){if("string"==typeof e)return G(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}var X=(0,T.observer)(function(e){var t=e.visible,n=(0,I.A)().styles,i=W((0,a.useState)(null),2),o=i[0],l=i[1],s=W((0,a.useState)([]),2),c=s[0],f=s[1],h=W((0,a.useState)(null),2),m=h[0],p=h[1],g=W((0,a.useState)(0),2),y=g[0],b=g[1],v=[],x=[],j="houseSchemeTestingPanel";u.nb.on_M(d.U.ShowCurrentHouseSchemeInfo,j,function(e){l(e)}),u.nb.on_M(d.U.ShowCurrentTestingRoomInfo,j,function(e){p(e)}),(0,a.useEffect)(function(){},[]);var _=function(e){if(x=[],o&&(null==o?void 0:o.room_testing_info_list)){var t=!0,n=!1,r=void 0;try{for(var a,i=o.room_testing_info_list[Symbol.iterator]();!(t=(a=i.next()).done);t=!0){var l=a.value;x.push({image_path:"https://test-3vj-pano.oss-cn-shenzhen-internal.aliyuncs.com//vr/layout/def.jpg?x-oss-process=image/resize,m_fixed,h_218,w_360",title:l.roomname+"-"+((null==l?void 0:l.uid)||""),centerTitle:"布局数量 ".concat(l.candidate_num||0," ").concat((null==l?void 0:l.solved)||""," "),bottomTitle:"".concat((null==l?void 0:l.transfer_from_scheme_room_id)||"")})}}catch(e){n=!0,r=e}finally{try{t||null==i.return||i.return()}finally{if(n)throw r}}}if(m){v=[];var s=$(m.room._furniture_list);s.sort(function(e,t){return t.default_drawing_order-e.default_drawing_order}),s.forEach(function(e){var t;(u.nb.IsDebug||!e._is_decoration&&!e._is_sub_board)&&v.push({image_path:(null===(t=J.eW[e.sub_category])||void 0===t?void 0:t.img_path)||"https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",title:e.modelLoc+" | "+e.sub_category,centerTitle:"".concat(Math.round(e.length),"*").concat(Math.round(e.depth),"*").concat(Math.round(e.height)),bottomTitle:"",figure_element:e})})}else v=[];f([{label:"房间",figureList:x,init_expanded:e},{label:"图例",figureList:v,init_expanded:!e}])};return(0,a.useEffect)(function(){_(!0)},[o]),(0,a.useEffect)(function(){_(!m)},[m]),(0,r.jsxs)("div",{className:n.houseSchemeTestingPanel,id:"houseSchemeTestingPanel",style:{display:t?"block":"none"},children:[(0,r.jsxs)("div",{className:"content",children:[(0,r.jsxs)("div",{className:"info_text",onClick:function(){var e;e=document.createElement("a"),document.body.appendChild(e),e.href="/Home?importType=importHouse",e.target="_blank",e.click(),document.body.removeChild(e)},children:[(0,r.jsx)("div",{children:"户型ID:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingRoomId})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"方案ID:"}),(0,r.jsx)("div",{children:null==o?void 0:o.schemeId})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"户型名称:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingName})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"空间分布:"}),(0,r.jsx)("div",{children:null==o?void 0:o.buildingRoomTypeName})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"面积:"}),(0,r.jsxs)("div",{children:[null==o?void 0:o.area,"㎡"]})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"标记信息:"}),(0,r.jsxs)("select",{value:(null==o?void 0:o.scheme_label)||"default",onChange:function(e){o.scheme_label=e.target.value,u.nb.DispatchEvent(u.n0.OnChangeTestingHouseSchemeLabel,o),b(y+1)},children:[(0,r.jsx)("option",{value:"default",children:"正常"}),(0,r.jsx)("option",{value:"room_error",children:"户型错误"}),(0,r.jsx)("option",{value:"layout_error",children:"布局异常"})]})]})]}),(0,r.jsx)("div",{style:{overflowY:"auto"},children:(0,r.jsx)(V.A,{menuList:c,showLayoutList:!0})})]})}),q=n(28888),Z=n(17655),Q=n(79489);function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ee(e,t,n[t])})}return e}function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Y(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var re=(0,T.observer)(function(e){var t,n=e.visible,i=(0,I.A)().styles,o=ne((0,a.useState)(null),2),l=o[0],s=o[1],c=ne((0,a.useState)(0),2),f=c[0],h=c[1],m=ne((0,a.useState)({}),2),p=m[0],g=m[1],y="datasetTestingPanel";u.nb.on_M(d.U.SetCurrentDataSet,y,function(e){s(e)}),u.nb.on_M(d.U.UpdateDatasetInfo,y,function(){h(f+1)});var b=["客餐厅","卫生间","厨房","卧室","阳台"];(0,a.useEffect)(function(){var e=!0,t=!1,n=void 0;try{for(var r,a=b[Symbol.iterator]();!(e=(r=a.next()).done);e=!0){var i=r.value;p[i]=!0}}catch(e){t=!0,n=e}finally{try{e||null==a.return||a.return()}finally{if(t)throw n}}},[]),(0,a.useEffect)(function(){},[p]);var v=[{text:"空",name:"None"},{text:"相似",name:"Transfer"},{text:"自相似",name:"Self-Transfer"},{text:"逻辑",name:"Logic"}];return(0,r.jsx)("div",{className:i.houseSchemeTestingPanel,id:y,style:{display:n?"block":"none"},children:(0,r.jsxs)("div",{className:"content",children:[(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"数据集:"}),(0,r.jsx)("div",{children:null==l?void 0:l.dataset_name})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"户型数量:"}),(0,r.jsx)("div",{children:(null==l||null===(t=l.data_list)||void 0===t?void 0:t.length)||0})]}),(0,r.jsxs)("div",{className:"info_text",children:[(0,r.jsx)("div",{children:"计算时间:"}),(0,r.jsx)("div",{children:(null==l?void 0:l._start_running_time)?((null==l?void 0:l._end_running_time)-(null==l?void 0:l._start_running_time))/1e3+"s":"---"})]}),(0,r.jsx)("div",{className:"info_text",children:(0,r.jsx)("button",{onClick:function(){if(l){var e=te({},l);e.data_list=l.data_list.map(function(e){var t=te({},e);return t.room_testing_info_list&&(t.room_testing_info_list=t.room_testing_info_list.map(function(e){var t=te({},e);return delete t.room,t})),t}),(0,Q.c6)(JSON.stringify(e),"datatest.json")}},children:"导出结果"})}),(null==l?void 0:l.rooms_counter)&&b.map(function(e,t){var n;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"info_text",onClick:function(){return p[t=e]=!p[t],void g(te({},p));var t},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(Z.In,{iconClass:"".concat(p[e]?"iconcaretdown":"iconcaretright"),className:"icon",size:14}),e]}),(0,r.jsx)("div",{children:(null===(n=l.rooms_counter[e])||void 0===n?void 0:n.computed_count)||0})]}),v.map(function(n,a){var i,o;return(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"info_text",style:{display:p[e]?"flex":"none"},onClick:function(){var t;!function(e){if(null==e?void 0:e.scheme_ids){var t={},n=!0,r=!1,a=void 0;try{for(var i,o=e.scheme_ids[Symbol.iterator]();!(n=(i=o.next()).done);n=!0)t[i.value]=!0}catch(e){r=!0,a=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw a}}u.nb.emit_M(d.U.SetHighlightSchemeIds,t)}else u.nb.emit_M(d.U.SetHighlightSchemeIds,{})}(null===(t=l.rooms_counter[e])||void 0===t?void 0:t.result_count[n.name])},children:[(0,r.jsx)("div",{className:"sub_info_text",children:n.text}),(0,r.jsx)("div",{children:(null===(o=l.rooms_counter[e])||void 0===o||null===(i=o.result_count[n.name])||void 0===i?void 0:i.text)||0})]})},y+"info_text_"+t+"_"+a)})]},y+"info_list_"+t)})]})})});function ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ie(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ae(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ae(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var oe=function(){var e=(0,I.A)().styles,t=ie((0,a.useState)(!1),2),n=t[0],i=t[1],o=ie((0,a.useState)(null),2),l=o[0],s=o[1],c=ie((0,a.useState)("户型信息"),2),f=c[0],h=c[1];return u.nb.on(d.U.ShowSchemeTestingRightPanel,function(e){i(e)}),u.nb.on_M(d.U.ComputingProgress,"SchemeTestingRightPanel",function(e){s(e)}),(0,a.useEffect)(function(){},[]),(0,r.jsxs)(r.Fragment,{children:[l&&(0,r.jsx)("div",{className:e.progressInfo,children:(0,r.jsx)(q.A,{title:l,color:"#000000"})}),(0,r.jsxs)("div",{className:e.rightPanel,id:"SchemeTestingRightPanel",style:{display:n?"block":"none"},children:[(0,r.jsxs)("div",{className:"title",children:[(0,r.jsx)("span",{className:"户型信息"==f?"active":"",onClick:function(){return h("户型信息")},children:"户型信息"}),"  |",(0,r.jsx)("span",{className:"测试汇总"==f?"active":"",onClick:function(){return h("测试汇总")},children:"测试汇总"})]}),(0,r.jsx)(re,{visible:"测试汇总"==f}),(0,r.jsx)(X,{visible:"户型信息"==f})]})]})},le=n(63436),se=n(8268);function ce(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function ue(){var e=ce([""]);return ue=function(){return e},e}function de(){var e=ce(["\n      position :absolute;\n      left:0;\n      top:0;\n      width: 320px;\n      height:100%;\n      z-index:5;\n      background:#fff;\n      padding-left: 15px;\n      .row_ele {\n        width:100%;\n        height:50px;\n        line-height:22px;\n        border-bottom:1px dashed #adf;\n        cursor:pointer;\n        user-select:text;\n        font-size:13px;\n         &:hover {\n          background :#efefef;\n         }\n         &.marked {\n            background :#ffefef;\n\n         }\n         &.checked {\n          background :#aaaaef;\n        }\n         select {\n          margin-left:10px;\n         }\n         input {\n            width:40px;\n            height:25px;\n            line-height:25px;\n            margin-left:10px;\n         }\n         .row_input {\n            margin-right:10px;\n         }\n         button {\n            height:25px;\n            line-height:15px;\n            border-radius :4px;\n            margin-left:5px;\n         }\n         .right_text {\n            float:right;\n            margin-right:5px;\n         }\n      }\n    "]);return de=function(){return e},e}function fe(){var e=ce(["\n      position :absolute;\n      right:0;\n      top:0;\n      width: 300px;\n      height:100%;\n      z-index:5;\n      background:#fff;\n      .Ctrlbtns {\n         position:absolute;\n         bottom:10px;\n         left:0;\n         width:100%;\n         text-align:center;\n         .btn_row {\n            line-height:30px;\n            margin-top:5px;\n         }\n      }\n      .tabs {\n         width:100%;\n         height:30px;\n         font-size: 18px;\n         line-height: 30px;\n         color:#aaa;\n         .tab {\n             display:inline-block;\n             padding:5px;\n             padding-left:10px;\n             padding-right:10px;\n             cursor:pointer;\n             &:hover {\n               color:#333333;\n             }\n             &.checked {\n               color:#333333;\n             }\n\n         }\n      }\n      .row_ele {\n         width:100%;\n         height:50px;\n         line-height:22px;\n         border-bottom:1px dashed #adf;\n         cursor:pointer;\n         user-select:text;\n         font-size:13px;\n          &:hover {\n           background :#efefef;\n          }\n          &.checked {\n           background :#aaaaef;\n         }\n          select {\n           margin-left:10px;\n          }\n          input {\n             width:40px;\n             height:25px;\n             line-height:25px;\n             margin-left:10px;\n          }\n          .row_input {\n             margin-right:10px;\n          }\n          button {\n             height:25px;\n             line-height:15px;\n             border-radius :4px;\n             margin-left:5px;\n          }\n          .right_text {\n             float:right;\n             margin-right:5px;\n          }\n       }\n    "]);return fe=function(){return e},e}function he(){var e=ce(["\n      float:left;\n      margin-left:20px;\n      margin-right:20px;\n      margin-top:20px;\n      padding:5px;\n      width: 300px;\n      height : calc(100% - 80px);\n      .report_row {\n         width:100%;\n         height:30px;\n         line-height:30px;\n         user-select:text;\n         font-size:14px;\n         &.checked {\n            background :#aaaaef;\n         }\n      }\n    "]);return he=function(){return e},e}function me(){var e=ce(["\n         float:left;\n         margin-left:20px;\n         margin-right:20px;\n         margin-top:20px;\n         padding:5px;\n         width: 300px;\n         height : calc(100% - 40px);\n         border-radius : 10px;\n         background : #ededf9;\n         &.compared_report {\n            background:#f9edf7;\n            margin-left:5px;\n            // margin-right:0px;\n         }\n         &.right_report {\n            margin:0;\n            background:#fff;\n         }\n         .report_row {\n            width:100%;\n            height:30px;\n            line-height:30px;\n            cursor:pointer;\n            user-select:text;\n            font-size:16px;\n            &.checked {\n               background :#aaaaef;\n            }\n         }\n         "]);return me=function(){return e},e}function pe(){var e=ce(["\n         width:200px;\n         margin:5px auto;\n         background:#efefef;\n         border-radius:5px;\n         overflow:hidden;\n         color:#aaa;\n         font-size:14px;\n         .tab {\n            float:left;\n            padding:5px;\n            width:100px;\n            text-align:center;\n            cursor:pointer;\n            &.checked {\n               color:#fff;\n               background:#7777ff;\n            }\n         }\n      "]);return pe=function(){return e},e}function ge(){var e=ce(["\n      position: absolute;\n      z-index:11;\n      left:340px;\n      min-width:120px;\n      top:5px;\n      background:#efefef;\n      border-radius:5px;\n      overflow:hidden;\n      color:#aaa;\n      .tab {\n         float:left;\n         padding:5px;\n         width:60px;\n         text-align:center;\n         cursor:pointer;\n         &.checked {\n            color:#fff;\n            background:#7777ff;\n         }\n      }\n\n    "]);return ge=function(){return e},e}function ye(){var e=ce(["\n         position : absolute;\n         right : 300px;\n         top: 0px;\n         z-index:11;\n\n      "]);return ye=function(){return e},e}function be(){var e=ce(["\n         position: absolute;\n         width: calc(100% - 60px);\n         height: 140px;\n         background: #fff;\n         left: 40px;\n         top: 20px;\n         border: 1px solid #eee;\n         padding: 10px;\n         box-shadow: 0 2px 24px rgba(0, 0, 0, .5);\n         border-radius: 5px;\n         .input_row {\n            width:100%;\n            height:30px;\n            margin-bottom:10px;\n            span {\n               cursor: pointer;\n            }\n\n       .methods_name {\n          padding: 10px;\n          text-align:center;\n          color : #aaa;\n          &:hover {\n            color : #aaaaff;\n\n          }  \n          &.checked {\n             color : #3333ff;\n          }\n       }\n    }\n    "]);return be=function(){return e},e}function ve(){var e=ce(["\n         position:absolute;\n         top:10px;\n         left:350px;\n         width: 1000px;\n         height : calc(100vh - 80px);\n         border-radius:10px;\n         border:1px solid #333; \n         background :#fff;\n         z-index:50;\n         .close_btn {\n            position:absolute;\n            right:10px;\n            top:10px;\n            cursor:pointer;\n            &:hover {\n               color:#0000ff;\n            }\n         }\n    "]);return ve=function(){return e},e}var xe=(0,se.rU)(function(e){var t=e.css;return{root:t(ue()),leftPanel:t(de()),rightPanel:t(fe()),resultInfo:t(he()),dataReport:t(me()),schemeListModeTabs:t(pe()),drawRoomModeTabs:t(ge()),rightToolBtns:t(ye()),dialogInputs:t(be()),test_report:t(ve())}}),je=n(64186),_e=n(69391),we=n(95391),Se=n(86417),ke=n(27143),Ie=n(41443),Ae=n(5640);function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ne(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Ce(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){Ne(i,r,a,o,l,"next",e)}function l(e){Ne(i,r,a,o,l,"throw",e)}o(void 0)})}}function Oe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Le(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||De(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Re(e){return function(e){if(Array.isArray(e))return Te(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||De(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function De(e,t){if(e){if("string"==typeof e)return Te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Te(e,t):void 0}}function Pe(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}function Ee(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}var Fe=!1,ze=!1,Me=60,Be=(0,T.observer)(function(){var e=xe().styles,t=Le((0,a.useState)(Fe),2),n=t[0],i=t[1],o=Le((0,a.useState)({room_name:"客餐厅",room_id:0,roomNum:1}),2),s=o[0],c=o[1],d=Le((0,a.useState)(!0),2),f=(d[0],d[1],Le((0,a.useState)(!1),2)),h=f[0],m=f[1],p=Le((0,a.useState)([]),2),g=p[0],y=p[1],b=Le((0,a.useState)(""),2),v=b[0],x=b[1],j=Le((0,a.useState)([{id:"Default",name:"默认数据集"}]),2),_=j[0],w=j[1],S=Le((0,a.useState)("Default"),2),k=S[0],I=S[1],T=Le((0,a.useState)(0),2),N=T[0],C=T[1],O=Le((0,a.useState)([{name:"广州市",code:"440100"}]),2),L=O[0],R=O[1],D=Le((0,a.useState)(!0),2),P=D[0],E=D[1],F=(0,a.useRef)(null),z=(0,a.useRef)(null),M="lastBuildingId",B="lastDatasetId",U=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e=e||k;var t=_.find(function(t){return t.id===e});return(null==t?void 0:t.name)||"默认数据集"},H=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Ce(function(){var n,r,a,i,o,l,d,f,h;return Pe(this,function(m){switch(m.label){case 0:return r=u.nb.instance.layout_container,(0,je.Ri)("authCode"),a=t.room_name||s.room_name,i=void 0===t.id?s.room_id:t.id,o=1,[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 1:return(d=null===(n=m.sent())||void 0===n?void 0:n.houseInfo)?[3,3]:[4,A.h.instance.makeHouseTestingInfoDataByBuildingId(e)];case 2:d=m.sent(),m.label=3;case 3:return(l=d)&&l.schemeXmlJson?[4,we.n.instance.addData({id:e,houseInfo:l},we.n.BuildingSchemeDataTable)]:[3,6];case 4:return m.sent(),[4,we.n.instance.addData({id:M,buildingId:e,roomInfo:{room_name:a,room_id:i,roomNum:o}},we.n.DefaultTable)];case 5:m.sent(),r.fromXmlSchemeData(l.schemeXmlJson,!0,Ie.N.LayoutLibrary),x(e),f=r._rooms.filter(function(e){return e.roomname.includes(a)}),o=f.length,(h=f[i]||f[0]||null)&&localStorage&&localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(h.exportExtRoomData())),(null==t?void 0:t.no_auto_layout)||u.nb.DispatchEvent(ke.c.TestingDatasetListOnRoomLoaded,!0),u.nb.instance.update(),m.label=6;case 6:return c({room_name:a,room_id:i,roomNum:o}),[2]}})})()},J=function(e){F&&F.current&&(F.current.innerHTML=e)},V=function(){return Ce(function(){var e,t,n,r,a,i,o,l,s;return Pe(this,function(c){switch(c.label){case 0:return t=JSON.parse,[4,(0,Ae.L7)(".json","Text")];case 1:if(e=t.apply(JSON,[c.sent().content]),u=e,null!=(d=Array)&&"undefined"!=typeof Symbol&&d[Symbol.hasInstance]?d[Symbol.hasInstance](u):u instanceof d){n=e,r=!0,a=!1,i=void 0;try{for(o=n[Symbol.iterator]();!(r=(l=o.next()).done);r=!0)(s=l.value).houseInfo&&s.id&&we.n.instance.addData(s,we.n.BuildingSchemeDataTable)}catch(e){a=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(a)throw i}}}return[2]}var u,d})})()},G=function(e){return Ce(function(){var t,n;return Pe(this,function(r){switch(r.label){case 0:return[4,we.n.instance.getTestingDatasetById(e)];case 1:return(n=(null===(t=r.sent())||void 0===t?void 0:t.buildingList)||[])&&(y(n),a=e,Ce(function(){return Pe(this,function(e){switch(e.label){case 0:return I(a),[4,we.n.instance.addData({id:B,dataset_id:a},we.n.DefaultTable)];case 1:return e.sent(),[2]}})})()),[2]}var a})})()},W=function(){return Ce(function(){var e,t,n;return Pe(this,function(r){switch(r.label){case 0:return[4,we.n.instance.getTestingDatasetList()];case 1:return e=r.sent(),w(e),t=k,!e.find(function(e){return e.id==k})&&e.length>0&&(n=e[0].id,I(n)),[2,t]}})})()};(0,a.useEffect)(function(){u.nb.on_M(ke.I.ShowTestingDatasetListPanel,"TestingDatasetListPanel",function(e){i(Fe=!Fe);Ce(function(){var e,t;return Pe(this,function(n){switch(n.label){case 0:return[4,W()];case 1:return n.sent(),[4,we.n.instance.getDataById(B,we.n.DefaultTable)];case 2:return t=(null===(e=n.sent())||void 0===e?void 0:e.dataset_id)||null,[4,G(t=t||k)];case 3:return n.sent(),[2]}})})(),Ce(function(){var e,t,n;return Pe(this,function(r){switch(r.label){case 0:return[4,we.n.instance.getDataById(M,we.n.BuildingSchemeDataTable)];case 1:return(e=r.sent()||null)&&((t=e.buildingId)&&x(t),(n=e.roomInfo)&&c(n)),[2]}})})()})},[]);for(var $=[],K=0;K<(s.roomNum||1);K++)$.push(K);var X=1e3,q=Math.floor(g.length/X)+1,Z=N;Z>=q&&(Z=q-1);for(var Y=[],ee=[],te=0;te<X;te++){var ne=g[Z*X+te];ne&&Y.push(ne)}for(var re=0;re<q;re++)ee.push(re);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"quickRoomTestingPanel",className:e.leftPanel,style:{display:n?"block":"none",position:"absolute",left:"0",top:"0",zIndex:10001,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"90px",paddingTop:"10px",lineHeight:"25px"},children:[(0,r.jsx)("select",{name:"dataset_list",onChange:function(e){G(e.target.value)},defaultValue:k,children:_.map(function(e,t){return(0,r.jsx)("option",{value:e.id,children:e.name},"dataset_"+t)})}),(0,r.jsx)(l.A,{onClick:function(){m(!0)},children:"编辑数据集"}),(0,r.jsx)(l.A,{onClick:function(){u.nb.emit_M(ke.I.ShowTestingDatasetListPanel,!1)},children:"关闭"}),(0,r.jsx)("br",{}),(0,r.jsx)("select",{name:"pageSelect",onChange:function(e){C(~~e.target.value)},children:ee.map(function(e){return(0,r.jsxs)("option",{value:e,children:["第",e+1,"页"]},"pageSelect"+e)})}),(0,r.jsx)("select",{name:"roomName",value:s.room_name,onChange:function(e){return Ce(function(){return Pe(this,function(t){return H(v,{room_name:e.target.value}),[2]})})()},children:["客餐厅","卫生间","厨房","卧室","入户花园"].map(function(e,t){return(0,r.jsx)("option",{value:e,children:e},"room_name"+t)})}),(0,r.jsx)("select",{name:"roomId",value:s.room_id,onChange:function(e){return Ce(function(){return Pe(this,function(t){return H(v,{id:~~e.target.value}),[2]})})()},children:$.map(function(e,t){return(0,r.jsxs)("option",{value:e,children:[e,":",s.roomNum-1]},"room_id_"+t)})})]}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 150px)"},children:Y.map(function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(e.buildingRoomId===v?"checked":""),onClick:function(){return H(e.buildingRoomId)},children:[N*X+t+1,":",e.buildingRoomId,"    ",e.cityName,(0,r.jsx)("br",{}),"     ",e.buildingName||"","    ",e.area?e.area+"m²":""]},"buildId_"+t)})})]}),h&&(0,r.jsxs)("div",{className:"DistrictPopUp",style:{position:"fixed",zIndex:10001,width:"900px",top:"0px",left:"300px"},children:[(0,r.jsx)("div",{className:"closeBtn",style:{position:"absolute",top:"40px",right:"30px",fontSize:"16px",cursor:"pointer",zIndex:10001},onClick:function(){return m(!1)},children:"X"}),(0,r.jsxs)("div",{className:e.dialogInputs,children:[(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 测试集名称: "}),(0,r.jsx)("input",{ref:z,defaultValue:U(),onChange:function(e){!function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t=t||k;var n=_.find(function(e){return e.id===t});n&&(n.name=e,w(Re(_)))}(e.target.value)}}),(0,r.jsx)("span",{children:" 当前总数 "}),(0,r.jsxs)("span",{children:[" ",g.length]}),"    ",(0,r.jsx)(l.A,{onClick:function(){Ce(function(){var e,t;return Pe(this,function(n){switch(n.label){case 0:return e=(0,Q.AU)(),(t=prompt("数据集名称",e))?(z&&z.current&&(z.current.value=t),[4,we.n.instance.addTestingDataset({id:e,name:t})]):[2];case 1:return n.sent(),[4,W()];case 2:return n.sent(),[4,G(e)];case 3:return n.sent(),[2]}})})()},children:" 新建数据集 "}),(0,r.jsx)(l.A,{onClick:function(){Ce(function(){var e;return Pe(this,function(t){switch(t.label){case 0:return"Default"===k?(alert("默认数据集无法删除"),[2]):confirm("确认删除数据集"+k+" "+U()+"?")?[4,we.n.instance.removeTestingDataset(k)]:[2];case 1:return t.sent(),[4,W()];case 2:return e=t.sent(),[4,G(e)];case 3:return t.sent(),[2]}})})()},children:"删除数据集"}),(0,r.jsx)("span",{ref:F})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)(l.A,{onClick:function(){return Ce(function(){var e,t,n,r,a;return Pe(this,function(i){switch(i.label){case 0:return(e=window.prompt("请输入户型ID",""))&&e.length>0?g.find(function(t){return t.buildingRoomId===e})?(H(e),[3,6]):[3,1]:[3,6];case 1:return[4,H(e)];case 2:return i.sent(),[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 3:return(n=null===(t=i.sent())||void 0===t?void 0:t.houseInfo)?(r=Re(g),a=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Oe(e,t,n[t])})}return e}({},n),a.schemeXmlJson&&delete a.schemeXmlJson,r.push(a),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:r})]):[3,5];case 4:return i.sent(),y(r),alert("添加成功"),[3,6];case 5:alert("添加户型失败"),i.label=6;case 6:return[2]}})})()},children:"手动添加"}),(0,r.jsx)(l.A,{onClick:function(){confirm("开始执行自动添加?")&&Ce(function(){var e,t,n,r,a,i,o,l,s,c,u,d;return Pe(this,function(f){switch(f.label){case 0:if(ze)return[2];ze=!0,e=Re(g),t=e.length,n=t,r=Me/20,a=["","保","绿","新","恒大","城投","万科","碧桂","区","花"],i=!0,o=!1,l=void 0,f.label=1;case 1:f.trys.push([1,6,7,8]),s=function(){var t,i,o,l,s;return Pe(this,function(c){switch(c.label){case 0:t=u.value,i=0,o=1,c.label=1;case 1:if(!(o<=r))return[3,10];if(e.length>5e3)return[3,10];J("   第".concat(o,"次请求 ")+t.name+", 数量"+i),l=1,c.label=2;case 2:return l<=4?[4,_e.Q.search(a[o-1]||"",t.code,20,l)]:[3,8];case 3:return((null==(s=c.sent())?void 0:s.records)||[]).forEach(function(t){e.find(function(e){return e.buildingRoomId===t.id})||(t.buildingRoomId=t.id,e.push(t),i++)}),J("   第".concat(o,"次请求 ")+t.name+", 数量"+i),e.length>n?[4,(0,Q.IP)(200)]:[3,6];case 4:return c.sent(),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:e})];case 5:c.sent(),n=e.length,y(Re(e)),c.label=6;case 6:if(i>=Me)return[3,8];c.label=7;case 7:return l++,[3,2];case 8:if(i>=Me)return[3,10];c.label=9;case 9:return o++,[3,1];case 10:return[2]}})},c=L[Symbol.iterator](),f.label=2;case 2:return(i=(u=c.next()).done)?[3,5]:[5,Ee(s())];case 3:f.sent(),f.label=4;case 4:return i=!0,[3,2];case 5:return[3,8];case 6:return d=f.sent(),o=!0,l=d,[3,8];case 7:try{i||null==c.return||c.return()}finally{if(o)throw l}return[7];case 8:return J(""),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:e})];case 9:return f.sent(),y(Re(e)),confirm("新增户型"+(e.length-t)+"个"),ze=!1,[2]}})})()},children:" 自动添加 "}),(0,r.jsx)(l.A,{onClick:function(){Ce(function(){var e,t,n;return Pe(this,function(r){switch(r.label){case 0:return e=prompt("请输出初始id:","0"),t=parseInt(e),isNaN(t)||t>=g.length?[2]:(t<0&&(t=0),(n=Re(g)).splice(t,g.length),[4,we.n.instance.addTestingDataset({id:k,name:U(),buildingList:n})]);case 1:return r.sent(),y(n),[2]}})})()},children:" 清空列表 "}),(0,r.jsx)(l.A,{onClick:function(){we.n.instance.exportAllTestingDataset()},children:" 导出数据集 "}),(0,r.jsx)(l.A,{onClick:function(){return Ce(function(){var e;return Pe(this,function(t){switch(t.label){case 0:return[4,(0,Ae.L7)(".json","Text")];case 1:e=t.sent().content,t.label=2;case 2:return t.trys.push([2,5,,6]),[4,we.n.instance.importTestingDataset(JSON.parse(e))];case 3:return t.sent(),[4,G(k)];case 4:return t.sent(),[3,6];case 5:return t.sent(),[3,6];case 6:return[2]}})})()},children:" 导入数据集 "}),(0,r.jsx)(l.A,{onClick:function(){Ce(function(){var e,t,n,r,a,i,o,l;return Pe(this,function(s){switch(s.label){case 0:e=0,t=!0,n=!1,r=void 0,s.label=1;case 1:s.trys.push([1,6,7,8]),a=g[Symbol.iterator](),s.label=2;case 2:return(t=(i=a.next()).done)?[3,5]:(o=i.value,J("缓存"+e+"/"+g.length+": "+o.buildingRoomId+" "+o.buildingName),[4,H(o.buildingRoomId,{no_auto_layout:!0})]);case 3:s.sent(),e++,s.label=4;case 4:return t=!0,[3,2];case 5:return[3,8];case 6:return l=s.sent(),n=!0,r=l,[3,8];case 7:try{t||null==a.return||a.return()}finally{if(n)throw r}return[7];case 8:return J(""),[2]}})})()},children:" 缓存数据 "}),(0,r.jsx)(l.A,{onClick:function(){Ce(function(){var e;return Pe(this,function(t){switch(t.label){case 0:return[4,we.n.instance.getAll(we.n.BuildingSchemeDataTable)];case 1:return e=t.sent(),(0,Ae.c6)(JSON.stringify(e),"buildingData.json","text/json"),[2]}})})()},children:" 导出户型缓存 "}),(0,r.jsx)(l.A,{onClick:function(){V()},children:" 导入户型缓存 "})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 目标城市 "}),(0,r.jsx)("span",{children:L.map(function(e,t){return(0,r.jsxs)("span",{onClick:function(){var t=Re(L).filter(function(t){return t.code!==e.code});R(t)},children:[" ",e.name," "]},"city_code_name_"+t)})}),"      ",(0,r.jsx)("input",{type:"number",step:20,min:20,max:500,defaultValue:Me,onChange:function(e){Me=~~e.target.value}}),"请求/每城市",(0,r.jsx)("span",{style:{float:"right",marginRight:20,color:"#07f"},onClick:function(){E(!P)},children:P?"收起列表":"展开列表"})]})]}),(0,r.jsx)(Se.Z,{is_visible:h&&P,onSelected:function(e){var t=Re(L);t.find(function(t){return t.code===e.code})||(t.push(e),R(t))}})]})]})}),Ue=n(41140),He=(n(74376),n(65640));function Je(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ve(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Ge(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){Ve(i,r,a,o,l,"next",e)}function l(e){Ve(i,r,a,o,l,"throw",e)}o(void 0)})}}function We(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){We(e,t,n[t])})}return e}function Ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Je(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Je(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xe(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var qe=function(){var e=[],t=(0,I.A)().styles,n=Ke((0,a.useState)(!1),2),i=(n[0],n[1],Ke((0,a.useState)(null),2)),o=i[0],l=i[1],s=Ke((0,a.useState)(e),2),c=s[0],f=s[1],h=Ke((0,a.useState)(null),2),m=h[0],p=h[1],g=Ke((0,a.useState)({pageIndex:1,pageSize:50,platList:[0,1]}),2),y=g[0],b=g[1],v=function(e){return Ge(function(){var t,n,r,a;return Xe(this,function(i){switch(i.label){case 0:for(var o in n=$e({},e))void 0===n[o]&&delete n[o];return[4,(0,je.Ap)({method:"post",url:"/api/njvr/RoomLayoutTemplate/page",data:n,timeout:3e3}).catch(function(e){return null})];case 1:return(null==(r=i.sent())||null===(t=r.result)||void 0===t?void 0:t.result)&&0!=r.result.result.length?((a=r.result.result)&&a.sort(function(e,t){return t.updateDate.localeCompare(e.updateDate)}),[2,a]):[2,[]]}})})()},x=function(e,t){var n=$e({},y),r=~~t;if(0==t.length)return n[e]&&delete n[e],void b(n);n[e]=r&&r>0&&r<2e3?r:t,b(n)};return u.nb.on_M(d.U.ModelRoomListRightPanel,"RoomTemplateListsPanel",function(e){l(e)}),(0,a.useEffect)(function(){Ge(function(){var t;return Xe(this,function(n){switch(n.label){case 0:return[4,v(y)];case 1:return t=n.sent(),f(e=t||[]),[2]}})})()},[]),(0,r.jsxs)(r.Fragment,{children:[o&&(0,r.jsxs)("div",{className:t.leftPanel,children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"140px"},children:[(0,r.jsx)("span",{children:"当前页"}),(0,r.jsx)("input",{className:"row_input",defaultValue:y.pageIndex,min:1,type:"number",onChange:function(e){return x("pageIndex",e.target.value)}}),(0,r.jsx)("span",{children:"每页显示"}),(0,r.jsx)("input",{className:"row_input",defaultValue:y.pageSize,min:1,type:"number",onChange:function(e){return x("pageSize",e.target.value)}}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"空间类型"}),(0,r.jsxs)("select",{className:"row_input",onChange:function(e){x("roomType",e.target.value)},children:[(0,r.jsx)("option",{value:""}),(0,r.jsx)("option",{value:"客餐厅",children:"客餐厅"}),(0,r.jsx)("option",{value:"卧室",children:"卧室"}),(0,r.jsx)("option",{value:"卫生间",children:"卫生间"}),(0,r.jsx)("option",{value:"阳台",children:"阳台"}),(0,r.jsx)("option",{value:"厨房",children:"厨房"})]}),(0,r.jsx)("br",{}),(0,r.jsx)("span",{children:"schemeId"}),(0,r.jsx)("input",{className:"row_input",style:{width:"200px"},defaultValue:y.schemeId||"",min:1,onChange:function(e){return x("schemeId",e.target.value)}}),(0,r.jsx)("br",{}),(0,r.jsx)("button",{style:{height:"25px",width:"60px",lineHeight:"20px"},onClick:function(){return Ge(function(){var e;return Xe(this,function(t){switch(t.label){case 0:return[4,v(y)];case 1:return e=t.sent(),f(e||[]),[2]}})})()},children:"搜索"})]}),null==c?void 0:c.map(function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+((null==m?void 0:m.id)==e.id?"checked":""),onClick:function(){!function(e){Ge(function(){var t,n,r;return Xe(this,function(a){if(t=u.nb.instance.layout_container,e.templateJson)try{(n=JSON.parse(e.templateJson))&&(r=u.nb.instance.Configs.saving_localstorage_layout_scheme,u.nb.instance.Configs.saving_localstorage_layout_scheme=!1,t.loadRoomEntityFromJson(n),u.nb.instance.Configs.saving_localstorage_layout_scheme=r)}catch(e){}return p(e),[2]})})()}(e)},children:[e.schemeId,"-",e.roomName," ",(null==e?void 0:e.nickName)||""]},"room_template"+t)})]}),(0,r.jsx)(r.Fragment,{children:o&&(0,r.jsxs)("div",{className:t.rightPanel,children:[m&&["id","layoutSchemeId","schemeId","roomId","nickName","houseTypeId","roomType","roomName","platform","codeA","codeB","codeC","createUser","createDate","updateDate"].map(function(e,t){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsxs)("span",{children:[e,": "]}),(0,r.jsx)("span",{children:m[e]})]},e+t)}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"编辑布局模板"}),(0,r.jsx)("button",{onClick:function(){return Ge(function(){var e,t;return Xe(this,function(n){return m?(e="/Home?importType=importHouse&id="+m.id,t=document.createElement("a"),document.body.appendChild(t),t.href=e,t.target="_blank",t.click(),document.body.removeChild(t),[2]):[2]})})()},children:"编辑"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"删除布局模板"}),(0,r.jsx)("button",{onClick:function(){var e;m.id&&confirm("确认删除布局模板"+m.id+"?")&&(e=m,Ge(function(){var t,n;return Xe(this,function(r){switch(r.label){case 0:return e.id?[4,(0,je.Ap)({method:"post",url:"/api/njvr/RoomLayoutTemplate/delete",data:{ids:[e.id]},timeout:3e3}).catch(function(e){return null})]:[2];case 1:return t=r.sent(),He.log(t),p(null),[4,v(y)];case 2:return n=r.sent(),f(n||[]),[2]}})})())},children:"删除"})]})]})})]})},Ze=n(88341),Qe=n(80277),Ye=n(1447),et=n(88098),tt=n(72978),nt=n(53232),rt=n(96892),at=n(69802);function it(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ot(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ot(e,t,n[t])})}return e}function st(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function ct(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||dt(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ut(e){return function(e){if(Array.isArray(e))return it(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||dt(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dt(e,t){if(e){if("string"==typeof e)return it(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?it(e,t):void 0}}var ft=function(e){var t,n=e.name,a=e.data,i=e.path,o=e.onValueChange,l=(null===(t=a.name)||void 0===t?void 0:t.value)||n;return void 0!==a.value?(0,r.jsx)(Ze.A.Item,{label:l,style:{maxWidth:400},children:(0,r.jsx)(Qe.A,{value:a.value,onChange:function(e){"number"!=typeof e&&null!==e||o(ut(i).concat(["value"]),e)}})}):a.paramItems?(0,r.jsx)(Ye.A,{title:l,size:"small",style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,border:"1px solid #e8e8e8",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},headStyle:{backgroundColor:"#f0f5ff",fontWeight:"bold",borderBottom:"1px solid #e8e8e8",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"},children:a.paramItems.map(function(e,t){return(0,r.jsx)("div",{style:{marginBottom:8},children:(0,r.jsxs)(et.A,{wrap:!0,children:[(0,r.jsx)(Qe.A,{placeholder:"最小值",value:e.minParam,style:{width:100},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"minParam"]),e)}}),(0,r.jsxs)(tt.A,{value:e.minEqual,onChange:function(e){return o(ut(i).concat(["paramItems",String(t),"minEqual"]),e)},style:{width:60},children:[(0,r.jsx)(tt.A.Option,{value:!0,children:"≤"}),(0,r.jsx)(tt.A.Option,{value:!1,children:"<"})]}),(0,r.jsx)("span",{children:"(参数值)"}),(0,r.jsxs)(tt.A,{value:e.maxEqual,onChange:function(e){return o(ut(i).concat(["paramItems",String(t),"maxEqual"]),e)},style:{width:60},children:[(0,r.jsx)(tt.A.Option,{value:!0,children:"≤"}),(0,r.jsx)(tt.A.Option,{value:!1,children:"<"})]}),(0,r.jsx)(Qe.A,{placeholder:"最大值",value:e.maxParam,style:{width:100},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"maxParam"]),e)}}),"score"in e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"：(得分)"}),(0,r.jsx)(Qe.A,{placeholder:"得分",value:e.score,style:{width:80},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"score"]),e)}})]}),"minScore"in e&&"maxScore"in e&&"intervalNum"in e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"：(最小得分)"}),(0,r.jsx)(Qe.A,{placeholder:"最小得分",value:e.minScore,style:{width:80},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"score"]),e)}}),(0,r.jsx)("span",{children:"：(最大得分)"}),(0,r.jsx)(Qe.A,{placeholder:"最大得分",value:e.maxScore,style:{width:80},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"score"]),e)}}),(0,r.jsx)("span",{children:"：(分段数)"}),(0,r.jsx)(Qe.A,{placeholder:"分段数",value:e.intervalNum,style:{width:80},onChange:function(e){"number"==typeof e&&o(ut(i).concat(["paramItems",String(t),"score"]),e)}})]})]})},t)})}):a.child?(0,r.jsx)(Ye.A,{title:l,size:"small",style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,border:"1px solid #e8e8e8",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},headStyle:{backgroundColor:"#f6ffed",fontWeight:"bold",borderBottom:"1px solid #e8e8e8",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"},children:Object.entries(a.child).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ft,{name:n,data:a,path:ut(i).concat(["child",n]),onValueChange:o},n)})}):null},ht=function(e){var t,n=e.groupName,a=e.groupData,i=e.path,o=e.onValueChange,l=(null===(t=a.name)||void 0===t?void 0:t.value)||n;return(0,r.jsx)(Ye.A,{title:l,style:{marginBottom:16,backgroundColor:"#f7f7f7",maxWidth:800,margin:"0 auto 16px auto",border:"1px solid #d9d9d9",borderRadius:"8px",boxShadow:"0 2px 12px rgba(0,0,0,0.08)"},styles:{header:{backgroundColor:"#e6f7ff",fontWeight:"bold",borderBottom:"1px solid #d9d9d9",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"}},children:Object.entries(a).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return"name"===n?null:(0,r.jsx)(ft,{name:n,data:a,path:ut(i).concat([n]),onValueChange:o},n)})})},mt=function(e){var t=ct((0,a.useState)(null),2),n=t[0],i=t[1],o=(0,I.A)().styles,s=(0,at.B)().t;(0,a.useEffect)(function(){c()},[]);var c=function(){var e=nt.TLayoutParamConfigurationManager.instance.getRoomParamConfigs(),t={bathRoomParamConfig:{data:{}},livingRoomParamConfig:{data:{}},kitchenRoomParamConfig:{data:{}},bedRoomParamConfig:{data:{}},entranceRoomParamConfig:{data:{}}},n=function(e){return e.reduce(function(e,t){var n=ct(t,2),r=n[0],a=n[1];if(!a)return e;e[r]={data:{}},a.name&&(e[r].data.name=a.name);var i=function(e){if(!e)return null;var t={};return e.name&&(t.name=e.name),e.paramItems&&(t.paramItems=e.paramItems),void 0!==e.value&&(t.value=e.value),e.child&&(t.child={},Object.entries(e.child).forEach(function(e){var n=ct(e,2),r=n[0],a=n[1];t.child[r]=i(a)})),t};return a&&Object.entries(a).forEach(function(t){var n=ct(t,2),a=n[0],o=n[1];"name"!==a&&o&&(e[r].data[a]=i(o))}),e},{})};e.forEach(function(e){var r=e.getRuleParamGroupConfigs(),a=Array.from(r.entries());e.getRoomType()===rt.RoomLayoutScoreType.k_bathRoom?t.bathRoomParamConfig.data=n(a):e.getRoomType()===rt.RoomLayoutScoreType.k_livingRoom?t.livingRoomParamConfig.data=n(a):e.getRoomType()===rt.RoomLayoutScoreType.k_kitchenRoom?t.kitchenRoomParamConfig.data=n(a):e.getRoomType()===rt.RoomLayoutScoreType.k_bedRoom?t.bedRoomParamConfig.data=n(a):e.getRoomType()===rt.RoomLayoutScoreType.k_entranceRoom&&(t.entranceRoomParamConfig.data=n(a))}),i(t)},u=function(e,t){i(function(n){if(!n)return null;for(var r=lt({},n),a=r,i=[],o=0;o<e.length-1;o++)i.push(a),a=a[e[o]];var l,s=a[e[e.length-1]];"object"==(void 0===s?"undefined":(l=s)&&"undefined"!=typeof Symbol&&l.constructor===Symbol?"symbol":typeof l)&&null!==s&&"name"in s?a[e[e.length-1]]=st(lt({},s),{value:t}):a[e[e.length-1]]=t;var c=nt.TLayoutParamConfigurationManager.instance.getRoomParamConfigs(),u=ct(e[0].split("ParamConfig"),1)[0],d={bathRoom:rt.RoomLayoutScoreType.k_bathRoom,livingRoom:rt.RoomLayoutScoreType.k_livingRoom,kitchenRoom:rt.RoomLayoutScoreType.k_kitchenRoom,bedRoom:rt.RoomLayoutScoreType.k_bedRoom}[u],f=c.find(function(e){return e.getRoomType()===d});if(f){var h=e[2],m=r[e[0]].data[h].data,p=Object.entries(m).reduce(function(e,t){var n=ct(t,2),r=n[0],a=n[1];return"name"===r||(e[r]=a),e},{});f.setRuleParamGroupConfig(h,p)}return r})};if(!n)return(0,r.jsxs)("div",{children:[s("加载中"),"..."]});var d="livingRoom";return e.defaultRoomKey&&(d={"客餐厅":"livingRoom","卧室":"bedRoom","厨房":"kitchenRoom","入户花园":"entranceRoom","卫生间":"bathRoom"}[e.defaultRoomKey]||e.defaultRoomKey||d),(0,r.jsxs)("div",{style:{position:"absolute",left:"50%",top:0,width:"100%",height:"100%",backgroundColor:"#fff",padding:"20px",zIndex:1e3,maxWidth:1e3,transform:"translate(-50%, 0px)"},children:[(0,r.jsx)("div",{style:{position:"absolute",left:0,right:0,top:0,bottom:50},children:(0,r.jsx)(h.A,{className:o.score_tabs,defaultActiveKey:"livingRoom",items:[{key:"bathRoom",label:"卫生间参数配置",children:Object.entries(n.bathRoomParamConfig.data).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:a.data,path:["bathRoomParamConfig","data",n,"data"],onValueChange:u},n)})},{key:"livingRoom",label:"客餐厅参数配置",children:Object.entries(n.livingRoomParamConfig.data).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:a.data,path:["livingRoomParamConfig","data",n,"data"],onValueChange:u},n)})},{key:"kitchenRoom",label:"厨房参数配置",children:Object.entries(n.kitchenRoomParamConfig.data).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:a.data,path:["kitchenRoomParamConfig","data",n,"data"],onValueChange:u},n)})},{key:"bedRoom",label:"卧室参数配置",children:Object.entries(n.bedRoomParamConfig.data).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:a.data,path:["bedRoomParamConfig","data",n,"data"],onValueChange:u},n)})},{key:"entranceRoom",label:"入户花园参数配置",children:Object.entries(n.entranceRoomParamConfig.data).map(function(e){var t=ct(e,2),n=t[0],a=t[1];return(0,r.jsx)(ht,{groupName:n,groupData:a.data,path:["entranceRoomParamConfig","data",n,"data"],onValueChange:u},n)})}],style:{backgroundColor:"#fafafa"}})}),(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=nt.TLayoutParamConfigurationManager.instance,t=e.toJson();e.saveJson(t)},style:{width:120},children:"保存配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=nt.TLayoutParamConfigurationManager.instance.toFormattedJson(),t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="layoutScoreParamConfig.json",r.click()},style:{width:120},children:"下载配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){nt.TLayoutParamConfigurationManager.instance.resetToDefaultConfig(),c()},style:{width:120},children:"重置为默认配置"})]})})]})},pt=n(12052),gt=n(51187),yt=n(62954),bt=n(49450),vt=n(37032),xt=n(90110),jt=n(87927),_t=n(29686),wt=n(60314),St=n(30895),kt=n(63974),It={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"},At=n(46778),Tt=function(e,t){return a.createElement(At.A,(0,kt.A)({},e,{ref:t,icon:It}))};var Nt=a.forwardRef(Tt),Ct=n(32155),Ot=n(37164),Lt=n(37791),Rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"unordered-list",theme:"outlined"},Dt=function(e,t){return a.createElement(At.A,(0,kt.A)({},e,{ref:t,icon:Rt}))};var Pt=a.forwardRef(Dt),Et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M839.6 433.8L749 150.5a9.24 9.24 0 00-8.9-6.5h-77.4c-4.1 0-7.6 2.6-8.9 6.5l-91.3 283.3c-.3.9-.5 1.9-.5 2.9 0 5.1 4.2 9.3 9.3 9.3h56.4c4.2 0 7.8-2.8 9-6.8l17.5-61.6h89l17.3 61.5c1.1 4 4.8 6.8 9 6.8h61.2c1 0 1.9-.1 2.8-.4 2.4-.8 4.3-2.4 5.5-4.6 1.1-2.2 1.3-4.7.6-7.1zM663.3 325.5l32.8-116.9h6.3l32.1 116.9h-71.2zm143.5 492.9H677.2v-.4l132.6-188.9c1.1-1.6 1.7-3.4 1.7-5.4v-36.4c0-5.1-4.2-9.3-9.3-9.3h-204c-5.1 0-9.3 4.2-9.3 9.3v43c0 5.1 4.2 9.3 9.3 9.3h122.6v.4L587.7 828.9a9.35 9.35 0 00-1.7 5.4v36.4c0 5.1 4.2 9.3 9.3 9.3h211.4c5.1 0 9.3-4.2 9.3-9.3v-43a9.2 9.2 0 00-9.2-9.3zM416 702h-76V172c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v530h-76c-6.7 0-10.5 7.8-6.3 13l112 141.9a8 8 0 0012.6 0l112-141.9c4.1-5.2.4-13-6.3-13z"}}]},name:"sort-ascending",theme:"outlined"},Ft=function(e,t){return a.createElement(At.A,(0,kt.A)({},e,{ref:t,icon:Et}))};var zt=a.forwardRef(Ft),Mt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M839.6 433.8L749 150.5a9.24 9.24 0 00-8.9-6.5h-77.4c-4.1 0-7.6 2.6-8.9 6.5l-91.3 283.3c-.3.9-.5 1.9-.5 2.9 0 5.1 4.2 9.3 9.3 9.3h56.4c4.2 0 7.8-2.8 9-6.8l17.5-61.6h89l17.3 61.5c1.1 4 4.8 6.8 9 6.8h61.2c1 0 1.9-.1 2.8-.4 2.4-.8 4.3-2.4 5.5-4.6 1.1-2.2 1.3-4.7.6-7.1zM663.3 325.5l32.8-116.9h6.3l32.1 116.9h-71.2zm143.5 492.9H677.2v-.4l132.6-188.9c1.1-1.6 1.7-3.4 1.7-5.4v-36.4c0-5.1-4.2-9.3-9.3-9.3h-204c-5.1 0-9.3 4.2-9.3 9.3v43c0 5.1 4.2 9.3 9.3 9.3h122.6v.4L587.7 828.9a9.35 9.35 0 00-1.7 5.4v36.4c0 5.1 4.2 9.3 9.3 9.3h211.4c5.1 0 9.3-4.2 9.3-9.3v-43a9.2 9.2 0 00-9.2-9.3zM310.3 167.1a8 8 0 00-12.6 0L185.7 309c-4.2 5.3-.4 13 6.3 13h76v530c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V322h76c6.7 0 10.5-7.8 6.3-13l-112-141.9z"}}]},name:"sort-descending",theme:"outlined"},Bt=function(e,t){return a.createElement(At.A,(0,kt.A)({},e,{ref:t,icon:Mt}))};var Ut=a.forwardRef(Bt),Ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},Jt=function(e,t){return a.createElement(At.A,(0,kt.A)({},e,{ref:t,icon:Ht}))};var Vt=a.forwardRef(Jt),Gt=n(16986),Wt=n(65640);function $t(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Kt(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){$t(i,r,a,o,l,"next",e)}function l(e){$t(i,r,a,o,l,"throw",e)}o(void 0)})}}function Xt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function qt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){qt(e,t,n[t])})}return e}function Qt(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Yt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r;return t=e,r=[{key:"insertLightTemplate",value:function(){var e=Zt({},{dataUrl:"",templateData:"",templateDesc:"",templateImage:"",templateName:"",templateType:1},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});Wt.log("insertLightTemplate 提交数据:",e);try{(0,Gt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/insert",data:Zt({},e),timeout:3e4}).then(function(e){e&&e.success||Wt.error("Fail to insertLightTemplate.")}).catch(function(e){Wt.error(e)})}catch(e){Wt.error(e)}}},{key:"editLightTemplate",value:function(e){return Kt(function(){var t,n,r,a;return Qt(this,function(i){switch(i.label){case 0:if(!e.id)return Wt.error("editLightTemplate: id is required"),[2,Promise.reject(new Error("id is required"))];i.label=1;case 1:return i.trys.push([1,4,,5]),Wt.log("获取模板 ".concat(e.id," 的原有内容")),[4,this.getLightTemplate(e.id)];case 2:return(t=i.sent())?(n=Zt({},t,e),Wt.log("原有模板内容:",t),Wt.log("待修改的参数:",e),Wt.log("合并后的提交数据:",n),[4,(0,Gt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/edit",data:n,timeout:3e4})]):(Wt.error("未找到id为 ".concat(e.id," 的模板")),[2,Promise.resolve(!0)]);case 3:return(r=i.sent())&&r.success?[2,!1]:(Wt.error("Fail to editLightTemplate."),[2,!0]);case 4:return a=i.sent(),Wt.error("编辑模板时发生错误:",a),[2,Promise.reject(a)];case 5:return[2]}})}).call(this)}},{key:"getLightTemplate",value:function(e){var t={id:e};return Wt.log("getLightTemplate 请求参数:",t),(0,Gt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/get",data:t,timeout:3e4}).then(function(t){return t&&t.success?(Wt.log("getLightTemplate 成功获取模板 ".concat(e," 内容:"),t.result),t.result):(Wt.error("Fail to getLightTemplate."),null)}).catch(function(e){return Wt.error("getLightTemplate 发生错误:",e),null})}},{key:"listLightTemplate",value:function(e){return Kt(function(){var t;return Qt(this,function(n){return t=e||{},[2,(0,Gt.Ap)({method:"post",url:"/api/njvr/layoutLightTemplate/listByPage",data:Zt({},t),timeout:3e4}).then(function(e){return e&&e.success?(Wt.log("listLightTemplate response:",e.result.result),e.result):(Wt.error("Fail to listLightTemplate."),null)})]})})()}},{key:"delete",value:function(e){return Kt(function(){return Qt(this,function(t){return[2,(0,Gt.Ap)({method:"post",url:"/api/njvr/LayoutLightTemplate/delete",data:{ids:e},timeout:3e4}).then(function(e){return e&&e.success?e.result:(Wt.error("Fail to delete light template."),null)})]})})()}}],(n=null)&&Xt(t.prototype,n),r&&Xt(t,r),e}(),en=n(44497),tn=n(65640);function nn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function rn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function an(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function on(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){an(e,t,n[t])})}return e}function ln(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function sn(e){return function(e){if(Array.isArray(e))return nn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return nn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nn(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var cn=[{groupName:"客餐厅区域灯光",groupData:[{lightName:"吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅门洞灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅阳台门灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅边缘灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"床体灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"书房区域",groupData:[{lightName:"书房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室区域",groupData:[{lightName:"茶室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"环境光",groupData:[{lightName:"环境光强度",brightness:50,color:"#F5F0E1"},{lightName:"阳光强度",brightness:50,color:"#F5F0E1"}]}],un=[{groupName:"客餐厅区域灯光",groupData:[{lightName:"客餐厅吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"电视柜灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"床尾顶光",brightness:50,color:"#F5F0E1"},{lightName:"床尾侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室/书房区域灯光",groupData:[{lightName:"茶室和书房吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]}],dn=[{name:"吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:10,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"客餐厅|卧室|主卧|次卧|客卧"}},{typeId:101,name:"过道灯光",lighting:{type:1,color:16777215,intensity:.8,length:"25%",width:"90%"},pose:{z:2370},condition:{spaceArea:"过道区"}},{name:"沙发灯光",typeId:2,category:"沙发",lighting:{type:1,color:16777215,intensity:6,width:"80%",length:"50%"},pose:{z:2350,gapOffset:100},condition:{spaceArea:"客厅区"}},{name:"沙发侧光",typeId:102,category:"茶几",lighting:{type:1,color:16777215,intensity:2,width:600,length:600},pose:{gapOffset:550,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅窗户灯光",typeId:103,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅门洞灯光",typeId:104,lighting:{type:1,color:16777215,intensity:4,width:"40%",length:"40%"},pose:{norOffset:300,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅阳台门灯光",typeId:105,lighting:{type:1,color:16777215,intensity:4,width:"40%",length:"40%"},pose:{norOffset:300,floorOffset:1e3,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"客厅边缘灯光",typeId:106,lighting:{type:1,color:16777215,intensity:2.5,width:"40%",length:"40%"},pose:{z:1300,lookAt:"center"},condition:{spaceArea:"客厅区"}},{name:"餐厅窗户灯光",typeId:107,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{spaceArea:"餐厅区"}},{name:"餐桌灯光",typeId:3,category:"餐桌",lighting:{type:1,color:16777215,intensity:2,width:"35%",length:"35%"},pose:{z:2370},condition:{spaceArea:"餐厅区"}},{name:"厨房窗户灯光",typeId:108,lighting:{type:1,color:16777215,intensity:24,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"厨房"}},{name:"厨房灯光",typeId:4,lighting:{type:1,color:16777215,intensity:10,width:"30%",length:"30%"},pose:{z:2270},condition:{roomName:"厨房"}},{name:"卧室窗户灯光",typeId:109,lighting:{type:1,color:16777215,intensity:8,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"床体灯光",typeId:110,category:"床",lighting:{type:1,color:16777215,intensity:6,width:"40%",length:"40%"},pose:{z:2370,align:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"卫生间窗户灯光",typeId:111,lighting:{type:1,color:16777215,intensity:8,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"卫生间"}},{name:"卫生间灯光",typeId:5,lighting:{type:1,color:16777215,intensity:8,width:"30%",length:"30%"},condition:{roomName:"卫生间"}},{name:"书房窗户灯光",typeId:112,lighting:{type:1,color:16777215,intensity:6,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"书房"}},{name:"书桌灯光",typeId:6,category:"书桌",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"书房|茶室"}},{name:"茶室窗户灯光",typeId:113,lighting:{type:1,color:16777215,intensity:4,width:"140%",length:"140%"},pose:{norOffset:300,floorOffset:1500,lookAt:"center"},condition:{roomName:"茶室"}},{name:"茶台灯光",typeId:7,category:"茶台",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"茶室"}}],fn=[{name:"客餐厅吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:11,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"客餐厅"}},{name:"卧室吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:6,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"茶室和书房吊顶灯槽灯光",typeId:1,lighting:{type:1,color:16777215,intensity:6,targetObjectName:"id5251202_Node_3",materialId:"368346740",length:16,width:16},condition:{roomName:"书房|茶室"}},{name:"沙发灯光",typeId:2,category:"沙发",lighting:{type:1,color:16777215,intensity:8,width:"80%",length:"50%"},pose:{z:2150,gapOffset:100},condition:{spaceArea:"客厅区"}},{typeId:201,name:"过道灯光",lighting:{type:1,color:16777215,intensity:.8,length:"25%",width:"90%"},pose:{z:2370},condition:{spaceArea:"过道区"}},{name:"电视柜灯光",typeId:202,category:"电视柜",lighting:{type:1,color:16777215,intensity:6,width:"80%",length:"50%"},pose:{z:2150,gapOffset:425},condition:{spaceArea:"客厅区"}},{name:"餐桌灯光",typeId:3,category:"餐桌",lighting:{type:1,color:16777215,intensity:6,width:"100%",length:"100%"},pose:{z:2150},condition:{spaceArea:"餐厅区"}},{name:"床尾顶光",typeId:203,category:"床",lighting:{type:1,color:16777215,intensity:7,width:"50%",length:"50%"},pose:{z:2500,align:"bottom"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"床尾侧光",typeId:204,category:"床",lighting:{type:1,color:16777215,intensity:5,width:"50%",length:"50%"},pose:{gapOffset:1200,floorOffset:1e3,lookAt:"center"},condition:{roomName:"卧室|主卧|次卧|客卧"}},{name:"厨房灯光",typeId:4,lighting:{type:1,color:16777215,intensity:45,width:"30%",length:"30%"},pose:{z:2270},condition:{roomName:"厨房"}},{name:"卫生间灯光",typeId:5,lighting:{type:1,color:16777215,intensity:26,width:"30%",length:"30%"},condition:{roomName:"卫生间"}},{name:"书桌灯光",typeId:6,category:"书桌",lighting:{type:1,color:16777215,intensity:28,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"书房|茶室"}},{name:"茶台灯光",typeId:7,category:"茶台",lighting:{type:1,color:16777215,intensity:2,width:"40%",length:"40%"},pose:{z:2370},condition:{roomName:"茶室"}}],hn=new(function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),an(this,"cachedImage",null),an(this,"templateImage",null),an(this,"dayLightConfigs",[{groupName:"客餐厅区域灯光",groupData:[{lightName:"吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅门洞灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅阳台门灯光",brightness:50,color:"#F5F0E1"},{lightName:"客厅边缘灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"床体灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"书房区域",groupData:[{lightName:"书房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室区域",groupData:[{lightName:"茶室窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间窗户灯光",brightness:50,color:"#F5F0E1"},{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"环境光",groupData:[{lightName:"环境光强度",brightness:50,color:"#F5F0E1"},{lightName:"阳光强度",brightness:50,color:"#F5F0E1"}]}]),an(this,"nightLightConfigs",[{groupName:"客餐厅区域灯光",groupData:[{lightName:"客餐厅吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"电视柜灯光",brightness:50,color:"#F5F0E1"},{lightName:"餐桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"沙发灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卧室区域灯光",groupData:[{lightName:"卧室吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"床尾顶光",brightness:50,color:"#F5F0E1"},{lightName:"床尾侧光",brightness:50,color:"#F5F0E1"}]},{groupName:"茶室/书房区域灯光",groupData:[{lightName:"茶室和书房吊顶灯槽灯光",brightness:50,color:"#F5F0E1"},{lightName:"书桌灯光",brightness:50,color:"#F5F0E1"},{lightName:"茶台灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"公共区域",groupData:[{lightName:"过道灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"厨房区域",groupData:[{lightName:"厨房灯光",brightness:50,color:"#F5F0E1"}]},{groupName:"卫生间区域",groupData:[{lightName:"卫生间灯光",brightness:50,color:"#F5F0E1"}]}]),an(this,"templateData",null),an(this,"dataUrl",null),an(this,"templateInfo",{name:"",category:"dayLight"}),an(this,"templateList",[]),an(this,"updateLightBrightness",function(e,n,r,a){var i="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs;n>=0&&n<i.length&&r>=0&&r<i[n].groupData.length&&(i[n].groupData[r].brightness=a)}),an(this,"updateLightColor",function(e,n,r,a){var i="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs;n>=0&&n<i.length&&r>=0&&r<i[n].groupData.length&&(i[n].groupData[r].color=a)}),an(this,"resetLightConfigs",function(e){"dayLight"===e?t.dayLightConfigs=JSON.parse(JSON.stringify(cn)):t.nightLightConfigs=JSON.parse(JSON.stringify(un))}),an(this,"setDayLightConfigs",function(e){t.dayLightConfigs=e}),an(this,"setNightLightConfigs",function(e){t.nightLightConfigs=e}),an(this,"restoreDefaultConfig",function(){t.dayLightConfigs=JSON.parse(JSON.stringify(cn)),t.nightLightConfigs=JSON.parse(JSON.stringify(un)),t.cachedImage=null,t.templateImage=null,t.templateData=null,t.dataUrl=null,t.templateInfo={name:"",category:"dayLight"}}),an(this,"setCachedImage",function(e,n){t.cachedImage=e}),an(this,"setTemplateImage",function(e,n){t.templateImage=e}),an(this,"setDataUrl",function(e,n){t.dataUrl=e}),an(this,"setCategory",function(e){t.templateInfo.category=e}),an(this,"initTemplateList",function(e){t.templateList=sn(e)}),an(this,"updateTemplateImage",function(e,n){t.templateList=t.templateList.map(function(t){return t.id===e?ln(on({},t),{templateImage:n}):t}),t.setTemplateImage(n)}),an(this,"updateTemplateName",function(e,n){t.templateList=t.templateList.map(function(t){return t.key===e?ln(on({},t),{templateName:n}):t}),t.templateInfo.name=n}),an(this,"deleteTemplate",function(e){t.templateList=t.templateList.filter(function(t){return t.key!==e})}),an(this,"addTemplate",function(e){var n=ln(on({},e),{key:"".concat(Date.now())});t.templateList=sn(t.templateList).concat([n])}),an(this,"formatLightConfigsByCategory",function(e){var n="dayLight"===e?t.dayLightConfigs:t.nightLightConfigs,r="dayLight"===e?dn:fn,a=JSON.parse(JSON.stringify(r)),i=new Map;return n.forEach(function(e,t){e.groupData.forEach(function(e){i.set(e.lightName,{brightness:e.brightness,color:e.color})})}),a.forEach(function(e,n){var r=i.get(e.name);if(r){var a;e.lighting.intensity=r.brightness;var o=r.color.trim();try{if(o.startsWith("rgba("))a=t.rgbaToHex(o);else if(o.startsWith("#")){var l=o.replace("#","");a=parseInt(l,16)}else tn.warn("不支持的颜色格式: ".concat(o,"，使用默认值")),a=16777215;e.lighting.color=a}catch(t){tn.error("颜色转换失败: ".concat(t.message,"，使用默认值")),e.lighting.color=16777215}}}),JSON.stringify(a,null,2)}),an(this,"validateJsonStructure",function(e,t){if(e.length!==t.length)return tn.log("结构验证失败：长度不匹配（导入: ".concat(e.length,", 模板: ").concat(t.length)),!1;for(var n=0;n<e.length;n++){var r=e[n],a=t[n];if(!r.name||!r.lighting)return tn.log("结构验证失败：第".concat(n,"项缺少必要字段")),!1;if(r.name!==a.name)return tn.log("结构验证失败：第".concat(n,"项名称不匹配（导入: ").concat(r.name,", 模板: ").concat(a.name)),!1;if("number"!=typeof r.lighting.intensity||"number"!=typeof r.lighting.color)return tn.log("结构验证失败：第".concat(n,"项灯光属性格式错误")),!1}return!0}),an(this,"convertToLightConfigGroup",function(e){var n=null;t.validateJsonStructure(e,dn)?(tn.log("JSON数据匹配成功：日间灯光"),n="dayLight",t.setCategory("dayLight")):t.validateJsonStructure(e,fn)&&(tn.log("JSON数据匹配成功：夜间灯光"),n="nightLight",t.setCategory("nightLight"));var r="dayLight"===n?cn:un,a=new Map;return e.forEach(function(e){a.set(e.name,{intensity:e.lighting.intensity,color:e.lighting.color})}),r.map(function(e){return ln(on({},e),{groupData:e.groupData.map(function(e){var t=a.get(e.lightName);return t?ln(on({},e),{brightness:t.intensity,color:"#".concat(t.color.toString(16).padStart(6,"0").toUpperCase())}):e})})})}),(0,en.makeAutoObservable)(this)}var t,n,r;return t=e,(n=[{key:"rgbaToHex",value:function(e){var t=e.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]*\s*\)/);if(!t)throw new Error("无效的RGBA颜色格式: ".concat(e));var n=Math.min(255,Math.max(0,parseInt(t[1],10))),r=Math.min(255,Math.max(0,parseInt(t[2],10))),a=Math.min(255,Math.max(0,parseInt(t[3],10))),i=function(e){var t=e.toString(16);return 1===t.length?"0".concat(t):t},o="".concat(i(n)).concat(i(r)).concat(i(a));return parseInt(o,16)}}])&&rn(t.prototype,n),r&&rn(t,r),e}()),mn=n(34746),pn=n(13915),gn=n(65640);function yn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function bn(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function vn(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){bn(i,r,a,o,l,"next",e)}function l(e){bn(i,r,a,o,l,"throw",e)}o(void 0)})}}function xn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){xn(e,t,n[t])})}return e}function _n(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function wn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||kn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sn(e){return function(e){if(Array.isArray(e))return yn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||kn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kn(e,t){if(e){if("string"==typeof e)return yn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?yn(e,t):void 0}}function In(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var An=function(e){return vn(function(){var t,n,r,a,i,o,l,s,c,u,d;return In(this,function(f){switch(f.label){case 0:return[4,(0,pn.o0)(12,e.name)];case 1:return t=f.sent(),n=t.accessKeyId,r=t.expireAt,a=t.readDomain,i=t.policy,o=t.securityToken,l=t.signature,s=t.keyPrefix,c=t.vendor,u=t.contentType,d=t.ossHost,[2,{uploader:new mn.d({contentType:u,policy:i,signature:l,accessKeyId:n,server:d.replace("-internal",""),expireAt:r,securityToken:o,path:s,vendor:c}),readDomain:a,ossHost:d}]}})})()},Tn=function(e){return vn(function(){var t,n,r,a,i;return In(this,function(l){switch(l.label){case 0:return l.trys.push([0,3,,4]),"image/jpeg"===e.type||"image/png"===e.type?[4,An(e)]:(o.A.error("请上传JPG/PNG格式的图片"),[2]);case 1:return t=l.sent(),n=t.uploader,r=t.readDomain,a=t.ossHost,[4,n.upload(e)];case 2:return i=l.sent(),[2,"".concat(r||a.replace("-internal",""),"/").concat(i.key)];case 3:throw l.sent();case 4:return[2]}})})()},Nn=function(e){var t=e.groupName,n=e.groupData,i=e.category,o="dayLight"===i?hn.dayLightConfigs.findIndex(function(e){return e.groupName===t}):hn.nightLightConfigs.findIndex(function(e){return e.groupName===t}),l=wn((0,a.useState)({visible:!1,index:-1}),2),s=l[0],c=l[1],u=wn((0,a.useState)(0),2),d=(u[0],u[1]),f=function(e,t){-1!==o&&(d(t),hn.updateLightBrightness(i,o,e,t))},h=function(e,t){-1!==o&&hn.updateLightColor(i,o,e,t)},m=function(){return(0,r.jsx)(St.sk,{disableColorPanel:!0,onColorChanged:function(e){h(s.index,e)}})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Ye.A,{title:t,style:{marginBottom:16,backgroundColor:"#ffffff",maxWidth:800,margin:"0 auto 16px auto",border:"1px solid #d9d9d9",borderRadius:"8px",boxShadow:"0 2px 12px rgba(0,0,0,0.08)"},styles:{header:{backgroundColor:"#ffffff",fontWeight:"bold",borderBottom:"1px solid #d9d9d9",borderTopLeftRadius:"8px",borderTopRightRadius:"8px"}},children:n.map(function(e,t){return(0,r.jsxs)("div",{style:{padding:"16px",display:"flex",alignItems:"center",gap:"16px",width:"100%",height:"100%",position:"relative"},children:[(0,r.jsx)("div",{style:{width:"30%"},children:(0,r.jsx)("div",{style:{width:"200px",height:"60px",border:"1px solid #000",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"14px",flexShrink:0},children:e.lightName})}),(0,r.jsx)("div",{style:{width:"1px",height:"80px",backgroundColor:"#e8e8e8",flexShrink:0}}),(0,r.jsxs)("div",{style:xn({width:"70%",display:"flex",alignItems:"flex-start",gap:"16px"},"alignItems","center"),children:[(0,r.jsx)(pt.A.Text,{style:{display:"inline-block",marginBottom:"6px",width:"30px"},children:"亮度"}),(0,r.jsx)("div",{style:{flex:1,minWidth:0},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,r.jsx)(gt.A,{min:0,max:100,value:e.brightness,onChange:function(e){return f(t,e)},style:{flex:1,minWidth:0}}),(0,r.jsx)(yt.A,{type:"number",min:0,max:100,value:e.brightness,onChange:function(e){return f(t,Number(e.target.value))},style:{width:"80px"},suffix:"%"})]})}),(0,r.jsx)(pt.A.Text,{style:{display:"inline-block",marginBottom:"6px",width:"30px"},children:"颜色"}),(0,r.jsx)("div",{style:{flex:1,minWidth:0},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:e.color,borderRadius:"4px",border:"1px solid #d9d9d9",cursor:"pointer"},onClick:function(){c({visible:!0,index:t})}}),(0,r.jsx)(yt.A,{value:e.color,onChange:function(e){return h(t,e.target.value)},placeholder:"输入颜色值",style:{flex:1,minWidth:0}})]})})]})]},e.lightName)})}),(0,r.jsx)(bt.A,{open:s.visible,onCancel:function(){c(function(e){return _n(jn({},e),{visible:!1})})},footer:null,children:(0,r.jsx)(m,{})})]})},Cn=function(e){var t=e.templates,n=wn((0,a.useState)(t),2),i=n[0],s=n[1],c=wn((0,a.useState)(""),2),u=c[0],d=c[1],f=wn((0,a.useState)(""),2),h=f[0],m=f[1],p=(0,a.useRef)(null),g=(0,a.useRef)(null),y=wn((0,a.useState)("createTimeDesc"),2),b=y[0],v=y[1];(0,a.useEffect)(function(){hn.initTemplateList(t)},[t]),(0,a.useEffect)(function(){s(t)},[t]);var x=function(e){return vn(function(){var t,n;return In(this,function(r){switch(r.label){case 0:return d(e),[4,Yt.listLightTemplate({templateName:e})];case 1:return(t=r.sent())&&(n=(t.result||[]).map(function(e){return _n(jn({},e),{templateType:1===e.templateType?"日光":"夜光"})}),s(n),hn.initTemplateList(n)),[2]}})})()},j=function(e){return vn(function(){var t,n,r,a;return In(this,function(l){switch(l.label){case 0:if(!g.current)return[2];if(!(r=(null===(n=g.current.input)||void 0===n||null===(t=n.value)||void 0===t?void 0:t.trim())||""))return o.A.error("模板名称不能为空"),[2];l.label=1;case 1:return l.trys.push([1,3,,4]),[4,Yt.editLightTemplate({id:e,templateName:r})];case 2:return l.sent()?o.A.error("名称修改失败"):(s(i.map(function(t){return t.id===e?_n(jn({},t),{templateName:r}):t})),m(""),o.A.success("名称修改成功")),[3,4];case 3:return a=l.sent(),gn.error("修改名称接口调用失败:",a),o.A.error("名称修改失败"),[3,4];case 4:return[2]}})})()},_=[{title:"序号",key:"index",width:"6%",render:function(e,t,n){return(0,r.jsx)("span",{children:n+1})}},{title:"模板封面",dataIndex:"templateImage",key:"templateImage",width:"12%",render:function(e,t){return(0,r.jsxs)("div",{style:{position:"relative",display:"inline-block"},children:[(0,r.jsx)("img",{src:e,alt:"".concat(t.templateName,"的封面"),style:{width:90,height:90,objectFit:"cover",borderRadius:4}}),(0,r.jsx)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:4,opacity:0,transition:"opacity 0.3s",cursor:"pointer"},onMouseEnter:function(e){return e.currentTarget.style.opacity="1"},onMouseLeave:function(e){return e.currentTarget.style.opacity="0"},onClick:function(){var e;return null===(e=p.current)||void 0===e?void 0:e.click()},children:(0,r.jsx)(vt.A,{name:"avatar",listType:"picture-card",showUploadList:!1,beforeUpload:function(e){return function(e,t){return vn(function(){var n;return In(this,function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),[4,Tn(e)];case 1:return n=r.sent(),s(i.map(function(e){return e.id===t?_n(jn({},e),{templateImage:n}):e})),hn.updateTemplateImage(t,n),[4,Yt.editLightTemplate({id:t,templateImage:n})];case 2:return r.sent()||(s(i.map(function(e){return e.id===t?_n(jn({},e),{templateImage:n}):e})),hn.updateTemplateImage(t,n),o.A.success("封面更新成功")),[3,4];case 3:return r.sent(),o.A.error("封面更新失败"),[3,4];case 4:return[2]}})})()}(e,t.id)},children:hn.cachedImage?(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,overflow:"hidden"},children:(0,r.jsx)("img",{src:hn.cachedImage,alt:"模板封面",style:{width:"100%",height:"100%",objectFit:"cover",display:"block"}})}):(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",cursor:"pointer"},children:(0,r.jsx)(l.A,{icon:(0,r.jsx)(Nt,{}),size:"small",type:"text",style:{color:"white"},children:"替换"})})})})]})}},{title:"模板名称",dataIndex:"templateName",key:"templateName",width:"20%",render:function(e,t){var n=function(e){return e.id===h}(t);return n?(0,r.jsx)(yt.A,{ref:g,id:"name-input-".concat(t.id),defaultValue:e,autoFocus:!0,onPressEnter:function(){return j(t.id)},style:{width:"100%"},onClick:function(e){return e.stopPropagation()},onBlur:function(){return j(t.id)}}):(0,r.jsx)(pt.A.Text,{underline:!!n,onClick:function(){return function(e){m(e.id)}(t)},style:{cursor:"pointer"},children:e})}},{title:"ID",dataIndex:"id",key:"id",width:"15%"},{title:"创建时间",dataIndex:"createDate",key:"createDate",width:"15%"},{title:"分类",dataIndex:"templateType",key:"templateType",width:"12%"},{title:"操作",key:"action",width:"20%",render:function(e,t){return(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{icon:(0,r.jsx)(Ct.A,{}),type:"text",style:{color:"#1890ff"},onClick:function(){return e=t.id,vn(function(){var t,n,r,a,i,l;return In(this,function(s){switch(s.label){case 0:return s.trys.push([0,4,,5]),[4,Yt.getLightTemplate(e)];case 1:return t=s.sent(),n=t.dataUrl,[4,fetch(n)];case 2:if(!(r=s.sent()).ok)throw new Error("下载失败: ".concat(r.statusText));return[4,r.blob()];case 3:return a=s.sent(),i=document.createElement("a"),l=URL.createObjectURL(a),i.href=l,i.download="LightConfig.json",i.click(),URL.revokeObjectURL(l),o.A.info("正在下载模板 ".concat(e)),[3,5];case 4:return s.sent(),o.A.error("下载模板 ".concat(e," 失败")),[3,5];case 5:return[2]}})})();var e},children:"下载"}),(0,r.jsx)(xt.A,{title:"确定要删除这个模板吗？",onConfirm:function(){return e=t.id,vn(function(){var t;return In(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,Yt.delete([e])];case 1:return n.sent()?(t=i.filter(function(t){return t.id!==e}),s(t),hn.initTemplateList(t),o.A.success("模板删除成功")):o.A.error("模板删除失败：接口返回异常"),[3,3];case 2:return n.sent(),o.A.error("模板删除失败，请重试"),[3,3];case 3:return[2]}})})();var e},okText:"是",cancelText:"否",children:(0,r.jsx)(l.A,{icon:(0,r.jsx)(Ot.A,{}),type:"text",style:{color:"#1890ff"},children:"删除"})})]})}}],w=i.filter(function(e){return e.templateName.includes(u)});return(0,r.jsxs)("div",{style:{padding:"20px"},children:[(0,r.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"4px",marginBottom:"16px",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{icon:(0,r.jsx)(Lt.A,{}),size:"middle",onClick:function(){return x(u)}}),(0,r.jsx)(yt.A,{placeholder:"搜索模板",allowClear:!0,style:{width:300},value:u,onChange:function(e){return d(e.target.value)},onPressEnter:function(){return x(u)},onClear:function(){d(""),s(t),hn.initTemplateList(t)}}),(0,r.jsx)(jt.A,{title:"category"===b?"当前按分类排序":"createTimeAsc"===b?"当前按创建时间正序排序":"当前按创建时间倒序排序",children:(0,r.jsx)(l.A,{icon:"category"===b?(0,r.jsx)(Pt,{}):"createTimeAsc"===b?(0,r.jsx)(zt,{}):(0,r.jsx)(Ut,{}),onClick:function(){var e="category"===b?"createTimeAsc":"createTimeAsc"===b?"createTimeDesc":"category";v(e);var t=Sn(i).sort(function(t,n){if("category"===e){var r="string"==typeof t.templateType?"日光"===t.templateType?1:0:t.templateType;return("string"==typeof n.templateType?"日光"===n.templateType?1:0:n.templateType)-r}return"createTimeAsc"===e?new Date(t.createDate).getTime()-new Date(n.createDate).getTime():new Date(n.createDate).getTime()-new Date(t.createDate).getTime()});s(t)},size:"middle"})})]})}),(0,r.jsx)(_t.A,{columns:_,dataSource:w,pagination:!1,bordered:!0,rowKey:"key"})]})},On=(0,T.observer)(function(e){var t=wn((0,a.useState)(null),2),n=t[0],i=t[1],s=(0,I.A)().styles,c=(0,at.B)().t,u=wn((0,a.useState)(!1),2),d=u[0],f=u[1],m=wn(Ze.A.useForm(),1)[0],p=wn((0,a.useState)([]),2),g=p[0],y=p[1],b=wn((0,a.useState)(1),2),v=b[0],x=b[1],j=wn((0,a.useState)(50),2),_=j[0],w=j[1],S=wn((0,a.useState)(0),2),k=S[0],A=S[1];(0,a.useEffect)(function(){T()},[]);var T=function(){i({dayLightParamConfig:{data:{}},nightLightParamConfig:{data:{}},modelLightParamConfig:{data:{}}}),vn(function(){var e,t,n;return In(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,Yt.listLightTemplate({})];case 1:return e=r.sent(),t=(e.result||[]).map(function(e){return _n(jn({},e),{templateType:1===e.templateType?"日光":"夜光"})}),y(t||[]),A(e.recordCount),gn.log("初始化模板数据:",e),[3,3];case 2:return n=r.sent(),gn.error("获取模板数据失败:",n),[3,3];case 3:return[2]}})})()},N=function(){var e=hn.templateInfo.category,t=hn.formatLightConfigsByCategory(e);return new File([t],"wireFrameImageJson.json",{type:"application/json"})},C=function(e,t){return vn(function(){var n,r;return In(this,function(a){switch(a.label){case 0:return x(e),w(t),[4,Yt.listLightTemplate({pageIndex:e,pageSize:t})];case 1:return(n=a.sent())&&(r=n.result.map(function(e){return _n(jn({},e),{templateType:1===e.templateType?"日光":"夜光"})}),y(Sn(r)),hn.initTemplateList(Sn(r))),[2]}})})()};return n?(0,r.jsxs)("div",{style:{position:"absolute",left:"50%",top:0,width:"100%",height:"100%",backgroundColor:"#fff",padding:"20px",zIndex:1e3,maxWidth:1e3,transform:"translate(-50%, 0px)"},children:[(0,r.jsx)("div",{style:{position:"absolute",left:0,right:0,top:0,bottom:50},children:(0,r.jsx)(h.A,{className:s.score_tabs,defaultActiveKey:"dayLight",items:[{key:"dayLight",label:"日光灯光模板配置",children:hn.dayLightConfigs.map(function(e){return(0,r.jsx)(Nn,{groupName:e.groupName,groupData:e.groupData,category:"dayLight"},e.groupName)})},{key:"nightLight",label:"夜间灯光模板配置",children:hn.nightLightConfigs.map(function(e){return(0,r.jsx)(Nn,{groupName:e.groupName,groupData:e.groupData,category:"nightLight"},e.groupName)})},{key:"modelLight",label:"灯光模板管理",children:(0,r.jsx)(Cn,{templates:g})}],style:{backgroundColor:"#ffffff"},onChange:function(e){hn.setCategory(e)}})}),("dayLight"===hn.templateInfo.category||"nightLight"===hn.templateInfo.category)&&(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001},children:(0,r.jsxs)(et.A,{size:"middle",children:[(0,r.jsx)(l.A,{type:"primary",onClick:function(){f(!0)},style:{width:150},children:"保存当前配置为模板"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=URL.createObjectURL(N()),t=document.createElement("a");t.href=e,t.download="LightConfig.json",t.click()},style:{width:120},children:"下载配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){hn.resetLightConfigs(hn.templateInfo.category)},style:{width:120},children:"恢复默认配置"}),(0,r.jsx)(l.A,{type:"primary",onClick:function(){var e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=function(e){return vn(function(){var t,n,r,a,i,l,s;return In(this,function(c){switch(c.label){case 0:if(n=e.target,!(r=null===(t=n.files)||void 0===t?void 0:t[0]))return o.A.warning("未选择文件"),[2];if("application/json"!==r.type&&!r.name.endsWith(".json"))return o.A.error("请上传 JSON 格式的文件"),[2];c.label=1;case 1:return c.trys.push([1,3,,4]),[4,new Promise(function(e,t){var n=new FileReader;n.onload=function(){return e(n.result)},n.onerror=t,n.readAsText(r)})];case 2:return a=c.sent(),i=JSON.parse(a),Array.isArray(i)?(l=hn.convertToLightConfigGroup(i),gn.log("转换后的配置:",l),"dayLight"===hn.templateInfo.category?(gn.log("类型",hn.templateInfo.category),hn.setDayLightConfigs(l),o.A.success("日光配置导入成功")):(gn.log("类型",hn.templateInfo.category),hn.setNightLightConfigs(l),o.A.success("夜景配置导入成功")),[3,4]):(o.A.error("JSON格式错误"),[2]);case 3:return s=c.sent(),o.A.error("加载失败: ".concat((u=s,(null!=(d=Error)&&"undefined"!=typeof Symbol&&d[Symbol.hasInstance]?d[Symbol.hasInstance](u):u instanceof d)?s.message:"解析错误"))),[3,4];case 4:return[2]}var u,d})})()},e.click()},style:{width:120},children:"加载灯光配置"})]})}),"modelLight"===hn.templateInfo.category&&(0,r.jsx)("div",{style:{position:"absolute",bottom:0,left:0,width:"100%",padding:"10px",backgroundColor:"#fff",borderTop:"1px solid #e8e8e8",textAlign:"center",zIndex:1001,display:"flex",justifyContent:"flex-end",alignItems:"center"},children:(0,r.jsx)(wt.A,{current:v,pageSize:_,total:k,onChange:C,onShowSizeChange:C,showSizeChanger:!0})}),(0,r.jsxs)(bt.A,{title:(0,r.jsxs)("div",{style:{textAlign:"center",width:"100%"},children:[(0,r.jsx)(pt.A.Title,{level:4,style:{margin:0},children:"保存为新灯光模板"}),(0,r.jsx)("div",{style:{height:"1px",backgroundColor:"#000",marginTop:8}})]}),open:d,onCancel:function(){f(!1)},footer:null,width:700,destroyOnClose:!0,children:[(0,r.jsxs)("div",{style:{display:"flex",gap:20},children:[(0,r.jsxs)("div",{style:{width:"30%",marginTop:0},children:[(0,r.jsx)(pt.A.Text,{type:"danger",style:{display:"inline-block"},children:"* "}),(0,r.jsx)(pt.A.Text,{style:{display:"inline-block"},children:"模板封面"}),(0,r.jsx)(vt.A,{name:"avatar",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,beforeUpload:function(e){return vn(function(){var t;return In(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,3,4]),[4,Tn(e)];case 1:return t=n.sent(),hn.setCachedImage(t),hn.setTemplateImage(t),m.setFieldValue("cover",t),[3,4];case 2:return n.sent(),[3,4];case 3:return[7];case 4:return[2]}})})()},children:hn.cachedImage?(0,r.jsx)("div",{style:{width:"100%",height:"100%",borderRadius:4,overflow:"hidden"},children:(0,r.jsx)("img",{src:hn.cachedImage,alt:"模板封面",style:{width:"100%",height:"100%",objectFit:"cover",display:"block"}})}):(0,r.jsxs)("div",{style:{width:"100%",height:"100%",borderRadius:4,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",cursor:"pointer"},children:[(0,r.jsx)(Vt,{style:{fontSize:24,color:"#1890ff"}}),(0,r.jsx)("p",{style:{marginTop:8},children:"点击上传"}),(0,r.jsx)("p",{style:{fontSize:12,color:"#666"},children:"支持JPG/PNG格式"})]})})]}),(0,r.jsx)("div",{style:{width:"70%"},children:(0,r.jsxs)(Ze.A,{form:m,layout:"vertical",children:[(0,r.jsx)(Ze.A.Item,{name:"name",label:"模板名称",rules:[{required:!0,message:"请输入模板名称"},{max:50,message:"名称不能超过50个字符"}],children:(0,r.jsx)(yt.A,{placeholder:"请输入模板名称"})}),(0,r.jsx)(Ze.A.Item,{name:"category",label:"模板分类",children:(0,r.jsx)("div",{style:{display:"flex",gap:8},children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A,{style:{backgroundColor:"dayLight"===hn.templateInfo.category||"modelLight"===hn.templateInfo.category?"#1890ff":"#ffffff",color:"dayLight"===hn.templateInfo.category||"modelLight"===hn.templateInfo.category?"#ffffff":"#000000",border:"1px solid #d9d9d9",borderRadius:4,padding:"8px 16px",cursor:"pointer",marginRight:8},children:"日光"}),(0,r.jsx)(l.A,{style:{backgroundColor:"nightLight"===hn.templateInfo.category?"#1890ff":"#ffffff",color:"nightLight"===hn.templateInfo.category?"#ffffff":"#000000",border:"1px solid #d9d9d9",borderRadius:4,padding:"8px 16px",cursor:"pointer"},children:"夜光"})]})})})]})})]}),(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:20,gap:10},children:[(0,r.jsx)(l.A,{onClick:function(){return f(!1)},style:{width:100},children:"取消"}),(0,r.jsx)(l.A,{type:"primary",style:{width:100},onClick:function(){return vn(function(){var e,t,n,r;return In(this,function(a){switch(a.label){case 0:return e="dayLight"===hn.templateInfo.category?1:2,[4,(0,Ae.Sf)(N())];case 1:return t=a.sent(),hn.setDataUrl(t),n=hn.templateImage,r=m.getFieldValue("name"),Yt.insertLightTemplate({dataUrl:hn.dataUrl,templateImage:n,templateName:r,templateType:e}),f(!1),m.setFieldsValue({name:""}),hn.setCachedImage(null),[2]}})})()},children:"保存"})]})]})]}):(0,r.jsxs)("div",{children:[c("加载中"),"..."]})}),Ln=n(7729);function Rn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Dn(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Pn(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){Dn(i,r,a,o,l,"next",e)}function l(e){Dn(i,r,a,o,l,"throw",e)}o(void 0)})}}function En(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||zn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fn(e){return function(e){if(Array.isArray(e))return Rn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||zn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zn(e,t){if(e){if("string"==typeof e)return Rn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Rn(e,t):void 0}}function Mn(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Bn=function(e){var t=xe().styles,n=En((0,a.useState)("Default"),2),i=n[0],o=n[1],s=En((0,a.useState)(0),2),c=(s[0],s[1],En((0,a.useState)("客餐厅"),2)),u=c[0],d=c[1],f=En((0,a.useState)(["BasicTransfer","SpacePartition"]),2),h=f[0],m=f[1],p=e.datasetNameList||[],g=Ln.gl,y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e=e||i;var t=p.find(function(t){return t.id===e});return(null==t?void 0:t.name)||"默认数据集"},b=(0,Q.AU)({format_type:1}),v=function(){return Pn(function(){var e;return Mn(this,function(t){switch(t.label){case 0:return[4,we.n.instance.getDataById("lastDatasetId",we.n.DefaultTable)];case 1:return function(e){Pn(function(){return Mn(this,function(t){return o(e),[2]})})()}((null===(e=t.sent())||void 0===e?void 0:e.dataset_id)||null||i),[2]}})})()},x=function(){(null==e?void 0:e.onHide)&&e.onHide()};return(0,a.useEffect)(function(){v()},[]),(0,r.jsxs)("div",{className:"CreateTaskPopUp",style:{position:"fixed",zIndex:10001,width:"900px",top:"0px",left:"300px"},children:[(0,r.jsx)("div",{className:"closeBtn",style:{position:"absolute",top:"40px",right:"30px",fontSize:"16px",cursor:"pointer",zIndex:10001},onClick:function(){return x()},children:"X"}),(0,r.jsxs)("div",{className:t.dialogInputs,style:{height:"400px"},children:[(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 测试集: "}),(0,r.jsx)("select",{name:"dataset_list",onChange:function(e){o(e.target.value)},defaultValue:i,children:p.map(function(e,t){return(0,r.jsx)("option",{value:e.id,children:e.name},"dataset_"+t)})})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 空间: "}),(0,r.jsx)("select",{name:"roomname_list",onChange:function(e){d(e.target.value)},defaultValue:u,children:["客餐厅","厨房","卧室","卫生间","书房","阳台"].map(function(e,t){return(0,r.jsx)("option",{value:e,children:e},"room_type"+t)})})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:"算法: "}),(0,r.jsx)("span",{children:Object.keys(g).map(function(e,t){return(0,r.jsx)("span",{className:"methods_name "+(h.includes(e)?"checked":""),onClick:function(){var t=Fn(h),n=t.indexOf(e);n<0?t.push(e):t.splice(n,1),m(t)},children:g[e]},"methods_"+e)})})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)("span",{children:" 日期: "}),(0,r.jsxs)("span",{children:[b," "]})]}),(0,r.jsxs)("div",{className:"input_row",children:[(0,r.jsx)(l.A,{onClick:function(){return Pn(function(){var t,n,r,a,o;return Mn(this,function(l){switch(l.label){case 0:return t=(0,Q.w_)("Test"),n=i,r=u,a=0,e.taskList.forEach(function(e){return a=Math.max(e.task_count||0,a)}),o={id:t,task_count:a+1,dataset_id:n,dataset_name:y(n),room_name:r,methods:Fn(h),create_date:b},[4,we.n.instance.addData(o,we.n.TestingTaskTable)];case 1:return l.sent(),x(),[2]}})})()},children:"创建"}),(0,r.jsx)(l.A,{onClick:function(){return x()},children:"取消"})]})]})]})},Un=Ln.gl,Hn=[{key:"task_name",ui_name:"任务序号"},{key:"dataset_name",ui_name:"数据集"},{key:"room_name",ui_name:"空间类型"},{key:"methods",ui_name:"算法类型"},{key:"dataset_size",ui_name:"测试总量"},{key:"create_date",ui_name:"创建时间"},{key:"average_solving_time",ui_name:"平均计算时长"},{key:"layout_cover_rate",ui_name:"布局覆盖率"},{key:"empty_layout_num",ui_name:"空布局户型数"},{key:"layout_valid_cadidate_rate",ui_name:"布局准出率"},{key:"layout_valid_candidate_num",ui_name:"准出的布局数"},{key:"layout_total_candidates_num",ui_name:"推荐布局的总数"}],Jn=function(e,t){if(!e)return"";var n=e[t];return"methods"===t?n.map(function(e){return Un[e]}).join(" "):"task_name"==t?e.dataset_name+" "+String(e.task_count||0).padStart(3,"0"):t.endsWith("time")?n+"ms":n},Vn=function(e){var t=xe().styles;return(0,r.jsxs)("div",{className:t.dataReport+" "+((null==e?void 0:e.className)||""),children:[(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center"},children:e.title}),Hn.map(function(t,n){return(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsxs)("span",{children:[t.ui_name,":"]}),"    ",(0,r.jsx)("span",{children:Jn(e.task,t.key)})]},"report_row"+t.key+"_"+n)}),e.onEnterTask&&(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center",marginTop:"20px"},children:(0,r.jsx)(l.A,{onClick:function(){return e.onEnterTask()},children:"进入任务"})})]})};function Gn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Wn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Gn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Gn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var $n=function(e){(0,I.A)().styles;var t=xe().styles,n=(Ln.gl,Wn((0,a.useState)(null),2)),i=n[0],o=n[1],l=e.taskList,s=e.current_task,c=l.filter(function(e){return e.id!=s.id&&e.dataset_id==s.dataset_id&&e.room_name===s.room_name});return(0,a.useEffect)(function(){},[]),(0,r.jsxs)("div",{className:t.test_report,children:[(0,r.jsx)("div",{className:"close_btn",onClick:function(){e.onHide&&e.onHide()},children:"X"}),(0,r.jsx)(Vn,{task:s,title:"当前任务",onEnterTask:e.onEnterTask}),(0,r.jsx)(Vn,{task:i,title:"对比任务",className:"compared_report"}),(0,r.jsxs)("div",{className:t.dataReport+" compared_report",children:[(0,r.jsx)("div",{className:"report_row",style:{textAlign:"center"},children:"关联任务列表"}),c.map(function(e,t){return(0,r.jsxs)("div",{className:"report_row "+(e.id===(null==i?void 0:i.id)?"checked":""),onClick:function(){o(e)},children:[(0,r.jsxs)("span",{children:[Jn(e,"task_name"),":"]}),"    ",(0,r.jsx)("span",{children:Jn(e,"methods")})]},"related_report_row_"+t)})]})]})},Kn=n(50707),Xn=n(65640);function qn(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Zn(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){qn(i,r,a,o,l,"next",e)}function l(e){qn(i,r,a,o,l,"throw",e)}o(void 0)})}}function Qn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Yn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function er(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var tr=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Yn(this,"_task",void 0),Yn(this,"_building_list",void 0),Yn(this,"_is_running",void 0),Yn(this,"_start_id",void 0),Yn(this,"onClickBuildingId",void 0),Yn(this,"onFinish",void 0),e._instance=this,this._task=null,this._building_list=null,this._is_running=!1,this._start_id=-1}var t,n,r;return t=e,n=[{key:"setTask",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;this._task=e,this._building_list=t,this._start_id=n}},{key:"readResultOnBuildingRoom",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Zn(function(){var n,r,a;return er(this,function(i){switch(i.label){case 0:return n=this._task.id+"-"+e,[4,we.n.instance.getDataById(n,we.n.TestingTaskDetailTable)];case 1:return(r=i.sent())?(t&&r.layout_scheme_list&&(a=r.layout_scheme_list.map(function(e){var n=new Kn.F;return n.importScheme(e),n.room=t,n}),t._layout_scheme_list=a||[]),[2,r]):[2]}})}).call(this)}},{key:"summaryResults",value:function(){return Zn(function(){var e,t,n,r,a,i,o,l,s,c,u,d,f;return er(this,function(h){switch(h.label){case 0:if(!this._task)return[2];this._task.average_solving_time=0,this._task.total_solving_time=0,this._task.layout_cover_rate=0,this._task.layout_total_candidates_num=0,this._task.layout_valid_cadidate_rate=0,e=0,this._task.layout_valid_candidate_num=0,this._task.empty_layout_num=0,t=!0,n=!1,r=void 0,h.label=1;case 1:h.trys.push([1,8,9,10]),a=this._building_list[Symbol.iterator](),h.label=2;case 2:return(t=(i=a.next()).done)?[3,7]:(o=i.value,[4,this.readResultOnBuildingRoom(o.buildingRoomId,null)]);case 3:return l=h.sent()||{layout_scheme_list:[]},0==(s=l.layout_scheme_list.filter(function(e){return Kn.F.checkIsValidByScores(e)})).length&&l.id?(this._task.empty_layout_num++,l.mark_content="有效布局为空",[4,we.n.instance.addData(l,we.n.TestingTaskDetailTable)]):[3,5];case 4:h.sent(),h.label=5;case 5:this._task.layout_total_candidates_num+=l.layout_scheme_list.length,this._task.layout_valid_candidate_num+=s.length,c=l.computing_duration,isNaN(c)&&(c=100),this._task.total_solving_time+=c,l.layout_scheme_list&&l.layout_scheme_list.length>0&&(e+=(s.length+1e-6)/(l.layout_scheme_list.length+1e-6)),h.label=6;case 6:return t=!0,[3,2];case 7:return[3,10];case 8:return u=h.sent(),n=!0,r=u,[3,10];case 9:try{t||null==a.return||a.return()}finally{if(n)throw r}return[7];case 10:return this._task.layout_valid_cadidate_rate=(0,Q.mk)(e/this._building_list.length,1e3),this._task.average_solving_time=(0,Q.mk)(this._task.total_solving_time/this._building_list.length),this._task.layout_cover_rate=(0,Q.mk)(1-this._task.empty_layout_num/this._building_list.length,1e3),[4,we.n.instance.addData(this._task,we.n.TestingTaskTable)];case 11:return h.sent(),d=nt.TLayoutParamConfigurationManager.instance.toJson(),[4,we.n.instance.getDataById(this._task.id,we.n.TestingTaskConfigsTable)];case 12:return(f=h.sent()||{})[nt.TLayoutParamConfigurationManager.IndexedDB_Prop_Key]?[3,14]:(f.id=this._task.id,f[nt.TLayoutParamConfigurationManager.IndexedDB_Prop_Key]=d,[4,we.n.instance.addData(f,we.n.TestingTaskConfigsTable)]);case 13:h.sent(),h.label=14;case 14:return[2]}})}).call(this)}},{key:"filterResults",value:function(e){return Zn(function(){var t,n,r,a,i,o,l,s,c;return er(this,function(u){switch(u.label){case 0:t=this,n=[],r=[],a=!0,i=!1,o=void 0;try{for(l=function(){var a=c.value;r.push(new Promise(function(r,i){t.readResultOnBuildingRoom(a.buildingRoomId,null).then(function(t){!0===e.marked&&(null==t?void 0:t.mark_content)&&(null==t?void 0:t.mark_content.trim().length)>0&&n.push(t),r(!0)})}))},s=this._building_list[Symbol.iterator]();!(a=(c=s.next()).done);a=!0)l()}catch(e){i=!0,o=e}finally{try{a||null==s.return||s.return()}finally{if(i)throw o}}return[4,Promise.all(r)];case 1:return u.sent(),[2,n]}})}).call(this)}},{key:"solveInHouseByIndex",value:function(e){return Zn(function(){var t,n,r,a,i,o,l,s,c,d,f,h,m;return er(this,function(p){switch(p.label){case 0:return Xn.clear(),n=this._building_list[e],this.onClickBuildingId&&this.onClickBuildingId(n.buildingRoomId),r=this._task.room_name,a=0,i=u.nb.instance.layout_container,o=u.nb.instance.layout_graph_solver,[4,we.n.instance.getDataById(n.buildingRoomId,we.n.BuildingSchemeDataTable)];case 1:return(l=null===(t=p.sent())||void 0===t?void 0:t.houseInfo)&&l.schemeXmlJson?(i.fromXmlSchemeData(l.schemeXmlJson),(s=i._rooms.filter(function(e){return e.roomname.includes(r)})).length,(c=s[a]||s[0]||null)?(d=(new Date).getTime(),this._task.methods.includes("BasicTransfer")||this._task.methods.includes("GroupTransfer")?[4,o.queryModelRoomsFromServer([c])]:[3,3]):[2,!1]):[3,6];case 2:p.sent(),p.label=3;case 3:return[4,i.applyRoomWithSolvingMethods(c,this._task.methods,null)];case 4:return f=p.sent(),h=(new Date).getTime(),m={id:this._task.id+"-"+n.buildingRoomId,task_id:this._task.id,building_id:n.buildingRoomId,room:c.exportExtRoomData(),layout_scheme_list:f.map(function(e){return e.exportScheme({export_debug_data:!1})}),computing_duration:h-d,computing_date:(0,Q.AU)({format_type:1})},[4,we.n.instance.addData(m,we.n.TestingTaskDetailTable)];case 5:p.sent(),p.label=6;case 6:return[2,!0]}})}).call(this)}},{key:"runTask",value:function(){return Zn(function(){var e,t;return er(this,function(n){switch(n.label){case 0:if(!this._is_running)return[2,!0];if(!this._building_list)return[2,!0];e=Math.max(this._start_id,0),t=e,n.label=1;case 1:return t<this._building_list.length?this._is_running?[4,this.solveInHouseByIndex(t)]:[2]:[3,5];case 2:return n.sent(),[4,(0,Q.IP)(50)];case 3:n.sent(),this._task.testing_date=(0,Q.AU)({format_type:1}),n.label=4;case 4:return t++,[3,1];case 5:return[4,this.summaryResults()];case 6:return n.sent(),[2,!0]}})}).call(this)}}],r=[{key:"instance",get:function(){return e._instance||new e,e._instance}},{key:"setTask",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;e.instance.setTask(t,n,r)}},{key:"IsRunning",get:function(){return e.instance._is_running},set:function(t){e.instance._is_running!==t&&(e.instance._is_running=t,t&&e.instance.runTask().then(function(t){e.instance.onFinish&&e.instance.onFinish()}))}}],n&&Qn(t.prototype,n),r&&Qn(t,r),e}();function nr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function rr(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function ar(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return nr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ir(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}Yn(tr,"_instance",null);var or=function(e){var t,n,i,o,l=xe().styles,s=ar((0,a.useState)((null===(t=e.roomResult)||void 0===t?void 0:t.fixed)||!1),2),c=s[0],u=s[1],d=e.roomResult,f=[];d&&(d.layout_scheme_list=d.layout_scheme_list||[],f=d.layout_scheme_list.filter(function(e){return Kn.F.checkIsValidByScores(e)}));var h,m=(0,a.useRef)(null),p=["有效布局为空"];"客餐厅"===(null==d||null===(i=d.room)||void 0===i||null===(n=i.room)||void 0===n?void 0:n.roomname)&&(h=p).push.apply(h,["沙发区朝向问题","缺失主要柜体","有更优解,但排序不靠前","餐厅区 位置并非最优","玄关柜有更优位置","柜体的尺寸比例看似不太合理","客厅区 和 餐厅区 分区不正确","客厅区 和 餐厅区 分区比例 不合理","电视-沙发距离太长，看起来不合理","沙发区 应用要允许比电视柜 宽","小空间（特别是狭长空间）分区不好"]);var g=function(){return(t=function(){var t;return ir(this,function(n){switch(n.label){case 0:return[4,we.n.instance.addData(d,we.n.TestingTaskDetailTable)];case 1:return n.sent(),e.markedData?(t=e.markedData,d.mark_content&&0!=d.mark_content.trim().length?t.markedDict[d.building_id]={mark_content:d.mark_content,fixed:d.fixed}:t.markedDict[d.building_id]&&delete t.markedDict[d.building_id],[4,we.n.instance.addData(t,we.n.TestingTaskMarkTable)]):[3,3];case 2:n.sent(),n.label=3;case 3:return[2]}})},function(){var e=this,n=arguments;return new Promise(function(r,a){var i=t.apply(e,n);function o(e){rr(i,r,a,o,l,"next",e)}function l(e){rr(i,r,a,o,l,"throw",e)}o(void 0)})})();var t};return(0,a.useEffect)(function(){m.current&&(m.current.value=d.mark_content||""),u((null==d?void 0:d.fixed)||!1)},[d]),d?(0,r.jsxs)("div",{className:l.resultInfo,children:[(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"列表序号:"}),"    ",(0,r.jsx)("span",{children:e.list_index+1})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"户型ID:"}),"    ",(0,r.jsx)("span",{children:d.building_id})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"测试日期:"}),"    ",(0,r.jsx)("span",{children:d.computing_date})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"计算耗时:"}),"    ",(0,r.jsxs)("span",{children:[d.computing_duration||0,"ms"]})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"布局数量:"}),"    ",(0,r.jsx)("span",{children:(d.layout_scheme_list||[]).length})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"准出数量:"}),"    ",(0,r.jsx)("span",{children:f.length})]}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"异常标记:"}),"    ",(0,r.jsx)("input",{style:{height:"22px"},ref:m,list:"mark_list",defaultValue:d.mark_content||"",onChange:function(e){d.mark_content=e.target.value,g()}}),(0,r.jsx)("datalist",{id:"mark_list",children:p.map(function(e,t){return(0,r.jsx)("option",{value:e,children:e},"mark_data_"+t)})})]}),d.mark_content&&d.mark_content.length>0&&(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"是否修复:"}),"    ",(0,r.jsx)("span",{onClick:function(){return e=!c,d.fixed=e,g(),void u(e);var e},children:c?"✔":"-"})]}),(0,r.jsx)("br",{}),(0,r.jsxs)("div",{className:"report_row",children:[(0,r.jsx)("span",{children:"异常数量:"}),"    ",(0,r.jsx)("span",{children:Object.keys((null==e||null===(o=e.markedData)||void 0===o?void 0:o.markedDict)||{}).length})]})]}):(0,r.jsx)(r.Fragment,{})};function lr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function sr(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function cr(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){sr(i,r,a,o,l,"next",e)}function l(e){sr(i,r,a,o,l,"throw",e)}o(void 0)})}}function ur(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function dr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||hr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fr(e){return function(e){if(Array.isArray(e))return lr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||hr(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hr(e,t){if(e){if("string"==typeof e)return lr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?lr(e,t):void 0}}function mr(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var pr=[],gr=function(e){var t="RunningTestingTaskPanel",n=e.current_task,i=xe().styles,o=dr((0,a.useState)([]),2),s=o[0],c=o[1],f=dr((0,a.useState)({}),2),h=f[0],m=f[1],p=dr((0,a.useState)(""),2),g=p[0],y=p[1],b=dr((0,a.useState)(!1),2),v=b[0],x=b[1],j=dr((0,a.useState)(!1),2),_=j[0],w=j[1],S=dr((0,a.useState)("SingleRoom"),2),I=S[0],T=S[1],N=dr((0,a.useState)(0),2),C=(N[0],N[1],dr((0,a.useState)(1e3),2)),O=C[0],L=C[1],R=dr((0,a.useState)(0),2),D=R[0],P=R[1],E=[{mode:"SingleRoom",name:"单空间"},{mode:"FullHouse",name:"查看全屋"}],F=["测试结果","重新计算"],z=dr((0,a.useState)("测试结果"),2),M=z[0],B=z[1],U=dr((0,a.useState)("Info"),2),H=U[0],J=U[1],V=dr((0,a.useState)(null),2),G=V[0],W=V[1],$=dr((0,a.useState)({id:n.id,markedDict:{}}),2),K=$[0],X=$[1],q=dr((0,a.useState)([]),2),Z=q[0],Q=q[1],Y=dr((0,a.useState)(!1),2),ee=Y[0],te=Y[1];tr.instance.onFinish=function(){w(!1)};var ne=(0,a.useRef)(null),re=K.markedDict,ae=function(e){return cr(function(){var t,r,a,i,o;return mr(this,function(l){switch(l.label){case 0:return t=u.nb.instance.layout_container,(r=t._selected_room)?e===M?[2]:"测试结果"!==e?[3,1]:(Z&&(null===(a=Z[0])||void 0===a?void 0:a.room)===r&&(t._selected_room._layout_scheme_list=fr(Z),u.nb.emit(d.U.LayoutSchemeList,{schemeList:r._layout_scheme_list,index:r.selectIndex})),[3,5]):[2];case 1:return i=u.nb.instance.layout_graph_solver,n.methods.includes("BasicTransfer")||n.methods.includes("GroupTransfer")?[4,i.queryModelRoomsFromServer([r])]:[3,3];case 2:l.sent(),l.label=3;case 3:return[4,t.applyRoomWithSolvingMethods(r,n.methods,null)];case 4:o=l.sent(),t._selected_room._layout_scheme_list=o,u.nb.emit(d.U.LayoutSchemeList,{schemeList:r._layout_scheme_list,index:r.selectIndex}),l.label=5;case 5:return B(e),[2]}})})()},ie=function(e){u.nb.instance.layout_container._drawing_layer_mode=e,T(e),u.nb.instance.update()},oe=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return cr(function(){var n,r,a;return mr(this,function(i){switch(i.label){case 0:return t?[4,tr.instance.filterResults({marked:!0})]:[3,4];case 1:return(n=i.sent())?(r={},n.forEach(function(e){r[e.building_id]={mark_content:e.mark_content,fixed:(null==e?void 0:e.fixed)||!1}}),K.markedDict=r,[4,we.n.instance.addData(K,we.n.TestingTaskMarkTable)]):[3,3];case 2:i.sent(),X(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ur(e,t,n[t])})}return e}({},K)),i.label=3;case 3:return window.confirm("更新标记列表"),[3,6];case 4:return[4,we.n.instance.getDataById(e.current_task.id,we.n.TestingTaskMarkTable)];case 5:(a=i.sent()||{id:e.current_task.id,markedDict:{}})&&X(a),i.label=6;case 6:return[2]}})})()},le=function(e){return cr(function(){var t,r,a,i,o,l,s,c,f;return mr(this,function(h){switch(h.label){case 0:return r=u.nb.instance.layout_container,(0,je.Ri)("authCode"),a=n.room_name,i=0,[4,we.n.instance.getDataById(e,we.n.BuildingSchemeDataTable)];case 1:return(l=null===(t=h.sent())||void 0===t?void 0:t.houseInfo)?[3,3]:[4,A.h.instance.makeHouseTestingInfoDataByBuildingId(e)];case 2:l=h.sent(),h.label=3;case 3:return(o=l)&&o.schemeXmlJson?(r.fromXmlSchemeData(o.schemeXmlJson,!0,Ie.N.LayoutLibrary),r.focusCenter(),y(e),s=r._rooms.filter(function(e){return e.roomname.includes(a)}),s.length,(c=s[i]||s[0]||null)&&(localStorage&&localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(c.exportExtRoomData())),r._selected_room=c),u.nb.instance.update(),[4,tr.instance.readResultOnBuildingRoom(e,c)]):[3,5];case 4:f=h.sent(),W(f),(null==c?void 0:c._layout_scheme_list)&&c._layout_scheme_list.length>0&&(Q(fr(c._layout_scheme_list)),se({value:c._layout_scheme_list[0],index:0}),u.nb.emit(d.U.LayoutSchemeList,{schemeList:c._layout_scheme_list,index:c.selectIndex})),h.label=5;case 5:return[2]}})})()},se=function(e){var t;if(e.value&&(null===(t=e.value)||void 0===t?void 0:t.IsRoomLayoutScheme)){var n=e.value;if(n.room){var r=n.room;r.furnitureList=[],r.selectIndex=e.index;var a=fr(n.figure_list.figure_elements);a.sort(function(e,t){var n=e.default_drawing_order-t.default_drawing_order;return 0==n?(e.min_z||0)-(t.min_z||0):n});var i=!0,o=!1,l=void 0;try{for(var s,c=a[Symbol.iterator]();!(i=(s=c.next()).done);i=!0){var d=s.value;r.addFurnitureElement(d),r.name.indexOf("厨房")<0&&d.clearMatchedMaterials()}}catch(e){o=!0,l=e}finally{try{i||null==c.return||c.return()}finally{if(o)throw l}}r._room_entity&&r._room_entity.updateSpaceLivingInfo({force_auto_sub_area:!0})}}u.nb.instance.update()},ce=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s)if(null==t?void 0:t.marked){if(!re)return;for(var n=h[g]||0,r=null;n>=0||n<s.length;){var a;if(!(r=(null===(a=s[n+=e])||void 0===a?void 0:a.buildingRoomId)||null)||re[r])break}r&&(le(r),ye(r))}else{var i,o=h[g]||0;if(o<0)return;var l=(null===(i=s[o+e])||void 0===i?void 0:i.buildingRoomId)||null;l&&(le(l),ye(l))}};(0,a.useEffect)(function(){return u.nb.on_M(ke.I.ShowTestingLayoutSchemeList,t,function(e){x(e)}),u.nb.on_M(d.U.OnAILayoutSchemeSelected,t,se),cr(function(){var e,t,r;return mr(this,function(a){switch(a.label){case 0:return[4,we.n.instance.getTestingDatasetById(n.dataset_id)];case 1:return t=(null===(e=a.sent())||void 0===e?void 0:e.buildingList)||[],c(t),r={},t.forEach(function(e,t){return r[e.buildingRoomId]=t}),m(r),tr.setTask(n,t),oe(),[2]}})})(),function(){u.nb.off_M(ke.I.ShowTestingLayoutSchemeList,t)}},[]);var ue=O,de=Math.floor(s.length/ue)+1,fe=D;fe>=de&&(fe=de-1);var he=[];pr.length=0;for(var me=0;me<ue;me++){var pe=s[fe*ue+me];pe&&pr.push(pe)}for(var ge=0;ge<de;ge++)he.push(ge);var ye=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g,t=pr.findIndex(function(t){return t.buildingRoomId===e});if(ne){var n=ne.current;n.scrollTo({top:50*(t-(n.clientHeight-50)/50/2),behavior:"smooth"})}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"RunningTestingPanel",className:i.leftPanel,style:{position:"absolute",left:"0",top:"0",zIndex:10002,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"100px",paddingTop:"10px",lineHeight:"30px",borderBottom:"1px solid #adf"},children:[(0,r.jsx)(l.A,{onClick:function(){e.onHide&&e.onHide()},children:"返回"}),(0,r.jsx)("select",{name:"pageSelect",onChange:function(e){P(~~e.target.value)},children:he.map(function(e){return(0,r.jsxs)("option",{value:e,children:["第",e+1,"页"]},"pageSelect"+e)})}),(0,r.jsx)("input",{type:"number",style:{width:"60px",height:"20px"},step:500,min:500,max:1e4,defaultValue:O,onChange:function(e){L(~~e.target.value)}}),(0,r.jsx)("span",{children:"个/页"}),(0,r.jsx)("br",{}),(0,r.jsx)(l.A,{onClick:function(){cr(function(){var e,t;return mr(this,function(r){return e=0,_||(t=prompt("从当前ID开始测试, 取消则从0开始",""+Math.max(s.findIndex(function(e){return e.buildingRoomId===g}),0)),e=Math.max(0,parseInt(t)||0),e%=ue),tr.instance.onClickBuildingId=function(e){le(e),ye(e)},tr.setTask(n,pr,e),tr.IsRunning=!tr.IsRunning,w(tr.IsRunning),[2]})})()},children:_?"停止测试":"启动测试"}),(0,r.jsx)(l.A,{onClick:function(){cr(function(){return mr(this,function(e){switch(e.label){case 0:return[4,tr.instance.summaryResults()];case 1:return e.sent(),[4,oe(!0)];case 2:return e.sent(),[2]}})})()},children:"汇总结果"}),(0,r.jsx)(l.A,{onClick:function(){oe()},children:"更新标记列表"}),(0,r.jsx)("br",{}),n.dataset_name+" "+String(n.task_count||0).padStart(3,"0"),"-",n.dataset_size,(0,r.jsx)("div",{className:"right_text",children:(n.methods||[]).map(function(e){return Ln.gl[e]||e}).join(" ")})]}),(0,r.jsx)("div",{ref:ne,style:{overflow:"auto",height:"calc(100vh - 140px)",display:v?"none":"block"},children:pr.map(function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(re[e.buildingRoomId]?"marked":"")+" "+(e.buildingRoomId===g?"checked":"")+" ",onClick:function(){return le(e.buildingRoomId)},children:[D*ue+t+1,":",e.buildingRoomId,"    ",e.cityName,(0,r.jsx)("br",{}),"     ",e.buildingName||"","    ",e.area?e.area+"m²":""]},"buildId_"+t)})}),v&&(0,r.jsx)("div",{className:i.schemeListModeTabs,children:F.map(function(e,t){return(0,r.jsx)("div",{className:"tab "+(M===e?"checked":""),onClick:function(){if(M==e){var n=F[(t+1)%F.length];ae(n)}else ae(e)},children:e},"show_scheme_list_mode_"+t)})}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 140px)",display:v?"block":"none"},children:(0,r.jsx)(k.A,{width:320,showSchemeName:!0})})]}),(0,r.jsx)("div",{className:i.drawRoomModeTabs,children:E.map(function(e,t){return(0,r.jsx)("div",{className:"tab "+(I==e.mode?"checked":""),onClick:function(){if(I==e.mode){var n=E[(t+1)%E.length];ie(n.mode)}else ie(e.mode)},children:e.name},"drawing_mode_"+t)})}),(0,r.jsx)("div",{className:i.rightToolBtns,children:(0,r.jsx)(l.A,{className:"topBtn",onClick:function(){te(!ee)},children:"S"})}),ee&&(0,r.jsxs)("div",{style:{width:800,height:600,position:"absolute",right:320,top:50,border:"1px solid #999",zIndex:2},children:[" ",(0,r.jsx)(mt,{defaultRoomKey:n.room_name})]}),(0,r.jsxs)("div",{className:"rightPanel "+i.rightPanel,children:[(0,r.jsxs)("div",{className:"tabs",children:[(0,r.jsx)("div",{className:"tab "+("Info"===H?"checked":""),onClick:function(){J("Info")},children:"测试信息"}),(0,r.jsx)("div",{className:"tab "+("Total"===H?"checked":""),onClick:function(){J("Total")},children:"结果汇总"})]}),"Info"===H&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"Ctrlbtns",children:[(0,r.jsxs)("div",{className:"btn_row",children:[(0,r.jsx)(l.A,{onClick:function(){return ce(-1,{marked:!0})},children:"上一个标记"}),(0,r.jsx)(l.A,{onClick:function(){return ce(1,{marked:!0})},children:"下一个标记"})]}),(0,r.jsxs)("div",{className:"btn_row",children:[(0,r.jsx)(l.A,{onClick:function(){return ce(-1)},children:"上一个户型"}),(0,r.jsx)(l.A,{onClick:function(){return ce(1)},children:"下一个户型"})]})]}),(0,r.jsx)(or,{roomResult:G,markedData:K,list_index:h[g]||0})]}),"Total"===H&&(0,r.jsx)(Vn,{task:n,title:"测试任务",className:"right_report"})]})]})};function yr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function br(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function vr(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){br(i,r,a,o,l,"next",e)}function l(e){br(i,r,a,o,l,"throw",e)}o(void 0)})}}function xr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return yr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jr(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var _r=!1,wr=function(){(0,I.A)().styles;var e=xr((0,a.useState)(_r),2),t=e[0],n=e[1],i=xe().styles,o=xr((0,a.useState)(null),2),s=o[0],c=o[1],d=xr((0,a.useState)([]),2),f=d[0],h=d[1],m=xr((0,a.useState)(!1),2),p=m[0],g=m[1],y=xr((0,a.useState)([]),2),b=y[0],v=y[1],x=xr((0,a.useState)(!1),2),j=x[0],_=x[1],w=Ln.gl,S=function(){return vr(function(){var e,t,n,r,a,i,o,l;return jr(this,function(s){switch(s.label){case 0:return[4,vr(function(){var e;return jr(this,function(t){switch(t.label){case 0:return[4,we.n.instance.getTestingDatasetList()];case 1:return e=t.sent(),v(e),[2,e]}})})()];case 1:return e=s.sent(),[4,we.n.instance.getAll(we.n.TestingTaskTable)];case 2:t=s.sent()||[],n=!0,r=!1,a=void 0;try{for(i=function(){var t=l.value,n=e.find(function(e){return e.id===t.dataset_id});t.dataset_name=(null==n?void 0:n.name)||t.dataset_name,t.dataset_size=(null==n?void 0:n.count)||0},o=t[Symbol.iterator]();!(n=(l=o.next()).done);n=!0)i()}catch(e){r=!0,a=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw a}}return t.sort(function(e,t){return t.create_date.localeCompare(e.create_date)}),h(t||[]),[2]}})})()};return(0,a.useEffect)(function(){u.nb.on_M(ke.I.ShowTestingTaskListPanel,"TestingTaskList",function(e){n(_r=null!==e?e:!_r),_r&&S()})},[]),t?j&&s?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(gr,{current_task:s,onHide:function(){return _(!1)}})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{id:"TestingTaskListPanel",className:i.leftPanel,style:{position:"absolute",left:"0",top:"0",zIndex:9999,paddingLeft:"0"},children:[(0,r.jsxs)("div",{className:"row_ele",style:{height:"75px",paddingTop:"10px",lineHeight:"32px"},children:[(0,r.jsx)(l.A,{onClick:function(){u.nb.emit_M(ke.I.ShowTestingDatasetListPanel,!0)},children:"数据集列表"}),(0,r.jsx)(l.A,{onClick:function(){return g(!0)},children:"创建任务"}),(0,r.jsx)(l.A,{onClick:function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return vr(function(){return jr(this,function(t){switch(t.label){case 0:return e=e||s,[4,we.n.instance.removeData(e.id,we.n.TestingTaskTable)];case 1:return t.sent(),[4,S()];case 2:return t.sent(),c(null),[2]}})})()}()},children:"删除任务"}),(0,r.jsx)("br",{})]}),(0,r.jsx)("div",{style:{overflow:"auto",height:"calc(100vh - 150px)"},children:f.map(function(e,t){return(0,r.jsxs)("div",{className:"row_ele "+(e.id===(null==s?void 0:s.id)?"checked":""),onClick:function(){c(e)},onDoubleClick:function(){c(e),_(!0)},title:"双击进入测试模式",children:[t+1,"、 ",e.dataset_name+" "+String(e.task_count||0).padStart(3,"0"),"-",e.dataset_size,(0,r.jsx)("div",{className:"right_text",children:e.create_date||""})," ",(0,r.jsx)("br",{}),"      ",e.room_name,(0,r.jsx)("div",{className:"right_text",children:(e.methods||[]).map(function(e){return w[e]||e}).join(" ")})]},"task_"+t)})})]}),s&&(0,r.jsx)($n,{current_task:s,taskList:f,onHide:function(){c(null)},onEnterTask:function(){_(!0)}}),p&&(0,r.jsx)(Bn,{taskList:f,datasetNameList:b,onHide:function(){g(!1),S()}})]}):(0,r.jsx)(r.Fragment,{})},Sr=n(52898),kr=n(15364),Ir=n(63616);function Ar(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Tr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ar(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ar(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Nr=function(){var e=(0,I.A)().styles,t=Tr((0,a.useState)(null),2),n=t[0],i=t[1],o=Tr((0,a.useState)(0),2),l=o[0],s=o[1];return(0,a.useEffect)(function(){u.nb.on(d.U.LayoutGraphTestingRightPanel,function(e){i(e)})},[]),(0,r.jsxs)("div",{className:e.rightPanel,style:{zIndex:n?5:-2},children:[(0,r.jsx)(Sr.A,{}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"显示控制"}),(0,r.jsxs)("select",{id:"select_visible_range_type",onChange:null==n?void 0:n.onSelectVisibleChange,children:[(0,r.jsx)("option",{value:"7",children:"所有元素"}),(0,r.jsx)("option",{value:"1",children:"地面元素"}),(0,r.jsx)("option",{value:"2",children:"悬挂元素"}),(0,r.jsx)("option",{value:"4",children:"顶部元素"})]})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"更新布局结果"}),(0,r.jsx)("button",{onClick:null==n?void 0:n.onUpdateAiLayoutBtnClick,children:"更新"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"微调"}),(0,r.jsx)("button",{onClick:null==n?void 0:n.onFinetuning,children:"微调"})]}),(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:"显示评分器"}),(0,r.jsx)("button",{onClick:function(){u.nb.emit(d.U.ShowLayoutScoreDialog,!0)},children:"显示&更新"})]}),(0,r.jsx)("div",{className:"row_container",style:{height:300},children:Object.keys(kr.Pv._layer_visible).map(function(e,t){return(0,r.jsxs)("div",{className:"row_div",children:[(0,r.jsx)("span",{children:kr.Pv._layer_visible[e].name}),(0,r.jsx)(Ir.A,{style:{marginLeft:10},defaultChecked:kr.Pv._layer_visible[e].visible,onChange:function(t){kr.Pv._layer_visible[e].visible=t.target.checked,s(l+1),u.nb.instance.update()}})]},"item"+t)})})]},"layout_graph_"+l)};function Cr(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function Or(){var e=Cr(["\n      display: flex;\n    "]);return Or=function(){return e},e}function Lr(){var e=Cr(["\n        /* background: #f2f2f2; */\n      padding: 14px 14px;\n      &:hover {\n          background-color: #d9d9d9;\n      }\n      &.checked {\n        background-color: #d9d9ff;\n      }\n    "]);return Lr=function(){return e},e}var Rr=(0,se.rU)(function(e){var t=e.css;return{menu_container:t(Or()),menu:t(Lr())}});function Dr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Pr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Er(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Dr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Dr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Fr={};function zr(e){var t=Rr().styles,n=Er((0,a.useState)(Fr),2),i=n[0],o=n[1],l=Er((0,a.useState)(""),2),s=l[0],c=l[1],d=function(e,t){e.subList&&(Fr[e.label]?delete Fr[e.label]:Fr[e.label]=!0,o(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Pr(e,t,n[t])})}return e}({},Fr))),e.command_name&&u.nb.RunCommand(e.command_name),c(e.label)};return e.labelList&&0!=e.labelList.length?(0,r.jsx)("div",{children:e.labelList.map(function(e,n){return(0,r.jsxs)("div",{id:"left-menu-room-list-"+n,className:t.menu+" "+(e.label===s?"checked":""),"data-room_name":e.name,onClick:function(){return d(e)},children:[(0,r.jsx)("div",{className:"label-text",children:e.label}),i&&i[e.label]&&e.subList&&e.subList.map(function(e,n){return(0,r.jsx)("div",{className:t.menu+" "+(e.label===s?"checked":""),"data-room_name":e.name,onClick:function(t){t.stopPropagation(),d(e)},children:e.label},"subItem"+n)})]},n)})}):(0,r.jsx)(r.Fragment,{})}function Mr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Br(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function Ur(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Mr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mr(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var Jr=(0,Ue.observer)(function(){var e=(0,i.A)().styles,t=Ur((0,a.useState)(!1),2),n=t[0],h=t[1],m=Ur((0,a.useState)(!1),2),p=m[0],g=m[1],y=Ur((0,a.useState)(!1),2),b=y[0],v=y[1],x=Ur((0,a.useState)(!1),2),j=x[0],_=x[1],w=Ur((0,a.useState)(null),2),k=w[0],I=w[1],A=Ur((0,a.useState)(!1),2),T=A[0],N=A[1],C=Ur((0,a.useState)(),2),O=C[0],L=C[1],R=Ur(o.A.useMessage(),2),D=R[0],P=R[1],E="trainingMessage";u.nb.NewApp(f.I.AppName),u.nb.UseApp(f.I.AppName),(0,a.useEffect)(function(){p&&u.nb.instance.update()},[p]),(0,a.useEffect)(function(){u.nb.instance&&(u.nb.t=function(e){return e},u.nb.instance.initialized||(u.nb.instance.init(),u.nb.instance.prepare().then(function(){u.nb.instance.update()})),u.nb.instance.bindCanvas(document.getElementById("room_canvas")),u.nb.instance.update()),u.nb.instance&&(u.nb.on(d.U.LoadingProgress,function(e){"start"===e.evt?h(!0):"end"===e.evt&&h(!1)}),u.nb.on(d.U.TrainingLabelsListHandle,function(e){L(e)}),u.nb.on(d.U.IssueRoomSelected,function(e){I(e)}),u.nb.on(d.U.TrainingMessage,function(e){e.key=E,e.duration=3,e.style={marginTop:"4vh",zIndex:99},D.open(e)}),u.nb.on(d.U.setLayoutScoreConfigPanelVisible,function(e){N(e)}))},[]);return(0,r.jsxs)("div",{className:e.root,children:[(0,r.jsx)(S,{updateIssueReportVisible:g,updateLayoutBatchTestVisible:v,updateModelLightConfigVisible:_}),(0,r.jsxs)("div",{className:e.content,style:b?{display:"none"}:{display:"block"},children:[(0,r.jsx)("div",{className:e.left_panel_container,children:(0,r.jsxs)("div",{className:e.left_panel_menu_box,children:[(0,r.jsx)(zr,{labelList:O}),(0,r.jsx)("div",{id:"side_list_div",className:e.left_panel_side_list})]})}),(0,r.jsx)(Be,{}),(0,r.jsx)(wr,{}),(0,r.jsxs)("div",{id:"body_container",className:e.canvas_pannel,children:[p&&(0,r.jsx)(l.A,{className:null!=k?e.enableAnalyseButton:e.disableAnalyseButton,disabled:null==k,type:"primary",onClick:function(e){return(t=function(){return Hr(this,function(t){return e.preventDefault(),g(!1),localStorage&&k&&(localStorage.setItem("layout_ai_training_current_room_data",JSON.stringify(k.exportExtRoomData())),u.nb.RunCommand("LayoutGraphTesting")),[2]})},function(){var e=this,n=arguments;return new Promise(function(r,a){var i=t.apply(e,n);function o(e){Br(i,r,a,o,l,"next",e)}function l(e){Br(i,r,a,o,l,"throw",e)}o(void 0)})})();var t},children:"计算空间布局"}),(0,r.jsx)("div",{style:{position:"absolute"},children:(0,r.jsx)(le.A,{})}),(0,r.jsx)("canvas",{id:"room_canvas",className:"canvas"})]}),(0,r.jsx)(H,{}),(0,r.jsx)(oe,{}),(0,r.jsx)(qe,{}),(0,r.jsx)(Nr,{}),T&&(0,r.jsx)(mt,{}),j&&(0,r.jsx)(On,{}),(0,r.jsx)("div",{id:"right_side_panel",className:e.right_side_panel}),(0,r.jsx)(c.Nt,{})]}),n&&(0,r.jsx)("div",{className:e.loading,children:(0,r.jsx)(s.A,{size:"large"})}),P]})})},52898:function(e,t,n){var r=n(13274),a=n(33313),i=n(41594),o=n(27347),l=n(88934),s=n(17655),c=n(55111),u=n(62837);function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){l=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw a}}return i}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){var n,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=t.call(e,i)}catch(e){l=[6,e],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}t.A=function(){(0,a.A)().styles;var e=h((0,i.useState)(!1),2),t=e[0],n=e[1],d=h((0,i.useState)([]),2),p=d[0],g=d[1],y=o.nb.t,b=((0,i.useRef)(null),o.nb.instance.layout_container),v=function(){return(e=function(){var e,t;return m(this,function(n){return(e=b._selected_room)&&(t=c.lj.ComputeScoreInRoom(e),g(t)),[2]})},function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){f(i,r,a,o,l,"next",e)}function l(e){f(i,r,a,o,l,"throw",e)}o(void 0)})})();var e},x=function(e){n(e)};return(0,i.useEffect)(function(){return o.nb.on(l.U.ShowLayoutScoreDialog,function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];x(e),e&&v()}),o.nb.on_M(l.U.UpdateLayoutScore,"LayoutScoreDialog",function(){v()}),function(){}},[]),(0,r.jsx)(r.Fragment,{children:t&&(0,r.jsx)(s._w,{title:y("布局评分器"),right:250,width:265,height:600,resizable:!0,draggable:!0,onClose:function(){x(!1)},bodyStyle:{background:"#ffffff",border:"0",boxShadow:"0"},children:(0,r.jsx)(u.A,{layoutScoreList:p,style:0})})})}},90110:function(e,t,n){n.d(t,{A:function(){return r}});var r=n(17254).A}}]);