import React, { useEffect, useState, useRef } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import Icon from '@/components/Icon/icon';
import { getPrefix } from '@/utils/common';
import { g_FigureImagePaths } from '@/Apps/LayoutAI/Drawing/FigureImagePaths';
import { Button, Tabs, Spin, Input, Tooltip, Row, Col, Slider, InputNumber } from '@svg/antd';
import { checkIsMobile, imgHostUrl } from '@/config';
import Aicabinet from '@/components/Aicabinet';
import { useTranslation } from 'react-i18next';
import { useStore } from '@/models';
import { getMaterialTopViewImage } from '@/services/material';
import { FigureCategoryManager } from '@/Apps/LayoutAI/Layout/TFigureElements/FigureCategoryManager';
import { I_MaterialMatchingItem } from '@/Apps/LayoutAI/Layout/IMaterialInterface';
import { MaterialService, I_MaterialSearchInfo } from '@/Apps/LayoutAI/Services/MaterialMatching/MaterialService';
import { checkTenantAuthorization } from '@/services/user';
import IconFont from '@/components/IconFont/iconFont';
import { EventName } from '@/Apps/EventSystem';
import { Permissions } from '@/Apps/LayoutAI/setting/Permissions';
import { PERMISSIONS } from '@/config/permissions';

// const NewReplaceProduct = lazy(() => import('@/components/NewReplaceProduct/newReplaceProduct'));
interface RoomSeriesPlanProps {
  selectedFigureElement: TFigureElement
}
interface Figure {
  imageUrl: string;
  name: string;
  modelId: string;
  length :number;
  width : number;
  height: number;
}
enum tabType {
  series = 'series',
  organ = 'organ',
  platform = 'platform',
}
const materialTemplate: Partial<I_MaterialMatchingItem> = {
  figureElement: null,
  modelId: '',
  name: '',
  imageUrl: '',
  length: 0,
  width: 0,
  height: 0,
}

const ModifyAuthCode:string = "10001146";

const GoodsProperties: React.FC<RoomSeriesPlanProps> = ({ selectedFigureElement }) => {
  const store = useStore();
  const isBusiness = store.userStore.getIsBusiness();
  const [hasPermissionkey, setHasPermission] = useState<boolean>(false);
  const { styles } = useStyles();
  const { t } = useTranslation();
  const [topInfo, setTopInfo] = useState<Figure>({
    imageUrl: '',
    name: '',
    modelId: '',
    length : 0,
    width : 0,
    height: 0,
  });
  const aicabinetRef = useRef(null);
  const [hoverInfo, setHoverInfo] = useState<I_MaterialMatchingItem>({} as I_MaterialMatchingItem);
  const [popoverTop, setPopoverTop] = useState(0);
  const [isShowPopover, setIsShowPopover] = useState(false);
  const [hideTimeoutId, setHideTimeoutId] = useState(null);
  // const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [inputValue, setInputValue] = useState<string>('');
  const [locked, setLocked] = useState<number>(-1);
  const [replacing, setIsReplacing] = useState(false);
  const goodListRootRef = useRef(null);
  const [businessSize, setBusinessSize] = useState(20);
  const [businessHasMore, setBusinessHasMore] = useState(true);

  const [platformSize, setPlatformSize] = useState(20);
  const [platformHasMore, setPlatformHasMore] = useState(true);

  const [searchIndex, setSearchIndex] = useState(1);
  const searchSize = 20;
  const [searchHasMore, setSearchHasMore] = useState(false);

  const [materialLoading, setMaterialLoading] = useState(false);

  const [materialList, setMaterialList] = useState<I_MaterialMatchingItem[]>(selectedFigureElement?._candidate_materials);
  const [platformMaterialList, setPlatformMaterialList] = useState<I_MaterialMatchingItem[]>([]);
  const [businessMaterialList, setBusinessMaterialList] = useState<I_MaterialMatchingItem[]>([]);
  const [selectedTab, setSelectedTab] = useState<tabType>(tabType.series);

  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [posZ, setPosZ] = useState(selectedFigureElement?._matched_material?.targetPosition?.z >= 0 ? selectedFigureElement?._matched_material?.targetPosition?.z : 0);
  const [businessProductType, setBusinessProductType] = useState<string>('');
  const [selectedIndexMap, setSelectedIndexMap] = useState<any[]>([
    {
      tabName: tabType.series,
      index: -1
    },
    {
      tabName: tabType.organ,
      index: -1
    },
    {
      tabName: tabType.platform,
      index: -1
    }
  ]);
  const [categoryId, setCategoryId] = useState<string>('');

  const [tabList, setTabList] = useState<{ label: string, key: string, disabled: boolean }[]>([
    {
      label: t('套系'),
      key: tabType.series,
      disabled: false
    },
    {
      label: t('企业库'),
      key: tabType.organ,
      disabled: false
    },
    Permissions.instance.hasPermission(PERMISSIONS.SERIES.CLOUD_MATERIALS) && {
      label: t('云素材'),
      key: tabType.platform,
      disabled: false
    }
  ]);
  const divRef = useRef(null);

  const openAICabinet = async () => {
    aicabinetRef.current.onModal();
  }

  // const openNewReplaceProduct = async() => {
  //   setIsReplacing(true);
  // }

  const handleParams = (params: TFigureElement) => {
    setTopInfo({
      imageUrl: params._matched_material.imageUrl,
      name: params._matched_material?.name,
      modelId: params._matched_material?.modelId,
      length: params._matched_material?.length || 0,
      width: params._matched_material?.width || 0,
      height: params.height || 0,
    });
  }

  const onClickLock = () => {
    if (selectedFigureElement._room.locked) {
      return;
    }

    if (locked == 1) {
      setLocked(0);
      selectedFigureElement.locked = false;
    } else {
      setLocked(1);
      selectedFigureElement.locked = true;
    }
  }

  useEffect(() => {
    setTabList(tabList.map((item: any) => {
      return { ...item, disabled: isLoading };
    }));
  }, [isLoading]);

  useEffect(() => {
    setInputValue('');
    setBusinessMaterialList([]);
    setPlatformMaterialList([]);
    if (FigureCategoryManager.isCustomCabinet(selectedFigureElement)) {
      setBusinessProductType('定制');
    } else {
      setBusinessProductType('成品');
    }
  }, [selectedFigureElement]);

  useEffect(() => {
    if (selectedFigureElement._matched_material && selectedFigureElement._matched_material.modelId) {
      setTopInfo({
        imageUrl: selectedFigureElement._matched_material.imageUrl,
        name: selectedFigureElement._matched_material?.name,
        modelId: selectedFigureElement._matched_material?.modelId,
        length: selectedFigureElement._matched_material?.length || selectedFigureElement.rect._w || 0,
        width: selectedFigureElement._matched_material?.width || selectedFigureElement.rect._h || 0,
        height: selectedFigureElement._matched_material?.height || 0,
      });
    }
    else {
      setTopInfo({
        imageUrl: g_FigureImagePaths[selectedFigureElement.sub_category]?.img_path || "https://3vj-fe.3vjia.com/layoutai/figures_imgs/square_pillar.svg",
        name: selectedFigureElement.modelLoc,
        modelId: null,
        length: selectedFigureElement.length || selectedFigureElement.rect._w || 0,
        width: selectedFigureElement.depth || selectedFigureElement.rect._h || 0,
        height: selectedFigureElement.height,
      });
    }
    if (selectedFigureElement._candidate_materials) {
      const index = selectedFigureElement._candidate_materials.findIndex((item: any) => item.modelId === selectedFigureElement._matched_material.modelId);
      if (index !== -1) {
        store.designStore.setSelectedIndex(index);
        setSelectedIndexMap(
          selectedIndexMap.map((item: any) => {
            if (item.tabName === tabType.series) {
              return { tabName: item.tabName, index };
            }
            return { tabName: item.tabName, index: -1 };
          })
        );
      }
      else {
        store.designStore.setSelectedIndex(-1);
      }
    }
    setLocked(selectedFigureElement.locked == null ? -1 : selectedFigureElement.locked ? 1 : 0);
    setMaterialList(selectedFigureElement._candidate_materials);
    setSelectedTab(tabType.series);
  }, [selectedFigureElement]);

  useEffect(() => {
    if (selectedFigureElement._matched_material) {
      async function fetchMaterialInfo() {
        const materialInfo = await MaterialService.getMaterialInfo([selectedFigureElement._matched_material.modelId]);
        if (materialInfo.length > 0) {
          setCategoryId(materialInfo[0].publicCategoryId);
        }
      }
      fetchMaterialInfo();
    }
  }, [selectedFigureElement]);

  const getBusinessList = async (businessInfo: I_MaterialSearchInfo) => {
    const materials = await getMaterialList(businessInfo);
    setBusinessMaterialList(materials);
    return materials;
  }

  const getPlatformList = async (platformInfo: I_MaterialSearchInfo) => {
    const materials = await getMaterialList(platformInfo);
    setPlatformMaterialList(materials);
    return materials;
  }

  const globalSearchMaterial = async (index: number) => {
    if (selectedTab === tabType.series) {
      const list = selectedFigureElement._candidate_materials?.filter((item: any) => {
        return item.name.includes(inputValue.trim()) || item.modelId.includes(inputValue.trim());
      });
      setMaterialList(list || []);
      setSearchHasMore(false);
    } else if (selectedTab === tabType.organ) {
      if (businessProductType === '成品') {
        const res = await MaterialService.finishedProductGlobalSearch({
          index: index,
          size: searchSize,
          name: inputValue,
        }, categoryId);
        if (res.data.success === false || res.data.result.recordCount === 0) {
          setSearchHasMore(false);
          setMaterialList([]);
          return;
        }
        const materialsList = await transformMaterialInfo(res.data.result.result);
        if (materialsList.length < searchSize) {
          setSearchHasMore(false);
        }
        setMaterialList((preList: I_MaterialMatchingItem[]) => [...preList, ...materialsList]);
      } else if (businessProductType === '定制') {
        const res = await MaterialService.customizedProductGlobalSearch({
          index: index,
          size: searchSize,
          name: inputValue,
        }, categoryId);
        if (res.data.success == false || res.data.result.recordCount === 0) {
          setSearchHasMore(false);
          setMaterialList([]);
          return;
        }
        const materialsList = await transformMaterialInfo(res.data.result.result);
        if (materialsList.length < searchSize) {
          setSearchHasMore(false);
        }
        setMaterialList((preList: I_MaterialMatchingItem[]) => [...preList, ...materialsList]);
      }
    } else if (selectedTab === tabType.platform) {
      const res = await MaterialService.platformGlobalSearch({
        index: index,
        size: searchSize,
        name: inputValue,
      }, categoryId);
      if (res.success === false || res.data.totalResults === '0') {
        setSearchHasMore(false);
        setMaterialList([]);
        return;
      }
      const materialsList = await transformMaterialInfo(res.data.materialExDTOList);
      if (materialsList.length < searchSize) {
        setSearchHasMore(false);
      }
      setMaterialList((preList: I_MaterialMatchingItem[]) => [...preList, ...materialsList]);
    }
  }

  const getMaterialList = async (searchInfo: I_MaterialSearchInfo) => {
    const res = await MaterialService.searchMaterial(searchInfo);
    if (res.data.data.length === 0) {
      return [];
    }
    const materiaIdList = res.data.data[0].mts;

    const materiaIds = materiaIdList.map((item: any) => item.materialId);
    const designMaterialInfoList = await MaterialService.getDesignMaterialInfoByIds(materiaIds);

    // 根据 materiaIds 的顺序对 designMaterialInfoList 进行排序
    const sortedDesignMaterialInfoList = materiaIds.map((id: string) =>
      designMaterialInfoList.find(item => item.MaterialId === id)
    ).filter(Boolean);
    return await transformMaterialInfo(sortedDesignMaterialInfoList);
  }

  const transformMaterialInfo = async (designMaterialInfoList: any[]): Promise<I_MaterialMatchingItem[]> => {
    return Promise.all(designMaterialInfoList.map(async (item: any) => {
      const material = { ...materialTemplate } as I_MaterialMatchingItem;
      material.modelId = item.MaterialId;
      material.name = item.MaterialName;
      material.imageUrl = `${imgHostUrl}${item.ImagePath}`;
      material.length = item.PICLength;
      material.width = item.PICWidth;
      material.height = item.PICHeight;
      material.modelLoc = selectedFigureElement.category;
      material.topViewImage = await getMaterialTopViewImage(item.MaterialId);
      return material;
    }));
  }

  const getMaterialSearchInfo = (num: number) => {
    const materialSearchInfo = {
      materialName: selectedFigureElement._matched_material?.name,
      materialId: selectedFigureElement._matched_material?.modelId,
      modelLocation: selectedFigureElement.modelLoc,
      traceId: '',
      imageUrl: selectedFigureElement._matched_material?.imageUrl,
      materialSource: '',
      size: [
        {
          lengthMin: 0,
          lengthMax: selectedFigureElement.length * 5
        }
      ],
      tenantId: '',
      num: num
    };
    const tenantId = store.userStore.userInfo.tenantId;
    const businessInfo = {
      ...materialSearchInfo,
      materialSource: 'organ',
      tenantId: tenantId,
      traceId: tenantId
    };

    const platformInfo = {
      ...materialSearchInfo,
      materialSource: 'platform',
      traceId: '*********'
    };

    return {
      businessInfo,
      platformInfo
    }
  }



  const onClickTab = async (key: string) => {
    setSearchIndex(1);
    setSearchHasMore(false);
    setIsSearching(false);
    setInputValue('');
    if (goodListRootRef?.current) {
      goodListRootRef.current.scrollTop = 0;
    }
    setIsLoading(true);

    if (key === tabType.series) {
      store.designStore.setSelectedIndex(selectedIndexMap.find(item => item.tabName === tabType.series).index);
      setSelectedTab(tabType.series);
      setMaterialList(selectedFigureElement._candidate_materials || []);
    }

    else if (key === tabType.organ) {
      setSelectedTab(tabType.organ);
      if (hasPermissionkey) {
        store.designStore.setSelectedIndex(selectedIndexMap.find(item => item.tabName === tabType.organ).index);
        if (businessMaterialList.length > 0) {
          setMaterialList(businessMaterialList);
        } else {
          const businessList = await loadOrganMaterialList(20);
          setMaterialList(businessList);
        }
      } else {
        setMaterialList([]);
      }
    }

    else if (key === tabType.platform) {
      store.designStore.setSelectedIndex(selectedIndexMap.find(item => item.tabName === tabType.platform).index);
      setSelectedTab(tabType.platform);
      if (platformMaterialList.length > 0) {
        setMaterialList(platformMaterialList);
      } else {
        const platformList = await loadPlatformMaterialList(20);
        setMaterialList(platformList);
      }
    }
    setIsLoading(false);
  }

  const handleScroll = async (e: any) => {
    if (materialLoading) {
      return;
    }
    if (selectedTab === tabType.series) {
      return
    }
    setMaterialLoading(true);
    const scrollBottom = e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight;
    if (scrollBottom < 900) {
      if (isSearching) {
        await searchLoad();
      } else {
        await notSearchLoad();
      }
    }
    setMaterialLoading(false);
  }

  const notSearchLoad = async () => {
    if (selectedTab === tabType.organ) {
      if (!businessHasMore) {
        return;
      }
      const newBusinessSize = businessSize + 20;
      setBusinessSize(newBusinessSize);
      const businessList = await loadOrganMaterialList(newBusinessSize);
      setMaterialList(businessList);
    } else if (selectedTab === tabType.platform) {
      if (!platformHasMore) {
        return;
      }
      const newPlatformSize = platformSize + 20;
      setPlatformSize(newPlatformSize);
      const platformList = await loadPlatformMaterialList(newPlatformSize);
      setMaterialList(platformList);
    }
  }

  const searchLoad = async () => {
    if (searchHasMore) {
      const index = searchIndex + 1;
      setSearchIndex(index);
      await globalSearchMaterial(index);
    }
  }

  const loadOrganMaterialList = async (size: number) => {
    const { businessInfo } = getMaterialSearchInfo(size);
    const businessList = await getBusinessList(businessInfo);
    businessList.map((item: any, index: number) => {
      if (item.modelId === selectedFigureElement._matched_material.modelId) {
        store.designStore.setSelectedIndex(index);
        setSelectedIndexMap(
          selectedIndexMap.map((item: any) => {
            if (item.tabName === tabType.organ) {
              return { tabName: item.tabName, index };
            }
            return { tabName: item.tabName, index: -1 };
          })
        );
      }
    });
    if (businessList.length < size) {
      setBusinessHasMore(false);
    }
    return businessList;
  }

  const loadPlatformMaterialList = async (size: number) => {
    const { platformInfo } = getMaterialSearchInfo(size);
    const platformList = await getPlatformList(platformInfo);
    platformList.map((item: any, index: number) => {
      if (item.modelId === selectedFigureElement._matched_material.modelId) {
        store.designStore.setSelectedIndex(index);
        setSelectedIndexMap(
          selectedIndexMap.map((item: any) => {
            if (item.tabName === tabType.platform) {
              return { tabName: item.tabName, index };
            }
            return { tabName: item.tabName, index: -1 };
          })
        );
      }
    });
    if (platformList.length < size) {
      setPlatformHasMore(false);
    }
    return platformList;
  };

  useEffect(() => {
    const checkPermission = async () => {
      const permission = await checkTenantAuthorization(ModifyAuthCode);
      if(permission) {
        setHasPermission(permission);
      }
    };
    checkPermission();
    LayoutAI_App.on(EventName.UpdateFigureElement, (figureElement: TFigureElement) => {
      if(selectedTab == tabType.series && figureElement?._candidate_materials)
      {
        let index = figureElement._candidate_materials.findIndex((item: any) => item.modelId === figureElement._matched_material.modelId);
        store.designStore.setSelectedIndex(index);
        setMaterialList([...figureElement._candidate_materials]);
      }
    });
  }, []);

  return (
    <div className={styles.root}>
      <div className={styles.categoryTitle}>{t('产品')}</div>
      <div className={styles.topInfo}>
        <div style={{ position: 'relative', display: 'inline-block', height: '64px', width: '72px' }}>
          <img id="good" src={topInfo.imageUrl} alt="" />
          {selectedFigureElement?.haveMatchedMaterial() && !selectedFigureElement?.checkIsMatchedSizeSuitable() && FigureCategoryManager.isCustom(selectedFigureElement) &&
            <div style={{ position: 'absolute', top: 0, left: 48 }}>
              <Icon
                iconClass="iconexclamationcircle_line"
                style={{
                  fontSize: '16px',
                  color: '#FCA905'
                }}
                className={styles.sizeWarning}
              />
            </div>}
        </div>
        <div>
          <div className={styles.category}>{t(topInfo.name)}</div>
          <div className={styles.size}>
            {selectedFigureElement?.category === "吊顶" ? (t("下吊") + " " + selectedFigureElement?._matched_material?.topOffset + "mm") : `${Math.round(selectedFigureElement?.matched_rect?._w || selectedFigureElement?.length)}*${Math.round(selectedFigureElement?.matched_rect?._h || selectedFigureElement?.depth)}*${Math.round(topInfo?.height)}`}
            <Tooltip placement="right" title={<>
                <div>{t('原尺寸')}:{ `${Math.round(selectedFigureElement?._matched_material.length)}*${Math.round(selectedFigureElement?._matched_material.width)}*${Math.round(selectedFigureElement?._matched_material.height)}`}</div>
              </>}>
              <IconFont type="icon-xinxi" style={{ fontSize: '16px', color: '#BCBEC2', cursor: 'pointer', marginLeft: '10px' }}></IconFont>
            </Tooltip>

          </div>
          <div className={styles.size}>ID: {topInfo.modelId}</div>
        </div>
        {/* {locked != -1 && <div id="lock" className={"lock_icon iconfont iconunlock_fill " + (locked == 1 ? "iconlock_fill" : "")} onClick={onClickLock}></div>} */}
      </div>
      <div className={styles.sliderHeight}>
        <span className={'sliderTitle'}>{t('离地高度')}</span>
        <Row align="middle">
          <Col span={15}>
            <Slider
              min={0}
              max={2800}
              onChange={(value: any) => {
                setPosZ(value);
                if(selectedFigureElement?._matched_material?.targetPosition?.z)
                {
                  selectedFigureElement._matched_material.targetPosition.z = value;
                }
              }}
              value={posZ}
            />
          </Col>
          <Col span={4}>
            <InputNumber
              min={0}
              max={2800}
              step={1}
              precision={0}
              size="small"
              style={{marginLeft:'6px',width:'80px' }}
              value={posZ}
              onChange={(value: any) => {
                setPosZ(value);
                if(selectedFigureElement?._matched_material?.targetPosition?.z)
                {
                  selectedFigureElement._matched_material.targetPosition.z = value;
                }
              }}
            />
          </Col>
        </Row>
      </div>
      {selectedFigureElement?.locked && <div className={styles.item_locked}></div>}

      {<div className={styles.changeLine}>
        <Tabs
          activeKey={selectedTab}
          items={tabList}
          onChange={(key) => {
            onClickTab(key);
          }}
          centered
          size="small"
        />
      </div>}
      <Spin spinning={isLoading} tip={t("加载中...")}>
        <div className={styles.findInfo}>
          <Input
            allowClear
            disabled={!hasPermissionkey && selectedTab === tabType.organ}
            value={inputValue}
            onChange={(event) => {
              if (event.currentTarget.value === '') {
                setIsSearching(false);
                if (selectedTab === tabType.organ) {
                  setMaterialList(businessMaterialList);
                } else if (selectedTab === tabType.platform) {
                  setMaterialList(platformMaterialList);
                } else {
                  setMaterialList(selectedFigureElement._candidate_materials);
                }
              }
              setInputValue(event.currentTarget.value);
            }}
            onKeyDown={async (data) => {
              if (data.key != 'Enter') return;
              if (inputValue.trim() === '') return;
              setMaterialList([]);
              setIsSearching(true);
              setSearchIndex(1);
              setSearchHasMore(true);
              setIsLoading(true);
              await globalSearchMaterial(1);
              setIsLoading(false);
            }}
            className={styles.container_input}
            placeholder={selectedTab === tabType.organ ? t("仅支持通过素材名称搜索") : t("搜索全部素材")} />
          <Icon
            className={styles.Icon}
            iconClass="iconsearch"
            style={{
              fontSize: '16px',
              color: '#6C7175',
              cursor: !hasPermissionkey && selectedTab === tabType.organ ? 'not-allowed' : 'pointer'
            }}
            onClick={async () => {
              if (!hasPermissionkey && selectedTab === tabType.organ) return;
              setIsSearching(true);
              setSearchIndex(1);
              setSearchHasMore(true);
              setIsLoading(true);
              await globalSearchMaterial(1);
              setIsLoading(false);
            }}
          >
          </Icon>
          {
            ['衣柜', '玄关柜', '餐边柜'].some(category => selectedFigureElement?.sub_category?.includes(category)) &&
            !store.userStore.aihouse && store.userStore.userInfo.tenantId !== 'C00002170' &&
            <Button style={{ marginLeft: 10 }} type="primary" size='small' onClick={openAICabinet}>
              {t('AI搭柜')}
            </Button>
          }
          {/* {!selectedFigureElement.locked && selectedFigureElement.haveMatchedMaterial() && !FigureCategoryManager.isCustomCabinet(selectedFigureElement)
            && (<Button style={{marginLeft: 5, fontSize: "12px"}} type="primary" size='small' onClick={openNewReplaceProduct}>{t('更多')}</Button>)
          } */}
        </div>
        {!replacing && materialList && materialList.length > 0 &&
          <div className={styles.goodListRoot} onScroll={handleScroll} ref={goodListRootRef}>
            <div className={styles.goodsList}>
              {materialList &&
                materialList.map((item: I_MaterialMatchingItem, index: number) => {
                  return (
                    <div
                      key={index}
                      className={`${styles.goodsItem} ${index === store.designStore.selectedIndex ? styles.selected : ''}`}
                      ref={divRef}
                      onMouseEnter={(e: any) => {
                        if (checkIsMobile()) return;
                        setHoverInfo(item);
                        const rect = e.currentTarget.getBoundingClientRect();
                        const middlePoint = rect.top + rect.height / 2 - 180;
                        const pageHeight = document.documentElement.clientHeight - 550;
                        const maxTop = Math.min(middlePoint, pageHeight);
                        setPopoverTop(maxTop);
                        setIsShowPopover(true);
                      }}
                      onMouseLeave={() => {
                        setIsShowPopover(false);
                      }}
                      onClick={() => {
                        if (selectedFigureElement.locked) return;
                        store.designStore.setSelectedIndex(index);
                        setSelectedIndexMap(selectedIndexMap.map((tab: any) => {
                          if (tab.tabName === selectedTab) {
                            return { tabName: tab.tabName, index };
                          }
                          return { tabName: tab.tabName, index: -1 };
                        }));
                        if(!item.figureElement)
                        {
                          item.figureElement = selectedFigureElement;
                        }
                        LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, item);
                        selectedFigureElement.material_source = selectedTab;
                        setTopInfo({
                          imageUrl: item.imageUrl,
                          name: item.name,
                          modelId: item.modelId,
                          length: item.targetSize?.length,
                          width: item.targetSize?.width,
                          height: item.targetSize?.height,
                        });
                      }}
                    >
                      {index === store.designStore.selectedIndex &&
                        <div className={styles.selectIcon}>
                          <Icon iconClass="iconxuanzhong" style={{ color: '#fff', fontSize: '13px' }}></Icon>
                        </div>
                      }
                      <img src={item.imageUrl} alt="" />
                      <div className={styles.sizeInfo}>{item.name}</div>
                      <div className={styles.sizeInfo}>{(Math.round(item?.length) + '*' + Math.round(item?.width) + '*' + Math.round(item?.height))}</div>
                    </div>
                  )
                })
              }
            </div>
            {
              isSearching ?
                (searchHasMore ?
                  <div className={styles.loading}>
                    <Spin tip={t('加载中...')} />
                  </div>
                  :
                  <div className={styles.loading}>
                    {t('——加载完毕——')}
                  </div>
                )
                :
                ((selectedTab === tabType.organ && businessHasMore) ||
                  (selectedTab === tabType.platform && platformHasMore) ?
                  <div className={styles.loading}>
                    <Spin tip={t('加载中...')} />
                  </div>
                  :
                  <div className={styles.loading}>
                    {t('——加载完毕——')}
                  </div>
                )
            }
          </div>
        }

        {
          !replacing &&
          selectedFigureElement?.haveMatchedMaterial2() &&
          !hasPermissionkey && selectedTab === tabType.organ &&
          <div className={styles.emptyInfo}>
            <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div className={'desc'} style={{ padding: '0 20px' }}>{t('该功能属于付费会员功能，请联系您的专属顾问进行升级~')}</div>
          </div>
        }

        {
          !replacing &&
          (!materialList || materialList.length == 0) &&
          selectedFigureElement?.haveMatchedMaterial2() &&
          !FigureCategoryManager.isCustomCabinet(selectedFigureElement) &&
          !(!hasPermissionkey && selectedTab === tabType.organ) &&
          <div className={styles.emptyInfo}>
            <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div className={'desc'}>{isSearching ? t('搜索结果为空，换个关键词试试吧~') : t('暂无内容,尝试搜索素材吧~')}</div>
          </div>
        }

      </Spin>
      {/* {!replacing && (!materialList || materialList.length == 0) && !selectedFigureElement?.haveMatchedMaterial() && !FigureCategoryManager.isCustomCabinet(selectedFigureElement) &&
        <NewReplaceProduct roomName={selectedFigureElement._room?.roomname} materialId={selectedFigureElement._candidate_materials?.[store.designStore.selectedIndex < 0 ? 0 : store.designStore.selectedIndex]?.modelId}
          modelLoc={selectedFigureElement.modelLoc}
          height={window.document.body.getBoundingClientRect().height - 285}
          closeable={false}
          keyword={t(selectedFigureElement.sub_category)}
          title={null}
          onReplaceMaterial={async (item: ResourceTipsDetailData) => {
            const topViewImage: string = await getMaterialTopViewImage(item.resourceId);
            const mm: I_MaterialMatchingItem = {
              modelId: item.resourceId,
              imageUrl: imgHostUrl + item.imageUrl,
              name: item.name,
              length: item.length,
              width: item.width,
              height: item.height,
              modelLoc: selectedFigureElement.modelLoc,
              modelFlag: item.modelFlag.toString(),
              topViewImage: topViewImage,
              figureElement: selectedFigureElement
            } as I_MaterialMatchingItem;
            LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, mm);
            setTopInfo({
              imageUrl: mm.imageUrl,
              name: mm.name,
              modelId: mm.modelId,
              length: mm.length,
              width: mm.width,
              height: mm.height,
            });
            const mindex = materialList?.findIndex((m) => m.modelId === item.resourceId);
            store.designStore.setSelectedIndex(mindex);
          }}
          onClose={() => { setIsReplacing(false) }} />
      } */}
      {/* {!replacing &&
        (!materialList || materialList.length == 0) &&
        selectedFigureElement?.haveMatchedMaterial2() &&
        FigureCategoryManager.isCustomCabinet(selectedFigureElement) &&
        !(!hasPermissionkey && selectedTab === tabType.organ) &&
        <div className={styles.emptyInfo}>
          <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
          <div className={'desc'}>{selectedFigureElement.category.includes('组合') ? t('暂无组合素材') : t('暂无内容')}</div>
          {
            selectedFigureElement.category.includes('组合') ? <div className={'desc'}>{t('双击组合内替换')}</div> : null
          }
          <div className={'desc'}>{selectedFigureElement.category.includes('组合') ? t('或联系管理员补全组合') : t('请联系管理员补全')}</div>
          {
            store.userStore.userInfo?.regSource !== 'aihouse' &&
            <Button type="primary" onClick={() => {
              window.open(`${AIDeskUrl}/rule`);
            }}>{t('去补全')}</Button>
          }
        </div>
      } */}
      {/* { !replacing && (!materialList || materialList.length == 0) && selectedFigureElement.haveMatchedMaterial2() && !FigureCategoryManager.isCustomCabinet(selectedFigureElement) &&
        <div className={styles.emptyInfo}>
          <img src={'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
          <div className={'desc'}>{t('该功能属于付费会员功能，请联系您的专属顾问进行升级')}</div>
        </div>
      } */}
      {/* {replacing && <NewReplaceProduct roomName={selectedFigureElement._room?.roomname} materialId={selectedFigureElement._candidate_materials?.[store.designStore.selectedIndex < 0 ? 0 : store.designStore.selectedIndex]?.modelId}
        modelLoc={selectedFigureElement.modelLoc}
        height={window.document.body.getBoundingClientRect().height - 164}
        closeable={true}
        title={t('替换产品')}
        seriesId={selectedFigureElement._candidate_materials?.[store.designStore.selectedIndex < 0 ? 0 : store.designStore.selectedIndex]?.seriesId}
        onReplaceMaterial={async (item: ResourceTipsDetailData) => {
          const topViewImage: string = await getMaterialTopViewImage(item.resourceId);
          const mm: I_MaterialMatchingItem = {
            modelId: item.resourceId,
            imageUrl: imgHostUrl + item.imageUrl,
            name: item.name,
            length: item.length,
            width: item.width,
            height: item.height,
            modelLoc: selectedFigureElement.modelLoc,
            modelFlag: item.modelFlag.toString(),
            topViewImage: topViewImage
          } as I_MaterialMatchingItem;
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ReplaceMaterial, mm);
          setTopInfo({
            imageUrl: mm.imageUrl,
            name: mm.name,
            modelId: mm.modelId,
            length: mm.length,
            width: mm.width,
            height: mm.height,
          });
          const mindex = materialList?.findIndex((m) => m.modelId === item.resourceId);
          store.designStore.setSelectedIndex(mindex);
        }}
        onClose={() => { setIsReplacing(false) }} />}
      {isShowPopover &&
        <div
          className={styles.goodPopover}
          style={{ top: popoverTop }}
          onMouseEnter={() => {
            if (hideTimeoutId) {
              clearTimeout(hideTimeoutId);
              setHideTimeoutId(null);
            }
          }}
          onMouseLeave={() => {
            const timeoutId = setTimeout(() => {
              setIsShowPopover(false);
            }, 500);
            setHideTimeoutId(timeoutId);
          }}>
          <img src={hoverInfo.imageUrl} alt="" />
          <div className={styles.goodPopoverInfo}>
            <div className={styles.name}>{hoverInfo.name}</div>
            <div className={styles.sizes}>{t('尺寸')}：{Math.round(hoverInfo?.length) + '*' + Math.round(hoverInfo?.width) + '*' + Math.round(hoverInfo?.height)}</div>
            <div className={styles.sizes}>{t('编号')}：{hoverInfo.modelId}</div>
            <div className={styles.sizes}>{t('推荐空间')}：{hoverInfo.applySpace ? t(hoverInfo.applySpace.join(",")) : t("全屋")}</div>

          </div>
        </div>
      } */}
      <Aicabinet onParams={handleParams} selectedFigureElement={selectedFigureElement} ref={aicabinetRef} />
    </div>
  );
};


export default observer(GoodsProperties);
