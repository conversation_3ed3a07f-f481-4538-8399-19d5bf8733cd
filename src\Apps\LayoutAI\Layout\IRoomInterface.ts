import { Vector3, Vector3<PERSON>ike } from "three";
import { ZRect } from "@layoutai/z_polygon";
import { I_ZRectData, ZRectShapeType } from "@layoutai/z_polygon";


export const KeyEntity = "Entity";
export const KeyPolyTargetType = "poly_target_type";
/**
 * 线条
 */
type LineEdge = {
    /** 线起点 */
    start: Vector3Like;
    /** 线终点 */
    end: Vector3Like;
};
export interface I_Wall {
    StartX: number;
    EndX: number;
    StartY: number;
    EndY: number;
    Thickness?: number;
    uid: number | string;
    boundary: LineEdge[];
    type?: IRoomEntityType;
}


export type SceneViewMode = "2D" | "3D_FirstPerson" | "3D"; /*[i18n:ignore]*/


export interface I_RoomModelLocs {
    name: string;
    modelloc: I_ModelLoc[];
}
export interface I_ModelLoc {
    desc?: string;
    name: string;
    publicCategory?: string[];
    chain?: string[];
}

export interface I_InnerWall {
    StartX: number;
    EndX: number;
    StartY: number;
    EndY: number;
    Thickness?: number;
    WallId?: number;
    type?: string;
}


export interface I_Entity3D {
    id?: number;
    length?: number;
    width?: number;
    height?: number;
    mirror?: number;
    type?: IRoomEntityType;
    realType?: IRoomEntityRealType;
    posX?: number;
    posY?: number;
    posZ?: number;
    rotateZ?: number;
    _nor_data?: Vector3Like;
    _center_data?: Vector3Like;
    center?: Vector3;
    nor?: Vector3;
    rect?: ZRect;
    [key: string]: any;
}


export enum AI_PolyTargetType {
    RoomArea = "RoomArea",
    Wall = "Wall",
    Door = "Door",
    Window = "Window",
    Furniture = "Furniture",
    StructureEntity = "StructureEntity",
    WaterPipe = "WaterPipe",
    Group = "Group",
    BaseGroup = "BaseGroup",
    Ruler = "Ruler",
    RoomSubArea = "SubArea"
}

export type SolverMethods = "BasicTransfer" | "GroupTransfer" | "SpacePartition" | "SubSpaceTransfer";

export interface I_CanvasImageResult {
    /**
     *  图像Base64
     */
    base64: string;
    /**
     *  宽
     */
    width: number;
    /**
     *  高
     */
    height: number;
    /**
     *  transform信息
     * 
     */
    transform: {
        _p_center: Vector3Like;
        _p_zval: number;
        _p_sc: number;
    },
    /**
     *  矩阵信息
     */
    project_matrix3: number[];
}
export interface I_RoomLayoutSchemeResult {
    room_name: string;
    uid: string;
    uuid?: string;
    layout_scheme_num?: number;
    layout_scheme_list?: any[];
}
export enum DefaultCeilingType {
    /**
     * 平顶
     */
    Flattop = "Flattop",
    /**
     *  窗帘盒
     */
    CurtainBox = "CurtainBox",
    /**
     *  悬边吊顶
     */
    OverhangingEdge = "OverhangingEdge",
    /**
     *  悬浮吊顶
     */
    Suspended = "Suspended"
}
export enum DefaultSectiontype {
    /**
     *  
     */
    RectSection = "RectSection",
    /**
     *  L型
     */
    LShapeSection = "LShapeSection",

}

export interface I_SectionData {
    points?: Vector3Like[];

}

/**
 *  生成吊顶层的方法
 */
export type GenCeilingLayerMethod = "Offset" | "Drawing" | "Rect";
/**
 *  灯槽信息
 */
export interface I_LightSlotData {
    /**
     *  1:外拓 -1:内缩
     */
    direction: number;

    extrude_length: number;

    extrude_height: number;

    /**
     *  截面轮廓
     */
    section_data?: I_SectionData;
}

export interface I_RoomSubAreaSimpleData {
    /**
     *  UUID, 类似指针的作用
     */
    uuid?: string;


    /**
     *  房间uuid
     */
    room_uuid?: string;
    /**
     *  在房间中的序号
     */
    index?: number;

    /**
     *  名称: 客厅、餐厅
     */
    name?: string;

    /**
     *  矩形信息: 其表达是 w,h + 一个法向nor + 一个背靠中心点
     */
    area_rect?: I_ZRectData;

    /**
     *  对应的多边形点位信息: 暂时都是矩形
     */
    area_points?: Vector3Like[];


    /**
     *  吊顶子区域: 如悬浮吊顶或悬边吊顶的中间的矩形, 平吊没有中间子区域
     */
    sub_ceiling_area_rect?: I_ZRectData;
    /**
     *  sub_ceiling_area_rect 对应的顶点
     */
    sub_ceiling_area_points?: Vector3Like[];

    /**
     *  默认吊顶类型, 参考DefaultCeilingLayerTemplate
     */
    ceiling_type?: string;

    /**
     *  如果ceiling_layer_data为空 且 如果设置了ceiling_type, 会调用默认的ceiling_type生成ceiling_layer_data;
     *  如果ceiling_layer_data不为空, 则会使用ceiling_layer_data的信息, 来构造生成吊顶
     */
    ceiling_layer_data?: I_CeilingLayerData;

    /**
     *  是否开关---自动生成子分区: 如果为true, 子分区会跟踪家装布局; 那么即使修改了area_rect, 很快也会变回来。
     */
    is_auto_sub_area?: boolean;

}
export interface I_SimpleFigureElement {
    uuid?: string;
    category?: string;
    sub_category?: string;

    materialId?: string;
    /**
     *  形状: 主要是矩形和圆形
     *  Circle | Rect
     */
    _rect_shape?: ZRectShapeType;

    /**
     *  0: 嵌装 1:明装
     */
    installType?: number;

    length?: number;  // 长度
    depth?: number;   // 深度
    height?: number;  // 高度
    topOffset?: number; // 暗装时的顶部偏移
    pos?: Vector3Like; // 中心点位置
    nor?: Vector3Like; // 法向
    _rect_data?: I_ZRectData;
    /**
     *  是否是饰品图元
     */
    _is_decoration?: boolean;

    // 灯光颜色
    color?: number;
    // 灯光亮度
    brightness?: number;
}

/**
 *   吊顶层: 是一个树状图层
 *   --- 这是一个最简封装
 */
export interface I_CeilingLayerData {

    name?: string;
    method: GenCeilingLayerMethod;
    ceiling_type?: string;
    offset_value?: number;
    offset_func?: string;

    drawing_points?: Vector3Like[];
    /**
     *   子吊顶-层
     */
    children?: I_CeilingLayerData[];
    /**
     *  离上一层的偏移
     */
    zvalToTop: number;

    /**
     *  离上一层的偏移
     */
    aroundToTop?: number;

    /**
     *  函数动态计算
     */
    zvalToTopFunc?: string;

    /**
     *  灯槽数据
     */
    lightSlotData?: I_LightSlotData;


    downLightsData?: I_SimpleFigureElement[];
    /**
     *  2D绘制样式
     */
    strokeStyle?: string;

}
export interface I_RoomSubAreasResult {
    room_name: string;
    room_uid: string;
    room_uuid: string;
    sub_areas: I_RoomSubAreaSimpleData[];
}


export var DefaultSection: { [key: string]: I_SectionData } = {}

DefaultSection[DefaultSectiontype.RectSection] = {
    points: [
        { x: 0, y: 0, z: 0 }, { x: 1, y: 0, z: 0 }, { x: 1, y: 1, z: 0 }, { x: 0, y: 1, z: 0 }
    ]
}

DefaultSection[DefaultSectiontype.LShapeSection] = {
    points: [
        { x: 0, y: 0, z: 0 }, { x: 1, y: 0, z: 0 }, { x: 1, y: 1, z: 0 }, { x: 0.6, y: 1, z: 0 }, { x: 0.6, y: 0.2, z: 0 }, { x: 0, y: 0.2, z: 0 }
    ]
}
export var DefaultCeilingLayerTemplate: { [key: string]: I_CeilingLayerData } = {}

export function cloneSectionData(data: I_SectionData): I_SectionData {
    return {
        points: data.points.map((p) => { return { x: p.x, y: p.y, z: p.z } })
    }
}
DefaultCeilingLayerTemplate[DefaultCeilingType.Flattop] = {
    name: DefaultCeilingType.Flattop,
    method: "Rect",
    zvalToTop: 400,
    zvalToTopFunc: "(maxCeilingHeight)"
}
DefaultCeilingLayerTemplate[DefaultCeilingType.CurtainBox] = {
    name: DefaultCeilingType.CurtainBox,
    method: "Rect",
    zvalToTop: 1 // 下吊1

}
DefaultCeilingLayerTemplate[DefaultCeilingType.OverhangingEdge] = {
    // 外层
    name: DefaultCeilingType.OverhangingEdge,
    method: "Rect",  // 方法 分为Rect 和 Offset 选择Offset 就会有offset_value 值设置
    zvalToTop: 400, // 离顶400
    zvalToTopFunc: "(maxCeilingHeight)", // 如果设置zvalToTopFunc 则使用函数计算zvalToTop自适应zvalToTop值就不生效了  
    children: [
        {
            // 内层 
            method: "Offset",  // 同外层方法
            zvalToTop: 1, // 同外层zvalToTop
            offset_value: 100, // 偏移值
            strokeStyle: "#aaa", // 线框颜色
            lightSlotData: {
                direction: -1, // 灯槽方向 1 为正方向  -1 为反方向
                extrude_length: 50, // 灯槽长度
                extrude_height: 20, // 灯槽高度
                section_data: cloneSectionData(DefaultSection[DefaultSectiontype.LShapeSection]), // 灯槽形状 这个固定
            }
        }
    ]
}
DefaultCeilingLayerTemplate[DefaultCeilingType.Suspended] = {
    name: DefaultCeilingType.Suspended,
    method: "Rect",
    zvalToTop: 1,
    children: [
        {
            method: "Offset",
            zvalToTop: 400, // 下掉400
            zvalToTopFunc: "(maxCeilingHeight)",
            offset_value: 400,
            strokeStyle: "#aaa",
            lightSlotData: {
                direction: 1,
                extrude_length: 50,
                extrude_height: 20,
                section_data: cloneSectionData(DefaultSection[DefaultSectiontype.LShapeSection]),
            }
        }
    ]
}

/**
 *  作为入参, 只有kgId是一定必要的
 */
export interface I_StyleSeriesItem {
    kgId: string | number;
    id?: number;
    seriesStyle?: string;
    ruleId?: number;
    ruleImageList?: string[];
    ruleName?: string;
    status?: number;
    thumbnail?: string;

    seedSchemeId?: string;
    schemeId?: string;
    schemeName?: string;
    name?: string;
    roomList?: any[];
    [key: string]: any;
}
/**
 *   门因为兼容一些测试数据, 暂时先用DOOR 全大写
 */
export type IRoomEntityType = "Base" | "Window" | "Door" | "Hallway" | "Wall" | "StructureEntity" | "RoomArea"
    | "Furniture" | "Cabinet" | "WaterPipe" | "Group" | "BaseGroup" | "GroupTemplate" | "ExDrawing" | "SubArea" | "Ruler" | "RoomSpaceMark" | "CeilingLayer" | "Ceiling"
    | "BaseSpace";

export type IRoomEntityRealType = "Wall" | "OutterWall" | "InnerWall" | "LoadBearingWall" | "SingleDoor" | "GeneratedWall" | "SlidingDoor" | "DoubleDoor" | "BayWindow" | "OneWindow" | "Flue"
    | "Platform" | "Beam" | "Pillar" | "Envelope_Pipe" | "SewagePipe" | "WastePipe" | "Lighting" | "LightStrip"
    | "SoftFurniture" | "Cabinet" | "Door" | "Decoration" | "Electricity" | "ViewCamera" | "MovingLine" | "ReferLine" | "CeilingLayer" | "Ceiling" | "DoorHole" | "SafetyDoor" | "Railing" | "PassDoor";

export enum TRoomNameType {
    Default = "Default",
    Balcony = "Balcony",
    Kitchen = "Kitchen",
    LivingRoom = "LivingRoom",
    Bedroom = "Bedroom",
    Study = "Study", // 书房
    Washroom = "Washroom",
    Entrance = "Entrance",
    StorageRoom = "StorageRoom",
    Living = "Living",
    Dining = "Dining",

}
/**
 *  分区类别:
 *     --- 卧室拆成: Sleeping|Dressing
 */
export const RoomSpaceAreaType = {
    LivingArea: "LivingArea",
    DiningArea: "DiningArea",
    SleepingArea: "SleepingArea",
    DressingArea: "DressingArea",
    WashingArea: "WashingArea",
    StudyArea: "StudyArea",
    EntertainmentArea: "EntertainmentArea",
    KitchenArea: "KitchenArea",
    BalconyArea: "BalconyArea",
    HallwayArea: "HallwayArea",
    EntranceArea: "EntranceArea",
    UnknownArea: "UnknownArea",
} as const;

export const BaseSpaceType = {
    RoomGroupSpace: "RoomGroupSpace",
    PublicAreaSpace: "PublicAreaSpace",
    OtherAreaSpace: "OtherAreaSpace",
    BaseSpace: "BaseSpace"
}
export type IRoomSpaceAreaType = typeof RoomSpaceAreaType[keyof typeof RoomSpaceAreaType];
export type IBaseSpaceType = typeof BaseSpaceType[keyof typeof BaseSpaceType];
export const IType2UITypeDict: { [key: string]: string } = {
    "Window": "窗",
    "OneWindow": "一字窗",
    "Door": "门",
    "StructureEntity": "结构件",
    "Wall": "墙",
    "SingleDoor": "单开门",
    "SlidingDoor": "推拉门",
    "DoubleDoor": "双开门",
    "BayWindow": "飘窗",
    "Flue": "烟道",
    "Platform": "地台",
    "Pillar": "柱子",
    "Beam": "横梁",
    "Envelope_Pipe": "包管",
    "RoomArea": "空间",
    "SubArea": "空间分区",
    "Furniture": "家具",
    "SewagePipe": "污水管",
    "WastePipe": "废水管",
    "Lighting": "照明模块",
    "SoftFurniture": "软装家具",
    "Cabinet": "定制柜",
    "InnerWall": "内墙",
    "OutterWall": "外墙",
    "LoadBearingWall": "承重墙",
    "Electricity": "水电",
    "ExDrawing": "画笔绘制",
    "MovingLine": "动线",
    "DoorHole": "门洞",
    "SafetyDoor": "子母门",
    "PassDoor": "垭口",
    "Railing": "栏杆",
    "BaseSpace": "基础空间",
    "GeneratedWall": "生成墙",
    [RoomSpaceAreaType.LivingArea]: "客厅区",
    [RoomSpaceAreaType.DiningArea]: "餐厅区",
    [RoomSpaceAreaType.SleepingArea]: "卧室区",
    [RoomSpaceAreaType.DressingArea]: "衣帽间",
    [RoomSpaceAreaType.WashingArea]: "洗漱区",
    [RoomSpaceAreaType.KitchenArea]: "厨房区",
    [RoomSpaceAreaType.BalconyArea]: "阳台区",
    [RoomSpaceAreaType.HallwayArea]: "过道区",
    [RoomSpaceAreaType.StudyArea]: "书房区",
    [RoomSpaceAreaType.EntertainmentArea]: "娱乐区",
    [RoomSpaceAreaType.EntranceArea]: "玄关区",
    [RoomSpaceAreaType.UnknownArea]: "未命名",
    [BaseSpaceType.PublicAreaSpace]: "公共区",
    [BaseSpaceType.RoomGroupSpace]: "房间区",
    [BaseSpaceType.OtherAreaSpace]: "其它区",
    [BaseSpaceType.BaseSpace]: "基础空间"

};

export const IUIType2TypeDict: { [key: string]: string } = {}
for (let key in IType2UITypeDict) {
    let val = IType2UITypeDict[key];
    IUIType2TypeDict[val] = key
}

export const IRoomFloorColor: { [key: string]: string } = {
    "客餐厅": "#fff",
    "卧室": "#fffaf3",
    "卫生间": "#f4fbff",
    "厨房": "#fff3ef",
    "阳台": "#fff",
    "书房": "#fffaf3",
    "入户花园": "#fff"
}
export interface I_Window extends I_Entity3D {
    id?: number;
    length?: number;
    width?: number;
    height?: number;
    mirror?: number;
    type?: IRoomEntityType;
    realType?: IRoomEntityRealType;
    posX?: number;
    posY?: number;
    posZ?: number;
    rotateZ?: number;
    openDirection?: number;
    _nor_data?: Vector3Like;
    _center_data?: Vector3Like;
    center?: Vector3;
    nor?: Vector3;
    rect?: ZRect;

    rooms?: I_Room[];
    room_names?: string[];
    // _is_sunning ?: boolean;

    layon_edge_length?: number;
}

export interface I_Door extends I_Entity3D {
}

/**
 *  柱子
 */
export interface I_Pillar extends I_Entity3D {
}

export interface I_Flue extends I_Entity3D {

}

export interface I_E_Pipe extends I_Entity3D {

}
/**
 *  横梁 
 */
export interface I_Beam extends I_Entity3D {

}
export enum DrawingFigureMode {
    Figure2D = 1,
    Texture = 2,
    Outline = 3,
}


export enum DrawingLayerNames {
    BlankDrawing = "BlankDrawing",  // 空图层, 应该是对应毛坯图层---保留，暂时没用
    CadEzdxfDrawing = "CadEzdxfDrawing",  // EzdxfCad数据层, 原始数据层
    CadRoomStrucure = "CadRoomStrucure",        // Cad房型数据层
    CadFurniture = "CadFurniture",        // Cad家具图元数据层
    CadCabinet = "CadCabinet",               // Cad定制柜图元数据层
    CadOutLine = "CadOutLine",               // Cad内轮廓数据层
    CadRoomName = "CadRoomName",        // 房间名称层
    CadDecorates = "CadDecorates",   // Cad饰品层
    CadSubRoomAreaDrawing = "CadSubRoomAreaDrawing", // 子分区
    CadLighting = "CadLighting",   // Cad灯光层
    CadElectricity = "CadElectricity",
    CadCeiling = "CadCeiling",   // Cad吊顶层
    AILayoutDrawing = "AILayoutDrawing",  // AI布局结果层--- 用于智能布置
    AIMatchingDrawing = "AIMatchingDrawing",  // AI布局结果层--- 用于智能布置
    CadFloorDrawing = "CadFloorDrawing",  // Cad地板数据层
    ExtDrawingDrawing = "ExtDrawingDrawing",
    ExportCadDrawing = "ExportCadDrawing",
    CadDimensionWallElement = "CadDimensionWallElements",   // 墙线标注层
    CadCopyImageDrawing = "CadCopyImageDrawing",     //临摹图
    RulerDrawing = "RulerDrawing",   // 标尺层
    Remodeling = "Remodeling",     // 拆改图层
    Scene3DViewImageLayer = "Scene3DViewImageLayer"

}
export interface I_Room {
    id?: number;
    _t_id?: number;
    uuid?: string;
    _uuid?: string;
    uid?: string;
    schemaId?: string;
    schemaName?: string;
    inner_walls?: I_InnerWall[];
    points?: number[][];
    area?: number;
    name?: string;
    roomname?: string;
    aliasName?: string;
    room_type?: string;
    windows?: I_Window[];
    pillars?: I_Pillar[];
    platforms?: I_Entity3D[];
    flues?: I_Flue[];
    pipes?: I_E_Pipe[];
    storey_height?: number;
    ceiling_infos?: object[];
    sub_areas?: I_RoomSubAreaSimpleData[];
}

export interface I_Layout {
    schemaId: string;
    schemaName: string;
    walls: I_Wall[];
    rooms: I_Room[];
    area: string;
}


export class TsAI_app {
    static Quiet: boolean = true;
    static get log() {
        if (TsAI_app.Quiet) {
            return (message: string, optionalParams: any) => { };
        }
        else {
            return console.log;
        }
    }
}



export enum TRoomAreaType {
    Default = "Default",
    Hallway = "Hallway", // 过道
    FreeArea = "FreeArea", // 自由过道区
    LivingMain = "LivingMain", // 客餐厅主区
    MixedArea = "MixedArea",
    DiningRoom = "DiningRoom",  // 餐厅区 or 其它区
    LivingCabinet = "LivingCabinet", // 客厅-柜子放置区
    EntranceArea = "EntranceArea"   // 入户区
}
export enum TRoomNeighborCode {
    Default = 0,
    Entrance = 1, // 入户
    Balcony = 2,  // 阳台
    Kitchen = 4,  // 厨房,
    Bedroom = 8,   // 卧室
    LivingRoom = 16
}


// export enum TRoomNameCHS_Type
// {
//     Default = "Default",
//     Balcony = "阳台",
//     Kitchen = "厨房",
//     LivingRoom = "客餐厅",
//     Bedroom = "卧室",
//     Study = "书房", // 书房
//     Washroom = "卫生间",
//     Entrance = "入户"
// }
export var TRoomNameDict: { [key: string]: string } = {
    "客餐厅": TRoomNameType.LivingRoom,
    "阳台": TRoomNameType.Balcony,
    "阳台**": TRoomNameType.Balcony,
    "厨房": TRoomNameType.Kitchen,
    "卧室": TRoomNameType.Bedroom,
    "卫生间": TRoomNameType.Washroom,
    "书房": TRoomNameType.Study,
    "入户花园": TRoomNameType.Entrance,
    "储物间": TRoomNameType.StorageRoom,
    "客厅区": TRoomNameType.Living,
    "餐厅区": TRoomNameType.Dining,
    "洗漱区": TRoomNameType.Washroom,
    "卧室区": TRoomNameType.Bedroom
}

export enum FigureZValRangeType {
    None = 0,
    OnFloor = 1,
    OnHalfWall = 2,
    HighCabinet = 3,
    OnRoomTop = 4,
    All = 7
}

export var TFigureVisibleRangeMap: { [key: number]: { z_min: number, z_max: number } } = {
    0: { z_min: -1000, z_max: 0 },
    1: { z_min: -1000, z_max: 1400 },
    2: { z_min: 1400, z_max: 2300 },
    4: { z_min: 2300, z_max: 3000 },
}


export interface I_BaseGroupElementInfo {
    group_p_center: Vector3Like;
    group_p_nor: Vector3Like;
    group_p_angle: number;

    group_p_center_pp?: Vector3;
    group_p_right_pp?: Vector3;
    group_p_top_pp?: Vector3;

    parent_rect?: ZRect;

    // 给一些可扩展
    [key: string]: any;
}
export enum TRoomEdgeProp {
    SrcRoomEdge = "SrcRoomEdge",
    WindowWeight = "WindowWeight",
    BindSunningEdge = "BindSunningEdge",
    BindMaxSolidEdge = "BindMaxSolidEdge",
    CandidateLevel = "CandidateLevel",
    SrcPolyId = "src_poly_id",
    RectEdgeId = "RectEdgeId"
}
export const HallwayWidth = 2001;
export const MaxHallwayWidth = 2501;
export const MidHallwayWidth = 1501;
export const MinHallwayWidth = 1200; // 最小过道距离
export const CodedLevelLen = 1000;
export const AlignWallDist = 250;
export const FreeAreaLen = 500;
export const MaxCabinetAreaDepth = 1001;
export const MaxWindowLayonDepth = 750;


// 玄关柜 距离 入户门的最远距离
export const MaxEntranceDist = 2200;
export const MinEntranceCabinetLength = 1000;
export const MaxEntranceCabinetLength = 2200;

export const EdgeProp_3DVisible = "EdgeProp_3DVisible";