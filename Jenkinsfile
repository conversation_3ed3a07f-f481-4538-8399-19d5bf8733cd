pipeline {
    agent {
        label 'built-in'
    }
    
    parameters {
        booleanParam(name: 'reinstall_dependencies', defaultValue: false, description: '重新安装React工程Node依赖包(使用pnpm)')
        booleanParam(name: 'only_zip_not_push', defaultValue: false, description: '构建后，仅将编译结果压缩打包，不提交到git仓库')
        string(name: 'git_branch', defaultValue: "develop", description: 'git代码仓库的分支名称. 目前的可用分支：develop, master')
    }

    stages {
        stage('代码更新') {
            steps {
                dir("D:/git/layout_ai") {
                    echo "当前使用的git分支: ${params.git_branch}"
                    script {
                        bat encoding: 'utf8', script: "IF EXIST 'dist' (RMDIR /S /Q 'dist')"
                        bat encoding: 'utf8', script: "del dist.zip"
                        bat encoding: 'utf8', script: "git fetch"
                        bat encoding: 'utf8', script: "git clean -fd"
                        bat encoding: 'utf8', script: "git reset HEAD --hard"
                        bat encoding: 'utf8', script: "git checkout ${params.git_branch}"
                        bat encoding: 'utf8', script: "git reset origin/${params.git_branch} --hard"
                        bat encoding: 'utf8', script: "git log -16"
                    }
                }
            }
        }
        stage('依赖安装') {
            steps {
                dir("D:/git/layout_ai") {
                    script {
                        if (params.reinstall_dependencies) {
                            bat encoding: 'utf8', script: "pnpm install --no-frozen-lockfile"
                        }
                }
            }
        }

        stage('编译构建') {
            steps {
                dir("D:/git/layout_ai") {
                    script {
                        bat encoding: 'utf8', script: "npm run build"
                    }
                }
            }
        }
        stage('提交编译产物') {
            steps {
                dir("D:/git/layout_ai") {
                    script {
                        if (params.only_zip_not_push == false) {
                            bat encoding: 'utf8', script: "git add ."
                            bat encoding: 'utf8', script: "node commit-build-log.js"
                            bat encoding: 'utf8', script: "git pull --rebase"
                            bat encoding: 'utf8', script: "git log -3"
                            bat encoding: 'utf8', script: "git push"
                        }
                        bat encoding: 'utf8', script: "IF EXIST ai-plugin-${BUILD_NUMBER}.zip (del /f /q ai-plugin-${BUILD_NUMBER}.zip)"
                        bat encoding: 'utf8', script: "IF EXIST ai-plugin-${BUILD_NUMBER} (RMDIR /S /Q ai-plugin-${BUILD_NUMBER})"
                        bat encoding: 'utf8', script: "REN dist ai-plugin-${BUILD_NUMBER}"
                        bat encoding: 'utf8', script: "powershell Compress-Archive -Path ai-plugin-${BUILD_NUMBER} -DestinationPath ai-plugin-${BUILD_NUMBER}.zip"
                        archiveArtifacts artifacts: "ai-plugin-${BUILD_NUMBER}.zip", fingerprint: true
                        bat encoding: 'utf8', script: "IF EXIST ai-plugin-${BUILD_NUMBER}.zip (del /f /q ai-plugin-${BUILD_NUMBER}.zip)"
                    }
                }
            }
        }
    }
}
