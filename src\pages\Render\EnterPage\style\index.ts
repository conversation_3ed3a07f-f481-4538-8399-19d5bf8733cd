import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ token, css }) => {
  return {
    enterPage: css`
      position:fixed;
      left:0;
      bottom:0;
      height: calc(var(--vh, 1vh) * 100);
      width:100%;
      z-index: 999;
      background: #fff;
      .slide-enter {
        transform: translateX(-100%);
        opacity: 0;
      }

      .slide-enter-active {
        transform: translateX(0);
        opacity: 1;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .slide-exit {
        transform: translateX(0);
        opacity: 1;
      }

      .slide-exit-active {
        transform: translateX(100%);
        opacity: 0;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }



      .slide-reverse-enter {
        transform: translateX(100%);
        opacity: 0;
      }

      .slide-reverse-enter-active {
        transform: translateX(0);
        opacity: 1;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .slide-reverse-exit {
        transform: translateX(0);
        opacity: 1;
      }

      .slide-reverse-exit-active {
        transform: translateX(-100%);
        opacity: 0;
        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
      }

      .upload_hx
      {
        position: absolute !important;
        right: 30px !important;
        bottom: 30px !important;
        top: auto !important;
        transform: none !important;
        z-index: 1000;
        display: flex;
        width: 48px;
        height: 90px;
        padding: 24px 2px;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        .upload_title {
          color: #333;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 10px;
          font-style: normal;
          font-weight: 400;
          line-height: 10px; /* 100% */
        }
      }
    `,
    selectHx: css`
      padding: 5px 10px 0 10px;
      height: 100%;
      /* .right_btns
      {
        position: fixed;
        right: 25px;
        top: 25px;
      } */

    `,
    hxRoot: css`
      width: 100%;
      height: 100%;
    `,
    selectDemand: css`
      padding: 0px 40px;
      height: calc(var(--vh, 1vh) * 100 - 170px);
      margin-top: 16px;
      overflow-y: scroll;
      ::-webkit-scrollbar
      {
        display: none;
      }
      .demandLabel
        {
          font-weight: 600;
          font-size: 16px;
          color: #000;
          margin-bottom: 8px;
          margin-top: 20px;
        }
      .demandItem
      {
       
        .tabRoot
        {
          display: flex;
          flex-wrap: wrap;
        }
      }
      .demandtab
      {
        display: flex;
        width: 100px;
        height: 32px;
        padding: 4px 16px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        background: #F2F3F4;
        margin-right: 12px;
        margin-bottom: 12px;
      }
      .selected
      {
        border-radius: 6px;
        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);
        color: #fff;
      }
    `,
    styleTitle: css`
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,
    demandRoot: css`
      width: 100%;
      height: 100%;
    `,
    hxHeader: css`
      display: flex;
      padding: 6px 20px;
      height: 40px;
      align-items: center;
      gap: 10px;
      align-self: stretch;

      .title {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1 0 0;
        .title_text {
          color: #282828;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
      }

      .layoutPlusButton{
        display: flex;
        padding: 4px 12px;
        align-items: center;
        gap: 4px;
        border-radius: 50px;
        background: linear-gradient(90deg, #EDE5FF 0%, #EAF0FF 100%);
        border: none;
        height: 30px;
        
        .text {
          color: #5C42FB !important;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 13px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
      .mySchemeButton{
        display: flex;
        padding: 4px 12px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 20px;
        background: #E9EBEB;
        border: none;
        height: 30px;
        .text {
          color: #282828 !important;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 13px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
    `,
    bottom: css`
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 88px;
      background: #fff;
      display: flex;
      align-items: center;
      padding: 20px 60px;
      justify-content: space-between;
      .ant-btn
      {
        width: 160px;
        height: 48px;
        border-radius: 24px;
      }
      .rotate
      {
        font-size: 16px;
        color: #5B5E60;
      }
    `,
    container_listInfo: css`
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      margin-top: 20px;
    `,
    container_list: css`
      width: calc(20% - 10px);
      height: auto;
      padding: 2px;
      box-sizing: border-box;
      position: relative;
      margin-right: 10px;
      @media (max-width: 800px) {
        width: calc(33.33% - 10px);
      }
      img{
        width: 100%;
        aspect-ratio: 5/3;
      }
    `,
    textInfo: css`
      padding: 0 5px;
      `,
    container_title: css`
      color: #282828;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      margin-top: 5px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      .ant-rate
      {
        color: #FFAA00;
        font-size: 16px !important;
        .ant-rate-star:not(:last-child)
        {
          margin-inline-end: 3px;
        }
      }
    `,
    container_desc: css`
      color: #6C7175;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 12px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      display: flex;
      margin-top: 5px;
    `,
  }

});
