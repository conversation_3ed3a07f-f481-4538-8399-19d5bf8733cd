import { createMagiccubeRequest } from "@svg/request";

function arrayBufferTo<PERSON>son(buffer: ArrayBuffer) {
    const decoder = new TextDecoder('utf-8');
    const jsonString = decoder.decode(new Uint8Array(buffer));
    return JSON.parse(jsonString);
}

const magiccubeRequest = createMagiccubeRequest();

/** 通用请求方法 */
async function postRequest(url: string, options?: { [key: string]: any }) {
    const args = {
        method: 'post',
        url,
        timeout: 10000,
        responseType: 'arraybuffer',
        data: options
    };
    const res = await magiccubeRequest(args).catch((e: any) => {
        console.error(e);
        return { success: false, msg: e.message };
    });
    if (res instanceof ArrayBuffer) {
        return arrayBufferToJson(res);
    }
    return res;
}

/** 修改方案  */
export async function editSchemeInfo(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutScheme/edit', options);
}

/** 删除方案  */
export async function deleteScheme(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutScheme/delete', options);
}

/** 创建方案副本  */
export async function copySheme(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutScheme/copy', options);
}

/** 获取方案列表  */
export async function listByPage(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutScheme/listByPage', options);
}

/** 获取方案表详情  */
export async function getSchemeInfo(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutScheme/get', options);
}

/** 分页查询方案附件信息表  */
export async function filesListByPage(options?: { [key: string]: any }) {
    return postRequest('api/njvr/LayoutSchemeFiles/listByPage', options);
}

/** 获取附件效果图列表  */
export async function getAiDrawImgList(options?: { [key: string]: any }) {
    return postRequest('api/njvr/aidrawImageResult/listByPage', options);
}