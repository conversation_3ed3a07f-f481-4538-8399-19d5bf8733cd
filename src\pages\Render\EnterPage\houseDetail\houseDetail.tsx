import React, { useEffect, useState } from 'react';
import useStyles from './style';
import { Icon } from "@svg/antd-cloud-design";
import { useStore } from '@/models';
import { Button } from "@svg/antd";
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { getMyCaseById } from "@/services/home";
import { message } from '@svg/antd';
import { useTranslation } from "react-i18next";

const HouseDetail: React.FC<{ 
      closeHouseDetail: () => void,
      toLayout: (type: 'layout' | 'view') => void,
      hxId?: string
    }> = ({ closeHouseDetail, toLayout, hxId }) => {
    const { styles } = useStyles();
    const { t } = useTranslation();
    const store = useStore();
    const { houseData } = store.trialStore;
    const [houseDataById, setHouseDataById] = useState<any>({});
    const [isMyCase, setIsMyCase] = useState(false); // 标记是否是从我的方案处跳转

    const getHouseDataById = async (hxId: string) => {
        const res = await getMyCaseById({id: hxId}).catch((e: any): null => {
            console.log(e);
            setHouseDataById({});
            return null;
        });
        if(res?.success) {
            setHouseDataById(res.result);
        }
    }

    const openLayoutScheme = (type: 'layout' | 'view') => {
        const auto_layout = type === 'layout' ? true : false;
        const eventParam = {
            ...houseDataById,
            auto_layout: auto_layout,
        }
        LayoutAI_App.DispatchEvent(LayoutAI_Events.OpenMyLayoutSchemeData, eventParam);
        LayoutAI_App.emit(EventName.OpenHouseSearching, false);
        store.homeStore.setIsShowHouseDetail({show: false, source: ''});
        store.homeStore.setShowEnterPage({show: false, source: ''});
    }

    const clickBtn = (type: 'layout' | 'view') => {
        if(isMyCase){
            // 我的方案
            openLayoutScheme(type);
            toLayout(type);
        } else {
            // 非我的方案
            toLayout(type);
            toSetLayout(type);
            closeHouseDetail();
        }
    }

    const toSetLayout = (type: 'layout' | 'view') => {
        if(!store.trialStore.houseData.id && !store.homeStore.img_base64){
            message.error(t('请先选择户型'));
            return;
        }
        if(type === 'layout')
        {
            if(store.homeStore.img_base64)
            {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, store.homeStore.img_base64);
            } else if(store.trialStore.houseData.id)
            {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: store.trialStore.houseData.id, name:''});
            }
        }
        else if(type === 'view') {
            if(store.homeStore.img_base64)
            {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.LoadImitateImageFile, store.homeStore.img_base64, );
            } else if(store.trialStore.houseData.id)
            {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.PostBuildingId, {id: store.trialStore.houseData.id, name:'', auto_layout: false});
            }
        }
        
        store.homeStore.setShowEnterPage({show: false, source: ''});
    }

    useEffect(() => {
        if(store.homeStore.isShowHouseDetail.source === 'myCase') {
            setIsMyCase(true);
        } else {
            setIsMyCase(false);
        }
    }, []);

    useEffect(() => {
        if(hxId) {
            getHouseDataById(hxId);
        }
    }, [hxId]);

    return (
        <div className={styles.container} onClick={(e) => e.stopPropagation()}>
            <div className={styles.header}>
                <div className='title'>
                    <span className='title_text'>户型详情</span>
                </div>
                <div className='close' onClick={(e) => {
                    e.stopPropagation();
                    closeHouseDetail();
                }}>
                    <Icon iconClass="icon-close1" style={{fontSize: '20px', color: '#282828'}}/>
                </div>
            </div>
            <div className={styles.content}>
                <div className='content_left'>
                    {isMyCase ? <img src={houseDataById.coverImage} alt="户型图" /> 
                        : <img src={houseData.imagePath} alt="户型图" />
                    }
                </div>
                <div className='content_right'>
                    <div className='title'>{houseData.buildingName}</div>
                    {!isMyCase &&<div className='info'>
                        <div className='info_item'>
                            <span className='info_item_label'>建筑面积</span>
                            <span className='info_item_value'>{`${houseData.area}平方米`}</span>
                        </div>
                        <div className='info_item'>
                            <span className='info_item_label'>户型信息</span>
                            <span className='info_item_value'>{houseData.roomTypeName}</span>
                        </div>
                        <div className='info_item'>
                            <span className='info_item_label'>户型朝向</span>
                            <span className='info_item_value'>{houseData.area}</span>
                        </div>
                        <div className='info_item'>
                            <span className='info_item_label'>所在地区</span>
                            <span className='info_item_value'>{`${houseData.provinceName}${houseData.cityName}`}</span>
                        </div>
                        <div className='info_item'>
                            <span className='info_item_label'>户型ID</span>
                            <span className='info_item_value'>{houseData.id}</span>
                        </div>
                    </div>}
                    {isMyCase && <div className='info'>
                        <div className='info_item'>
                            <span className='info_item_label'>建筑面积</span>
                            <span className='info_item_value'>{`${houseDataById.area}平方米`}</span>
                        </div>
                        <div className='info_item'>
                            <span className='info_item_label'>户型ID</span>
                            <span className='info_item_value'>{houseDataById.id}</span>
                        </div>
                    </div>}
                    <div className='btnBox'>
                        <Button className='btn' onClick={() => {
                            clickBtn('layout');
                        }}>去布局</Button>
                        <Button className='btn' onClick={() => {
                            clickBtn('view');
                        }}>看效果</Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HouseDetail;
