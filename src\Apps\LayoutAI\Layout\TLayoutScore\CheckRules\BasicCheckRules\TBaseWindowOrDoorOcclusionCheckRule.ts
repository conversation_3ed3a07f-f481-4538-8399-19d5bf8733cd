import { I_Window } from "../../../IRoomInterface";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { TrimType } from "../../TLayoutJudge";
import { I_CheckRuleOptions, TCheckRule } from "../TCheckRule";
import { TBaseRoomToolUtil } from "./TBaseRoomToolUtil";


export class TBaseWindowOrDoorOcclusionCheckRule extends TCheckRule
{
    private isWindow: boolean;
    private isGetScore: boolean;
    private minWindowLength : number;
    constructor(figure_categories: string[], options: I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
        this.isWindow = options?.otherExtendInfo?.isWindow || false;
        this.isGetScore = options?.otherExtendInfo?.isGetScore || false;
        this.minWindowLength = options?.otherExtendInfo?.minWindowLength || 0;

    }

    checkFigureScore(room: TRoom, figure_element: TFigureElement, main_figures?: TFigureElement[]): any {
        let windows: I_Window[] = room.windows.filter(window => this.isWindow ? 
            window.type == "Window" : 
            window.type == "Door");
        windows = windows.filter((win)=>(win.length||0) > this.minWindowLength);
        if(!windows.length)
        {
            return {score: 0};
        }
        let figureLlen: number = 0;
        // let occlusionFigureArea: number = 0;
        let fineTuningFigures: TFigureElement[] = [];
        let extendRatio: number = 0;
        // TODO 还需要继续进行扩展
        let recordDoorInfo: Map<I_Window, number[]> = new Map<I_Window, number[]>();
        for(let figure of main_figures)
        {
            for(let window of windows)
            {
                let occlusionWindowInfo: any = TBaseRoomToolUtil.instance.calOcclusionWindonLen(window, figure);
                if(!occlusionWindowInfo.occlusionLen)
                {
                    continue;
                }

                if(this.isWindow)
                {
                    let windowFloorHeight: number = 900;
                    let windowHeight: number = 1300;
                    if((figure.height + figure.rect.zval) < windowFloorHeight || figure.rect.zval > (windowFloorHeight + windowHeight))
                    {
                        continue;
                    }
                }
                if(occlusionWindowInfo.isLayonBack)
                {
                    if(occlusionWindowInfo.occlusionLen / Math.min(figure.rect.length, window.rect.length) > 1 - 1e-2)
                    {
                        let windowLeft: number = Math.min(TBaseRoomToolUtil.instance.calDistance(figure.rect.leftEdge, window.rect.leftEdge),
                            TBaseRoomToolUtil.instance.calDistance(figure.rect.rightEdge, window.rect.leftEdge));
                        let windowRight: number = Math.min(TBaseRoomToolUtil.instance.calDistance(figure.rect.leftEdge, window.rect.rightEdge),
                            TBaseRoomToolUtil.instance.calDistance(figure.rect.rightEdge, window.rect.rightEdge));
                        extendRatio += (1 + Math.min(windowLeft, windowRight) / figure.rect.backEdge.length);
                    }
                }
                else
                {
                    let norDis: number = Number.POSITIVE_INFINITY;
                    for(let vertex of figure.rect.vertices)
                    {
                        let dis: number = Math.abs(vertex.pos.clone().sub(window.rect.backEdge.v0.pos).dot(window.rect.backEdge.nor));
                        if(dis < norDis)
                        {
                            norDis = dis;
                        }
                    }
                    extendRatio += (1 + (window.length - norDis)/window.length);
                }
                figureLlen += occlusionWindowInfo.occlusionLen;
                if(!this.isWindow && this.isGetScore)
                {
                    if(!recordDoorInfo.has(window))
                    {
                        recordDoorInfo.set(window, [occlusionWindowInfo.occlusionLen]);
                    }
                    else
                    {
                        recordDoorInfo.get(window).push(occlusionWindowInfo.occlusionLen);
                    }
                }
                
                fineTuningFigures.push(figure);
            }
        }
        let windowLength: number = getAllWindowLength(windows);
        fineTuningFigures = Array.from(new Set(fineTuningFigures));
        let indexValue: number = figureLlen / windowLength + extendRatio;
        let score: number = figureLlen / windowLength;
        if(this.isGetScore)
        {
            score = 0;
            // 这个还需要判断与那个门有遮挡
            for(let entry of recordDoorInfo.entries())
            {
                let doorOcclusionLens: number[] = recordDoorInfo.get(entry[0]);
                let doorLen: number = entry[0].length;
                let occlusionSumLen: number = 0;
                for(let occlusionLen of doorOcclusionLens)
                {
                    occlusionSumLen += occlusionLen;
                }
                let ratio: number = occlusionSumLen / doorLen;
                if(ratio < 0.01)
                {
                    ratio = 0;
                    fineTuningFigures = [];
                }
                if(entry[0].realType != "SlidingDoor")
                {
                    if(ratio > 0)
                    {
                        score = linearScore(ratio, 0, -100);
                        break;
                    }
                }
                else
                {
                    if (ratio > 0.7) {
                        score = -100;
                        break;
                    }
                    else if (ratio > 0.2) {
                        score = -30;
                    }
                    else if (ratio > 0.01) {
                        score = -10;
                    }
                    else
                    {
                        score = 0;
                    }
                }
            }
        }
        return {score: score, fineTuningFigures: fineTuningFigures, fineTuningTypes: [TrimType.k_move],indexValue: indexValue};
    }
}
function getAllWindowLength(windows: I_Window[])
{
    let allLength: number = 0;
    for(let window of windows)
    {
        allLength += window.length;
    }
    return allLength;
}

function linearScore(value: number, minScore: number, maxScore: number): number
{
    let targetScore: number = Math.ceil((maxScore - minScore) * value + minScore);
    return targetScore;
}