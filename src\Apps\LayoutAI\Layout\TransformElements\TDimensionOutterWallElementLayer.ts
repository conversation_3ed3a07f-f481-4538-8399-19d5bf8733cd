import { checkIsMobile } from '@/config';
import { TAppManagerBase } from '../../../AppManagerBase';
import { CadDrawingLayerType, TDrawingLayer } from '../../../LayoutAI/Drawing/TDrawingLayer';
import { T_DimensionOutterWallElement } from './T_DimensionOutterWallElement';

export class TDimensionOutterWallElementLayer extends TDrawingLayer {
  _dimension_outter_wall_element: T_DimensionOutterWallElement;

  constructor(manager: TAppManagerBase) {
    super(CadDrawingLayerType.CadRoomStrucure, manager);
    this._dirty = true;
    this._dimension_outter_wall_element = new T_DimensionOutterWallElement(
      manager.layout_container
    );
  }

  get room_list() {
    return this.layout_container._rooms;
  }
  _updateLayerContent(): void {}

  onDraw(): void {
    if (this._dimension_outter_wall_element) {
      this._dimension_outter_wall_element.updateElement();
    }
    if (this._dimension_outter_wall_element.draw_dimension) {
      this.painter.strokeStyle = '#282828'; // 标注线的颜色
      this.painter.fillStyle = '#282828'; // 标注字体的颜色
      this.painter._context.lineWidth = 1; // 标注线的线宽
      this.painter._context.beginPath();
      let font_size = checkIsMobile() ? 1.8 / this.painter._p_sc : 3 / this.painter._p_sc; // 标注字体大小
      for (let dim of this._dimension_outter_wall_element.draw_dimension) {
        dim._font_size = font_size; // 标注字体大小
        this.painter.drawDimension(dim);
      }
      this.painter._context.closePath();
    }
    this._dimension_outter_wall_element.drawCanvas(this.painter);
  }
}
