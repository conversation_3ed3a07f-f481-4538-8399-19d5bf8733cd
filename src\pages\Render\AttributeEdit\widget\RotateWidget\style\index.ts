import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        root: css`
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 12px;

            label {
                display: flex;
                align-items: center;
            }

            div {
                display: flex;
                align-items: center;
            }

            .svg-input-number-suffix {
                position: relative;
                top: 0px;
                right: 3px;
                height: 2px;
            }

            .svg-input-number-small {
                width: 73px;
                height: 24px;
                line-height: 24px;
                font-size: 18px;
                transform: translateX(-8px);
            }
        `,
        rotate: css`
            margin-right: 15px;
        `
    }
});