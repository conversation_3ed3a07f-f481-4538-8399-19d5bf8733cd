"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[5670],{75670:function(n,e,t){t.d(e,{A:function(){return I}});var r=t(13274),a=t(41594),i=t(15696),o=t(69802),l=t(9003),s=t(27347),c=t(98612);function u(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function d(){var n=u(["\n      display: flex;\n      flex-direction: row-reverse;\n      background-color: #fff;\n      position: sticky;\n      bottom: 0;\n      align-items: center;\n      background-color: #f6f7f9;\n      padding-top: 7px;\n    "]);return d=function(){return n},n}function p(){var n=u(["\n    width: 100%;\n    max-height: 100%;\n    border-radius: 12px;\n    padding: 0 0 0 20px;\n    display: flex;\n    flex-direction: column;\n    .atlas_header{\n      display:flex;\n      justify-content: space-between;\n\n      .segmented{\n\n      }\n\n      .back_button{\n        display:flex;\n        align-items: center;\n        margin-right: 20px;\n        height: 30px;\n        width: 74px;\n        border-radius: 8px;\n        background: #FFFFFF;\n        border: 1px solid #00000026;\n        margin-bottom: 10px;\n        cursor: pointer;\n        span{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: left;\n        }\n      }\n    }\n    "]);return p=function(){return n},n}function f(){var n=u(["\n      width:100%;\n      height: 100%;\n      padding-right: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #959598;\n    "]);return f=function(){return n},n}function m(){var n=u(["\n      display: grid;\n      gap: 20px;\n      flex: 1;\n      overflow-y: auto;\n\n      /* 隐藏滚动条 - Webkit浏览器 */\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      \n      /* 隐藏滚动条 - Firefox */\n      scrollbar-width: none;\n      \n      /* 隐藏滚动条 - IE */\n      -ms-overflow-style: none;\n\n      // grid-template-rows: repeat(auto-fill, 200px);\n\n      @media screen and (min-width: 1400px) {\n          grid-template-columns: repeat(5, calc(20% - 20px));\n      }\n      \n      @media screen and (max-width: 1400px) and (min-width: 960px) {\n          grid-template-columns: repeat(4, calc(25% - 20px));\n      }\n      \n      @media screen and (max-width: 960px) and (min-width: 560px) {\n          grid-template-columns: repeat(3, calc(33.33% - 20px));\n      }\n      @media screen and (max-width: 560px) and (min-width: 320px) {\n          grid-template-columns: repeat(2, calc(50% - 20px));\n      }\n      @media screen and (max-width: 320px) {\n          grid-template-columns: repeat(1, 100%);\n      }\n\n      &::-webkit-scrollbar {\n          width: 6px;\n      }\n      \n      &::-webkit-scrollbar-thumb {\n          background: #00000026;\n          border-radius: 3px;\n      }\n      \n      &::-webkit-scrollbar-track {\n          background: transparent;\n      }\n    "]);return m=function(){return n},n}function h(){var n=u(["\n      border: 1px solid transparent;\n      // margin-bottom: 20px;\n      .main_img_container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 4px;\n        border: none;\n        background: #F5F5F5;\n        position: relative;\n        overflow: hidden;\n        .ant-image {\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        .ant-image-mask{\n          opacity: 0;\n        }\n        img {\n          max-width: 100%;\n          max-height: 100%;\n          border-radius: 4px;\n          object-fit: contain;\n          position: absolute;\n          left: 50%;\n          transform: translateX(-50%);\n        }\n\n        .number_tag {\n            position: absolute;\n            top: 8px;\n            left: 8px;\n            width: 30px;\n            height: 30px;\n            border-radius: 4px;\n            background: #0000007F;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #FFFFFF;\n            font-size: 14px;\n            z-index: 1;\n        }\n      }\n\n      .main_loading_container{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-direction: column;\n        width: 100%;\n        // height: 181px;\n        aspect-ratio: 4 / 3;\n        border-radius: 8px;\n        background: linear-gradient(97.08deg, rgba(173, 144, 255, 0.1) 0%, rgba(142, 174, 255, 0.1) 100%);\n        position: relative;\n\n        &::before {\n          content: '';\n          position: absolute;\n          inset: 0;\n          border-radius: 8px;\n          padding: 1px;\n          background: linear-gradient(119.36deg, #E9E0FF 0%, #BCC8FF 100%);\n          mask: linear-gradient(#fff 0 0) content-box, \n                linear-gradient(#fff 0 0);\n          mask-composite: exclude;\n          -webkit-mask: linear-gradient(#fff 0 0) content-box, \n                       linear-gradient(#fff 0 0);\n          -webkit-mask-composite: xor;\n          pointer-events: none;\n        }\n        \n        span{\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-top: 4px;\n        }\n      }\n\n      .info_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        height: 22px;\n        margin: 8px 0;\n        padding: 0 8px;\n        \n        .name{\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 14px;\n          line-height: 1.57;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          max-width: 180px;\n          \n          @media screen and (max-width: 1400px) and (min-width: 960px) {\n            max-width: 150px;\n          }\n          \n          @media screen and (max-width: 960px) and (min-width: 560px) {\n            max-width: 120px;\n          }\n          @media screen and (max-width: 560px) {\n            max-width: 80px;\n          }\n        }\n        \n        .time {\n          color: #959598;\n          font-family: PingFang SC;\n          font-weight: normal;\n          font-size: 12px;\n          line-height: 1.67;\n          letter-spacing: 0px;\n          text-align: center;\n          white-space: nowrap;\n          overflow: hidden;\n          // text-overflow: ellipsis;\n          max-width: 80px;\n        }\n      }\n    "]);return h=function(){return n},n}var x=(0,t(8268).rU)(function(n){var e=n.css;return{PageContainer:e(d()),root:e(p()),noData:e(f()),content:e(m()),item:e(h())}}),g=t(30268),b=t(87927),w=t(14181),y=t(76330),v=t(32085),j=t(56070),S=t(95301),k=t(60314);function A(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function F(n,e,t,r,a,i,o){try{var l=n[i](o),s=l.value}catch(n){return void t(n)}l.done?e(s):Promise.resolve(s).then(r,a)}function _(n){return function(){var e=this,t=arguments;return new Promise(function(r,a){var i=n.apply(e,t);function o(n){F(i,r,a,o,l,"next",n)}function l(n){F(i,r,a,o,l,"throw",n)}o(void 0)})}}function C(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,a,i=[],o=!0,l=!1;try{for(t=t.call(n);!(o=(r=t.next()).done)&&(i.push(r.value),!e||i.length!==e);o=!0);}catch(n){l=!0,a=n}finally{try{o||null==t.return||t.return()}finally{if(l)throw a}}return i}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return A(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return A(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(n,e){var t,r,a,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(l){return function(s){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(i=0)),i;)try{if(t=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==l[0]&&2!==l[0])){i=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){i.label=l[1];break}if(6===l[0]&&i.label<a[1]){i.label=a[1],a=l;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(l);break}a[2]&&i.ops.pop(),i.trys.pop();continue}l=e.call(n,i)}catch(n){l=[6,n],r=0}finally{t=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,s])}}}var I=(0,i.observer)(function(n){n.setZIndexOfMobileAtlas;var e=(0,o.B)().t,t=(0,l.P)(),i=x().styles,u=C((0,a.useState)([]),2),d=u[0],p=u[1],f=C((0,a.useState)(1),2),m=f[0],h=f[1],A=C((0,a.useState)(1),2),F=A[0],I=A[1],R=C((0,a.useState)(15),2),P=R[0],z=(R[1],C((0,a.useState)(0),2)),M=z[0],E=z[1],O=C((0,a.useState)(new Set),2),D=O[0],L=O[1];s.nb.UseApp(c.e.AppName),s.nb.instance&&(s.nb.t=e);var T=function(){return _(function(){var n;return N(this,function(e){switch(e.label){case 0:return[4,w.w.instance.queryImageList("",m,P)];case 1:return n=e.sent(),p(n.result),E(n.recordCount),[2]}})})()};(0,a.useEffect)(function(){"aidraw"===t.homeStore.atlasMode?T():"render"===t.homeStore.atlasMode&&_(function(){var n,e,t,r,a,i;return N(this,function(o){switch(o.label){case 0:return[4,v.s.instance.requestAtlas({userId:"",flag:"",schemeId:"",pageIndex:F,pageSize:P,all:"",isRtx:0,resolutionTag:"",type:0,exclusiveRealAdjustLight:!1,authCode:""})];case 1:return(n=o.sent()).success&&(a=null==n||null===(e=n.res)||void 0===e?void 0:e.data,(i=null==n||null===(r=n.res)||void 0===r||null===(t=r.data)||void 0===t?void 0:t.ReturnList)&&(null==i?void 0:i.length)>0&&(i.forEach(function(n){n.imageResult=3===n.Status?"".concat((0,j.mB)(),"/").concat(n.FileIdOutPut2):null,n.layoutName=n.Remark,n.createDate=n.CreateDate,n.imageResultList=[""]}),p(i),E(a.TotalResults))),[2]}})})(),L(new Set)},[m,F,t.homeStore.atlasMode]),(0,a.useEffect)(function(){t.homeStore.refreshAtlas&&(h(1),T(),t.homeStore.setRefreshAtlas(!1))},[t.homeStore.refreshAtlas]);var B=function(){return(0,r.jsx)(k.A,{simple:!0,defaultCurrent:1,current:"aidraw"===t.homeStore.atlasMode?m:F,onChange:function(n){"aidraw"===t.homeStore.atlasMode?h(n):I(n)},total:M,pageSize:15,showSizeChanger:!1})};return(0,r.jsxs)("div",{className:i.root,children:[(0,r.jsxs)("div",{className:"atlas_header",children:[(0,r.jsx)(S.A,{options:["标准渲染","AI绘图"],value:"aidraw"===t.homeStore.atlasMode?"AI绘图":"标准渲染",onChange:function(n){"AI绘图"===n?t.homeStore.setAtlasMode("aidraw"):t.homeStore.setAtlasMode("render")},className:"segmented"}),!0===t.homeStore.showAtlas?(0,r.jsxs)("div",{className:"back_button",onClick:function(){return t.homeStore.setShowAtlas(!1)},children:[(0,r.jsx)(y.A,{style:{margin:"7px 2px 7px 12px"},type:"icon-line_left"}),(0,r.jsx)("span",{style:{height:22,width:28},children:e("返回")})]}):null]}),d.length<=0&&(0,r.jsx)("div",{className:i.noData,children:e("-暂无数据-")}),(0,r.jsx)("div",{className:i.content,onScroll:function(n){},children:d.map(function(n,t){return(0,r.jsxs)("div",{className:i.item,onClick:function(){return null},children:[n.imageResult&&!D.has(n.imageResult)?(0,r.jsxs)("div",{className:"main_img_container",children:[(0,r.jsx)(g.A,{onError:function(){return e=n.imageResult,void L(function(n){return new Set(n).add(e)});var e},src:n.imageResult}),(0,r.jsx)("div",{className:"number_tag",children:n.imageResultList.length})]}):(0,r.jsxs)("div",{className:"main_loading_container",children:[(0,r.jsx)("img",{src:"https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png",alt:"",style:{height:"40px"}}),(0,r.jsx)("span",{children:e("生成中...")})]}),(0,r.jsxs)("div",{className:"info_content",children:[(0,r.jsx)(b.A,{title:e(n.layoutName),children:(0,r.jsx)("span",{className:"name",children:e(n.layoutName)})}),(0,r.jsx)(b.A,{title:e(n.createDate),children:(0,r.jsx)("span",{className:"time",children:e(n.createDate.split(" ")[0])})})]})]},"history_"+t)})}),(0,r.jsx)("div",{className:i.PageContainer,children:(0,r.jsx)(B,{})},"tablePagination")]})})}}]);