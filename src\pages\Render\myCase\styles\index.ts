import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
    return {
        myCaseRoot: css`
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 999;
            background: #fff;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-shrink: 0;
            background: #fff;
            width: 85vw;
            height: 90vh;
        `,
        header: css`
            display: flex;
            height: 52px;
            padding: 6px 20px;
            align-items: center;
            gap: 10px;
            flex-shrink: 0;
            align-self: stretch;
            width: 100%;
            box-sizing: border-box;
            .title {
                display: flex;
                align-items: center;
                gap: 16px;
                flex: 1 0 0;
                .title_text {
                    color: #282828;
                    font-family: "PingFang SC";
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }
            }
            .close {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
            }
        `,
        content: css`
            display: flex;
            padding: 0 20px 20px 20px;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            flex: 1 0 0;
            height: calc(100% - 52px);
            align-self: stretch;
        `,
        search: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            align-self: stretch;
            height: 30px;
            .search_input {
                width: 340px;
                padding-left: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50px;
                border: 1px solid rgba(0, 0, 0, 0.15);
                background: #FFF;
                .ant-input-outlined {
                    border: none;
                }
                .ant-input-group-addon {
                    z-index: 1;
                    border-radius: 50px !important;
                    .ant-btn {
                        border: none !important;
                        padding: 0 16px;
                        gap: 8px;
                        align-self: stretch;
                        border-radius: 50px !important;
                        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
                        border-top-left-radius: 50px !important;
                        border-bottom-left-radius: 50px !important;
                        border-top-right-radius: 50px !important;
                        border-bottom-right-radius: 50px !important;
                    }
                }
                
                /* 更具体的选择器 */
                .ant-input-group-wrapper .ant-input-group-addon .ant-btn {
                    border-radius: 50px !important;
                    border-top-left-radius: 50px !important;
                    border-bottom-left-radius: 50px !important;
                    border-top-right-radius: 50px !important;
                    border-bottom-right-radius: 50px !important;
                }
                
                /* 针对搜索按钮的特殊样式 */
                .ant-input-search-button {
                    border-radius: 50px !important;
                    border-top-left-radius: 50px !important;
                    border-bottom-left-radius: 50px !important;
                    border-top-right-radius: 50px !important;
                    border-bottom-right-radius: 50px !important;
                }
            }
            .searchChoice {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                .searchChoiceItem {
                    display: flex;
                    align-items: center;
                    .ant-space {
                        display: flex;
                        align-items: center;
                        gap: 2px;
                        .ant-space-item {
                            color: var(--map-colorText, #282828);
                            font-family: "PingFang SC";
                            font-size: 13px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 22px;
                        }
                    }
                }
            }
        `,
        list: css`
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px 20px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
            width: 100%;
            height: calc(100% - 30px);
            
            /* 隐藏滚动条 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            
            &::-webkit-scrollbar {
                display: none; /* Chrome, Safari and Opera */
            }
            
            .listItem {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                flex: 1 0 0;
                border-radius: 8px;
                border: 1px solid rgba(0, 0, 0, 0.06);
                background: #FFF;
                .listItemImg {
                    display: flex;
                    padding: 8px;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    align-self: stretch;
                    background: #F4F5F5;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
                .itemInfo {
                    display: flex;
                    padding: 8px 16px 16px 16px;
                    flex-direction: column;
                    align-items: flex-start;
                    align-self: stretch;
                }
            }
            
            .no-more {
                grid-column: 1 / -1;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
                .no-more-text {
                    color: #999;
                    font-size: 13px;
                }
            }
        `,
    };
});