import { AI2BaseModeHandler } from "@/Apps/AI2Design/AI2BaseModeHandler";
import { CadBaseSubHandler } from "../CadEditSubHandlers/CadBaseSubHandler";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { I_MouseEvent } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { T_WallsChangedOpertaionInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_WallsChangedOpertaionInfo";
import { TWall } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TWall";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { T_DeleteWindowOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_DeleteWindowOperationInfo";
import { T_DeleteStructureOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_DeleteStructureOperationInfo";
import { IPropertyUI } from "@/Apps/LayoutAI/Layout/TLayoutEntities/IPropertyUI";
import { TBaseEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity";
import { Vector3Like } from "three";
import { AI_PolyTargetType } from "@/Apps/LayoutAI/Layout/IRoomInterface";


export class HouseDesignSubHandler extends CadBaseSubHandler {
    _thickness: number = 120;
    _candidate_point_type: string = "";
    lockCopyImage: boolean;
    constructor(cad_mode_handler: AI2BaseModeHandler) {
        super(cad_mode_handler);
        this.lockCopyImage = true;
    }

    get furniture_entities() {
        return this._cad_mode_handler.furniture_entities;
    }

    set furniture_entities(entities: TFurnitureEntity[]) {
        this._cad_mode_handler.furniture_entities = entities;
    }
    onmousedown(ev: I_MouseEvent): void {
        super.onmousedown(ev);
    }
    onmousemove(ev: I_MouseEvent): void {
        let pos = { x: ev.posX, y: ev.posY, z: 0 };
        if (ev.buttons == 0) {
            this.updateHoverRect(pos);
        }
        else if (ev.buttons == 1) {

        }
        this._cad_mode_handler._is_moving_element = false;

    }
    onmouseup(ev: I_MouseEvent): void {
        this._cad_mode_handler._is_moving_element = false;

        this.updateAttributes("init");

        this.updateCandidateRects();
        this.updateWholeBox();

        this.updateSelectionState();


        this.updateAttributes("force");

        this.update();



    }
    // 空间被选中
    onRoomEntitySelected(entity: TRoomEntity): Promise<void> {


        return;
    }


    bindTargetPropertiesOfEntity(entity: TBaseEntity): { mode: string; title: string; properties: { [key: string]: IPropertyUI; }; } {
        let params = super.bindTargetPropertiesOfEntity(entity);
        if (entity.type === "RoomArea") {
            if (params.properties['sub_area_widget']) {
                delete params.properties['sub_area_widget'];
            }
        }
        return params;
    }
    updateCandidateRects() {
        this.candidate_rects.length = 0;
        this.candidate_rects.push(...this.container.getCandidateRects(["RoomArea", "Wall", "Door", "Window",
            "StructureEntity"]
            , { ignore_realtypes: ["Decoration", "Electricity"] }, { "Door": 2, "Window": 2 }));
        this.updateExsorbRects();
    }
    updateExsorbRects(): void {
        this.exsorb_rects.length = 0;
        this.exsorb_rects.push(...this.container.getCandidateRects(["Wall", "StructureEntity"], { ignore_realtypes: ["Beam"] }));

    }
    updateSelectedRect(pos: Vector3Like): void {
        let target_ele = this.getTransformElementContainPos(pos);

        this.selected_target.selected_transform_element = target_ele;
        let transform_rect = target_ele?._target_rect;
        let rect_on_pos = this.getTargetRectContainsPos(pos);
        if (transform_rect) {
            // 如果选中的移动元素 和 点击位置 都有实体就优先选中高优先级的实体
            let transform_entity = TBaseEntity.getEntityOfRect(transform_rect);
            let entity_on_pos = TBaseEntity.getEntityOfRect(rect_on_pos);
            rect_on_pos = transform_entity?._priority_for_selection > entity_on_pos?._priority_for_selection ? transform_rect : rect_on_pos;
        } else {
            rect_on_pos = this.getTargetRectContainsPos(pos);
        }
        if (rect_on_pos !== this.selected_target.selected_rect) {
            this.selected_target.selected_rect = rect_on_pos;

        }
        this.updateTransformElements();

        if (this.selected_target.selected_rect) {
            const type = TBaseEntity.get_polygon_type(this.selected_target.selected_rect);
            if (!this.selected_target.selected_transform_element) {
                let moving_wall_element = this._cad_mode_handler.transform_moving_wall_element;
                let moving_element = this._cad_mode_handler.transform_moving_element;
                let moving_struture_element = this._cad_mode_handler.transform_moving_struture_element;
                
                if (['StructureEntity'].includes(type)) {
                    this.selected_target.selected_transform_element = moving_element;
                } else if (['Door', 'Window'].includes(type)) {
                    this.selected_target.selected_transform_element = moving_struture_element;
                } else if (['Wall'].includes(type)) { 
                    this.selected_target.selected_transform_element = moving_wall_element;
                }else{
                    this.selected_target.selected_transform_element = null;
                }
            }
            let selected_entity = TBaseEntity.getEntityOfRect(this.selected_target.selected_rect);
            // 选中实体
            this.selected_target.selected_entity = selected_entity;
        }
        else {
            // 判断是否点击到临摹底图
            this.selectedCopyImageRect(pos);

            this.selected_target.selected_combination_entitys = [];
            this.selected_target.selected_entity = null;
        }

        this.updateSelectionState();
        this.update();
    }
    // 户型i编辑中暂时用这种方式寻找实体  后续将这里的逻辑抽取到选择器中
    updateHoverRect(pos: Vector3Like): void {
        let target_ele = this.getTransformElementContainPos(pos);

        this.selected_target.hover_transform_element = target_ele;

        if (target_ele) {
            target_ele.onhover();
            this.manager.setCanvasCursorState(target_ele._cursor_state);
        }
        else {
            this.manager.setCanvasCursorState(LayoutAI_CursorState.Default);
        }
        let candidateRect = null;
        let max_priority = -999;
        for(let rect of this.candidate_rects)
        {
            let type = TBaseEntity.get_polygon_type(rect);
            // 只关注户型相关的实体
            if(!["RoomArea","Wall","Door","Window","StructureEntity"].includes(type))
            {
                continue;
            }
            let entity = TBaseEntity.getEntityOfRect(rect);
            if(!entity) continue;
            let dist = (entity.getDistanceForMousePosSelection(pos));
            if (dist < 0) {
                // 点击位置在实体内，选择优先级高的实体
                if (entity._priority_for_selection > max_priority) {
                    max_priority = entity._priority_for_selection;
                    candidateRect = rect;
                }
            }
        }

        let candidateEntity = TBaseEntity.getEntityOfRect(candidateRect);
        if(candidateEntity)
        {

            if(candidateEntity !== this.selected_target.hovered_entity)
            {
                candidateEntity.is_hovered = true;
                if(this.selected_target.hovered_entity)
                {
                    this.selected_target.hovered_entity.is_hovered = false;
                }
            }
            this.selected_target.hover_rect = candidateRect;
            this.selected_target.hovered_entity = candidateEntity;
        }
        else{
            if(this.selected_target.hovered_entity)
            {
                this.selected_target.hovered_entity.is_hovered = false;
            }
            this.selected_target.hover_rect = null;
            this.selected_target.hovered_entity = null;
        }
        
        this.update();
    }

    selectedCopyImageRect(pos: Vector3Like): void {
        if (this.lockCopyImage) return;
        let copy_image_rect = this.container.copyImageRect;
        if (copy_image_rect && copy_image_rect.containsPoint(pos)) {
            LayoutAI_App.RunCommand(LayoutAI_Commands.MoveCopyImageHandler);
        }
    }

    updateTransformElements() {
        let target_rect = this.selected_target.selected_rect;


        if (target_rect &&
            (TBaseEntity.get_polygon_type(target_rect) == AI_PolyTargetType.RoomArea)) {
            target_rect = null;
        }



        for (let ele of this.transform_elements) {
            ele.bindTargetRect(target_rect, this.selected_target.selected_combination_entitys);
        }
    }

    deleteElement(): void {
        if (!this.selected_target.selected_rect) return;

        let selected_rect = this.selected_target.selected_rect;
        if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Wall) {

            let operation_info = new T_WallsChangedOpertaionInfo(this.manager);
            operation_info.recordSrcWalls([TBaseEntity.getEntityOfRect(selected_rect) as TWall]);
            operation_info.recordTargetWalls([]);
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.cleanSelection();
            this.update();
        } else if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Door ||
            TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.Window) {
            let operation_info = new T_DeleteWindowOperationInfo(this.manager);
            operation_info.target_rect = this.selected_target.selected_rect;
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.selected_target.selected_combination_entitys = [];
            this.cleanSelection();
            this.update();
        }
        else if (TBaseEntity.get_polygon_type(selected_rect) === AI_PolyTargetType.StructureEntity) {
            let operation_info = new T_DeleteStructureOperationInfo(this.manager);
            operation_info.target_rect = this.selected_target.selected_rect;
            operation_info.redo(this.manager);
            this.manager.appendOperationInfo(operation_info);
            this.cleanSelection();
            this.update();
        }
        this.updateCandidateRects();

    }
    handleEvent(evt_name: string, evt_param: any): void {
        if (evt_name === LayoutAI_Events.HandleSwitchDrawingLayer) {
            let state: { [key: string]: boolean } = evt_param;
            for (let layer_name in state) {
                if (this.manager.drawing_layers[layer_name] !== undefined) {
                    this.manager.drawing_layers[layer_name].visible = state[layer_name];
                }

                if (this.manager.layer_CadFloorLayer) {
                    this.manager.layer_CadFloorLayer.visible = this.manager.layer_CadRoomFrameLayer.visible;

                    // if(!this.manager.layer_CadRoomFrameLayer.visible)
                    // {
                    //     this.manager.layer_OutLineLayer.visible = false;
                    // }
                }
            }

            this.manager.onLayerVisibilityChanged();
            this.update();
        }
        if (evt_name === LayoutAI_Events.setTopWallMenuProps) {
            this._thickness = parseFloat(evt_param.width);
            this._candidate_point_type = evt_param.lineValue;
        }
        else if (evt_name === LayoutAI_Events.DimensionInput) {
            let target_rect = this.selected_target.selected_rect;
            if (this.selected_target.selected_transform_element) {
                this.selected_target.selected_transform_element.recordOriginRect(this.selected_target.selected_transform_element._target_rect);
                this.selected_target.selected_transform_element.recordOriginCombinationRect(this.selected_target.selected_combination_entitys);
            }
            let input = evt_param;
            if (!input || !input.value) return;
            let num = parseFloat(input.value);
            if (typeof num === 'number') {
                let _nor_val = JSON.parse(input.getAttribute('_nor_v3'));
                // 编辑前的值
                let origin_num = Number(input.getAttribute('origin_num'));

                // num是编辑的后的值
                // edit_num是差值
                let edit_num: any = origin_num - num;
                // if(edit_num < 0) return;
                let t_center = { x: target_rect.rect_center.x + _nor_val.x * edit_num, y: target_rect.rect_center.y + _nor_val.y * edit_num, z: target_rect.rect_center.z };
                target_rect.rect_center = t_center;

                // 组合内图元移动
                if (this.selected_target.selected_combination_entitys.length > 0) {
                    for (let t_entity of this.selected_target.selected_combination_entitys) {
                        t_entity.rect.rect_center = { x: t_entity.rect.rect_center.x + _nor_val.x * edit_num, y: t_entity.rect.rect_center.y + _nor_val.y * edit_num, z: t_entity.rect.rect_center.z };
                    }
                }
                if (this.selected_target.selected_transform_element) {
                    let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
                    if (info) {
                        this.manager.appendOperationInfo(info);
                    }
                }
                target_rect.updateRect();
                this.update();
                return;
            }
        }
        else if (evt_name === LayoutAI_Events.DimensionWall) {
            let target_rect = this.selected_target.selected_rect;
            let input = evt_param;
            if (!input || !input.value) return;
            this.selected_target.selected_transform_element.startTransform();
            let num = parseFloat(input.value);
            if (typeof num === 'number') {
                let _nor_val = JSON.parse(input.getAttribute('_nor_v3'));
                let origin_num = Number(input.getAttribute('origin_num'));
                let edit_num: any = origin_num - num;
                let t_center = { x: target_rect.rect_center.x + _nor_val.x * edit_num, y: target_rect.rect_center.y + _nor_val.y * edit_num, z: target_rect.rect_center.z };
                target_rect.rect_center = t_center;
                target_rect.updateRect();

            }
            let target_wall = TBaseEntity.getEntityOfRect(target_rect) as TWall;
            let entity = target_wall;
            if (entity) {


                entity.update();

                let wall = entity as TWall;


                if (wall.updateInWallWindows) {
                    wall.updateInWallWindows();
                }
                wall.updateNeighborWallsAfterMoving();
                // wall.updateNewWall();
                wall.updateWinDoorLength();
            }
            if (this.selected_target.selected_transform_element) {
                let info = this.selected_target.selected_transform_element.updateOperaionInfo(this.manager);
                if (info) {
                    this.manager.appendOperationInfo(info);
                }
            }
            this.update();
        }
        else if (evt_name === LayoutAI_Events.HandleLockCopyImage) {
            this.lockCopyImage = evt_param;
        }else if (evt_name === LayoutAI_Events.scale) {
            this.painter._p_sc = evt_param;
            this._is_painter_center_wheel = true;
            this.update();
        }
    }
}