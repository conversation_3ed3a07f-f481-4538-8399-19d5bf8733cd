import { Arrangeable } from "@/config/enum";
import { <PERSON><PERSON><PERSON>, Vector3, Vector3Like } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";
import { ZEdge } from "@layoutai/z_polygon";
import { ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { ModelLocPublicCategoryMap } from "../AICadData/ModelLocPubliccategoryMap";
import { I_SwjLineEdge, I_SwjRoom, I_SwjWall } from "../AICadData/SwjLayoutData";
import { g_FigureImagePaths } from "../Drawing/FigureImagePaths";
import { TPainter } from "../Drawing/TPainter";
import { GenDateUUid, Math_Round, Vec3toMetaRounded, compareNames } from "@layoutai/z_polygon";
import { MathUtils } from "../Utils/math_utils";
import { makeDimensionsOfEdge } from "@layoutai/z_polygon";
import { ZPainter } from "@layoutai/z_polygon";
import {
    AlignWallDist, FigureZValRangeType, HallwayWidth,
    IRoomEntityRealType,
    I_Door,
    I_E_Pipe,
    I_Entity3D,
    I_Flue, I_InnerWall, I_Layout, I_Pillar, I_Room, I_Window,
    MaxWindowLayonDepth,
    TFigureVisibleRangeMap, TRoomAreaType, TRoomNameDict, TRoomNameType
} from "./IRoomInterface";
import { TFeatureShape } from "./TFeatureShape/TFeatureShape";
import { T_L_Shape } from "./TFeatureShape/T_L_Shape";
import { T_S_Shape } from "./TFeatureShape/T_S_Shape";
import { T_Simple_Shape } from "./TFeatureShape/T_Simple_Shape";
import { T_T_Shape } from "./TFeatureShape/T_T_Shape";
import { FigureCategoryManager } from "./TFigureElements/FigureCategoryManager";
import { TFigureElement } from "./TFigureElements/TFigureElement";
import { TFigureList } from "./TFigureElements/TFigureList";
import { AreaGridBoard } from "./TLayoutGraph/AreaGrids/AreaGrids";
import { TGroupTemplate } from "./TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TRoomLayoutScheme } from "./TLayoutScheme/TRoomLayoutScheme";
import { TPolyPartition } from "./TPolyPartition/TPolyPartition";
import { TBaseGroupEntity } from "./TLayoutEntities/TBaseGroupEntity";
import { TRoomEntity } from "./TLayoutEntities/TRoomEntity";
import { TWindowDoorEntity } from "./TLayoutEntities/TWinDoorEntity";
import { I_TRoom, TRoomShape } from "./TRoomShape";
import { TSeriesSample } from "./TSeriesSample";
import { ENV } from "@/config";
import { TRoomExporter } from "./TLayoutEntities/loader/TRoomExporter";
import { TBaseEntity } from "./TLayoutEntities/TBaseEntity";
import { TWinDoorDrawingUtils } from "./TLayoutEntities/utils/TWinDoorDrawingUtils";

export interface I_ExtRoom {
    scheme_id?: string;
    room_id?: string;
    scheme_room_id?: string;
    uid?: string;
    room: I_Room;
    swj_room?: I_SwjRoom;
    createDate?: string;

    server_data?: any;
    id?: string;
}

export interface I_SubSpacePoly {
    area_name: string;
    area_points?: Vector3Like[];
    area_poly?: ZPolygon;
    color?: string;
}

export class TRoom implements I_TRoom {
    _t_id: number; // 临时id
    id: number;
    room_id: number; // 区分id, 不一定从0开始
    uid: string; // swj 自定义uid
    uuid: string;
    inner_walls: I_InnerWall[];
    points: number[][];
    boundary: I_SwjLineEdge[];

    name: string;
    aliasName: string;
    roomname: string;
    room_size: string;
    area: number;
    windows: I_Window[];
    /**
     *  进入本空间的门，排除掉从本空间进入其他空间的门
     */
    doors: I_Door[];

    flues: I_Flue[];
    pillars: I_Pillar[];
    pipes: I_E_Pipe[];
    platforms: I_Entity3D[];

    room_shape: TRoomShape;

    shape_list: TRoomShape[];

    /**
     *  最大内置矩形
     */
    max_R_shape: TRoomShape;

    /**
     *  最大内置L形
     */
    max_L_shape: TRoomShape;

    /**
     *  从RoomShape里面取出有效的形状区域
     */
    valid_shape_list: TRoomShape[];

    feature_shapes: TFeatureShape[];

    schemeId: string;
    schemeName: string;
    _painter: ZPainter;
    _layout: I_Layout;
    _room_data: I_Room;

    _furniture_list: TFigureElement[];

    /**
     *  默认undefined
     */
    _layout_scheme_list: TRoomLayoutScheme[];
    /**
     *  是否需要请求计算scheme
     *  _query_scheme_dirty
     */
    _query_scheme_dirty?: boolean;

    /**
     *  门的图元，用于素材匹配
     */
    _door_figure_list: TFigureElement[];

    /**
     *  吊顶、吊顶扣板的图元，用于素材匹配
     *  客餐厅有：客厅吊顶区域、餐厅
     */
    _ceilling_list: TFigureElement[];

    /**
     *  窗帘盒图元
     */
    _curtain_ceiling_list: TFigureElement[];



    /**
     *  台面图元列表
     */
    _tabletop_list: TFigureElement[];

    /**
     *  地板贴图
     */
    public _tile: TFigureElement;

    /**
     *  墙面贴图
     */
    public _wallTexture: TFigureElement;

    private _hadMakeHardElements: boolean;

    /**
     *  组合模板列表
     */
    _group_template_list: TGroupTemplate[];

    /**
     *  子分区列表: 只是临时用于测试, 后续会弃用
     */
    _sub_space_list: I_SubSpacePoly[];


    /**
     *  原始户型数据
     */
    _swj_room_data: I_SwjRoom;

    selectable: boolean;
    selected: boolean;

    _room_entity: TRoomEntity;

    isSelectSeries: boolean;
    /**
     *  套系信息
     */
    _series_sample_info?: TSeriesSample;
    _cabinetStyleId ?: string;
    kgId: number;
    selectIndex: number;
    mode: string;

    /**
     *  区域格子点, 默认是空的
     */
    _area_grids_board: AreaGridBoard;

    _locked: boolean;
    _layout_lock: boolean;

    _scope_series_map: { soft: TSeriesSample, cabinet: TSeriesSample, hard: TSeriesSample, remaining: TSeriesSample } | null;

    private _current_apply_scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean };

    private _furnished_apply_scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean };

    private _remaining_furnished_apply_scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean };

    private _waiting_for_furnish_remaining: boolean;

    //记录上一次常规套系应用流程中未匹配套系素材的图元列表
    //剩余的、未匹配套系素材的图元列表
    public _remaining_figure_list: TFigureElement[];
    //记录上一次补全套系应用流程中未匹配套系素材的图元列表
    public _unmatched_remaining_figure_list: TFigureElement[];

    /**
     *  吊顶下吊高度
     */
    private _ceiling_height: number;

    constructor(data: I_Room = {}) {
        this.importRoomData(data);
    }


    toString(): string {
        return "TRoom  " +
            (this.uid ? "uid=" + this.uid + "," : "") +
            (this.name ? "type=" + this.name : "") +
            (this.roomname && this.roomname != this.name ? ",name=" + this.roomname : "") +
            (this.room_size ? ",area=" + this.room_size : "") +
            (this.schemeId ? ",schemaId=" + this.schemeId : "") +
            (this.schemeName ? ",schemaName=" + this.schemeName : "");
    }

    brief(): string {
        return (this.uid ? (this.uid + " ") : "") +
            (this.room_type ? (this.room_type + " ") : "") +
            (this.roomname && this.roomname != this.room_type ? (this.roomname + " ") : "") +
            (this.area ? (this.area.toFixed(2) + "m²") : "");
    }

    set ceilingHeight(t: number) {
        this._ceiling_height = t;
    }

    public getMaxCabinetHeight(): number {
        if (this._room_entity) {
            return this._room_entity.max_cabinet_height;
        } else {
            return 0;
        }
    }

    public isApplyHardCategory(): boolean {
        return this._scope_series_map == null || this._scope_series_map.hard != null;
    }

    public isApplySoftCategory(): boolean {
        return this._scope_series_map == null || this._scope_series_map.soft != null;
    }

    public isApplyCabinetCategory(): boolean {
        return this._scope_series_map == null || this._scope_series_map.cabinet != null;
    }

    public isCurrentApplyHardCategory(): boolean {
        return this._current_apply_scope == null || this._current_apply_scope.hard;
    }

    public isCurrentApplySoftCategory(): boolean {
        return this._current_apply_scope == null || this._current_apply_scope.soft;
    }

    public isCurrentApplyCabinetCategory(): boolean {
        return this._current_apply_scope == null || this._current_apply_scope.cabinet;
    }
    public isCurrentApplyRemainingCategory(): boolean {
        return this._current_apply_scope != null && this._current_apply_scope.remaining;
    }

    public updateCurrentScopeSeries(series: TSeriesSample) {
        if (!this.currentApplyScope || !this._scope_series_map) return;
        if (this.currentApplyScope.cabinet) {
            this._scope_series_map.cabinet = series;
        }
        if (this.currentApplyScope.hard) {
            this._scope_series_map.hard = series;
        }
        if (this.currentApplyScope.soft) {
            this._scope_series_map.soft = series;
        }
        if (this.currentApplyScope.remaining) {
            this._scope_series_map.remaining = series;
        }
    }

    /**
     *  获得硬装列表
     */
    public getHardDecorationList() {
        let ans: TFigureElement[] = [];
        if (this._wallTexture) {
            ans.push(this._wallTexture);
        }
        if (this._ceilling_list) {
            ans.push(...this._ceilling_list);
        }
        if (this._tile) {
            ans.push(this._tile);
        }
        if (this._door_figure_list) {
            ans.push(...this._door_figure_list);
        }
        return ans;
    }

    public addFurnitureElements(fes: TFigureElement[]) {
        this._furniture_list.push(...fes);
        // let logContent = "TRoom.addFurnitureElements()";
        // fes.forEach((fe) => {
        //     logContent += "  " + fe.toString() + "\n";
        // });
        // console.info(logContent);
    }

    public setTileElement(fe: TFigureElement) {
        this._tile = fe;
    }
    public getOrCreateTile() {
        this._tile = this._tile || TFigureElement.createSimple("地面");
        this._tile._room = this;
        return this._tile;
    }

    public get tile() {
        return this.getOrCreateTile();
    }

    public getOrCreateWallTexture() {
        this._wallTexture = this._wallTexture || TFigureElement.createSimple("墙面");
        this._wallTexture._room = this;
        return this._wallTexture;
    }

    public get wallTexture() {
        return this.getOrCreateWallTexture();
    }
    public addFurnitureElement(fe: TFigureElement) {
        this._furniture_list.push(fe);
        // let logContent = "TRoom.addFurnitureElement()";
        // logContent += "  " + fe.toString() + "\n";
        // console.info(logContent);
    }

    public resetFurnitureList() {
        this._furniture_list.length = 0;
        if (this._tabletop_list) {
            this._tabletop_list.length = 0;
        }
    }

    public resetSubSpaceAreas() {
        this._sub_space_list.length = 0;
    }

    public set furnitureList(list: TFigureElement[]) {
        // let logContent = "TRoom.setFurnitureList() \n";
        // list.forEach((fe) => {
        //     logContent += "    " + fe.toString() + "\n";
        // });
        // console.info(logContent);
        this._furniture_list = list;
    }

    public get furnitureList(): TFigureElement[] {
        return this._furniture_list;
    }

    public clearMatchedMaterials(): void {
        if (this._furniture_list) {
            this._furniture_list.forEach((fe) => {
                fe.clearAllMatchedMaterials();
                fe.getAlternativeFigureElements().forEach(fe => fe.clearMatchedMaterials());
            });
        }
        if (this._ceilling_list) {
            this._ceilling_list.forEach((fe) => {
                fe.clearAllMatchedMaterials();
                fe.getAlternativeFigureElements().forEach(fe => fe.clearMatchedMaterials());
            });
        }
        if (this._wallTexture) {
            this._wallTexture.clearAllMatchedMaterials();
        }
        if(this.decoration_elements){
            this.decoration_elements.length = 0;
        }

        if (this._tile) {
            this._tile.clearMatchedMaterials();
            this._tile.pictureViewImg = null;
        }

        if (this._room_entity) {
            let win_rects = this._room_entity._win_rects;
            for (let win_rect of win_rects) {
                let win_entity = win_rect._attached_elements["Entity"] as TWindowDoorEntity;
                if (win_entity && win_entity._win_figure_element) {
                    win_entity._win_figure_element.clearMatchedMaterials();
                }
            }
        }

        if (this._door_figure_list) {
            this._door_figure_list.forEach((fe) => {
                fe.clearMatchedMaterials();
            });
        }
    }

    public set haveGeneratedHardElements(t: boolean) {
        this._hadMakeHardElements = t;
    }

    public get haveGeneratedHardElements(): boolean {
        return this._hadMakeHardElements;
    }

    public isFigureElementInCurrentScope(fe: TFigureElement): boolean {
        let isInScope: boolean = false;
        if (this._current_apply_scope) {
            if (this._current_apply_scope.soft && FigureCategoryManager.isSoftFigure(fe)) {
                isInScope = true;
            }
            if (this._current_apply_scope.cabinet && FigureCategoryManager.isCustomCabinet(fe)) {
                isInScope = true;
            }
            if (this._current_apply_scope.hard && FigureCategoryManager.isHardFigure(fe)) {
                isInScope = true;
            }
            if (this._current_apply_scope.remaining && !fe.haveMatchedMaterial()) {
                isInScope = true;
            }
        } else {
            isInScope = false;
        }
        return isInScope;
    }

    public isFigureElementInFurnishedScope(fe: TFigureElement): boolean {
        let isInScope: boolean = false;
        if (this._current_apply_scope) {
            if (this.furnishedApplyScope.soft && FigureCategoryManager.isSoftFigure(fe)) {
                isInScope = true;
            }
            if (this.furnishedApplyScope.cabinet && FigureCategoryManager.isCustomCabinet(fe)) {
                isInScope = true;
            }
            if (this.furnishedApplyScope.hard && FigureCategoryManager.isHardFigure(fe)) {
                isInScope = true;
            }
        } else {
            isInScope = false;
        }
        return isInScope;
    }

    public clearMatchedMaterialsInCurrentScope(): void {
        if (this._furniture_list) {
            this._furniture_list.forEach((fe) => {
                if (this.isFigureElementInCurrentScope(fe) && !fe.locked) {
                    fe.clearMatchedMaterials();
                }
            });
        }
        if (this._tile && this.isApplyHardCategory()) {
            this._tile.pictureViewImg = null;
        }
    }

    public addApplyScope(scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean }, series: TSeriesSample) {
        if (this._scope_series_map == null) {
            this._scope_series_map = {
                soft: scope.soft ? series : null,
                cabinet: scope.cabinet ? series : null,
                hard: scope.hard ? series : null,
                remaining: scope.remaining ? series : null
            };
        } else {
            if (scope.soft) {
                this._scope_series_map.soft = series;
            }
            if (scope.cabinet) {
                this._scope_series_map.cabinet = series;
            }
            if (scope.hard) {
                this._scope_series_map.hard = series;
            }
        }
    }

    public get currentApplyScope() {
        return this._current_apply_scope;
    }

    public get furnishedApplyScope() {
        return this._furnished_apply_scope || this._current_apply_scope;
    }

    public get decoration_elements() {
        if (this._room_entity) {
            return this._room_entity.decoration_elements;
        }
        else {
            return null;
        }
    }

    public updateFurnishedApplyScope() {
        if (this._furnished_apply_scope == null) {
            this._furnished_apply_scope = this._current_apply_scope;
        } else {
            if (this._current_apply_scope.soft) {
                this._furnished_apply_scope.soft = true;
            }
            if (this._current_apply_scope.hard) {
                this._furnished_apply_scope.hard = true;
            }
            if (this._current_apply_scope.cabinet) {
                this._furnished_apply_scope.cabinet = true;
            }
        }
    }

    public updateRemainingFurnishedApplyScope() {
        this._remaining_furnished_apply_scope = this.currentApplyScope;
    }

    public getHardFurnishingElements(): TFigureElement[] {
        let hardFigureElements: TFigureElement[] = [];
        if (this._ceilling_list) {
            hardFigureElements.push(...this._ceilling_list);
        }

        if (this._room_entity) {
            let win_rects = this._room_entity._win_rects;
            for (let win_rect of win_rects) {
                let win_entity = win_rect._attached_elements["Entity"] as TWindowDoorEntity;
                if (win_entity && win_entity._win_figure_element) {
                    hardFigureElements.push(win_entity._win_figure_element);
                }
            }
        }

        hardFigureElements.push(this.wallTexture);
        hardFigureElements.push(this.tile);
        hardFigureElements.push(...this._door_figure_list);

        return hardFigureElements;
    }

    private shouldIgnoreMatching(fe: TFigureElement) {
        const unconsideredModellocSuffixs: string[] = ["灯", "饰品", "窗", "门", "收口板", "Default"];
        for (let modellocSuffix of unconsideredModellocSuffixs) {
            if (fe.modelLoc.endsWith(modellocSuffix)) {
                return true;
            }
        }
        return false;
    }

    public hasUnFurnishedFigureElement(): boolean {
        let unFurnishedList: TFigureElement[] = [];
        this._furniture_list.forEach((fe) => {
            if (this.shouldIgnoreMatching(fe)) return;
            if (fe.haveDeletedMaterial()) return;
            if (!fe.haveMatchedMaterial()) {
                if (fe.furnitureEntity instanceof TBaseGroupEntity) {
                    let allMembersMatched = true;
                    let members: TFigureElement[] = (fe.furnitureEntity as TBaseGroupEntity).disassembled_figure_elements;
                    for (let member of members) {
                        if (!member.haveMatchedMaterial()) {
                            allMembersMatched = false;
                            break;
                        }
                    }
                    if (allMembersMatched) {
                        return;
                    }
                    unFurnishedList.push(fe);
                } else {
                    unFurnishedList.push(fe);
                }
            }
        });
        // this.getHardFurnishingElements().forEach((fe) => {
        //     if (!fe.haveMatchedMaterial()) {
        //         unFurnishedList.push(fe);
        //     }
        // });

        if (ENV != 'prod' && unFurnishedList.length > 0) {
            console.info(`[UnFurnished Room's FigureElement]  Room ${this.brief()}\n  ${unFurnishedList.map((fe) => fe.toString()).join("\n  ")}`);
        }

        return unFurnishedList.length > 0;
    }

    public hasUnFurnishedApplyScope(): boolean {
        if (this._furnished_apply_scope == null && this._current_apply_scope == null) {
            return false;
        }

        if (this._furnished_apply_scope == null) {
            return false;
        }

        let softUnFurnished = !this._furnished_apply_scope.soft && (this._remaining_furnished_apply_scope == null || !this._remaining_furnished_apply_scope.soft);
        let hardUnFurnished = !this._furnished_apply_scope.hard && (this._remaining_furnished_apply_scope == null || !this._remaining_furnished_apply_scope.hard);
        let cabinetUnFurnished = !this._furnished_apply_scope.cabinet && (this._remaining_furnished_apply_scope == null || !this._remaining_furnished_apply_scope.cabinet);

        if (softUnFurnished || hardUnFurnished || cabinetUnFurnished) {
            if (ENV == 'dev') {
                console.info(`[UnFurnished Room's ApplyScope]  Room ${this.brief()}\n  ${softUnFurnished ? "soft" : ""} ${hardUnFurnished ? "hard" : ""} ${cabinetUnFurnished ? "cabinet" : ""}`);
            }
            return true;
        }
        return false;
    }

    public setCurrentApplyScope(scope: { soft: boolean, cabinet: boolean, hard: boolean, remaining: boolean }) {
        this._current_apply_scope = scope;
    }

    public clearApplyScope() {
        this._scope_series_map = null;
        this._current_apply_scope = null;
        this._furnished_apply_scope = null;
    }

    public startFurnishRemaining() {
        this._waiting_for_furnish_remaining = true;
    }

    public finishFurnishRemaining() {
        this._waiting_for_furnish_remaining = false;
    }

    public get waitingForFurshiRemaining() {
        return this._waiting_for_furnish_remaining;
    }

    get room_type() {
        return this.roomname;
    }

    get locked() {
        return this._locked;
    }

    set locked(lock: boolean) {
        this._locked = lock;
        this._furniture_list.forEach((fe) => {
            fe.locked = lock;
        });
        if (this._door_figure_list != null) {
            this._door_figure_list.forEach((door) => {
                door.locked = lock;
            });
        }
        if (this._ceilling_list != null) {
            this._ceilling_list.forEach((ceiling) => {
                ceiling.locked = lock;
            });
        }
        if (this._tile) {
            this._tile.locked = lock;
        }
        if (this._wallTexture) {
            this._wallTexture.locked = lock;
        }
    }

    get layoutLock() {
        return this._layout_lock;
    }

    set layoutLock(lock: boolean) {
        this._layout_lock = lock;
    }

    toLayoutScheme() {
        let layout_scheme = new TRoomLayoutScheme();

        let figure_elements: TFigureElement[] = [];

        this._furniture_list.forEach(fig => {
            if (fig?.furnitureEntity) {
                figure_elements.push(...fig.furnitureEntity.disassembled_figure_elements);
            }
            else {
                figure_elements.push(fig);
            }
        })
        figure_elements.sort((a, b) => b.default_drawing_order - a.default_drawing_order);


        layout_scheme.figure_list = new TFigureList({
            target_room_names: this.roomname,
            figure_elements: figure_elements as any
        });

        layout_scheme._scheme_name = "DIY布局";
        layout_scheme.room = this;
        return layout_scheme;
    }

    public fromLayoutScheme(ls: TRoomLayoutScheme) {
        this.addFurnitureElements(ls.figure_list.figure_elements);
    }


    adjustLineSegmentsOrder(lineSegments: I_SwjLineEdge[]): I_SwjLineEdge[] {
        const segmentMap = new Map<string, I_SwjLineEdge>();
        const adjustedSegments: I_SwjLineEdge[] = [];

        // 创建线段哈希映射，以便轻松查找连接线段
        lineSegments.forEach(segment => {
            const startKey = `${segment.start.x},${segment.start.y}`;
            const endKey = `${segment.end.x},${segment.end.y}`;
            segmentMap.set(startKey, segment);
            segmentMap.set(endKey, segment);
        });

        if (lineSegments.length === 0) {
            return adjustedSegments; // 没有线段，返回空数组
        }

        let currentSegment = lineSegments[0]; // 从第一个线段开始

        while (adjustedSegments.length < lineSegments.length) {
            adjustedSegments.push(currentSegment); // 添加当前线段到结果数组

            const endKey = `${currentSegment.end.x},${currentSegment.end.y}`;
            const nextSegment = segmentMap.get(endKey);

            if (nextSegment) {
                segmentMap.delete(endKey); // 确保每条线段只被添加一次
                currentSegment = nextSegment; // 移动到下一个线段
            } else {
                // 没有下一个线段，从未连接的线段中选择一个作为下一个线段
                segmentMap.forEach((segment: I_SwjLineEdge) => {
                    if (segment !== currentSegment) {
                        currentSegment = segment;
                        return;
                    }
                });
            }
        }

        return adjustedSegments;
    }

    public get ceilingHeight(): number {

        return this._ceiling_height;
    }
    // 标准房间名称 
    public get standardName(): string {
        return TRoom.getStdRoomNameByName(this.name);
    }

    public get curtainFigureElemens(): TFigureElement[] {
        let curtain_figures = null;
        if (this._furniture_list) {
            curtain_figures = this._furniture_list.filter((ele) => compareNames([ele.category], ["窗帘"]));
        }
        return curtain_figures;
    }

    public static roomType2NameMap: {
        [key: string]: string[];
    } = {
            "未命名": ["未命名"],
            "卧室": ["卧室", "主卧", "次卧", "客卧", "女孩房", "男孩房", "儿童房", "长辈房", "老人房", "多功能房","客房","大床房","双床房"],
            "卫生间": ["卫生间", "主卫", "次卫", "客卫", "公卫", "卫", "马桶"],
            "书房": ["书房", "茶室", "酒窖"],
            "客餐厅": ["客餐厅", "客厅", "餐厅"],
            "阳台": ["阳台"],
            "厨房": ["厨房", "厨卫"],
            "入户花园": ["入户花园", "花园", "入户"],
            "衣帽间": ["衣帽间", "衣帽"],
            "其它":["其它","楼梯间","电梯间","客梯", "风井","强电间","弱电间","布草间","回收间","洗消间"]
        };

    public static getRoomTypeByName(roomName: string, default_name: string = null) {
        for (let roomType in TRoom.roomType2NameMap) {
            if (compareNames([roomName], TRoom.roomType2NameMap[roomType])) {
                return roomType;
            }
        }
        if (default_name) return default_name;
        return roomName;
    }

    /**
     * 获得标准空间名称
     * @param name  
     * @returns 
     */
    public static getStdRoomNameByName(name: string) {
        for (let roomType in TRoom.roomType2NameMap) {
            for (let rooname of TRoom.roomType2NameMap[roomType]) {
                if (compareNames([rooname], [name])) {
                    return roomType;
                }
            }
        }
        return name;
    }


    /**
     * 海外获取空间名称
     * @param name  
     * @returns 
     */
    public static getOverseasRoomNameByName(name: string) {

        for (const [key, values] of Object.entries(TRoom.roomType2NameMap)) {
            if (values.includes(name)) {
                return key;
            }
        }
        return '未命名';
    }


    fromSwjRoom(room: I_SwjRoom) {
        this._swj_room_data = room;
        this.id = room.ind;
        this.room_id = room.room_id;
        this.uid = (room.uid || "0") as string;
        this.room_size = room.room_size;
        if (!this.room_size && room.area) {
            this.room_size = String(room.area.toFixed(2))
        }
        this.area = room.area;
        this.name = room.name || room.roomname;
        // this.roomname = room.name;
        this.roomname = TRoom.getRoomTypeByName(this.name);
        // this.room_type = this.getRoomTypeByName(room.name);
        this.boundary = room.boundary ? (this.adjustLineSegmentsOrder(room.boundary) as any) : null;

        this.inner_walls = [];
        this.points = [];
        if (room.inner_wall_list) {
            for (let inner_wall of room.inner_wall_list) {
                this.inner_walls.push({
                    StartX: inner_wall.start_x || 0,
                    StartY: inner_wall.start_y || 0,
                    EndX: inner_wall.end_x || 0,
                    EndY: inner_wall.end_y || 0,
                    Thickness: inner_wall.thick || 120,
                    type: inner_wall.type
                });
            }
        }

        if (this.boundary) {
            for (let line of this.boundary) {
                this.points.push([line.start.x, line.start.y]);
                this.points.push([line.end.x, line.end.y]);
            }
        }
        else {
            for (let inner_wall of room.inner_wall_list) {
                this.points.push([inner_wall.start_x, inner_wall.start_y]);
            }
        }


        this.windows = [];
        if (room.window_list) {
            for (let win of room.window_list) {
                this.windows.push({
                    posX: win.pos_x,
                    posY: win.pos_y,
                    rotateZ: win.rotate_z,
                    mirror: 0,
                    width: win.width,
                    length: win.length,
                    height: win.height,
                    type: "Window",
                    realType: "OneWindow",
                    room_names: win.room_names || []
                });
            }
        }

        if (room.door_list) {
            for (let win of room.door_list) {
                this.windows.push({
                    posX: win.pos_x,
                    posY: win.pos_y,
                    rotateZ: win.rotate_z,
                    mirror: 0,
                    width: win.width,
                    length: win.length,
                    height: win.height,
                    type: "Door",
                    realType: "SingleDoor",
                    openDirection: win.openDirection,
                    room_names: win.room_names || []
                });
            }
        }

        if (room.pillar_list) {
            for (let pillar of room.pillar_list) {
                this.pillars.push({
                    posX: pillar.pos_x,
                    posY: pillar.pos_y,
                    rotateZ: pillar.rotate_z,
                    width: pillar.width,
                    length: pillar.length,
                    height: pillar.height,
                    type: "StructureEntity",
                    realType: "Pillar"
                })
            }
        }
        if (room.flue_list) {
            for (let flue of room.flue_list) {
                this.flues.push({
                    posX: flue.pos_x,
                    posY: flue.pos_y,
                    rotateZ: flue.rotate_z,
                    width: flue.width,
                    length: flue.length,
                    height: flue.height,
                    type: "StructureEntity",
                    realType: "Flue"
                })
            }
        }
        if (room.pipe_list) {
            for (let pipe of room.pipe_list) {
                this.pipes.push({
                    posX: pipe.pos_x,
                    posY: pipe.pos_y,
                    rotateZ: pipe.rotate_z,
                    width: pipe.width,
                    length: pipe.length,
                    height: pipe.height,
                    type: "StructureEntity",
                    realType: "Envelope_Pipe"
                })
            }
        }
        if (room.platform_list) {
            for (let platform of room.platform_list) {
                this.platforms.push({
                    posX: platform.pos_x,
                    posY: platform.pos_y,
                    rotateZ: platform.rotate_z,
                    width: platform.width,
                    length: platform.length,
                    height: platform.height,
                    type: "StructureEntity",
                    realType: "Platform"
                })
            }
        }
        this.furnitureList = [];
        if (room.furniture_group_list) {
            for (let furniture of room.furniture_group_list) {
                this.addFurnitureElement(TFigureElement.fromSwjFurniture(furniture, this.roomname));

            }

        }
        if (room.furniture_list) {
            for (let furniture of room.furniture_list) {
                if (furniture.type === "BaseGroup") {
                    let fe = TFigureElement.fromSwjFurniture(furniture);
                    let entity = new TBaseGroupEntity(fe);
                    entity.updateCategory(furniture.public_category);
                    let sub_figures = furniture.sub_list.map((ele) => {
                        let fe = TFigureElement.fromSwjFurniture(ele);
                        return fe;
                    });
                    sub_figures.forEach((fe) => {
                        this.addFurnitureElement(fe);
                    })
                }
                else if (furniture.group_template_code && furniture.group_template_code.length > 0) {
                    let figure_element = TFigureElement.fromSwjFurniture(furniture, this.roomname);
                    let rect = figure_element.rect;
                    let group_template = TGroupTemplate.getGroupTemplateByGroupCode(furniture.group_template_code, rect);

                    if (group_template.current_s_group) {
                        let figure_elements = group_template.current_s_group.figure_elements;
                        figure_elements.forEach(ele => {
                            ele._group_uuid = "";
                            ele._group_main_figure = false;
                            ele._group_cate = "";
                            this.addFurnitureElement(ele);
                        })
                    }
                }
                else {
                    this.addFurnitureElement(TFigureElement.fromSwjFurniture(furniture, this.roomname));
                }
            }

        }


        for (let figure of this._furniture_list) {
            let unvalid_names = ['饰品', '碗', '化妆品']; // ["酒水","工艺品","灯","食品","茶具","摆件"];

            let flag = true;
            for (let name of unvalid_names) {
                if (figure.category.indexOf(name) >= 0 || figure.sub_category.indexOf(name) >= 0) {
                    flag = false;
                }
            }
            if (figure.min_z >= 1500) {
                flag = false;
            }
            if (!flag) {
                figure._ex_prop['visible'] = 0;
            }

            figure.postProcessStrangeZValAndHeight();


        }
        this.initRoomShape();




        this._furniture_list.sort((a, b) => {
            let la = ModelLocPublicCategoryMap.getModelLocOrder(a.category);
            let lb = ModelLocPublicCategoryMap.getModelLocOrder(b.category);
            if (Math.abs(la - lb) > 0.1) {
                return la - lb;
            }
            else {
                return a.max_z - b.max_z;
            }
        }); // 先按最大值从高到低排序




        for (let win of this.windows) {
            let target_edge: ZEdge = null;
            for (let edge of this.room_shape._poly.edges) {
                if (Math.abs(edge.nor.dot(win.rect.nor)) >= 0.999) {
                    let pp = edge.projectEdge2d(win.rect.rect_center);
                    if (pp.x < 0 || pp.x > edge.length) continue;
                    if (Math.abs(pp.y) <= win.width) {
                        target_edge = edge;
                    }
                }
            }

            if (target_edge) {
                win.rect._nor.copy(target_edge.nor);
                let pp = target_edge.projectEdge2d(win.rect._back_center);

                win.rect._back_center.copy(target_edge.unprojectEdge2d({ x: pp.x, y: 0 }));

                win.rect.updateRect();
            }
        }



        return this;


    }
    importRoomData(data: I_Room) {
        this._room_data = data;

        this.id = data.id || 0;
        this.room_id = -1;
        this.uuid = data.uuid || GenDateUUid();
        this.uid = data.uid || '';
        this.room_size = "";
        this._t_id = data._t_id || -1;
        this.inner_walls = data.inner_walls || [];
        this.points = data.points || [];
        this.name = data.name;
        this.aliasName = data.aliasName || "";
        this.roomname = data.roomname;
        this.windows = data.windows || [];
        this.pillars = data.pillars || [];
        this.flues = data.flues || [];
        this.pipes = data.pipes || [];
        this.platforms = data.platforms || [];
        this._door_figure_list = [];
        this._furniture_list = [];
        this.room_shape = new TRoomShape();
        this.schemeId = "";
        this.schemeName = "";
        this.shape_list = [];
        this.valid_shape_list = null;
        this.max_R_shape = null;
        this.max_L_shape = null;
        this.feature_shapes = [];
        this._layout = null;
        this._swj_room_data = null;
        this._group_template_list = null;
        this.boundary = [];
        this.selectable = true;
        this._room_entity = null;
        this.isSelectSeries = false;
        this.kgId = null;
        this.selectIndex = 0;
        this.mode = '';
        this._locked = false;
        this._scope_series_map = null;
        this._current_apply_scope = null;
        this._furnished_apply_scope = null;
        this._remaining_furnished_apply_scope = null;
        this._waiting_for_furnish_remaining = false;

        this._sub_space_list = [];
        this._remaining_figure_list = [];

        this._hadMakeHardElements = false;

        this.initRoomShape();

    }

    exportRoomData(): I_Room {
        let points: number[][] = [];
        for (let v of this.room_shape._poly.vertices) {
            let p = v.pos.toArray();
            for (let i in p) {
                p[i] = Math_Round(p[i]);
            }
            points.push(p);
        }
        return {
            _t_id: this._t_id,
            points: points,
            uuid: this.uuid,
            id: this.id,
            uid: this.uid,
            name: this.name,
            aliasName: this.aliasName || this.name,
            roomname: this.roomname,
            room_type: this.room_type,
            schemaId: this.schemeId,
            schemaName: this.schemeName,
            area: this.area,
            windows: TRoomExporter.exportEntity3DList(this.windows),
            pillars: TRoomExporter.exportEntity3DList(this.pillars),
            flues: TRoomExporter.exportEntity3DList(this.flues),
            pipes: TRoomExporter.exportEntity3DList(this.pipes),
            platforms: TRoomExporter.exportEntity3DList(this.platforms),
        }
    }
    exportSwjRoomData(export_wall_thickness: number = 0) {

        let windows: I_Entity3D[] = [];
        let doors: I_Entity3D[] = [];

        for (let win of this.windows) {
            if (win.type === "Window") {
                windows.push(win);
            }
            else {
                doors.push(win);
            }
        }
        let floor = null;
        if (this._swj_room_data?.floor) {
            floor = this._swj_room_data.floor;
        }
        else {
            floor = TRoomExporter.exportRoomFloor(this.uid, this.room_shape._poly);
        }
        let data: I_SwjRoom = {
            uid: this.uid || '0',
            uuid: this.uuid,
            name: this.name,
            roomname: this.roomname,
            room_type: this.room_type,
            floor: this._swj_room_data?.floor ? this._swj_room_data.floor : TRoomExporter.exportRoomFloor(this.uid, this.room_shape._poly),
            // floor: this._swj_room_data ? this._swj_room_data.floor : null,
            door_list: TRoomExporter.exportEntity3DList(doors, true),
            window_list: TRoomExporter.exportEntity3DList(windows, true),
            pillar_list: TRoomExporter.exportEntity3DList(this.pillars, true),
            flue_list: TRoomExporter.exportEntity3DList(this.flues, true),
            pipe_list: TRoomExporter.exportEntity3DList(this.pipes, true),
            platform_list: TRoomExporter.exportEntity3DList(this.platforms, true),
            furniture_list: TRoomExporter.exportNoEntityFurnitureList(this.furnitureList)
        }

        data.uid = this.uid;
        let boundary: I_SwjLineEdge[] = [];

        let poly = this.room_shape._poly;
        if (poly.orientation_z_nor.z > 0) {
            poly = poly.clone();
            poly.invertOrder();

        }
        for (let edge of poly.edges) {
            boundary.push({
                start: Vec3toMetaRounded(edge.v0.pos),
                end: Vec3toMetaRounded(edge.v1.pos)
            })
        }
        data.boundary = boundary;
        let walls: I_SwjWall[] = [];
        if (export_wall_thickness > 0.5) {
            let uid = 5;
            let mid_line_offset_poly = poly.clone().expandPolygon(export_wall_thickness / 2);
            for (let edge of mid_line_offset_poly.edges) {
                let rect = new ZRect(edge.length, export_wall_thickness);
                rect.nor = edge.nor;
                rect.rect_center = edge.center;

                let start_pos = rect.unproject({ x: -rect.w / 2, y: 0 });
                let end_pos = rect.unproject({ x: rect.w / 2, y: 0 });
                walls.push({
                    start_x: start_pos.x,
                    start_y: start_pos.y,
                    end_x: end_pos.x,
                    end_y: end_pos.y,
                    pos_x: rect.rect_center.x,
                    pos_y: rect.rect_center.y,
                    pos_z: 0,
                    rotate_z: rect.rotation_z,
                    length: rect.w,
                    thick: rect.h,
                    uid: uid++,
                    uuid: generateUUID(),
                    boundary: []
                });

            }
            data.wall_list = walls;

        }
        data.scope_series_map = this._scope_series_map;
        if (this._ceilling_list && this._ceilling_list.length > 0) {
            data.ceiling_element_list = [];
            this._ceilling_list.forEach((ele) => {
                data.ceiling_element_list.push(ele.exportJson());
            });
        }
        if (this._wallTexture) {
            data.wall_element = this._wallTexture.exportJson();
        }
        if (this._tile) {
            data.floor_element = this._tile.exportJson();
        }
        return data;
    }

    exportExtRoomData(): I_ExtRoom {
        return {
            scheme_id: this.schemeId,
            room_id: '' + this.room_id,
            scheme_room_id: this.schemeName + "-" + this.room_id,
            uid: this.uid,
            room: this.exportRoomData(),
            swj_room: this._swj_room_data
        }
    }



    initRoomShape() {
        this.room_shape._poly.clear();

        let v_points: Vector3[] = [];
        for (let p of this.points) {
            v_points.push(new Vector3(p[0], p[1], 0));
        }

        this.room_shape.initByPoints(v_points);



        for (let window of this.windows) {
            // 对历史数据进行一次处理
            if (window.type as string == "DOOR") {
                window.type = "Door"
            }
            if (!window.rect) {
                window.center = new Vector3(window.posX, window.posY, 0);
                window.nor = new Vector3(0, 1, 0).applyEuler(new Euler(0, 0, window.rotateZ));
                if (window._nor_data) {
                    window.nor.copy(window._nor_data as Vector3Like);

                }

                window.rect = new ZRect(window.length, window.width);
                window.rect._nor.copy(window.nor);
                window.rotateZ = window.rect.rotation_z;
                window.rect.rect_center = window.center;
            }
            if (!window.room_names) {
                window.room_names = [];
            }
            if (window.room_names.length <= 1) {

                if (window.length > HallwayWidth) {
                    window.room_names.push("阳台**");
                }
            }
        }
        if (!this.pillars) this.pillars = [];
        if (!this.flues) this.flues = [];
        if (!this.pipes) this.pipes = [];


        let entities: I_Entity3D[] = [...this.pillars, ...this.flues, ...this.platforms, ...this.pipes];

        for (let pillar of entities) {
            if (!pillar.rect) {
                pillar.center = new Vector3(pillar.posX, pillar.posY, 0);
                pillar.nor = new Vector3(0, 1, 0).applyEuler(new Euler(0, 0, pillar.rotateZ));

                pillar.rect = new ZRect(pillar.length, pillar.width);
                pillar.rect._nor.copy(pillar.nor);
                pillar.rect.rect_center = pillar.center;
            }
        }



    }

    get room_center() {
        if (this.room_shape._poly) {
            return this.room_shape._poly.bbox_center;
        }
        else {
            return new Vector3();
        }
    }

    public drawOnCanvas(painter: TPainter, canvas: HTMLCanvasElement = null, figure_elements: TFigureElement[] = null, options:
        { canvas_width?: number, canvas_height?: number, wall_color?: string, floor_color?: string, draw_wall?: boolean, draw_text?: boolean, figure_label_color?: boolean } = { draw_text: true, draw_wall: true }) {
        if (!canvas) return;
        let ts = painter.exportTransformData();

        let default_canvas = painter._canvas;


        let currentRoom = this;
        let canvasElement = canvas;
        painter.bindCanvas(canvasElement);
        painter.clean();


        painter.p_center = currentRoom.room_shape._poly.computeBBox().getCenter(new Vector3());
        let boxSize = currentRoom.room_shape._poly._boundingbox.getSize(new Vector3());
        let roomBoxWidth = boxSize.x + 240;
        let roomBoxHeight = boxSize.y + 240;

        canvasElement.width = options.canvas_width || 500;
        canvasElement.height = options.canvas_height || 334;


        let scaleW = canvasElement.width / roomBoxWidth;
        let scaleH = canvasElement.height / roomBoxHeight;
        painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.85;
        currentRoom._painter = painter;


        painter.enter_drawpoly();



        if (options.draw_wall) {
            let offset_poly = currentRoom.room_shape._poly.clone().expandPolygon(240);
            let poly = currentRoom.room_shape._poly;
            if (offset_poly.edges.length == poly.edges.length) {
                for (let i in poly.edges) {
                    let edge = poly.edges[i];
                    let t_edge = offset_poly.edges[i];
                    let t_points: Vector3[] = [
                        edge.v0.pos, edge.center, edge.v1.pos,
                        t_edge.v1.pos, t_edge.center, t_edge.v0.pos
                    ]
                    let wall_poly = new ZPolygon();
                    wall_poly.initByVertices(t_points);
                    painter.fillStyle = options.wall_color || "#A2A2A5";
                    painter.fillPolygon(wall_poly, 0.8);
                }

            }
        }
        else {
            painter.strokeStyle = "#A2A2A5";
            painter.strokePolygons([this.room_shape._poly]);

        }


        if (options.floor_color) {
            painter.fillStyle = options.floor_color;
            painter.fillPolygons([this.room_shape._poly], 1.);
        }
        else {
            if (this._room_entity) {
                let pattern = painter.getPattern(this.roomname + "-RoomArea") || painter.getPattern("RoomArea");
                if (!pattern) return;
                painter.fillPolygonWithImage(this.room_shape._poly,
                    pattern.img, false, 0.75);
            }

        }

        currentRoom.drawRoom(8, 0, false, false);
        this.drawFigureElements(figure_elements, FigureZValRangeType.HighCabinet, { show_text: options.draw_text, figure_label_color: options.figure_label_color });

        painter.leave_drawpoly();

        painter.bindCanvas(default_canvas);
        painter.importTransformData(ts, false);
    }

    public drawRoomWithWalls(wall_thickness: number = 240) {
        let currentRoom = this;
        let offset_poly = currentRoom.room_shape._poly.clone().expandPolygon(wall_thickness);
        let poly = currentRoom.room_shape._poly;
        if (offset_poly.edges.length == poly.edges.length) {
            for (let i in poly.edges) {
                let edge = poly.edges[i];
                let t_edge = offset_poly.edges[i];
                let t_points: Vector3[] = [
                    edge.v0.pos, edge.center, edge.v1.pos,
                    t_edge.v1.pos, t_edge.center, t_edge.v0.pos
                ]
                let wall_poly = new ZPolygon();
                wall_poly.initByVertices(t_points);
                this._painter.fillStyle = "#000";
                this._painter.fillPolygon(wall_poly, 0.8);
            }

        }


    }


    public updateFeatures(using_shape_codes: string[] = null) {
        if (!using_shape_codes) {
            if (compareNames([this.room_type], ["客餐厅"], false) == 1) {
                using_shape_codes = ['I'];
            }
            else {
                using_shape_codes = ['R'];
            }
        }
        TRoomShape.optimizePoly(this.room_shape._poly);

        try {
            this.updateWindowsTypes();
            this.computeShapeList();
            this.computeFeatureShapes(using_shape_codes);
            if (this.feature_shapes) {
                for (let shape of this.feature_shapes) {
                    shape.updateShapeProperties(false);
                    shape.updateShapeCode();
                }
            }

            if (this.room_shape._feature_shape) {
                let shape = this.room_shape._feature_shape;
                shape.updateShapeProperties(false);
                shape.updateShapeCode();
            }

        } catch (error) {
            console.log("Error at  TRoom.updateFeatures()", error);
        }
    }
    cleanWindowsOfPoly(poly: ZPolygon) {
        for (let edge of poly.edges) {
            TRoomShape.initWindowOfEdge(edge);
        }
    }

    cleanStructureOfPoly(poly: ZPolygon) {
        for (let edge of poly.edges) {
            TRoomShape.initStructureEntityOfEdge(edge);
        }
    }
    addWindowsToPoly(poly: ZPolygon, win_dist: number = MaxWindowLayonDepth) {

        for (let window of this.windows) {
            let rect = window.rect;
            let o_edge = rect.frontEdge;
            for (let edge of poly.edges) {
                if (!rect.checkSameNormal(edge.nor, true)) continue;

                let pp = edge.projectEdge2d(rect.rect_center);
                let l_res = { layon_len: 0, ll: 0, rr: 0 };
                if (edge.islayOn(o_edge, rect.h + win_dist, 0.2, l_res)) {

                    TRoomShape.pushWindowToEdge(edge, window);
                }
            }
        }

        // 如果有地台, 当飘窗处理
        if (this.platforms) {
            for (let platform of this.platforms) {
                if (!platform.rect) continue;
                let rect = platform.rect;
                let o_edge = rect._outter_edges()[0];
                for (let edge of poly.edges) {
                    if (Math.abs(rect._nor.dot(edge.nor)) < 0.9) continue;

                    let l_res = { layon_len: 0, ll: 0, rr: 0 };
                    let window = { ...platform };
                    window.rect = window.rect.clone();
                    window.center = window.rect.rect_center;
                    window.nor = window.rect.nor.clone();
                    window.type = "Window";
                    window.realType = "BayWindow";
                    if (edge.islayOn(o_edge, rect.h + 100, 0.2, l_res)) {
                        TRoomShape.pushWindowToEdge(edge, window);
                    }
                }
            }
        }
    }

    addStructureEntitiesToPoly(poly: ZPolygon, structures: IRoomEntityRealType[] = ["Flue"]) {
        let entities: I_Entity3D[] = [];
        if (structures.indexOf("Flue") >= 0) {
            entities.push(...this.flues);

        }

        for (let flue of entities) {
            let rect = flue.rect;
            if (!rect) {
                console.warn("No Flue Rect!!");
                continue;
            }
            for (let o_edge of rect.edges) {
                for (let edge of poly.edges) {
                    if (Math.abs(rect._nor.dot(edge.nor)) < 0.9) continue;

                    let l_res = { layon_len: 0, ll: 0, rr: 0 };
                    if (edge.islayOn(o_edge, AlignWallDist, 0.2, l_res)) {
                        TRoomShape.pushStructureEntityToEdge(edge, flue);
                    }
                }
            }


        }

    }
    computeShapeList() {
        this.shape_list = [];

        let queue: TRoomShape[] = [];
        queue.push(this.room_shape);


        for (let qi = 0; qi < queue.length; qi++) {
            let shape = queue[qi];
            let t_shapes = shape.splitShape();
            if (t_shapes && t_shapes.length > 0) {
                for (let shape of t_shapes) {
                    queue.push(shape);
                }
            }
            if (queue.length > 1000) {
                break;
            }
        }
        this.shape_list = queue;


        for (let id = this.shape_list.length - 1; id >= 0; id--) {
            let shape = this.shape_list[id];
            shape._area = -1;

            shape.checkAreaNeighborRoomType(this);

            shape.checkAreaType(this);
        }
        for (let shape of this.shape_list) {
            shape.updateArea();
        }

        this.max_R_shape = null;
        this.max_L_shape = null;
        for (let shape of this.shape_list) {

            if (shape._poly.edges.length == 4) {
                if (shape._area == 0) {
                    shape.updateArea();
                }

                if (!this.max_R_shape || shape._area > this.max_R_shape._area) {
                    this.max_R_shape = shape;
                }
            }

            if (shape._poly.edges.length == 6) {
                if (!this.max_L_shape || shape._area > this.max_L_shape._area) {
                    this.max_L_shape = shape;
                }
            }
        }
        if (!this.max_R_shape) {
            this.max_R_shape = this.shape_list[0] || this.room_shape;
        }

        if (this.max_L_shape && this.max_R_shape) {
            if (this.max_L_shape._area < this.max_R_shape._area) {
                this.max_L_shape = null;
            }
        }
        for (let i in this.shape_list) {
            this.shape_list[i]._id_num = ~~i;
        }
        if (this.shape_list.length > 0) {
            this.shape_list[0].updateIdText();
        }

        this.computeOrientation();
        /**
         *  添加客餐厅的逻辑
         */
        TPolyPartition.instance.splitRoomPoly(this);




    }

    /**
     *  分析定向
     */
    computeOrientation() {
        if (!this.max_R_shape) return;
        // 保证现有最大矩形


        let rect = ZRect.computeMainRect(this.max_R_shape._poly);

        if (!rect) return;

        // TsAI_app.log("Max Rect Shape",this.max_rect_shape);

        // 以入户门来进行定向

        let doors: I_Window[] = [];

        let windows: I_Window[] = [];
        for (let win of this.windows) {
            if (win.type == "Door") {
                doors.push(win);

                if (compareNames(win.room_names, ["阳台"]) > 0) {
                    windows.push(win);
                }
            }
            else if (win.type == "Window") {
                windows.push(win);
            }
        }
        let door_score = (a: I_Window) => {
            if (a.room_names.length <= 1) {
                return 2;
            }
            if (compareNames(a.room_names, ["客餐厅"]) > 0) {
                return 1;
            }
            else {
                return 0;
            }
        }
        doors.sort((a, b) => door_score(b) - door_score(a));
        windows.sort((a, b) => b.length - a.length);


        let check_edge_window_weight = (edge: ZEdge) => {
            let wins = TRoomShape.getWindowsOfEdge(edge) || [];
            let tw = 0;
            for (let win of wins) {
                if (win.type == "Door") {
                    tw = Math.max(tw, 2);
                }
                else {
                    tw = Math.max(tw, 1);
                }
            }


            return tw;
        }

        if (compareNames([this.roomname], ["厨房", "阳台", "卫生间", "客餐厅"]) == 1 || windows.length == 0) {
            if (rect.w < rect.h) {
                let nor = rect.nor.clone().cross(new Vector3(0, 0, 1)).normalize();
                let tmp = rect.h;
                rect._h = rect._w;
                rect._w = tmp;

                rect.nor = nor;
                rect.updateRect();
            }

            // console.log("rect",rect.w,rect.h,this.max_rect_shape);
            this.cleanWindowsOfPoly(rect);

            // 给矩形添加窗户信息
            this.addWindowsToPoly(rect);

            // 确认是否有窗
            let back_win_weight = check_edge_window_weight(rect.backEdge);
            let front_win_weight = check_edge_window_weight(rect.frontEdge);
            if (back_win_weight >= 2) {
                let r_center = rect.rect_center;
                rect.nor = rect.nor.clone().negate();
                rect.rect_center = r_center;
            }
            else {
                if (doors && doors[0]) {
                    let pp = rect.project(doors[0].center);
                    if (pp.y < 0 && front_win_weight < 2) {
                        let r_center = rect.rect_center;
                        rect.nor = rect.nor.clone().negate();
                        rect.rect_center = r_center;
                    }

                }
            }
            this.cleanWindowsOfPoly(rect);
            this.addWindowsToPoly(rect);

            let next_edge = rect.backEdge.next_edge;
            let prev_edge = rect.backEdge.prev_edge;

            let next_win_weight = check_edge_window_weight(next_edge);
            let prev_win_weight = check_edge_window_weight(prev_edge);


            let invert_dv = false;
            if (next_win_weight == prev_win_weight) {
                if (doors && doors[0]) {
                    let pp = rect.project(doors[0].center);
                    if (pp.x > 0) {
                        invert_dv = true;
                    }

                }
            }
            else {

                let tmap: { [key: number]: number } = { 0: 1, 1: 2, 2: 0 };
                invert_dv = tmap[next_win_weight] < tmap[prev_win_weight];
            }

            if (invert_dv) {
                rect.u_dv = rect.dv.clone().negate();
                rect.updateRect();
            }



            this.cleanWindowsOfPoly(rect);
            this.addWindowsToPoly(rect);

        }
        else if (windows.length == 1) // 如果有窗,且只有一个窗
        {

            let win_edge: ZEdge = null;
            let min_dist = 9999999;
            for (let edge of rect.edges) {

                if (Math.abs(edge.nor.dot(windows[0].rect.nor)) < 0.1) continue;

                let win_rect = windows[0].rect;
                let win_back_edge = win_rect.backEdge;
                let pp = edge.projectEdge2d(windows[0].center);

                if (edge.islayOn(win_back_edge, min_dist, 0.1)) {
                    if (!win_edge || Math.abs(pp.y) < min_dist) {
                        win_edge = edge;
                        min_dist = Math.abs(pp.y);
                    }
                }

            }

            if (win_edge) {
                let win_nor = win_edge.nor;

                let t_nor = win_nor.clone().cross(new Vector3(0, 0, 1)).normalize();
                if (t_nor.length() > 0.5) {
                    let bbox = rect.computeBBox();
                    rect = ZRect.fromBox3(bbox, t_nor);
                }
                let main_door = doors[0];
                if (main_door) {
                    let pp = rect.project(main_door.center);

                    if (pp.y < 0) {
                        let rect_center = rect.rect_center;
                        let t_nor = rect.nor.clone().negate();
                        rect.nor = t_nor;
                        rect.rect_center = rect_center;
                    }

                }

                if (windows[0]) {
                    let pp = rect.project(windows[0].center);
                    if (pp.x < 0) {
                        rect.u_dv = rect.dv.negate();
                        rect.updateRect();
                    }
                }
            }




        }
        else {

            this.addWindowsToPoly(rect);
            let target_edge: ZEdge = null;

            for (let edge of rect.edges) {
                let wins = TRoomShape.getWindowsOfEdge(edge);

                if (wins) {
                    let has_window = false;
                    for (let t_win of wins) {
                        if (t_win.type === "Window" || t_win.realType === "BayWindow") {
                            has_window = true;
                        }
                    }
                    if (has_window) continue;
                }
                if (!target_edge || edge.length > target_edge.length) {
                    target_edge = edge;
                }
            }
            if (target_edge) {
                let win_nor = target_edge.nor;

                let t_nor = win_nor.clone().negate();
                if (t_nor.length() > 0.5) {
                    let bbox = rect.computeBBox();
                    rect = ZRect.fromBox3(bbox, t_nor);
                }


                this.addWindowsToPoly(rect);

                let wins = TRoomShape.getWindowsOfEdge(rect.edges[1]);
                if (wins && wins.length > 0) {
                    let win = wins[0];
                    if (win.realType === "BayWindow" || win.type === "Window")  // 如果下一条边是窗, 则跳过
                    {
                        rect.u_dv = rect.dv.negate();
                        rect.updateRect();
                    }

                }

            }
            else {

            }
            let back_win_weight = check_edge_window_weight(rect.backEdge);
            let front_win_weight = check_edge_window_weight(rect.frontEdge);
            if (back_win_weight >= 2) {
                let r_center = rect.rect_center;
                rect.nor = rect.nor.clone().negate();
                rect.rect_center = r_center;
            }
            else {
                if (doors && doors[0]) {
                    let pp = rect.project(doors[0].center);
                    if (pp.y < 0 && front_win_weight < 2) {
                        let r_center = rect.rect_center;
                        rect.nor = rect.nor.clone().negate();
                        rect.rect_center = r_center;
                    }

                }
            }
        }

        if (compareNames([this.roomname], ["厨房"]))  // 厨房要根据烟道的方向来
        {
            this.cleanStructureOfPoly(rect);
            // this.addStructureEntitiesToPoly(rect);

            // let back_edge = rect.backEdge;

            // let next_structures = TRoomShape.getStructureEntityOfEdge(back_edge.next_edge);
            // let prev_structures = TRoomShape.getStructureEntityOfEdge(back_edge.prev_edge);





        }

        this.max_R_shape._rect = rect;

        // TsAI_app.log(rect.dv);



    }
    get_neighbor_room_type(win: I_Window) {
        let room_names = win.room_names || [];

        for (let name of room_names) {
            if (name == this.roomname || name == this.room_type) continue;
            return TRoomNameDict[name] || name; // 返回第一个不同的房间            
        }

        return TRoomNameType.Entrance;
    }
    computeRoomShapeNeighborDistance(shape: TRoomShape, neighbor_types: TRoomNameType[]
        = [TRoomNameType.Entrance, TRoomNameType.Balcony, TRoomNameType.Kitchen, TRoomNameType.Washroom]) {
        shape._neighbor_room_distance = {};

        for (let room_type of neighbor_types) {
            if (!shape._neighbor_room_distance[room_type]) {
                shape._neighbor_room_distance[room_type] = 9999999;
            }
        }

        for (let win of this.windows) {
            let rect = win.rect;
            if (!rect) continue;
            win.room_names = win.room_names || [];

            let room_type = this.get_neighbor_room_type(win);

            if (win.type !== "Door" && room_type !== TRoomNameType.Balcony) continue;

            if (compareNames([room_type], neighbor_types) == 0) continue;




            let min_dist = 9999999;
            for (let v of rect.vertices) {
                let dist = shape._poly.distanceToPoint(v.pos, AlignWallDist);
                if (Math.abs(dist) < min_dist) {
                    min_dist = Math.abs(dist);
                }
            }

            shape._neighbor_room_distance[room_type] = Math.min(shape._neighbor_room_distance[room_type], min_dist);


        }
    }
    computeFeatureShapes(using_shape_codes: string[] = ['R']) {
        this.feature_shapes = [];
        let tmp_feature_shapes: TFeatureShape[] = [];
        let underline_score = 0.6;

        if (using_shape_codes.indexOf("I") >= 0) {
            let t_shape = new T_Simple_Shape();
            if (t_shape.findFeatureShape(this) >= underline_score) {
                this.feature_shapes.push(t_shape);
            }
        }
        if (using_shape_codes.indexOf('T') >= 0) {
            let t_shape = new T_T_Shape();
            tmp_feature_shapes.push(t_shape);
            if (t_shape.findFeatureShape(this) >= underline_score) {
                this.feature_shapes.push(t_shape);
            }

        }

        if (using_shape_codes.indexOf('S') >= 0) {
            let s_shape = new T_S_Shape();
            tmp_feature_shapes.push(s_shape);
            if (s_shape.findFeatureShape(this) >= underline_score) {
                this.feature_shapes.push(s_shape);
            }
        }

        if (using_shape_codes.indexOf('L') >= 0) {
            let l_shape = new T_L_Shape();
            tmp_feature_shapes.push(l_shape);
            if (l_shape.findFeatureShape(this) >= underline_score) {
                this.feature_shapes.push(l_shape);
            }
        }


        // let r_shape = new T_R_Shape();
        // if(r_shape.findFeatureShape(this) >= underline_score)
        // {
        //     this.feature_shapes.push(r_shape);
        //     tmp_feature_shapes.push(r_shape);

        // }
        // else{

        //     let r2_shape = new T_Rk_Shape();
        //     if(r2_shape.findFeatureShape(this) >= underline_score)
        //     {
        //         this.feature_shapes.push(r2_shape);
        //     }
        //     tmp_feature_shapes.push(r2_shape);
        // }
        // if(this.feature_shapes.length == 0)
        // {


        //     let r3_shape = new T_Rk_Shape(3);
        //     r3_shape.findFeatureShape(this);
        //     if(r3_shape._fit_area_score > 0.5)
        //     {
        //         this.feature_shapes.push(r3_shape);
        //     }

        //     tmp_feature_shapes.sort((a,b)=>b._fit_area_score - a._fit_area_score);

        //     if(tmp_feature_shapes.length > 0)
        //     {

        //         let score_line = tmp_feature_shapes[0]._fit_area_score;

        //         for(let shape of tmp_feature_shapes)
        //         {
        //             if(shape._fit_area_score > score_line * 0.8)
        //             {
        //                 this.feature_shapes.push(shape);
        //             }
        //         }
        //     }

        // }


        for (let feature_shape of this.feature_shapes) {
            feature_shape._updateShapeEdgeProp();
            // feature_shape.updateShapeCode();
        }
        this.feature_shapes.sort((a, b) => {
            if (a._fit_area_score > 0.80 && b._fit_area_score > 0.80) {
                return a._order - b._order;
            }
            return b._fit_area_score - a._fit_area_score
        });


        // 给room_shape添加特征多边形  主要是为了统一计算对象
        if (this.room_shape) {
            this.room_shape._feature_shape = new TFeatureShape();

            this.room_shape._feature_shape.findFeatureShape(this);
            // this.room_shape._feature_shape._initContours();
        }

    }
    updateWindowsTypes() {
        if (!this._layout) return;
        if (!this._room_data || !this._room_data?.uuid) {
            this._room_data = this.exportRoomData();
        }
        for (let c_win of this.windows) {
            if (!c_win.rooms) {

                c_win.rooms = [this._room_data];
            }

        }

        for (let room of this._layout.rooms) {
            if (room === this._room_data) continue;
            if (room.uuid == this._room_data.uuid) continue;
            if (!room.windows) continue;


            for (let window of room.windows) {
                window.center = new Vector3(window.posX, window.posY, 0);

                for (let c_win of this.windows) {
                    c_win.center = new Vector3(c_win.posX, c_win.posY, 0);
                    let dist = c_win.center.distanceTo(window.center);
                    if (dist < 200 || window.id == c_win.id && c_win.id !== undefined) {

                        let r_id = -1;
                        for (let id in c_win.rooms) {
                            let r_room = c_win.rooms[id];
                            if (r_room == room || r_room.uuid == room.uuid) {
                                r_id = ~~id;
                                break;
                            }
                        }
                        if (r_id < 0) {
                            window.rooms.push(room);
                        }
                    }
                }
            }
        }

        for (let window of this.windows) {
            if (!window.room_names) {
                window.room_names = [];
            }

            for (let room of window.rooms) {
                if (room.roomname) {
                    if (compareNames(window.room_names, [room.roomname]) == 0) {
                        window.room_names.push(room.roomname);
                    }
                }
            }
        }
    }


    static getWindowText(window: I_Window) {
        let type = window.type;
        let label: string = null;

        if (type.length > 0) {
            if (type === "Door") {
                label = "门";
            }
            else if (type === "Window") {
                if (window.realType != null && window.realType.indexOf("BayWindow") >= 0) {
                    label = "飘窗";
                } else {
                    label = "窗";
                }
            }
            else if (type === "Hallway") {
                label = "过道";
            }
        }

        if (!label) label = type;
        return label;
    }

    static getWindowTextWithRoomName(window: I_Window, roomname: string = "") {
        let type = TRoom.getWindowText(window);

        if (window.room_names && window.room_names.length > 0) {

            for (let name of window.room_names) {
                if (name.indexOf(roomname) >= 0) {
                    continue;
                }
                type += "-" + name;
            }
        }
        return type;
    }

    static getWindowFillColorByRoomNames(window: I_Window, roomName: string = "") {
        let color = "#777";

        if (compareNames(window.room_names, ["阳台"]) == 1) {
            return "#ff0";
        }
        for (let name of window.room_names) {
            if (name == roomName) continue;
            if (name === "卫生间") {
                return "#fff";
            }
            else if (name.indexOf("卧室") >= 0) {
                return "#a42";
            }
            else if (name.indexOf("厨") >= 0) {
                return "#4a2";
            }
            else if (name.indexOf("儿童") >= 0) {
                return "#a42";
            }
            else if (name.indexOf("储物") >= 0) {
                return "#aa2";
            }
            else if (name.indexOf("书") >= 0) {
                return "#772";
            }

        }

        return color;
    }
    static getWindowFillColor(window: I_Window) {
        let color = "#777";
        if (!window) return color;

        let realType = window.realType || "OneWindow";
        if (realType === "BayWindow") {
            return "#919092";
        }
        if (window.type === "Window") {
            return "#919092";
        }
        if (window.type === "Door") {
            return "#919092";
        }

        if (window.type === "Hallway") {
            return "#fff";
        }
        if (window.type) {
            if (window.room_names.length === 1) {
                if (window.length >= HallwayWidth) {
                    return "#ff0";
                }
            }
        }


    }
    static drawWindow(window: I_Window, painter: ZPainter) {

        if (!painter) return;
        let windowColor = TRoom.getWindowFillColor(window);
        painter.fillStyle = windowColor;
        // painter.fillStyle = "#919092";
        painter.fillPolygon(window.rect, 1);
        painter.strokeStyle = windowColor;
        painter.drawEdges(window.rect.edges);

        painter.fillStyle = "#000";
        let rotateZ = window.rotateZ;

        if (Math.abs(rotateZ - Math.PI) < 0.1) {
            rotateZ = 0;
        }

        let realType = window.realType || "";

        if (realType.indexOf("BayWindow") >= 0) {
            let normal = new Vector3(Math.cos(window.rotateZ + Math.PI), Math.sin(window.rotateZ + Math.PI), 0);
            let windowRectCenter = MathUtils.walkDistanceAlongDirectionFromPoint(new Vector3(window.rect.back_center.x, window.rect.back_center.y, 0), MathUtils.negativeTangentVector(normal), 240);

            painter.fillStyle = windowColor;
            painter.fillPolygon(window.rect, 1);

            let newBayWindowOutRect: ZRect = new ZRect(window.rect.w + 200, window.rect.h + 100 - 240);
            newBayWindowOutRect.nor = MathUtils.negativeTangentVector(normal);
            newBayWindowOutRect._back_center = windowRectCenter;
            newBayWindowOutRect.updateRect();

            let newBayWindowOut2Rect: ZRect = new ZRect(window.rect.w + 400, window.rect.h + 200 - 240);
            newBayWindowOut2Rect.nor = MathUtils.negativeTangentVector(normal);
            newBayWindowOut2Rect._back_center = windowRectCenter;
            newBayWindowOut2Rect.updateRect();

            painter.strokeStyle = windowColor;
            painter.strokePolygons([newBayWindowOutRect, newBayWindowOut2Rect]);

            painter.fillStyle = "#000";
            painter.drawText(this.getWindowText(window), window.rect.rect_center, 0, 38, 15, false, true);

            // painter.drawLineSegment(new Vector3(window.posX, window.posY, 0), windowRectCenter, "red");
        }

        // painter.drawText(this.getWindowText(window),window.center,rotateZ,20,15,false);
    }

    drawWindowWithText(window: I_Window, painter: ZPainter) {
        if (!painter) painter = this._painter;
        let color = TRoom.getWindowFillColorByRoomNames(window, this.roomname);
        painter.fillStyle = color;
        // painter.fillStyle = "#919092";
        painter.fillPolygon(window.rect, 0.6);
        painter.strokeStyle = "#919092";
        painter.drawEdges(window.rect.edges);

        painter.fillStyle = "#000";
        let rotateZ = window.rotateZ;

        if (Math.abs(rotateZ - Math.PI) < 0.1) {
            rotateZ = 0;
        }
        painter.drawText(TRoom.getWindowText(window) + (window.length).toFixed(0), window.center, rotateZ, 20, 20, false);
    }


    static drawDoor(door: I_Window, painter: ZPainter) {
        // console.log("drawDoor: " + door.realType + "," + door.rotateZ + "," + door.openDirection);
        let doorRectCenter = new Vector3(door.posX, door.posY, 0);
        let door_realtype = door.realType || "SingleDoor";
        let openDirection = door.openDirection || 1;
        if (door_realtype.indexOf("SingleDoor") >= 0
            && door.openDirection != null && (door.openDirection == 4 || door.openDirection == 6 || door.openDirection == 0 || door.openDirection == 1)) {
            let normal = new Vector3(Math.cos(door.rotateZ + Math.PI), Math.sin(door.rotateZ + Math.PI), 0);
            if (door.openDirection == 4) {
                let rectEdgeCenter = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.tangentVector(normal), door.width / 2);
                let rectEdgeVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                painter.drawCircleQuarter(rectEdgeVertex, door.length, MathUtils.rotationAngle(normal), true, "#CFCFCF");
                // painter.drawLineSegment(doorRectCenter, rectEdgeCenter, "red");
            } else if (door.openDirection == 6) {
                let rectEdgeCenter = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.tangentVector(normal), door.width / 2);
                let rectEdgeVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                painter.drawCircleQuarter(rectEdgeVertex, door.length, MathUtils.rotationAngle(normal), false, "#CFCFCF");
                // painter.drawLineSegment(doorRectCenter, rectEdgeCenter, "red");
            } else if (door.openDirection == 0) {
                let rectEdgeCenter = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeTangentVector(normal), door.width / 2);
                let rectEdgeVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                painter.drawCircleQuarter(rectEdgeVertex, door.length, MathUtils.rotationAngle(normal) + Math.PI / 2, true, "#CFCFCF");
                // painter.drawLineSegment(doorRectCenter, rectEdgeCenter, "red");
            } else if (door.openDirection == 1) {
                let rectEdgeCenter = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeTangentVector(normal), door.width / 2);
                let rectEdgeVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                painter.drawCircleQuarter(rectEdgeVertex, door.length, MathUtils.rotationAngle(normal) - Math.PI / 2, false, "#CFCFCF");
                // painter.drawLineSegment(doorRectCenter, rectEdgeCenter, "red");
            }
        }
        if (door_realtype.indexOf("DoubleDoor") >= 0
            && door.openDirection != null && (door.openDirection == 4 || door.openDirection == 6)) {
            let normal = new Vector3(Math.cos(door.rotateZ + Math.PI), Math.sin(door.rotateZ + Math.PI), 0);
            if (door.openDirection == 4) {
                let rectEdgeLeftVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                let rectEdgeRightVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                painter.drawCircleQuarter(rectEdgeLeftVertex, door.length / 2, MathUtils.rotationAngle(normal), true, "#CFCFCF");
                painter.drawCircleQuarter(rectEdgeRightVertex, door.length / 2, MathUtils.rotationAngle(normal), false, "#CFCFCF");
            } else if (door.openDirection == 6) {
                let rectEdgeLeftVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                let rectEdgeRightVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                painter.drawCircleQuarter(rectEdgeLeftVertex, door.length / 2, MathUtils.rotationAngle(normal), false, "#CFCFCF");
                painter.drawCircleQuarter(rectEdgeRightVertex, door.length / 2, MathUtils.rotationAngle(normal), true, "#CFCFCF");
            }
        }
        if (door_realtype.indexOf("SafetyDoor") >= 0
            && door.openDirection != null && (door.openDirection == 4 || door.openDirection == 6)) {
            let normal = new Vector3(Math.cos(door.rotateZ + Math.PI), Math.sin(door.rotateZ + Math.PI), 0);
            if (door.openDirection == 4) {
                let rectEdgeLeftVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                let rectEdgeRightVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                painter.drawCircleQuarter(rectEdgeLeftVertex, door.length / 3, MathUtils.rotationAngle(normal), false, "#CFCFCF");
                painter.drawCircleQuarter(rectEdgeRightVertex, door.length * 2 / 3, MathUtils.rotationAngle(normal), true, "#CFCFCF");
            } else if (door.openDirection == 6) {
                let rectEdgeLeftVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, MathUtils.negativeVector(normal), door.length / 2);
                let rectEdgeRightVertex = MathUtils.walkDistanceAlongDirectionFromPoint(doorRectCenter, normal, door.length / 2);
                painter.drawCircleQuarter(rectEdgeLeftVertex, door.length * 2 / 3, MathUtils.rotationAngle(normal), false, "#CFCFCF");
                painter.drawCircleQuarter(rectEdgeRightVertex, door.length / 3, MathUtils.rotationAngle(normal), true, "#CFCFCF");
            }
        }

        let color = TRoom.getWindowFillColor(door);
        painter.fillStyle = color;
        painter.fillPolygon(door.rect, 0.8);
        painter.strokeStyle = "#919092";
        painter.drawEdges(door.rect.edges);

    }

    getShapeNum() {
        return this.shape_list.length;
    }
    getFeatureNum() {
        return this.feature_shapes.length;
    }
    /**
     * 绘制模式 mode:
     *   Mode 0:  无特定绘制内容, 只看show_origin_poly 和 show_dims是否绘制
     *   Mode 1:  绘制room_shape分解出来的第qi个子形状, shape_list里面的内容 
     *   Mode 4:  绘制room_shape分解出来的所有子形状--- shape_list里的所有形状
     *   Mode 2:  绘制特征多边形---feature_shape， 是指这个多边形化简成常见的形状, 比如矩形,L形、S形等等
     *   Mode 5:  暂时跟Mode 2一样
     *   Mode 6:  仅仅绘制room的多边形，并简单填充
     * @param mode  
     * @param qi 
     * @param show_origin_poly 
     * @param show_dims 
     * @returns 
     */
    drawRoom(mode: number = 0, qi: number = 0, show_origin_poly: boolean = true, show_dims: boolean = false) {
        if (!this._painter) return;

        this._painter._context.lineWidth = 3;
        // let b_center = this.room_shape._poly.bbox_center;

        // this._painter._p_center.x = b_center.x;
        // this._painter._p_center.y = b_center.y;

        this._painter.strokeStyle = "#a42";
        if (mode === 1) {
            let shape = this.shape_list[qi];
            if (shape) {
                this._painter.fillStyle = "#a22";
                this._painter.fillPolygon(shape._poly, 0.5);
                this._painter.drawEdges(shape._poly.edges);

                this._painter.strokeStyle = "#000";
                // console.log(qi,shape._area,shape);
            }

        }
        else if (mode === 2 || mode === 5) {
            let feature = this.feature_shapes[qi];
            if (feature) {
                if (feature._poly) {
                    let poly = feature._poly;

                    this._painter.fillStyle = "#a22";
                    this._painter.fillPolygon(poly, 0.5);
                }
                else if (feature._room_shapes.length > 1) {
                    for (let room_shape of feature._room_shapes) {
                        let poly = room_shape._poly;
                        this._painter.fillStyle = "#a22";
                        this._painter.fillPolygon(poly, 0.5);
                        this._painter.drawEdges(poly.edges);

                        this._painter.strokeStyle = "#000";

                        this._painter.strokeStyle = "#a42";

                        let lw = this._painter._context.lineWidth;
                        this._painter._context.lineWidth = lw * 4;
                        this._painter.drawEdges([poly.edges[0]]);
                        this._painter.strokeStyle = "#ff0";

                        this._painter.drawEdges([poly.backEdge]);
                        this._painter.strokeStyle = "#000";
                        this._painter._context.lineWidth = lw;

                    }
                }

                if (!show_origin_poly) {
                    if (feature._contours) {
                        for (let edge of feature._contours.edges) {
                            let windows = TRoomShape.getWindowsOfEdge(edge);
                            if (windows) {
                                for (let window of windows) {
                                    TRoom.drawWindow(window, this._painter);
                                }
                            }
                        }
                    }
                }
            }
        }

        else if (mode === 3) {
            for (let shape of this.shape_list) {
                if (shape._children.length > 0) continue;
                this._painter.strokeStyle = "#000";

                this._painter.drawEdges(shape._poly.edges);
                let center = shape._poly.bbox_center;
                this._painter.drawText(shape._id_text, center, 0, 20);
                this._painter.strokeStyle = "#000";
            }

        }
        else if (mode === 4) {
            for (let shape of this.shape_list) {
                if (shape._children.length > 0) continue;
                if (shape._area_type === TRoomAreaType.Hallway) {
                    this._painter.fillPolygon(shape._poly, 0.2);
                }
                this._painter.drawEdges(shape._poly.edges);


            }
            this._painter.strokeStyle = "#000";

        }
        else if (mode === 6) {
            let roomFillColor = "#BFBFBF";
            if (this.selected) {
                roomFillColor = "#C7E2FE";
            }
            this._painter.fillStyle = roomFillColor;
            this._painter.fillPolygon(this.room_shape._poly, 1.0);
        }
        else if (mode === 7) // 测试模式
        {
            this._painter.strokeStyle = "#000";
            //    this._painter.strokePolygons([ this.room_shape._poly]);

            this.drawRoomWindows("Training");
            let room = this;
            //    if(room.valid_shape_list)
            //    {
            //        for(let shape of room.valid_shape_list)
            //        {
            //            let fill_color = shape.area_fillcolor;
            //            this._painter.fillStyle = fill_color;
            //            this._painter.fillPolygons([shape._poly],0.1);
            //            this._painter.strokeStyle = "#aaa";
            //            this._painter.strokePolygons([shape._poly]);

            //        }
            //    }

            for (let pillar of this.flues) {
                if (pillar.rect) {
                    this._painter.strokeStyle = "#000000";
                    this._painter.fillStyle = "#fff";
                    this._painter.fillPolygons([pillar.rect], 1.);

                    this._painter.fillStyle = "#000000";

                    if (pillar.type == "StructureEntity" && pillar.realType == "Flue") {
                        this._painter.drawDxfBlockInRect("SVJ-烟道", pillar.rect);
                        //    this._painter.drawText("烟道", pillar.rect.rect_center);
                    }
                }
            }
            for (let pillar of this.pipes) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";

                    this._painter.strokePolygons([pillar.rect]);
                    if (pillar.type == "StructureEntity") {
                        this._painter.drawText("包管", pillar.rect.rect_center);
                    }
                }
            }
            for (let pillar of this.platforms) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";
                    this._painter.strokePolygons([pillar.rect]);

                }
            }
        }
        else if (mode === 8) {
            this._painter.strokeStyle = "#000";
            // this._painter.fillStyle = "#fff";
            // this._painter.fillPolygons([ this.room_shape._poly],1.);
            for (let window of this.windows) {
                let entity = TBaseEntity.getEntityOfRect(window.rect);
                if (entity) {
                    this._painter.fillStyle = "#fff";
                    this._painter.fillPolygon(window.rect, 1.);
                    entity.drawEntity(this._painter as TPainter, { is_draw_figure: true });
                }
                else {
                    if (window.type === "Door") {
                        TRoom.drawDoor(window, this._painter);
                    } else {
                        TRoom.drawWindow(window, this._painter);
                    }
                }

            }
            for (let pillar of this.flues) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";

                    this._painter.strokePolygons([pillar.rect]);
                    if (pillar.type == "StructureEntity" && pillar.realType == "Flue") {
                        this._painter.drawText("烟道", pillar.rect.rect_center);
                    }
                }
            }
            for (let pillar of this.pipes) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";

                    this._painter.strokePolygons([pillar.rect]);
                    if (pillar.type == "StructureEntity") {
                        this._painter.drawText("包管", pillar.rect.rect_center);
                    }
                }
            }
            for (let pillar of this.platforms) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";
                    this._painter.strokePolygons([pillar.rect]);

                }
            }
        }



        if (show_dims) {
            this._painter.fillStyle = "#000";
            this._painter.strokeStyle = "#000";
            for (let edge of this.room_shape._feature_shape._w_poly.edges) {
                let dim = makeDimensionsOfEdge(edge, 2);
                dim.offset_len = 500;
                dim._font_size = 50;
                dim.text_offset_len = 100;
                dim._font_size = 20;
                this._painter.drawDimension(dim);
            }
        }
        if (show_origin_poly) {
            let roomFillColor = "#BFBFBF";
            if (this.selected) {
                roomFillColor = "#C7E2FE";
            }
            this._painter.fillStyle = roomFillColor;
            this._painter.fillPolygon(this.room_shape._poly, 1.0);

            for (let window of this.windows) {
                let entity = TBaseEntity.getEntityOfRect(window.rect);
                if (entity) {
                    this._painter.fillStyle = "#fff";
                    this._painter.fillPolygon(window.rect, 1.);
                    entity.drawEntity(this._painter as TPainter, { is_draw_figure: true });
                }
                else {
                    if (window.type === "Door") {
                        TRoom.drawDoor(window, this._painter);
                    } else {
                        TRoom.drawWindow(window, this._painter);
                    }
                }

            }

            for (let pillar of this.flues) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";

                    this._painter.strokePolygons([pillar.rect]);
                    if (pillar.type == "StructureEntity" && pillar.realType == "Flue") {
                        this._painter.drawText("烟道", pillar.rect.rect_center);
                    }
                }
            }
            for (let pillar of this.pipes) {
                if (pillar.rect) {
                    this._painter.fillStyle = "#000000";
                    this._painter.strokeStyle = "#000000";

                    this._painter.strokePolygons([pillar.rect]);
                    if (pillar.type == "StructureEntity") {
                        this._painter.drawText("包管", pillar.rect.rect_center);
                    }
                }
            }

        }



    }



    drawRoomText() {
        let oldFillStyle = this._painter.fillStyle;
        this._painter.fillStyle = "#000"
        if (this.max_R_shape == null) {
            this.computeShapeList();
        }
        let roomMaxRect = this.max_R_shape.getRect();
        let room_center = roomMaxRect != null ? roomMaxRect.rect_center : this.room_shape._poly.bbox_center;
        this._painter.drawText(this.name + "\n" + this.room_size + "m²", room_center, 0, 38, 15, false, true);
        this._painter.fillStyle = oldFillStyle;
    }

    drawRoomWindows(mode: "Layout" | "Training") {
        if (mode == "Layout") {
            for (let window of this.windows) {
                TWinDoorDrawingUtils.drawWinDoorRect(this._painter as TPainter, window.rect, window.type, window.realType, { is_draw_figure: true });
            }
        }
        else {
            for (let window of this.windows) {
                this.drawWindowWithText(window, this._painter);
            }
        }

    }

    /**
     * 绘制组合模板
     *    --->  相当于绘制分区
     * 
     *  drawGroupTemplates
     */
    drawGroupTemplates(figure_z_val_range_type: FigureZValRangeType = FigureZValRangeType.All) {
        if (!this._group_template_list) return;
        if (!this._painter) return;


        for (let group_template of this._group_template_list) {
            let rect = group_template._target_rect;

            let config = group_template.getFigureGroupConfigs(this.roomname);
            if (config) {
                let z_val_range_type = config.zval_range_type || FigureZValRangeType.All;
                if ((z_val_range_type & figure_z_val_range_type as number) == 0) {
                    continue;
                }
            }

            this._painter.strokeStyle = "#000";
            this._painter.fillStyle = "#fff";
            this._painter.strokePolygons([rect]);
            this._painter.fillPolygons([rect], 0.5);


            let center = rect.rect_center;
            this._painter.fillStyle = "#000";
            let text = group_template?.group_space_category || group_template?.seed_figure_group?.group_space_category || group_template?.seed_figure_group?.group_code || "";
            if (text.length > 4) {
                text = text.substring(0, text.length - 4) + "\n" + text.substring(text.length - 4);
            }
            if (text.indexOf("收口板") >= 0) {
                continue;
            }
            this._painter.drawText(text, center);
        }
    }

    /**
     *  绘制图元
     */
    drawFigureElements(figures: TFigureElement[] = null, figure_z_val_range_type: FigureZValRangeType = FigureZValRangeType.All,
        options: { show_text?: boolean, figure_label_color?: boolean } = { show_text: true }) {
        figures = figures || [...this._furniture_list];

        if (!figures) return;

        let t_figures: TFigureElement[] = [];
        figures.forEach((ele) => {
            if (ele.furnitureEntity) {
                let elements = ele.furnitureEntity.disassembled_figure_elements || [];
                t_figures.push(...elements);
            }
            else {
                t_figures.push(ele);
            }

        });
        figures = t_figures;

        figures.sort((a, b) => a.default_drawing_order - b.default_drawing_order);
        let zval_ranges: { z_min: number, z_max: number }[] = [];
        if (figure_z_val_range_type !== FigureZValRangeType.None) {
            let t = 1;

            for (let ti = 0; ti < 5; ti++) {
                if (TFigureVisibleRangeMap[t] && (figure_z_val_range_type & t)) {
                    zval_ranges.push(TFigureVisibleRangeMap[t]);
                }
                t = t << 1;
            }
        }
        for (let figure of figures) {

            let check_zvals = false;
            if (zval_ranges.length == 0) check_zvals = true;
            for (let zval_range of zval_ranges) {
                let z_min = zval_range.z_min || 0;
                let z_max = zval_range.z_max || 100000;

                if (figure.min_z >= z_min && figure.min_z <= z_max) {
                    check_zvals = true;
                    break;
                }
            }

            if (!check_zvals) continue;
            if (figure._wireFrameImage && figure._wireFrameImage.width) {
                figure.drawOutline(this._painter as TPainter, options.show_text || false, "#000", 10);
            }
            else {
                figure.drawFigure(this._painter as TPainter, options.show_text || false, "#000", 10);

            }

            if (options.figure_label_color) {
                let keys = Object.keys(g_FigureImagePaths);
                let id = Math.max(keys.indexOf(figure.category), keys.indexOf(figure.sub_category));
                if (id < -1) id = 255;

                this._painter.fillColorIndex = id;
                this._painter.fillPolygons([figure.rect], 0.6);
            }
        }
    }

    findOutFigureElementBackWall(figureElement: TFigureElement) {
        figureElement.rect.clearAttached(TFigureElement.BackWallEdge);
        let attached_wall_dist = 100;

        for (let wallEdge of this.room_shape._poly.edges) {
            if (figureElement.rect.checkInverseNormal(wallEdge.nor)
                && wallEdge.islayOn(figureElement.rect.backEdge, attached_wall_dist, 0.8)) {
                figureElement.rect._attached_elements[TFigureElement.BackWallEdge] = wallEdge;
                return wallEdge;
            }
        }
        return null;
    }

    findOutCurtainWindow(curtain: TFigureElement): I_Window {
        let ajacentDist = 200;

        for (let window of this.windows) {
            if (curtain.rect.checkSameNormal(window.nor, true)) {
                let wcd = window.rect.project(curtain.rect.rect_center);
                if (Math.abs(wcd.x) < window.rect.length / 2 + ajacentDist
                    && Math.abs(wcd.y) < curtain.rect.length / 2 + ajacentDist) {
                    return window;
                }
            }
        }
        return null;
    }


    initAreaGridsBoard(grid_len: number = 50) {
        this._area_grids_board = new AreaGridBoard(grid_len);
        this._area_grids_board.initGridsByRoom(this);
    }

    static compareRoomData(data0: I_ExtRoom, data1: I_ExtRoom, tol: number = 10) {
        if (!data0.swj_room || !data1.swj_room) return -1;

        if (!data0.swj_room.boundary || !data1.swj_room.boundary) return -1;
        if (!data0.swj_room.furniture_list || !data1.swj_room.furniture_list) return -1;

        if (data0.swj_room.boundary.length !== data1.swj_room.boundary.length) return -1;

        for (let id in data0.swj_room.boundary) {
            let b0 = data0.swj_room.boundary[id];
            let b1 = data1.swj_room.boundary[id];

            let dist0 = (new Vector3().copy(b0.start).distanceTo(b1.start));
            let dist1 = (new Vector3().copy(b0.end).distanceTo(b1.end));

            if (Math.max(dist0, dist1) > tol) return -1;
        }

        if (data0.swj_room.furniture_list.length !== data1.swj_room.furniture_list.length) return -1;

        for (let id in data0.swj_room.furniture_list) {
            let fe0 = data0.swj_room.furniture_list[id];
            let fe1 = data0.swj_room.furniture_list[id];


            let pos0 = new Vector3(fe0.pos_x, fe0.pos_y, 0);
            let pos1 = new Vector3(fe1.pos_x, fe1.pos_y, 0);

            let size0 = new Vector3(fe0.length, fe0.width, 0);
            let size1 = new Vector3(fe1.length, fe1.width, 0);

            let dist0 = pos0.distanceTo(pos1);
            let dist1 = size0.distanceTo(size1);

            if (dist0 > tol) return -1;
            if (dist1 > tol) return -1;

            if (fe0._figure_element && fe1._figure_element) {
                if (fe0._figure_element.sub_category !== fe1._figure_element.sub_category) return -1;
            }

        }

        return 1;

    }

    updateName(name: string) {
        let needs_updated_layouts = false;
        if (this.name !== name) {
            needs_updated_layouts = true;
        }
        this.name = name;
        this.roomname = TRoom.getRoomTypeByName(this.name);

        if (needs_updated_layouts) {
            if (this._layout_scheme_list) {
                this._layout_scheme_list.length = 0;
            }
        }
    }

    checkIsSelectable() {
        this.selectable = false;
        Arrangeable.forEach((supportedRoomType) => {
            if (this.room_type.indexOf(supportedRoomType) > -1) {
                this.selectable = true;
            }
        });
    }

    prepareRestartFurnishRemaining() {
        if (this._unmatched_remaining_figure_list && this._unmatched_remaining_figure_list.length > 0) {
            this._remaining_figure_list = this._unmatched_remaining_figure_list;
            this._unmatched_remaining_figure_list = [];
        }

        if (this._remaining_furnished_apply_scope) {
            if (this._remaining_furnished_apply_scope.cabinet) {
                this._furnished_apply_scope.cabinet = true;
            }
            if (this._remaining_furnished_apply_scope.soft) {
                this._furnished_apply_scope.soft = true;
            }
            if (this._remaining_furnished_apply_scope.hard) {
                this._furnished_apply_scope.hard = true;
            }
        }
    }

    haveSomeMatchedMaterial(): boolean {
        let allFigureElements: TFigureElement[] = [];
        if (this._furniture_list && this._furniture_list.length > 0) {
            allFigureElements.push(...this._furniture_list);
        }
        return allFigureElements.some((item: TFigureElement) => item && item.haveMatchedMaterial2());
    }
}