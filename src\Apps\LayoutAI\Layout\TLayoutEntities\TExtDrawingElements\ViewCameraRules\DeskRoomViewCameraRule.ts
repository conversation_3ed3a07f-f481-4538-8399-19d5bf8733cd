// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@layoutai/z_polygon";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { PerspectiveCamera, Vector3 } from "three";
import { compareNames } from "@layoutai/z_polygon";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { BedRoomViewCameraRule } from "./BedRoomViewCameraRule";
export class DeskRoomViewCameraRule extends BaseViewCameraRule {
    constructor() {
        super();
    }
    // 计算床视角
    static  generateViewCamera(desk_element: TFigureElement, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}): TViewCameraEntity[] {
        let camera = new PerspectiveCamera(75, 3.0 / 4.0, 300, 20000);
        let view_cameras: TViewCameraEntity[] = [];
        let desk_rect = desk_element?.matched_rect || desk_element?.rect;
        if(!desk_rect) return view_cameras;
        const min_hallway_length = 2000;
        let min_category_dist = 350; // 最小柜体宽度
        let category = room_entity.furniture_entities.find(f => compareNames([...f.category], ["柜体"]));
        let is_in_center = room_entity._main_rect.rect_center.distanceTo(desk_rect.rect_center) < 100;
        for(let edge of desk_rect.edges){
            if (!edge) return;
            let int_data = room_entity._room_poly.getRayIntersection(edge.unprojectEdge2d({ x: edge.length / 2, y: 0 }), edge.nor.clone().negate());
            if (!int_data || !int_data.point) return;

            let target_center = edge.center;
            let dist_to_front_wall = int_data.point.clone().sub(target_center).length();
            let add_dist = Math.abs(edge.nor.x * 1) > 0.9 ?  desk_rect.h: desk_rect._w; 
            
            // 墙上的相机到主rect前沿线的距离
            let dist_to_back_wall = int_data.point.clone().sub(edge.center).length() - add_dist;
            // 如果离墙的距离小于最小柜体宽度，并且是朝向墙体那一侧的，则不考虑
            if(dist_to_back_wall < min_category_dist && edge.center.distanceTo(desk_rect.frontEdge.center) < 10) continue;

            // 如果离墙的距离小于最小柜体宽度，并且没有柜体，则不考虑
            if(dist_to_back_wall < min_category_dist && !category) continue;

        
            let max_dist = min_hallway_length + add_dist;

            // if(dist_to_front_wall + 300 > min_hallway_length)
            // {
            //     max_dist = dist_to_front_wall + 300;
            // }
            let t_dist = max_dist;
            let t_rect = new ZRect(500, 500);
            t_rect.nor = edge.nor;
            t_rect.zval = 1150; // 默认高度还是高一些1400mm更合理
            let dist_step = 200;

            let iter = 20;
            while (iter--) {
                let pos = new Vector3();
                if(is_in_center)
                {
                    pos = edge.unprojectEdge2d({ x: edge.length / 2, y: -t_dist });
                } else 
                {
                    let edge_with_same_edge = room_entity._room_poly.getEdgeWithSameNor(edge.nor);
                    if(edge_with_same_edge)
                    {
                        let pp = edge.projectEdge2d(edge_with_same_edge.center);
                        pos = edge.unprojectEdge2d({ x: pp.x, y: -t_dist });
                    }
                }
                t_rect.rect_center = pos;

                TViewCameraEntity.updateCameraByRect(camera, t_rect);

                let mvp_matrix = camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse);


                let s_p0 = TViewCameraEntity.cameraProjectPos(mvp_matrix, edge.v0.pos);

                let xx = Math.abs(s_p0.x);
                let yy = Math.abs(s_p0.y);

                let ml = Math.max(xx, yy);

                if (ml < 0.40 || t_dist > max_dist - 10) {
                    break;
                }

                t_dist += dist_step;
                if (t_dist > max_dist) t_dist = max_dist;
            }
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(t_rect);

            view_camera._room_entity = room_entity;
            let view_camera_name = prefix;
            if(desk_rect && edge.nor.dot(desk_rect.nor) > 0.9)
            {
                view_camera_name = '书房 - 朝向书桌正面';
                view_camera._target = ['书桌'];
                view_camera._dir = '正面';
            } else
            {
                view_camera_name = '书房 - 书桌侧面';
                view_camera._target = ['书桌'];
                view_camera._dir = '侧面';
            }
            view_camera.name = view_camera_name;
            view_camera._is_focus_mode = (options.no_focus_mode || false) ? false : true;

            if (!view_camera.is_focus_mode) {
                view_camera.near = 600;
            }

            // 默认65
            view_camera.fov = 75;
            // // 如果离餐桌的距离大于700mm，并且离墙的距离小于1200mm, fov设置为75
            // if(dist_to_back_wall > 700 && dist_to_back_wall < 1200){
            //     view_camera.fov = 75;
            // }
            view_camera.is_focus_mode = true;
            view_camera._view_center = desk_rect.rect_center;
            view_camera._room_entity = room_entity;
            view_camera._main_rect = desk_rect;
            view_camera.hideFurnitures = this.hideFurnitures;
            view_cameras.push(view_camera);
        }
        return view_cameras;
    }
}