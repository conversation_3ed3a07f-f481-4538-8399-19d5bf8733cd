import React, { useEffect, useState, useRef, useReducer } from 'react';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { observer } from "mobx-react-lite";
import { EventName } from '@/Apps/EventSystem';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { Icon } from '@svg/antd-cloud-design';
import { Image } from '@svg/antd';
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import { useTranslation } from 'react-i18next';

interface RoomSeriesPlanProps {
    menuList: menuListProps[];
    multiExpand ?: boolean;
    showLayoutList: boolean;
  }
interface menuListProps {
    label: string;
    figureList: figure[];
    init_expanded ?: boolean;
}
interface figure {
  img: string;
  title: string;
  category: string;
}
interface Module {
    image: string;
    title: string;
    label: string;
}
interface FigureItem{
  image_path:string,room:TRoom,
  isFloor?:boolean,label:string,
  title:string,title2:string,title3:string,
  img:string,img1:string,img2:string,
  centerTitle:string,bottomTitle:string,checked?:boolean, area:string, isLocked?:boolean,
  figure_element?:TFigureElement, imgTitle?:string};

const MenuList: React.FC<RoomSeriesPlanProps> = ({menuList,multiExpand=true,showLayoutList}) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const [figureDataList, setFigureDataList] = useState<Module[]>([]);
    const [currentMenu, setCurrentMenu] = useState<{label?:string, child?:any[]}>({});
    const [expanded, setExpanded] = useState<{[key:number]:boolean}>({});
    const [subExpanded,setSubExpanded] = useState<{[key:string]:boolean}>({});
    const [menuListInfo, setMenuListInfo] = useState([]);
    const menuBoxRef = useRef(null);
    const [, forceUpdate] = useReducer(x => x + 1, 0);
    
    const handleLabelClick = (data: any, index: number) => {
        let list: any = [];

        let state = expanded[index] || false;
        let t_expanded = {...expanded};
        if(!multiExpand)
        {
          t_expanded = {};
        }
        t_expanded[index] = !state;

        setExpanded(t_expanded);
        setCurrentMenu(data)
        setFigureDataList(list);
        
    };
    const handleSubLabelClick = (uuid:string)=>
    {
      if(subExpanded[uuid])
      {
         delete subExpanded[uuid];
      }
      else{
        subExpanded[uuid] = true;
      }
      setSubExpanded({...subExpanded});
    }

    const handleItemClick = (subItem: FigureItem)=>{
      if(subItem.figure_element)
      {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.AIMatchingFigureSelected, subItem.figure_element);
      }
      
    }

    const isExpanded = (index: number) => {
        return expanded[index];
    };
    const isSubExpanded = (uuid:string,subIndex:number=-1)=>{
      // if(Object.keys(subExpanded).length == 0)
      // {
      //     if(subIndex==0)
      //     {
      //       return true;
      //     }
      // }
      return subExpanded[uuid] || false;
    };

    const figureInfo = (itemLabel:string, subItem:FigureItem, subIndex:string)=>{
      return <div 
      key={"t_figure"+subIndex} 
      className={`
        ${styles.figure_item} 
        ${styles.figure_item_qt}  
        ${["硬装","定制","软装"].includes(itemLabel)?styles.figure_item_sp:"" /*[i18n:ignore]*/} 
        ${subItem.figure_element?.locked?styles.figure_item_locked:''} 
        ${subItem.checked?styles.figure_item_checked:''}`
      }
      onClick={() => {
        if(subItem.figure_element?.locked!==true && ["硬装","定制","软装","图例","家具"].includes(itemLabel) /*[i18n:ignore]*/)
        {
          handleItemClick(subItem)
        }
      }} >
 
      {subItem.figure_element?.locked && <div className={styles.mask_item_locked}></div>}


      <Image src={(`${subItem.image_path}`)} preview={false}/>
      {subItem.imgTitle && <div className={"imgTitle"}>{subItem.imgTitle}</div>}
      <div style={{width: '70%'}}>
          <div className={styles.category} title={subItem.title}>{subItem.title} </div>
          <div className={styles.size}>{t(subItem.centerTitle)}</div>
          <div className={styles.size} title={t(subItem.bottomTitle)}>{t(subItem.bottomTitle)}</div>
      </div>
      {subItem.isFloor!==true && 
        <div style={{position:"absolute",right:"0"}}>
          <div className={styles.lock_icon+" lock_icon iconfont iconunlock_fill " + (subItem.figure_element?.locked ? "iconlock_fill":"")} onClick={(ev) => {
            if(subItem.room && subItem.room?.locked) return;
              if(subItem.figure_element)
              {
                subItem.figure_element.locked = !subItem.figure_element.locked;
                LayoutAI_App.emit(EventName.RoomMaterialsUpdated,true);
                LayoutAI_App.instance.update();
                ev.stopPropagation();

              }
            }}> 
            </div>
        </div>

      }
    </div>
    }
    const floorRoomInfo = (subItem:FigureItem, subIndex: number)=>{
      return <div className={styles.figure_item_fg} key={"floorRoomInfo_"+subIndex}>
      
              {subItem.isFloor===true ?
                  <div className={styles.figure_item+" "+styles.figure_item_title} key={"floor_room_info_"+subIndex}>
                      <Icon
                            iconClass={`${isSubExpanded(subItem?.room?.uuid,subIndex) ? 'iconcaretdown' : 'iconcaretright'}`}
                            className="icon"
                            size={14}
                            onClick={() => handleSubLabelClick(subItem.room?.uuid)}
                          />
                      <span onClick={() => handleSubLabelClick(subItem.room?.uuid)}> {t(subItem.room.name)} </span>


                      {subItem.room != null && 
                      <div className={styles.lock_icon+" lock_icon iconfont iconunlock_fill " + (subItem.room.locked ? "iconlock_fill":"")} onClick={() => {
                                              subItem.room.locked = !subItem.room.locked;
                                              LayoutAI_App.instance.update();

                                              forceUpdate();
                                          }}> 
                        </div>}
                </div> :
                <div className={styles.figure_item+" "+styles.figure_item_title} key={"floor_room_info_"+subIndex}>
                      {t(subItem.room.name)} 
                      {subItem.room != null && 
                      <div className={styles.lock_icon+" lock_icon iconfont iconunlock_fill " + (subItem.room.locked? "iconlock_fill":"")} onClick={() => {
                                              subItem.room.locked = !subItem.room.locked;
                                              LayoutAI_App.emit(EventName.RoomMaterialsUpdated,true);
                                              LayoutAI_App.instance.update();

                                          }}> 
                        </div>}
                </div>          
              }
 
    
                {(subItem.isFloor !== true || isSubExpanded(subItem.room?.uuid || "",subIndex)) && <>
                  {subItem.title &&  figureInfo(t("风格"),{isFloor:true, isLocked:subItem.room.locked, imgTitle:t("软装"), title:subItem.title,image_path:subItem.img,centerTitle:"",bottomTitle:""} as FigureItem,"style_"+'0')}
                  {subItem.title2 && figureInfo(t("风格"),{isFloor:true, isLocked:subItem.room.locked,imgTitle:t("定制"), title:subItem.title2,image_path:subItem.img1,centerTitle:"",bottomTitle:""} as FigureItem,"style_"+'1')}
                  {subItem.title3 && figureInfo(t("风格"),{isFloor:true, isLocked:subItem.room.locked,imgTitle:t("硬装"),title:subItem.title3,image_path:subItem.img2,centerTitle:"",bottomTitle:""} as FigureItem,"style_"+'2')}
                </>}
              </div>


           
    }
    useEffect(() => {
      for(let i in menuList)
      {
        expanded[i] = (menuList[i].init_expanded!==undefined)? menuList[i].init_expanded : (multiExpand?true:false);
      }
      setExpanded(expanded);

      let fg_item = menuList.find((item)=>item.label==="风格"); /*[i18n:ignore]*/
      if(fg_item)
      {
        if((fg_item?.figureList[0]) && (fg_item.figureList[0] as any as FigureItem).room)
        {
           let res: {[key: string]: boolean} = {};
           res[(fg_item.figureList[0] as any as FigureItem).room.uuid] = true;
            setSubExpanded(res);
        }
      }
      
      setTimeout(() => {
            if (menuList.length > 0) {
                setMenuListInfo(menuList);
            }
        }, 10);
    },[menuList])
    return (
        <div className={styles.root}>
            <div className={styles.menu_box}
              id='menu_box'
              ref={menuBoxRef}>
              <ul className="menu" style={{maxHeight: showLayoutList ? 'calc(100vh - 190px)' : 'calc(100vh - 220px)'}}>
                {menuListInfo.map((item, index) => (
                  <li key={item.label}>
                    <div
                      className={`label ${isExpanded(index) ? 'active' : ''}`}
                      onClick={() => handleLabelClick(item, index)}
                    >
                      <Icon
                        iconClass={`${isExpanded(index) ? 'iconcaretdown' : 'iconcaretright'}`}
                        className="icon"
                        size={14}
                      />
                      <span className="label_name">{t(item.label)}</span>
                    </div>
                    {isExpanded(index) && 
                      <div className={styles.figureListInfo}>
                          {item.figureList.map((subItem: FigureItem, subIndex: number) => {
                              return item.label != '风格' ? figureInfo(item.label,subItem,subIndex.toString()): floorRoomInfo(subItem,subIndex);   /*[i18n:ignore]*/
                          })}
                      </div>
                    }                    
                  </li>
                ))}
              </ul>
            </div>
        </div>
    );
};


export default observer(MenuList);
