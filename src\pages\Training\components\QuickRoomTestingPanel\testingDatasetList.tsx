import useCommonStyles from '../../common_style/index';
import useStyles from './style';
import { useEffect, useRef, useState } from 'react';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { getCookie } from '@/utils';
import { BuildingService } from '@/Apps/LayoutAI/Services/Basic/BuildingService';
import { IndexedDBService } from '@/Apps/LayoutAI/Services/IndexedDB/IndexedDBService';
import { observer } from 'mobx-react-lite';
import { DistrictPopup, I_City } from '@/pages/Mobile/houseSearchPopup/districtPopup';
import { HouseSchemeTestingService } from '@/Apps/LayoutAI/Services/Training/HouseSchemeTestingService';
import { I_HouseSchemeInfo, I_HouseSchemeTestingInfo } from '@/Apps/LayoutAI/Services/Training/HouseSchemeRepositoryService';
import { Button } from '@svg/antd';
import { loadFile } from '@/IndexDB';
import { generateUUID } from 'three/src/math/MathUtils.js';
import { TrainingTestingEvents, TrainingTestingSignals } from '@/Apps/Training/TrainningEvents';
import { SchemeSourceType } from '@/Apps/LayoutAI/AICadData/SwjLayoutData';
import { openFileInput, saveFileAs } from '@/Apps/LayoutAI/Utils/file_utils';
import { Sleep, formatCurrentTime } from "@layoutai/z_polygon";

var g_IsDisplay = false;
var isQuerying = false;
// 对单个城市请求的默认数量
var queryNumForSingleCity = 60;


const TestingDatasetListPanel: React.FC = () => {

    // const common_styles = useCommonStyles().styles;
    const {styles} = useStyles();

    const [isDisplay, setIsDisplay] = useState<boolean>(g_IsDisplay);

    const RoomNames =["客餐厅","卫生间","厨房","卧室", "入户花园"];
    const [currentRoomInfo,setCurrentRoomInfo] = useState<{room_name:string,room_id:number,roomNum?:number}>({room_name:"客餐厅",room_id:0,roomNum:1});

    const [showHouseSchemeList, setShowHouseSchemeList] = useState<boolean>(true);

    const [isShowAddDataDialog,setIsShowAddHouseDataDialog] = useState<boolean>(false);
    const [currentBuildingList,setCurrentBuildingList] = useState<I_HouseSchemeInfo[]>([]);
    const [currentBuildingId,setCurrentBuildingId] = useState<string>("");
    const [datasetNameList,setDatasetNameList] = useState<{id:string,name:string}[]>([{id:"Default",name:"默认数据集"}]);
    const [currentDatasetId,setCurrentDatasetId] = useState<string>("Default");

    const [currentPageId,setCurrentPageId] = useState<number>(0); 
    const [targetCities, setTargetCities] = useState<I_City[]>([{"name":"广州市","code":"440100"}]);
    const [isShowDistricPopup,setIsShowDistricPopup] = useState<boolean>(true);

    let queryInfoLogRef = useRef(null);
    let datasetNameInputRef = useRef(null);
    

    const object_id = "TestingDatasetListPanel";


    const lastBuildingId = "lastBuildingId";
    const lastDatasetId = "lastDatasetId";

    
    const getDatasetNameById = (id:string = null)=>
    {
        id = id || currentDatasetId;
        let data = datasetNameList.find((data)=>data.id===id);
        return data?.name || "默认数据集";
    }
    const setDatasetNameById = (name:string,id:string = null)=>{
        id = id || currentDatasetId;
        let data = datasetNameList.find((data)=>data.id===id);
        if(data)
        {
            data.name = name;

            setDatasetNameList([...datasetNameList]);
        }
    }

    const onClickBuildingId = async (buildingId:string,options:{room_name?:string,id?:number,no_auto_layout?:boolean}={})=>{

        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        let authCode = getCookie("authCode");
        let c_room_name = options.room_name || currentRoomInfo.room_name;
        let c_room_id = options.id===undefined? currentRoomInfo.room_id:options.id;
        let c_room_num = 1;
        
        let res : I_HouseSchemeTestingInfo = (await IndexedDBService.instance.getDataById(buildingId,IndexedDBService.BuildingSchemeDataTable))?.houseInfo 
        || await HouseSchemeTestingService.instance.makeHouseTestingInfoDataByBuildingId(buildingId); 
        if(res && res.schemeXmlJson)
        {
            await IndexedDBService.instance.addData({id:buildingId,houseInfo:res},IndexedDBService.BuildingSchemeDataTable);
         
         
            await IndexedDBService.instance.addData({id:lastBuildingId,buildingId: buildingId,
                roomInfo:{room_name:c_room_name,room_id:c_room_id,roomNum:c_room_num}},IndexedDBService.DefaultTable);

            container.fromXmlSchemeData(res.schemeXmlJson,true, SchemeSourceType.LayoutLibrary);
    
            // console.log(container);
            // container.updateRoomsFromEntities();
            setCurrentBuildingId(buildingId);
            let target_rooms = container._rooms.filter((room)=>room.roomname.includes(c_room_name));
            c_room_num = target_rooms.length;
            let target_room = target_rooms[c_room_id] || target_rooms[0] || null;
            if(target_room)
            {
                if(localStorage)
                {
                    localStorage.setItem("layout_ai_training_current_room_data", JSON.stringify(target_room.exportExtRoomData()));
                }
            }
            if(!options?.no_auto_layout)
            {
                LayoutAI_App.DispatchEvent(TrainingTestingEvents.TestingDatasetListOnRoomLoaded,true);
            }

            LayoutAI_App.instance.update();
        }
        setCurrentRoomInfo({room_name:c_room_name,room_id:c_room_id,roomNum:c_room_num});


    }

    const setQueryingLog = (str:string)=>{
        if(queryInfoLogRef && queryInfoLogRef.current)
        {
            (queryInfoLogRef.current as HTMLElement).innerHTML = str;
        }
    }
    const extendBuildingList = async ()=>{

        if(isQuerying) return;
        isQuerying = true;
        let list:I_HouseSchemeInfo[] = [...currentBuildingList];

        let src_num = list.length;
        let prev_num = src_num;

        let t_time = queryNumForSingleCity / 20;
        let keywords = ["","保","绿","新","恒大","城投","万科","碧桂","区","花"]
        for(let city of targetCities)
        {
            let oneCityCount = 0;
            for(let i=1; i <=t_time;i++)
            {
                if(list.length > 5000) break;
                setQueryingLog(`   第${i}次请求 `+city.name+", 数量"+oneCityCount);
                for(let pageId=1; pageId <= 4; pageId++)
                {
                    let res = await BuildingService.search(keywords[i-1]||"",city.code,20, pageId);
                    let records:any[] = res?.records || [];
                    records.forEach((data)=>{
                        if(list.find(t_data=>t_data.buildingRoomId===data.id)) return;
                        data.buildingRoomId = data.id;
                        list.push(data);
                        oneCityCount++;
                    });
                    setQueryingLog(`   第${i}次请求 `+city.name+", 数量"+oneCityCount);
                    if(list.length > prev_num)
                    {
                        await Sleep(200);
                        await IndexedDBService.instance.addTestingDataset({id:currentDatasetId, name:getDatasetNameById(), buildingList: list});
                        prev_num = list.length;
                        setCurrentBuildingList([...list]);
    
                    }
                    if(oneCityCount >= queryNumForSingleCity)
                    {
                        break;
                    }
                }
                if(oneCityCount >= queryNumForSingleCity)
                {
                    break;
                }
            }
        }
        setQueryingLog("");
        await IndexedDBService.instance.addTestingDataset({id:currentDatasetId, name:getDatasetNameById(), buildingList: list});
        setCurrentBuildingList([...list]);
        confirm("新增户型"+(list.length - src_num)+"个");
        isQuerying = false;

    }

    const checkAndLoadingBuildingData = async ()=> {
        let count = 0;
        for(let data of currentBuildingList)
        {
            setQueryingLog(`缓存`+count+"/"+currentBuildingList.length+": "+data.buildingRoomId+" "+data.buildingName);

            await onClickBuildingId(data.buildingRoomId,{no_auto_layout:true});
            // await Sleep(200);
            count++;
        }
        setQueryingLog("");
    }

    const exportBuildingDataJson = async ()=>{
        let list = await IndexedDBService.instance.getAll(IndexedDBService.BuildingSchemeDataTable);
        
        saveFileAs(JSON.stringify(list),"buildingData.json","text/json");
    }
    const importBuildingDataJson = async ()=>{
        let data = JSON.parse( (await openFileInput(".json","Text") as any).content);
        if(data instanceof Array)
        {
            let list = data as any[];
            for(let t_data of list)
            {
                if(t_data['houseInfo'] && t_data['id'])
                {
                    IndexedDBService.instance.addData(t_data,IndexedDBService.BuildingSchemeDataTable);
                }
            }
        }

    }

    const createNewDataset = async ()=>{
        let date_id = formatCurrentTime();
        let dataset_name = prompt("数据集名称", date_id);
        if(!dataset_name) return;
       
        if(datasetNameInputRef && datasetNameInputRef.current)
        {
            datasetNameInputRef.current.value = dataset_name;
        }
        await IndexedDBService.instance.addTestingDataset({id:date_id,name:dataset_name});

        await queryDatasetList();

        await onLoadDatasetById(date_id);
    }
    const removeCurrentDataset = async ()=>{
        if(currentDatasetId==="Default")
        {
            alert("默认数据集无法删除");
            return;
        }
        if(!confirm("确认删除数据集"+currentDatasetId+" "+getDatasetNameById()+"?"))
        {
            return;
        }
        await IndexedDBService.instance.removeTestingDataset(currentDatasetId);

        let current_id = await queryDatasetList();

        await onLoadDatasetById(current_id);




    }


    const setAndSaveCurrentDatasetId = async (date_id:string)=>{
        setCurrentDatasetId(date_id);
        await IndexedDBService.instance.addData({id:lastDatasetId, dataset_id: date_id},IndexedDBService.DefaultTable);
    }
    const onLoadDatasetById = async (dataset_id:string)=>{
        let res = (await IndexedDBService.instance.getTestingDatasetById(dataset_id))?.buildingList || [];
        if(res)
        {
            // console.log(currentDatasetId,res);
            setCurrentBuildingList(res);

            setAndSaveCurrentDatasetId(dataset_id);
        }
    }

    const queryDatasetList = async ()=>{
        let datasetList : {id:string,name:string}[] = await IndexedDBService.instance.getTestingDatasetList();
        setDatasetNameList(datasetList);
        let currentId = currentDatasetId;
        if(!datasetList.find(data=>data.id == currentDatasetId) && datasetList.length > 0)
        {
            let currentId = datasetList[0].id;
            setCurrentDatasetId(currentId);
        }
        return currentId;
    }
    const clearBuildingList = async ()=>{
        let id = prompt("请输出初始id:","0");
        let t_id = parseInt(id);

        if(isNaN(t_id)) return;
        if(t_id >= currentBuildingList.length) return;
        if(t_id < 0)
        {
            t_id = 0;
        }
        let list = [...currentBuildingList];
        list.splice(t_id,currentBuildingList.length);
        // list.length = 0;
        await IndexedDBService.instance.addTestingDataset({id:currentDatasetId, name: getDatasetNameById(), buildingList: list});
        setCurrentBuildingList(list);
    }
    const addBuildingId = async ()=>{
        let buildingId = window.prompt("请输入户型ID","");
        if(buildingId && buildingId.length > 0)
        {
            if(currentBuildingList.find(data=>data.buildingRoomId===buildingId))
            {
                onClickBuildingId(buildingId);
            }
            else{
        
                await onClickBuildingId(buildingId);

                let res : I_HouseSchemeTestingInfo = ((await IndexedDBService.instance.getDataById(buildingId,IndexedDBService.BuildingSchemeDataTable)))?.houseInfo;      
                if(res)
                {
                    let buildingList = [...currentBuildingList];
                    let data :I_HouseSchemeTestingInfo= {...res};
                    if(data.schemeXmlJson) {
                        delete data.schemeXmlJson;
                    }
                    buildingList.push(data);
                    await IndexedDBService.instance.addTestingDataset({id:currentDatasetId, name:getDatasetNameById(),buildingList: buildingList});

                    setCurrentBuildingList(buildingList);

                    alert("添加成功");
                }
                else{
                    alert("添加户型失败");
                }


            }
        }
    }

    useEffect(()=>{
        LayoutAI_App.on_M(TrainingTestingSignals.ShowTestingDatasetListPanel,object_id,(t:boolean)=>{
            g_IsDisplay = !g_IsDisplay;

            
            setIsDisplay(g_IsDisplay);

            const loadBuildingByLastBuildingId = async ()=>{
                let data =(await IndexedDBService.instance.getDataById(lastBuildingId,IndexedDBService.BuildingSchemeDataTable)) || null;
                if(data)
                {
                    let buildingId = data.buildingId;
                    if(buildingId)
                    {
                        setCurrentBuildingId(buildingId);
                    }
                    let roomInfo = data.roomInfo;
                    if(roomInfo)
                    {
                        setCurrentRoomInfo(roomInfo);
                    }
                }
            }
            
            const loadDatasetId = async ()=>{
                await queryDatasetList();

                let dataset_id = (await IndexedDBService.instance.getDataById(lastDatasetId,IndexedDBService.DefaultTable))?.dataset_id || null;
                dataset_id = dataset_id || currentDatasetId;

                await onLoadDatasetById(dataset_id);

            }
            loadDatasetId();
            loadBuildingByLastBuildingId();

        });

    },[]);

    let houseIds = [];
    for(let i=0; i < (currentRoomInfo.roomNum||1);i++)
    {
        houseIds.push(i);
    }
    let pageSize = 1000;
    let pageNum = Math.floor(currentBuildingList.length / pageSize)+1;
    let pageId = currentPageId;
    if(pageId >= pageNum) pageId = pageNum-1;

    let buildingList = [];
    let pageList = [];
    for(let i=0;i < pageSize; i++)
    {
        let data = currentBuildingList[pageId * pageSize + i];
        if(data)
        {
            buildingList.push(data);
        }
    }
    for(let i=0; i < pageNum; i++)
    {
        pageList.push(i);
    }


    
    return (
        <>
            <div id="quickRoomTestingPanel" className={styles.leftPanel} style={{ display: isDisplay ? "block" : "none", position: "absolute", left: "0", top: "0", zIndex: 10001, paddingLeft:"0"}}>
                <div className='row_ele' style={{height:"90px",paddingTop:"10px",lineHeight:"25px"}}>
                    <select name='dataset_list' onChange={(ev)=>{
                        onLoadDatasetById(ev.target.value);
                    }} defaultValue={currentDatasetId}>
                        {datasetNameList.map((datasetNameData,index)=><option key={"dataset_"+index} value={datasetNameData.id}>{datasetNameData.name}</option>)}
                    </select>
                    <Button onClick={()=>{setIsShowAddHouseDataDialog(true)}}>编辑数据集</Button>
                    <Button onClick={()=>{                  
                         LayoutAI_App.emit_M(TrainingTestingSignals.ShowTestingDatasetListPanel,false); 
                    }}>关闭</Button>

                    <br/>
                    <select name="pageSelect" onChange={(ev)=>{
                        setCurrentPageId(~~ev.target.value);
                    }}>
                        {pageList.map((id)=><option key={"pageSelect"+id} value={id}>
                            第{id+1}页
                        </option>)}
                    </select>
                    <select name='roomName' value={currentRoomInfo.room_name} onChange={async (ev)=>{

                        onClickBuildingId(currentBuildingId, {room_name:ev.target.value});
                    }}>
                        {RoomNames.map((room_name,index)=><option key={"room_name"+index} value={room_name} >{room_name}</option>)}
                    </select>
                    
                    <select name="roomId" value={currentRoomInfo.room_id}  onChange={async (ev)=>{
                            onClickBuildingId(currentBuildingId,{id:~~ev.target.value});
                        }}>
                            {houseIds.map((id,index)=><option  key={"room_id_"+index} value={id}>{id}:{currentRoomInfo.roomNum-1}</option>)}
                        </select>
                </div>
                <div style={{overflow:"auto", height: "calc(100vh - 150px)"}}>
                    {buildingList.map((item,index)=>
                        <div key={"buildId_"+index} className={'row_ele '+(item.buildingRoomId===currentBuildingId?"checked":"")} onClick={()=>onClickBuildingId(item.buildingRoomId)}>
                            {currentPageId * pageSize + index+1}:{item.buildingRoomId} &nbsp;&nbsp; {item.cityName}
                             <br /> &nbsp;&nbsp;&nbsp;&nbsp;{item.buildingName || ""} &nbsp;&nbsp; {item.area?(item.area+"m²"):""}
                        </div>
                    )}
                </div>

            </div>
            {isShowAddDataDialog && <div className='DistrictPopUp' style={{position:"fixed",zIndex:10001,width:"900px",top:"0px",left:"300px"}}>
                <div className='closeBtn' style={{position:"absolute", top:"40px",right:"30px",fontSize:"16px",cursor:"pointer",zIndex:10001}} onClick={()=>setIsShowAddHouseDataDialog(false)}>X</div>
                <div className={styles.dialogInputs} >
                     <div className='input_row'>
                        <span> 测试集名称: </span>
                        <input ref={datasetNameInputRef} defaultValue={getDatasetNameById()} onChange={(ev)=>{
                            setDatasetNameById(ev.target.value);
                        }}></input>
                       <span> 当前总数 </span><span> {currentBuildingList.length}</span>
                       &nbsp;&nbsp;&nbsp;&nbsp;
                       <Button onClick={()=>{createNewDataset()}}> 新建数据集 </Button>
                       <Button onClick={()=>{removeCurrentDataset()}}>删除数据集</Button>

                        <span ref={queryInfoLogRef}></span>
                    </div>

                    <div className='input_row'>
                        <Button onClick={addBuildingId}>手动添加</Button>

                        <Button onClick={()=>{
                            if(confirm(("开始执行自动添加?")))
                            {
                                extendBuildingList();
                            }

                        }}> 自动添加 </Button>
                       

                        <Button onClick={()=>{clearBuildingList()}}> 清空列表 </Button>

                        <Button onClick={()=>{
                            IndexedDBService.instance.exportAllTestingDataset();
                        }}> 导出数据集 </Button>

                        <Button onClick={async ()=>{
                            let data = (await openFileInput(".json","Text") as any).content;

                            try {
                                await  IndexedDBService.instance.importTestingDataset(JSON.parse(data));
                                await onLoadDatasetById(currentDatasetId);

                            } catch (error) {
                                
                            }
                        }}> 导入数据集 </Button>

                        <Button onClick={()=>{checkAndLoadingBuildingData()}}> 缓存数据 </Button>

                        <Button onClick={()=>{exportBuildingDataJson()}}> 导出户型缓存 </Button>
                        <Button onClick={()=>{importBuildingDataJson()}}> 导入户型缓存 </Button>

                    </div>

                    <div className='input_row'>
                        <span> 目标城市 </span>
                        <span>{targetCities.map((city,index)=><span key={"city_code_name_"+index} onClick={()=>{
                                let cities = [...targetCities].filter(c=>c.code !== city.code);
                                setTargetCities(cities);
                        }}> {city.name} </span>)}</span>  &nbsp;&nbsp;&nbsp;&nbsp;
                        <input type='number' step={20}  min={20} max={500} defaultValue={queryNumForSingleCity} onChange={(ev)=>{
                            queryNumForSingleCity = ~~ev.target.value;
                        }}></input>请求/每城市
                        <span style={{float:"right",marginRight:20,color:"#07f"}} onClick={()=>{
                            setIsShowDistricPopup(!isShowDistricPopup);
                        }}>{isShowDistricPopup?("收起列表"):("展开列表")}</span>
                    </div>
                </div>
       
                <DistrictPopup is_visible={isShowAddDataDialog && isShowDistricPopup} onSelected={(city)=>{
                    let cities = [...targetCities];
                    let hasCity = cities.find(c=>c.code === city.code);
                    if(!hasCity) {
                        cities.push(city);
                    }
                    else{
                        return;
                     //   cities = cities.splice(cities.indexOf(hasCity),1);
                    }
                    setTargetCities(cities);

                }}></DistrictPopup>
            </div>}


        </>

    )
}


export default observer(TestingDatasetListPanel);