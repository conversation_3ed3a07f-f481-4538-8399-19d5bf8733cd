import { magiccubeDpAiWebRequest, openApiRequest } from '@/utils';
import { message } from '@svg/antd';
// /**
//  * @description 可布置样板间列表
//  */
// export async function getKgSchemeList(params: any) {
//   const res = await magiccubeDpAiWebRequest({
//     timeout: 60000, // 设置超时时间为 60 秒
//     method: 'post',
//     url: '/dp-ai-web/getKgSchemeList',
//     data: {
//         ...params,
//       }
//   });
//   return res.success && res.data ? res.data : null;
// }
/**
 * @description 可布置样板间列表
 */
export async function getKgSchemeList(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/ruleseedscheme/pageRelatedScheme`,
    data: {
        ...params,
    },
    timeout: 60000,
  }).catch((e:any):any=>{
    return null;
  });
;
  return res?.success && res?.result ? res.result : null;
}

/**
 * @description 产生硬装素材布局数据
 */
export async function generateHardMaterials(params: any) {
  params.entry = 20;
  params.count = 0;
  params.schemeId = "0";
  params.seed = {};

  let arrangeResult = await spaceArrange(params);
  if (arrangeResult["ceiling"] != null) {
    for(let i = 0; i < arrangeResult["ceiling"].length; i++) {
      arrangeResult["ceiling"][i].materialID = arrangeResult["ceiling"][i].materialId;
    }
  }
  return arrangeResult;
}

/**
 * @description 可布置样板间列表
 */
 export async function spaceArrange(params: any) {
  const TIMEOUT_DURATION = 120000; // 设置超时时间，单位为毫秒

  const timeoutPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error('请求超时'));
    }, TIMEOUT_DURATION);
  });

  try {
    const res = await Promise.race([
      magiccubeDpAiWebRequest({
        method: 'post',
        url: `/dp-ai-web/spaceArrange`,
        data: {
          ...params,
        },
        timeout: 120000,
      }),
      timeoutPromise,
    ]);

    if (res.success && res.data) {
      return res;
    } else {
      message.error('接口请求失败');
      return res;
    }
  } catch (error) {
    message.error('接口请求超时');
    return null;
  }
}
/**
 * @description 户型检测
 */
export async function checkHouse(params: any) {
  const res = await magiccubeDpAiWebRequest({
    method: 'post',
    url: `/dp-ai-web/checkHouse`,
    data: {
        ...params,
      }
  }).catch((e:any):any=>{
    return null;
  });

  return  res?.success && res?.data ? res.data : null;
}
/**
 * @description 获取方案详情
 */
export async function getScheme(params: any) {
  const res = await magiccubeDpAiWebRequest({
    method: 'post',
    url: `/dp-ai-web/schemeMaterialDetail`,
    data: {
        ...params,
      }
  }).catch((e:any):any=>{
    return null;
  });
  return res?.success && res.data ? res.data : null;
}
/**
 * @description 获取方案详情
 */
export async function getSpaceInfo(params: any) {
  const res = await magiccubeDpAiWebRequest({
    method: 'post',
    url: `/dp-ai-web/getSpaceInfo`,
    data: {
        ...params,
      }
  }).catch((e:any):any=>{
    return null;
  });
  return res?.success && res.data ? res.data : null;
}
/**
 * @description 获取企业布置风格
 */
export async function getStyles() {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/vrrule/getAllStyles`,
  }).catch((e:any):any=>{
    return null;
  });
  return res?.success && res.result ? res.result : null;
}
/**
 * @description 获取卧室具体的空间名称
 */
export async function getBedrooms(params: any) {
  const res = await magiccubeDpAiWebRequest({
    method: 'post',
    url: `/dp-ai-web/renameBedrooms`,
    data: {
        ...params,
      }
  }).catch((e:any):any=>{
    return null;
  });
  return res?.success && res.data ? res.data : null;
}
/**
 * @description 获取套系的俯视图
 */
export async function getPictureView(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/KgMaterial/materialViewImages`,
    data: {
        ...params,
      }
  }).catch((e:any)=>{
    console.error(e);
  });
  if (res?.success && res.result) {
    return res.result;
  } else {
    // message.error('俯视图接口请求失败');
    return null;
  }
}



/**
 * @description 创建布局方案附件
 */
export async function createLayoutSchemeAttachment(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/layoutSchemeFiles/insert`,
    data: {
        ...params,
      }
  }).catch((e:any)=>{
    console.error(e);
  });
  if (res?.success && res.result) {
    return res.result;
  } else {
    message.error('创建布局方案附件接口请求失败');
    return null;
  }
}


/**
 * @description 小程序写入线索轨迹
 */
export async function insertClueTrack(params: any) {
  const res = await openApiRequest({
    method: 'post',
    url: `/api/njvr/aiwxClueTrack/insert`,
    data: {
        ...params,
      }
  }).catch((e:any)=>{
    console.error(e);
  });
  if (res?.success && res.result) {
    return res.result;
  } else {
    message.error('小程序写入线索轨迹接口请求失败');
    return null;
  }
}

/**
 * @description 微信分享 jsSDK 签名
 */
export async function getSingWxJs(url: string) {
  const res = await openApiRequest({
    method: 'post',
    url: '/api/njvr/layoutWardrobeFavorite/singWxJs',
    data: {
      url: url
    }
  }).catch((e:any)=>{
    console.error(e);
  });
  if (res?.success && res.result) {
    return res.result;
  } else {
    message.error('微信分享 jsSDK 签名接口请求失败');
    return null;    
  }
}
