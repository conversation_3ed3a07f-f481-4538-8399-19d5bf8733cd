
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      width: 600px;
      height: 650px;
      background:#fff;
      position:fixed;
      overflow: hidden;
      z-index:10;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 0 20px;
      .iconicon
      {
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .houseSearch {
        border-radius: 8px;
        width: 80%;
        max-width:520px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #e5e7eb;
        margin: 40px auto;
        display: flex;
        .search-btn {
          width: 60px;
          background: #147FFA;
          border-radius: 0 8px 8px 0;
          text-align: center;
          font-family: PingFangSC-Semibold;
          font-size: 15px;
          color: #fff;
          font-weight: 550;
          cursor: pointer;
        }
        .search-input {
          width: calc(100% - 120px);
          padding-left: 22px;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #2e3238 !important;
          box-sizing: border-box;
          border: none;
          background: none;
          box-shadow: none;
        }
        .candidate_buiding_names {
          width: calc(100vw - 200px);
          background:#fff;
          left:70px;
          height : 250px;
          position:absolute;
          top:60px;
          padding: 20px 0 10px;
          z-index: 2;
          box-shadow: 0 3px 12px rgba(0, 0, 0, .2);
          border-radius: 0 0 2px 2px;
        }
        .select-operate {
          display: flex;
          justify-content: space-between;
          line-height: 22px;
          height: 30px;
          position: relative;
          margin-bottom: 4px;
          padding: 0 20px;
          font-size: 14px;
          color: #747b81;
        }
        .select_item {
          padding: 0 20px;
          height: 38px;
          line-height: 38px;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #2e3238;
          font-weight: 600;
          display: block;
          cursor: pointer;
          &:hover {
            background:rgba(0,0,0,.02)
          }
          .select-item_left {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #2e3238;
            font-weight: 400;
          }
          .select-item_left {
            display: inline-block;
            float:left;
          }
          .select-item_right {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #2e3238;
            font-weight: 400;
            display: inline-block;
            float:right;
          }
        }
      }
      .style_trigger__1c72413f {
        width: 60px;
        cursor: pointer;
        text-align: center;
        display: flex;
        justify-content: center;
      }
      .style_city_label__1c72413f {
        font-size: 15px;
        font-weight: 550;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        max-width: 108px;
      }
      .line {
        width: 1px;
        height: 20px;
        left: 94px;
        top: 10px;
      }
      .anticon {
        display: inline-block;
        color: inherit;
        font-style: normal;
        line-height: 40px;
        text-align: center;
        text-transform: none;
        vertical-align: -.125em;
        text-rendering: optimizelegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      .buildingContent {
         display:flex;
         flex-flow:row wrap;
         /* margin:auto; */
         height: calc(100% - 200px);
         overflow-y:auto;
         justify-content: space-between;
      }

      .house-card {
          width: 175px;
          height: 278px;
          padding: 8px 8px 16px;
          border-radius: 8px;
          box-shadow: 0 2px 24px rgba(0, 0, 0, .1);
          transition: all .3s;
          text-align:center;
          cursor:pointer;
          &:hover {
            box-shadow: 0 2px 24px rgba(0, 0, 0, .5);

          }
          img {
            width:100%;
          }
          .house-card__info {
            font-size: 14px;
            line-height: 22px;
            text-align:left;
            .name {
              color: #2e3238;
              line-height: 24px;
              font-weight: 600;
              margin-top:5px;
            }
            .detail-wrap {
              margin-top: 2px;
              display: block;
            }
            .houseType{
              color: #2e3238;
              margin-top:2px;
            }
            .location {
              color: #a5adb6;
              margin-top:2px;

            }
          }
      }

    }
    `,
    row : css`
      width:100%;
      min-height:70px;
    `,
    hx_logo:css`
      width: 140px;
      height: 46px;
      position:absolute;
      top:50px;
      left : calc(50% - 70px);
      background: url(data:image/png;base64,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);
      background-size: cover;
    `,
    districts:css`
      position:absolute;
      width: calc(100% - 60px);
      height: 400px;
      top:160px;
      left:40px;
      text-align:left;
      overflow-y:auto;
      background:#fff;
      border:1px solid #eee;
      box-shadow: 0 2px 24px rgba(0, 0, 0, .5);

      .row {
          width:100%;
          line-heght:20px;
      }
      .provice {
        float:left;
        margin-right:20px;
        margin-left:10px;
        padding:2px;
        cursor:pointer;
      }
      .cities {
        float:left;
        width:calc(100% - 90px);
      }
      .city {
        float:left;
        margin-right:10px;
        padding:2px;
        cursor:pointer;
        &:hover {
          background:#aaaaaa33;
        }
      }
    `
  }
});
