import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  // color: ${token.colorPrimary};
  return {
    root: css`
      position: absolute;
      left: 0;
      top: 20px;
      z-index: 99;
      height: 100%;
      background-color: #fff;
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
      .swj-businessComponent-propsPanelContainer
      {
        position: relative;
        right: 0 !important;
        left: auto !important;
      }
    `,
  }
});
