import { checkIsMobile } from '@/config';
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    root: css`
      width:100%;
      height:calc(var(--vh, 1vh) * 100);
      position: relative;
      .custom-keyboard {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #ccc;
        padding: 10px;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
      }

      .keypad {
          display: flex;
          flex-wrap: wrap;
      }

      .keypad button {
          flex: 1 0 30%; /* 控制按钮大小 */
          margin: 5px;
          padding: 15px;
          font-size: 18px;
          cursor: pointer;
      }
      
    `,
    content:css`
      position: fixed;
      top: 0;
      left: 0; 
      right: 0;
      bottom: 0;
      overflow: hidden;
      input {
        z-index : 3;
      }
    `,
    seleceView:css`
      position: absolute;
      top: 52px;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 12px 20px 20px 20px;
      background-color: #fff;
      display: flex;
      gap: 12px;
      flex-shrink: 0;
      height: calc(100% - 52px);
      width: 100%;
      box-sizing: border-box;
    `,
    canvas3d:css`
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 4px 4px 12px 4px;
      border-radius: 12px;
      align-items: center;
      z-index: 1;
      background-color: #fff;
      overflow: hidden;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      position: relative;
      .scene3d_container{
        width: calc(100% - 8px);
        height: calc(100% - 48px);
        border: none;
        position: relative;
        .view_select_box{
          position: absolute;
          bottom: 18px;
          left: 50%;
          max-width: 70%;
          min-width: 300px;
          transform: translateX(-50%);
          overflow: hidden;
          z-index: 100;
          border-radius: 8px;
          backdrop-filter: blur(10px);
        }
      }
      .canvas3d_btns{
        display: flex;
        gap: 12px;
        align-items: center;
        .canvas3d_btn{
          display: flex;
          padding: 6px 16px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          border-radius: 20px;
          background: linear-gradient(91deg, #BA63F0 -0.97%, #5C42FB 100%);
          color: #fff;
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
        }
      }
      .house_match_container{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
      }
    `,
    canvas_pannel: css`
        position: relative;
        overflow: hidden;
        background-color: #eaeaea;
        background-image:
         -webkit-linear-gradient(180deg, #e2e2e2 1px, transparent 1px) ,
          -webkit-linear-gradient(90deg, #e2e2e2 1px, transparent 1px);
        background-size: 50px 50px;
        background-position: calc(50% + 25px) 0%;
        border-radius: 12px;
        /* &.left_panel_layout {
          height : calc(100%);
        } */
        .canvas {
          position : absolute;
          left: 0px;
          top: 0px;
          width: 100%;
          height: 100%;
          touch-action: none;
          &.canvas_drawing {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;
          }
          &.canvas_moving {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png), auto;
          }
          &.canvas_leftmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;
          }
          &.canvas_rightmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;
          }
          &.canvas_acrossmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;
          }
          &.canvas_verticalmove {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;
          }
          &.canvas_text {
            cursor : text;
          }
          &.canvas_pointer {
            cursor : pointer;
          }
          &.canvas_splitWall {
            cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 0 0,auto;
          }
        }
        .layoutBtn {
          position: absolute;
          top: 12px;
          left: 12px;
          z-index: 99;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 24px;
          padding: 2px 11px;
          border-radius: 4px;
          background: rgba(0, 0, 0, 0.50);

          color: #FFF;
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 166.7%; /* 20.004px */
        }
        .canvas_btns {
          width: auto;
          margin: 0 auto;
          position: fixed;
          display: flex;
          justify-content: center;
          bottom: 35px;
          z-index:10;
          left: 50%;
          transform: translateX(-50%);
          .btn {
            ${checkIsMobile() ?
            `
              width: 120px;
              height: 36px;
              font-size: 14px;
            `
            :
            `
              width: 200px;
              height: 48px;
              font-size: 16px;
            `
          }
            border-radius: 6px;
            border: none;

            font-weight: 600;
            margin-right: 10px;
            margin-left: 10px;
          }
          .design_btn {
            background: #e6e6e6;
            margin-right: 20px;
          }
          @media screen and (max-height: 600px){
            bottom: 50px !important;
          }
        }
    `,
    navigation : css`
      position:absolute;
      top:0px;
      width:100%;
      height:50px;
      border-bottom:1px solid #eee;
      background:#fff;
      z-index:5;
    `,
    backBtn : css`
      position:absolute;
      z-index:2;
      padding-left:2px;
      font-size:14px;
      line-height:50px;
      color:#333;
      float:left;
    `,
    forwardBtn : css`
      position:absolute;
      right:0;
      z-index:2;
      padding-right:10px;
      font-size:14px;
      line-height:50px;
      color:#333;
    `,
    schemeNameSpan:css`
      width:100%;
      font-size:16px;
      line-height:50px;
      text-align:center;
    `,
    overlay: css`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */
      z-index: 999; /* 确保蒙层在其他元素之上 */
    `,
    focusIcon: css`
      position: fixed;
      top: 68px;
      right: 12px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      box-shadow: 0px 6px 20px 0px #00000014;
      font-size: 30px;
      // 竖屏样式

      @media screen and (max-width: 450px) { // 手机宽度
        width: 28px !important;
        height: 28px !important;
        font-size: 16px !important;
      }     
      
      @media screen and (orientation: portrait) {
        width: 48px;
        height: 48px;
      }

      // 横屏样式
      @media screen and (orientation: landscape) {
        width: 40px;
        height: 40px;
        font-size: 25px;
      }
    `,
    multiSchemeIcon: css`
      position: fixed;
      top: 120px; /* Adjust this value to position it below the focusIcon */
      right: 12px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      box-shadow: 0px 6px 20px 0px #00000014;
      font-size: 30px;

      @media screen and (max-width: 450px) {
        width: 28px !important;
        height: 28px !important;
        font-size: 16px !important;
      }     
      
      @media screen and (orientation: portrait) {
        width: 48px;
        height: 48px;
      }

      @media screen and (orientation: landscape) {
        width: 40px;
        height: 40px;
        font-size: 25px;
      }
    `,
    RoomAreaBtns: css`
      position: absolute;
      top: 0px;
      left: 0;
      width: 100%;
      height: 45px;
      z-index: 999;
      background-color: #fff;
    `,
    aiDraw: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
    `,
    mobile_atlas_container:css`
      padding: 20px;
      position:absolute;
      left: 0;
      top: 0;
      width:100%;
      height:100%;
      z-index:-1;
      background: #f6f7f9;
    `,
    selectViewContainer:css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99;
    `,
    myCase: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
      background: rgba(0, 0, 0, 0.50);
    `,
    houseDetail: css`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
      background: rgba(0, 0, 0, 0.5);
    `,
  }

});
