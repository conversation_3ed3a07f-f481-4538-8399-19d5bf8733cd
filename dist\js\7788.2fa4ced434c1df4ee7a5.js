"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[7788],{16238:function(n,t,e){e.d(t,{A:function(){return _}});var r=e(13274),o=e(41594);function i(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=i(["\n      position: fixed;\n      bottom: 16px;\n      right: 260px;\n      width: auto;\n      height: 36px;\n      background-color: #fff;\n      border-radius: 4px;\n      box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.10), 0px 2px 8px 0px rgba(0, 0, 0, 0.10);\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    "]);return a=function(){return n},n}function c(){var n=i(["\n      font-size: 12px;\n      font-weight: 600;\n      color: #959598;\n      padding: 0 6px;\n      margin: 0 4px;\n      cursor: pointer;\n      height: 26px;\n      border-radius: 4px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      .icon{\n        width: 20px;\n        height: 20px;\n      }\n    "]);return c=function(){return n},n}function u(){var n=i(["\n      color: #147FFA !important;\n      background: #E6F1FF;\n    "]);return u=function(){return n},n}var s=(0,e(8268).rU)(function(n){n.token;var t=n.css;return{bar:t(a()),title:t(c()),active:t(u())}}),l=e(9003),f=e(15696),p=e(69802),d=e(27347),h=e(32184),x=e(88934),g=e(10371),m=e(67869);function v(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function b(n,t,e,r,o,i,a){try{var c=n[i](a),u=c.value}catch(n){return void e(n)}c.done?t(u):Promise.resolve(u).then(r,o)}function w(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,o,i=[],a=!0,c=!1;try{for(e=e.call(n);!(a=(r=e.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(n){c=!0,o=n}finally{try{a||null==e.return||e.return()}finally{if(c)throw o}}return i}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return v(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return v(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(n,t){var e,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=c(0),a.throw=c(1),a.return=c(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(c){return function(u){return function(c){if(e)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(e=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,r=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==c[0]&&2!==c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(n,i)}catch(n){c=[6,n],r=0}finally{e=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var _=(0,f.observer)(function(){var n=s().styles,t=(0,l.P)(),e=(0,p.B)().t,i=w((0,o.useState)("outline"),2),a=i[0],c=i[1],u=function(){return(n=function(){var n,e,r,o,i,a,c;return y(this,function(u){return d.nb.DispatchEvent(d.n0.Match3dPreviewMaterials,null),n=d.nb.instance.layout_container,d.nb.emit(x.U.Show3DViewer,1),(e=d.nb.instance.scene3D)&&e.setCemeraMode(g.I5.FirstPerson),t.homeStore.setPreview3D(!0),r=n._room_entities,(o=r.reduce(function(n,t){return n?t._area>n._area?t:n:t},null))?(e.setCenter((null==o||null===(i=o._main_rect)||void 0===i?void 0:i.rect_center)||new m.Pq0(0,0,0)),e.update()):e.setCenter((null===(c=r[0])||void 0===c||null===(a=c._main_rect)||void 0===a?void 0:a.rect_center)||new m.Pq0(0,0,0)),t.homeStore.setViewMode("3D_FirstPerson"),e&&e.startRender(),[2]})},function(){var t=this,e=arguments;return new Promise(function(r,o){var i=n.apply(t,e);function a(n){b(i,r,o,a,c,"next",n)}function c(n){b(i,r,o,a,c,"throw",n)}a(void 0)})})();var n};(0,o.useEffect)(function(){},[]);var f=d.nb.instance;return(0,r.jsxs)("div",{className:n.bar,style:{zIndex:window.location.pathname.includes("share")&&999},children:[(0,r.jsxs)("div",{className:"".concat(n.title," ").concat("outline"===a?n.active:""),onClick:function(){f.layout_container.drawing_figure_mode=h.qB.Outline,t.homeStore.setPreview3D(!1),t.homeStore.setViewMode("2D"),d.nb.emit(x.U.Show3DViewer,0),f.update(),c("outline")},children:[(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,r.jsx)("use",{xlinkHref:"".concat("outline"===a?"#iconShowoutline_Sel":"#iconShowoutline_Nor")})}),e("轮廓模式")]}),(0,r.jsxs)("div",{className:"".concat(n.title," ").concat("material"===a?n.active:""),onClick:function(){f.layout_container.drawing_figure_mode=h.qB.Texture,f.update(),t.homeStore.setPreview3D(!1),t.homeStore.setViewMode("2D"),d.nb.emit(x.U.Show3DViewer,0),c("material")},children:[(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,r.jsx)("use",{xlinkHref:"".concat("material"===a?"#iconShowmaterial_Sel":"#iconShowmaterial_Nor")})}),e("材质模式")]}),window.location.pathname.includes("share")&&(0,r.jsxs)("div",{className:"".concat(n.title," ").concat("3d"===a?n.active:""),onClick:function(){u(),c("3d")},children:[(0,r.jsx)("svg",{className:"icon","aria-hidden":"true",children:(0,r.jsx)("use",{xlinkHref:"".concat("#icona-Group402")})}),e("3D预览")]})]})})},41185:function(n,t,e){var r=e(23825),o=e(8268);function i(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}function a(){var n=i(["\n      .swj-top-menu-button-item {\n        width: 80px;\n        text-align: center;\n      }\n      .ant-modal-content\n      {\n        padding: 10px 10px !important;\n      }\n      /* @keyframes slideIn {\n        from {\n          transform: translateY(-48px);\n        }\n        to {\n          transform: translateY(0);\n        }\n      }\n      @keyframes slideIn1 {\n        from {\n          transform: translateX(-340px);\n        }\n        to {\n          transform: translateX(0);\n        }\n      }\n      .svg-friga1\n      {\n        animation: slideIn .8s forwards;\n      }\n      .swj-left-menu-bar-container{\n        animation: slideIn1 .8s forwards;\n      } */\n    "]);return a=function(){return n},n}function c(){var n=i(["\n        position:absolute;\n        -webkit-transform:rotate(90deg);\n        -webkit-transform-origin:0% 0%;/*1.重置旋转中心*/\n        \n        -moz-transform: rotate(90deg);\n        -moz-transform-origin:0% 0%;\n        \n        -ms-transform: rotate(90deg);\n        -ms-transform-origin:0% 0%;\n        \n        transform: rotate(90deg);\n        transform-origin:0% 0%;\n        \n        width: 100vh;/*2.利用 vh 重置 ‘宽度’ */\n        height: 100vw;/* 3.利用 vw 重置 ‘高度’ */\n        top: 0;\n        left: 100vw;\n\n    "]);return c=function(){return n},n}function u(){var n=i(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: calc(100% - 48px);\n      background-color: rgba(255, 255, 255);\n      z-index: 9999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    "]);return u=function(){return n},n}function s(){var n=i(["\n      position: absolute;\n      top: 48px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return s=function(){return n},n}function l(){var n=i(["\n      position: fixed;\n      top: -200px;\n      left: 700px;\n      width: 100vw;\n      height: 500px;\n      background: #FFF;\n      z-index: 9999999;\n    "]);return l=function(){return n},n}function f(){var n=i(["\n      position: fixed;\n      top: 48px;\n      left: 0;\n      width: 0px;\n      bottom: 0;\n      background-color: #fff;\n      z-index:1;\n      .iconfont\n      {\n        width: 20px !important;\n        height: 24px !important;\n      } \n    "]);return f=function(){return n},n}function p(){var n=i(["\n      position: fixed;\n      top: 48px;\n      left: 0;\n      width: 280px;\n      margin-left: 0px;\n      bottom: 0;\n      background-color: #fff;\n      z-index:1;\n      .iconfont\n      {\n        width: 20px !important;\n        height: 24px !important;\n      } \n    "]);return p=function(){return n},n}function d(){var n=i(["\n      left: -30px !important;\n      top: -200px !important;\n    "]);return d=function(){return n},n}function h(){var n=i(["\n      position: absolute;\n      left: 0px;\n      top: -100px;\n      background-color: #EAEAEB;\n      width : calc(100% + 100px);\n      height : calc(100% + 200px);\n      overflow: hidden;\n      .canvas {\n        position : absolute;\n        left: 0px;\n        top: 0px;\n        &.canvas_drawing {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_drawing.png) 8 8,auto;\n        }\n        &.canvas_moving {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_moving.png) 16 16,auto;\n        }\n        &.canvas_leftmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_leftmove.png) 16 16,auto;\n        }\n        &.canvas_rightmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_rightmove.png) 16 16,auto;\n        }\n        &.canvas_acrossmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_acrossmove.png) 16 16,auto;\n        }\n        &.canvas_verticalmove {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/cursor_verticalmove.png) 16 16,auto;\n        }\n        &.canvas_text {\n          cursor : text;\n        }\n        &.canvas_pointer {\n          cursor : pointer;\n        }\n        &.canvas_splitWall {\n          cursor : url(https://3vj-fe.3vjia.com/layoutai/icons/split.png) 8 16,auto;\n        }\n      }\n\n      .canvas_btns {\n        width: auto;\n        margin: 0 auto;\n        position: fixed;\n        display: flex;\n        justify-content: center;\n        bottom: 35px;\n        z-index:10;\n        left: 50%;\n        transform: translateX(-50%);\n        .btn {\n          ","\n          border-radius: 6px;\n          border: none;\n\n          font-weight: 600;\n          margin-right: 10px;\n          margin-left: 10px;\n        }\n        .design_btn {\n          background: #e6e6e6;\n          margin-right: 20px;\n        }\n        @media screen and (max-height: 600px){\n          bottom: 50px !important;\n        }\n      }\n    "]);return h=function(){return n},n}function x(){var n=i(["\n      position: absolute;\n      left: 50%;\n      transform: translateX(-50%);\n      margin-left: -120px;\n      width: 500px;\n      top: 20px;\n      z-index: 20;\n    "]);return x=function(){return n},n}function g(){var n=i(["\n      position: absolute;\n      top: 0%;\n      padding-top: 21%;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.3); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return g=function(){return n},n}function m(){var n=i(["\n      z-index: 99;\n  \n      ","\n    "]);return m=function(){return n},n}function v(){var n=i(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0.5); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return v=function(){return n},n}function b(){var n=i(["\n        width: 400px;\n        height: 300px;\n        position:absolute;\n        right:245px;\n        top:5px;\n    "]);return b=function(){return n},n}function w(){var n=i(["\n      height: 48px;\n      width: 100%;\n      position: fixed;\n      top: 48px;\n      background-color: #262626;\n      text-align: center;\n      color: #fff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    "]);return w=function(){return n},n}function y(){var n=i(["\n      /* z-index: 99999; */\n      display: flex;\n      position: fixed;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      justify-content: center;\n      text-align: center;\n      img{\n        width: 256px;\n        height: 256px;\n      }\n      .first\n      {\n        color: #1C1F23;\n        font-family: PingFang SC;\n        font-weight: medium;\n        font-size: 24px;\n        line-height: 32px;\n        font-weight: 600;\n        margin: 20px 0 8px 0;\n      }\n      .second\n      {\n        color: #00000072;\n        font-weight: regular;\n        font-size: 14px;\n        line-height: 22px;\n      }\n    "]);return y=function(){return n},n}function _(){var n=i(["\n      position: fixed;\n      height: 250px;\n      bottom: 0px;\n      background-color: #fff;\n      width: 100%;\n      padding: 15px 48px;\n      .shcmeName\n      {\n        color: #282828;\n        font-family: SF Pro Text;\n        font-weight: semibold;\n        font-size: 20px;\n        line-height: 1.4;\n        font-weight: 600;\n      }\n      .info\n      {\n        display: inline-flex;\n        font-weight: 600;\n        margin-top: 10px;\n        .name\n        {\n          color: #5B5E60;\n          font-family: PingFang SC;\n          font-weight: regular;\n          font-size: 16px;\n          line-height: 1.5;\n          letter-spacing: 0px;\n          text-align: left;\n          margin-bottom: 5px;\n          width: 140px;\n        }\n      }\n      .btn\n      {\n        text-align: center;\n        margin: 0 auto;\n\n      }\n      .btnInfo\n      {\n        text-align: center;\n        margin-top: 20px;\n        .btn\n        {\n          border-radius: 8px;\n          background: #F4F5F5;\n          width: 200px;\n          height: 48px;\n          border: none;\n          color: #282828;\n          font-family: PingFang SC;\n          font-weight: semibold;\n          font-size: 16px;\n          line-height: 1.5;\n          font-weight: 600;\n        }\n      }\n      .shareImage\n      {\n        color: #5B5E60;\n        margin-top: 12px;\n        font-size: 16px;\n        img{\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          margin-right: 5px;\n        }\n\n      }\n    "]);return _=function(){return n},n}function j(){var n=i(["\n      padding: 24px;\n      font-size: 14px; \n      font-weight: 600;\n      img{\n        width: 36px;\n        height: 36px;\n        border-radius: 50%;\n        margin-right: 10px;\n      }\n    "]);return j=function(){return n},n}function k(){var n=i(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return k=function(){return n},n}function z(){var n=i(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 9999;\n    "]);return z=function(){return n},n}function S(){var n=i(["\n      width: 100%;\n      height: 99%;\n      border: none;\n      overflow-y: hidden;\n    "]);return S=function(){return n},n}function F(){var n=i(["\n      position: fixed;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      align-content: space-between;\n      div#unfurnish-tip-container {\n        margin-top: 25px;\n        flex: 1;\n        span#unfurnish-tip {\n          margin-top: 30px;\n          font-size: 16px;\n          font-weight: 600;\n          color: #000;\n        }\n      }\n      div#unfurnish-btn-container {\n        flex: 1;\n        Button {\n          margin-left: 16px;\n          margin-right: 16px;\n        }\n      }\n      top: 40%;\n      left: 40%;\n      width: 450px;\n      height: 130px;\n      border-radius: 10px;\n      background-color: rgba(255, 255, 255, 0.9); /* 设置背景颜色为半透明的黑色 */\n      z-index: 999; /* 确保蒙层在其他元素之上 */\n    "]);return F=function(){return n},n}t.A=(0,o.rU)(function(n){n.token;var t=n.css;return{root:t(a()),landscape:t(c()),loading:t(u()),content:t(s()),model_capture_container:t(l()),side_pannel:t(f()),left_panel:t(p()),canvas_pannel_mobile:t(d()),canvas_pannel:t(h(),(0,r.fZ)()?"\n            width: 120px;\n            height: 36px;\n            font-size: 14px;\n          ":"\n            width: 200px;\n            height: 48px;\n            font-size: 16px;\n          "),layout_steps:t(x()),progressInfo:t(g()),left_content:t(m(),(0,r.fZ)()?"\n        width: 180px !important;\n        min-width: 180px !important;\n      ":"\n        min-width: 280px !important;\n       "),overlay:t(v()),scene3d:t(b()),MeasurScaleMode:t(w()),expireEmpty:t(y()),mobileBottom:t(_()),shareBox:t(j()),canvas3d:t(k()),aiDraw:t(z()),material_iframe:t(S()),comfirmFurnishDialog:t(F())}})}}]);