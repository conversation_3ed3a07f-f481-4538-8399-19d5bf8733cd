import React, { useEffect, useState } from 'react';
import useStyles from './style/index';

import { Spin, Button, message } from '@svg/antd';

import { AttributeEdit } from '@/components';
// import { useNavigate } from "react-router-dom";
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { EventName } from '@/Apps/EventSystem';
import { TRoomNameType } from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { TrainingManager } from '@/Apps/Training/TrainingManager';
import Training_Topbar from './components/Training_Topbar/Training_Topbar';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import SchemeTestingLeftPanel from './components/LayoutSchemeTesting/SchemeTestingLeftPanel/schemeTestingLeftPanel';
import SchemeTestingRightPanel from './components/LayoutSchemeTesting/SchemeTestingRightPanel/schemeTestingRightPanel';
import RoomResultList from './components/RoomResultList/roomResultList';
import DreamHouseTestingPanel from './components/DreamHouseTestingPanel/dreamHouseTestingPanel';
import TestingDatasetListPanel from './components/QuickRoomTestingPanel/testingDatasetList';
import { observer } from 'mobx-react';
import RoomTemplateListsPanel from './components/RoomTemplateQueryPanel/roomTemplatesListPanel';
import LayoutScoreConfigPanel from './components/LayoutScoreConfigPanel/LayoutScoreConfigPanel';
import ModelLightConfigPanel from './components/ModelLightConfigPanel/ModelLightConfigPanel';
import TestingTaskListPanel from './components/QuickRoomTestingPanel/testingTaskList';
import LayoutGraphTestingPanel from './components/LayoutGraphTestingPanel/LayoutGraphTestingPanel';
import LeftLabelList, { I_LableCommand } from './components/LeftLabelList/leftLabelList';

/**
 * @description 主页
 */
const TrainingPage: React.FC = () => {
  const { styles } = useStyles();

  const [loading, setLoading] = useState<boolean>(false);

  const [issueReportVisible, setIssueReportVisible] = useState<boolean>(false);
  const [layoutBatchTestVisible, setLayoutBatchTestVisible] = useState<boolean>(false);
  const [modelLightConfigVisible, setModelLightConfigVisible] = useState<boolean>(false);
  const [issueRoomSelected, setIssueRoomSelected] = useState<TRoom>(null);
  const [layoutScoreConfigPanelVisible, setLayoutScoreConfigPanelVisible] = useState<boolean>(false);



  const [label_list, set_label_list] = useState<I_LableCommand[]>();

  const [messageApi, contextHolder] = message.useMessage();
  const messageKey: string = "trainingMessage";

  // const navigate = useNavigate();
  LayoutAI_App.NewApp(TrainingManager.AppName);

  LayoutAI_App.UseApp(TrainingManager.AppName);




  useEffect(() => {
    if (issueReportVisible) {
      LayoutAI_App.instance.update();
    }
  }, [issueReportVisible]);

  function initEventHandler() {
    if (LayoutAI_App.instance) {

      LayoutAI_App.on(EventName.LoadingProgress, (progress) => {
        if (progress.evt === "start") {
          setLoading(true);
        } else if (progress.evt === "end") {
          setLoading(false);
        }
      });

      LayoutAI_App.on(EventName.TrainingLabelsListHandle, (params) => {
        set_label_list(params);
      });

      LayoutAI_App.on(EventName.IssueRoomSelected, (room: TRoom) => {
        setIssueRoomSelected(room);
      });


      LayoutAI_App.on(EventName.TrainingMessage, (msgObj: any) => {
        msgObj.key = messageKey;
        msgObj.duration = 3,
          msgObj.style = {
            marginTop: '4vh',
            zIndex: 99,
          };
        messageApi.open(msgObj);
      });

      LayoutAI_App.on(EventName.setLayoutScoreConfigPanelVisible, (visible: boolean) => {
        setLayoutScoreConfigPanelVisible(visible);
      });

    }
  }

  // const toLayoutPage = ()=>{
  //   navigate('/layout')
  // }

  useEffect(() => {
    // LayoutAI_App.UseApp(RoomQueryManager.AppID);

    if (LayoutAI_App.instance) {
      // console.log(LayoutAI_App.instance);
      LayoutAI_App.t = (t: string) => { return t };
      if (!LayoutAI_App.instance.initialized) {
        LayoutAI_App.instance.init();
        LayoutAI_App.instance.prepare().then(() => {
          LayoutAI_App.instance.update();
        })
      }
      LayoutAI_App.instance.bindCanvas(document.getElementById("room_canvas") as HTMLCanvasElement);

      LayoutAI_App.instance.update();
    }

    initEventHandler();
  }, []); // 空数组-> 第一次渲染后执行

  const analyseRoom = async (e: any) => {
    e.preventDefault();
    setIssueReportVisible(false);
    if (localStorage && issueRoomSelected) {
      {
        localStorage.setItem("layout_ai_training_current_room_data", JSON.stringify(issueRoomSelected.exportExtRoomData()));
        LayoutAI_App.RunCommand("LayoutGraphTesting");
      }
    }
  }

  return (
    <div className={styles.root}>
      <Training_Topbar
        updateIssueReportVisible={setIssueReportVisible}
        updateLayoutBatchTestVisible={setLayoutBatchTestVisible}
        updateModelLightConfigVisible={setModelLightConfigVisible} />
      <div className={styles.content} style={layoutBatchTestVisible ? { display: "none" } : { display: "block" }}>
        <div className={styles.left_panel_container}>
          <div className={styles.left_panel_menu_box}>
            <LeftLabelList labelList={label_list}></LeftLabelList>
            <div id="side_list_div" className={styles.left_panel_side_list} />
          </div>
        </div>
        <TestingDatasetListPanel></TestingDatasetListPanel>
        <TestingTaskListPanel></TestingTaskListPanel>

        <div id="body_container" className={styles.canvas_pannel}>
          {issueReportVisible && <Button className={issueRoomSelected != null ? styles.enableAnalyseButton : styles.disableAnalyseButton}
            disabled={issueRoomSelected == null} type={'primary'}
            onClick={analyseRoom}>计算空间布局
          </Button>}
          <div style={{ position: 'absolute' }}>
            <RoomResultList></RoomResultList>
          </div>
          <canvas
            id="room_canvas"
            className="canvas"
          />
        </div>
        <SchemeTestingLeftPanel></SchemeTestingLeftPanel>
        <SchemeTestingRightPanel></SchemeTestingRightPanel>

        <RoomTemplateListsPanel></RoomTemplateListsPanel>
        <LayoutGraphTestingPanel></LayoutGraphTestingPanel>
        {/* <QuickTestingPanel></QuickTestingPanel> */}
        {/* <LayoutConfigureRightPanel></LayoutConfigureRightPanel> */}

        {/* <DreamHouseTestingPanel></DreamHouseTestingPanel> */}
        {layoutScoreConfigPanelVisible &&
          <LayoutScoreConfigPanel></LayoutScoreConfigPanel>
        }
        {modelLightConfigVisible &&
          <ModelLightConfigPanel></ModelLightConfigPanel>
        }
        <div id="right_side_panel" className={styles.right_side_panel}>
        </div>
        { <AttributeEdit />}
      </div>
      {loading && <div className={styles.loading}><Spin size="large" /></div>}
      {contextHolder}
    </div>
  );
};

export default observer(TrainingPage);
