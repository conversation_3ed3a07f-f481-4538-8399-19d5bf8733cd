import { useTranslation } from "react-i18next";
import useStyles from './style';
import { observer } from "mobx-react-lite";
import { message  } from "@svg/antd";
import { useEffect, useRef, useState } from "react";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from "@/Apps/LayoutAI_App";
import { useStore } from "@/models";
import { EventName } from "@/Apps/EventSystem";
import SelectHouse from "./selectHouse/selectHouse";
import { CSSTransition } from 'react-transition-group';
import { FileOpenResult, openFileInput } from "@/Apps/LayoutAI/Utils/file_utils";
import { Icon } from "@svg/antd-cloud-design";
/**
 * @description 主页
 */
export enum LightMainEvents {
    showLight3DViewer = "showLight3DViewer"
}
const EnterPage: React.FC = () => {
    const roomRef = useRef(null);
    const { t } = useTranslation();
    const { styles } = useStyles();
    const store = useStore();

    const [step, setStep] = useState<number>(0);
    
    // 确保当EnterPage显示时重置为2D全屏模式
    useEffect(() => {
        store.homeStore.setViewMode('2D');
    }, []);
    
    const onUploadHxImg = async()=>{
        let fileResult: FileOpenResult = await openFileInput('image/*').catch(e => {
            return null;
            });
        if(fileResult.content)
        {
            store.homeStore.setImgBase64(fileResult.content);
            message.success(t('上传户型图成功'));
            // setStep(1);
        } else 
        {
            message.warning(t('上传户型图失败'));
        }
    }


    const backClick = () => {
        // 发送消息，请求关闭iframe
        window.parent.postMessage({
            origin: 'layoutai.api',
            type: 'closeIframe',
            data: {
                canClose: true
            }
        }, '*');
    }
    const selectHX_header = () => {
        return (
            
            <div className={styles.hxHeader}>
                <div className='title'>
                    <div className='title_text'>搜户型</div>
                </div>
                <div className='layoutPlusButton'
                    onClick={() => {
                        console.log('跳转AI布局plus');
                    }}
                >
                    <Icon iconClass="icongallery1" style={{fontSize: '16px', color: '#5C42FB'}}/>
                    <div className='text'>跳转AI布局plus</div>
                </div>
                <div className='mySchemeButton'
                    onClick={() => {store.homeStore.setShowMySchemeList(true);}}
                >
                    <Icon iconClass="icongallery1" style={{fontSize: '16px', color: '#282828'}}/>
                    <div className='text'>我的方案</div>
                </div>
                <div className='closePage' onClick={backClick}>
                    <Icon iconClass="icon-close1" style={{fontSize: '20px', color: '#282828'}}/>
                </div>
            </div>
        )
    }

    /**
     * @description 选择户型
     */
    const selectHx = () => {
        return (
            <div className={styles.selectHx}>
                <div className={styles.hxRoot}>
                    <SelectHouse />
                </div>

                {/* <div className='right_btns'>
                    <div className='btn' onClick={onOpenMyScheme}>
                    <img src={'./static/icons/myPlan.svg'} alt="" /><span>我的方案</span>
                    </div>

                </div> */}
            </div>
        )
    }

    return (
        <div className={styles.enterPage}>
            {selectHX_header()}
            <div className='upload_hx' onClick={onUploadHxImg}>
                <img style={{width: '24px', height: '24px'}} src={'https://3vj-fe.3vjia.com/layoutai/icons/upload.svg'} alt="" />
                <div  className='upload_title'>
                    上传户型
                </div>
            </div>
            <CSSTransition
                in={step === 0}
                timeout={300}
                classNames={step === 1 ? "slide-reverse" : "slide"}
                mountOnEnter
                appear
                style={{display: step === 0 ? 'block' : 'none'}}
            >
                {selectHx()}
            </CSSTransition>
        </div>
    );
};

export default observer(EnterPage);
