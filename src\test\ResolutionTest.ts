import { GUI } from "lil-gui";
import { ServerRenderService } from "@/Apps/LayoutAI/Services/ServerRender/ServerRenderService";
import { ResolutionConfig, ResolutionTag } from "@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType";
import { IFrameMsgServerExFuncs } from "@/pages/SdkFrame/MsgCenter/IFrameMsgServerExFuncs";

export class ResolutionTest {
    private _gui: GUI;

    constructor(gui: GUI) {
        this._gui = gui;
    }

    public addResolutionCtr() {
        let resolutionFolder = this._gui.addFolder('渲染分辨率');
        resolutionFolder.close();

        const displayOptions: { [key: string]: ResolutionTag } = {};
        Object.values(ResolutionTag).forEach(tag => {
            displayOptions[ResolutionConfig[tag].name] = tag;
        });

        const resolutionData = {
            resolution: ResolutionConfig[ServerRenderService.resolutionTag].name
        };

        resolutionFolder.add(resolutionData, 'resolution', displayOptions)
            .name('分辨率')
            .onChange((value: string) => {
                IFrameMsgServerExFuncs.setResolution({ resolutionTag: value })
            });
    }
}