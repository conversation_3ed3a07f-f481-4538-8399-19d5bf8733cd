

/**
* @description 衣柜朝向类型
* <AUTHOR>
* @date 2025-07-26
* @lastEditTime 2025-07-26 10:26:46
* @lastEditors xuld
*/
export enum WardrobeDirType {
    // 平行
    PARALLEL_LEFT = 'PARALLEL_LEFT', // 平行 - 左
    PARALLEL_RIGHT = 'PA<PERSON><PERSON><PERSON>_RIGHT', // 平行 - 右
    PARALLEL_LEFT_FRONT = 'PARALLEL_LEFT_FRONT', // 平行 - 左前
    PARALLEL_RIGHT_FRONT = 'PARALLEL_RIGHT_FRONT', // 平行 - 右前
    // 垂直
    VERTICAL_FRONT = 'VERTICAL_FRONT', // 垂直 - 前
    VERTICAL_LEFT_BACK = 'VERTICAL_LEFT_BACK', // 垂直 - 左后
    VERTICAL_RIGHT_BACK = 'VERTICAL_RIGHT_BACK', // 垂直 - 右后
}