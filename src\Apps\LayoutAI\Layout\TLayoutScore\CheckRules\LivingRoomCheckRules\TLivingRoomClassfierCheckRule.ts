import { ZRect } from "@layoutai/z_polygon";
import { I_Window } from "../../../IRoomInterface";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TRoom } from "../../../TRoom";
import { DoorType, I_Range2D, TBaseRoomToolUtil } from "../BasicCheckRules/TBaseRoomToolUtil";
import { I_CheckRuleOptions, TCheckRule } from "../TCheckRule";
import { TLivingRoomToolUtil } from "./TLivingRoomToolUtil";
import { Vector3, VectorKeyframeTrack } from "three";
import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { splitSpaceForLivingRoom } from "../../../TLayoutEntities/utils/SplitSpaceForLivingRoom";

export class TLivingRoomClassfierCheckRule extends TCheckRule
{
    protected categoryFigures: Map<string, TFigureElement[]>;

    protected doorInfos: Map<DoorType, I_Window[]>;

    private _cabinetName: string = TLivingRoomToolUtil.instance.cabinetName;
    private _livingName: string = TLivingRoomToolUtil.instance.livingName;
    private _diningName: string = TLivingRoomToolUtil.instance.diningName;
    private _diningTableName: string = TLivingRoomToolUtil.instance.diningTableName;
    private _diningChairName: string = TLivingRoomToolUtil.instance.diningChairName;
    private _sofaName: string = TLivingRoomToolUtil.instance.sofaName;
    private _livingTableName: string = TLivingRoomToolUtil.instance.livingTableName;
    private _TVName: string = TLivingRoomToolUtil.instance.TVName;
    private _bookCabinetName = TLivingRoomToolUtil.instance.bookCabinetName;
    private _diningCabinetName: string = TLivingRoomToolUtil.instance.diningCabinetName;
    private _livingCabinetName: string = TLivingRoomToolUtil.instance.livingCabinetName;
    private _backWallName: string = TLivingRoomToolUtil.instance.backWallName;
    private _fixedName: string = TLivingRoomToolUtil.instance.fixedName;

    private _validSpaceRange: any = null;
    private _diningSpaceRange: any = null;
    private _livingSpaceRange: any = null;
    // 同时还需要计算分区线，并通过此分区线进行线段，分区
    private _splitLivingAndDiningSpaceEdge: ZEdge = null;

    constructor(figure_categories:string[], options : I_CheckRuleOptions = {})
    {
        super(figure_categories, options);
        this.categoryFigures = new Map<string, TFigureElement[]>();
    }

    protected setParamConfig(): void {
    }

    protected preProcessFigures(room: TRoom, figureElements: TFigureElement[]): void {
        // TODO 将传入的figure进行分门别类, 具体的可以参考厨房以及卫生间
        let allSingleFigures: TFigureElement[] = TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(figureElements);
        this.processFigure(room, allSingleFigures);
    }

    protected processFigure(room: TRoom, figureElements: TFigureElement[])
    {
        let cabintFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getCabintFigures(figureElements);
        let livingFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getLivingFigures(figureElements);
        let diningFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningFigures(figureElements);
        let diningTableFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningTableFigures(figureElements);
        let diningChairFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningChairFigures(figureElements);
        let sofaFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getSofaFigures(figureElements);
        let livingTableFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getLivingTableFigures(figureElements);
        let TVFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getTVFigures(figureElements);
        let bookCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getBookCabinetFigures(figureElements);
        let diningCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getDiningCabinetFigures(figureElements);
        let livingCabinetFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getLivingCabinetFigures(figureElements);
        let backWallFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getBackWallFigures(figureElements);
        let fixedFigures: TFigureElement[] = TLivingRoomToolUtil.instance.getFixedFigures(figureElements);

        this.categoryFigures.set(this.cabinetName, cabintFigures);
        this.categoryFigures.set(this.livingName, livingFigures);
        this.categoryFigures.set(this.diningName, diningFigures);
        this.categoryFigures.set(this.diningTableName, diningTableFigures);
        this.categoryFigures.set(this._diningChairName, diningChairFigures);
        this.categoryFigures.set(this.sofaName, sofaFigures);
        this.categoryFigures.set(this.livingTableName, livingTableFigures);
        this.categoryFigures.set(this.TVName, TVFigures);
        this.categoryFigures.set(this.bookCabinetName, bookCabinetFigures);
        this.categoryFigures.set(this.diningCabinetName, diningCabinetFigures);
        this.categoryFigures.set(this.livingCabinetName, livingCabinetFigures);
        this.categoryFigures.set(this.backWallName, backWallFigures);
        this.categoryFigures.set(this.fixedName, fixedFigures);

        // 解析客餐厅各个门的类别
        this.doorInfos = TBaseRoomToolUtil.instance.getDoorInfos(room);

        let splitSpaceInfo = splitSpaceForLivingRoom(room, figureElements);
        if(splitSpaceInfo)
        {
            if(splitSpaceInfo.livingSpace && splitSpaceInfo.livingSpace.length)
            {
                this._livingSpaceRange = splitSpaceInfo.livingSpace[0];
            }
            if(splitSpaceInfo.diningSpace && splitSpaceInfo.diningSpace.length)
            {
                this._diningSpaceRange = splitSpaceInfo.diningSpace[0];
            }
        }
    }

    protected isLivingNearDoorWithDining(doorType: DoorType): boolean
    {
        let doors: I_Window[] = this.doorInfos.get(doorType);
        if(doors && doors.length > 0)
        {
            let livingAndBalconyDist: number = 0;
            let diningAndBalconyDist: number = 0;
            for(let door of doors)
            {
                let doorCenter: Vector3 = door.rect.rect_center;
                let livingFigures: TFigureElement[] = this.categoryFigures.get(this.livingName);
                let diningFigures: TFigureElement[] = this.categoryFigures.get(this.diningName);
                let livingCenter: Vector3 = TBaseRoomToolUtil.instance.calAverageCenterByFigures(livingFigures);
                let diningCenter: Vector3 = TBaseRoomToolUtil.instance.calAverageCenterByFigures(diningFigures);
                livingAndBalconyDist += doorCenter.distanceTo(livingCenter);
                diningAndBalconyDist += doorCenter.distanceTo(diningCenter);
                
            }
            return livingAndBalconyDist < diningAndBalconyDist;
        }
        return false;
    }

    protected clear()
    {
        this.categoryFigures?.clear();
        this.doorInfos?.clear();
        this._livingSpaceRange = null;
        this._diningSpaceRange = null;
        this._validSpaceRange = null;
    }

    checkValue(room:TRoom, figure_elements:TFigureElement[] = null)
    {
        let allSingleFigures: TFigureElement[] = TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(figure_elements);
        let score = super.checkValue(room, allSingleFigures);
        this.clear();
        return score;
    }

    public get cabinetName(): string
    {
        return this._cabinetName;
    }

    public get livingName(): string
    {
        return this._livingName;
    }

    public get diningName(): string
    {
        return this._diningName;
    }
    
    public get diningTableName(): string
    {
        return this._diningTableName;
    }

    public get diningChairName(): string
    {
        return this._diningChairName;
    }

    public get sofaName(): string
    {
        return this._sofaName;
    }

    public get livingTableName(): string
    {
        return this._livingTableName;
    }

    public get TVName(): string
    {
        return this._TVName;
    }

    public get bookCabinetName(): string
    {
        return this._bookCabinetName;
    }

    public get diningCabinetName(): string
    {
        return this._diningCabinetName;
    }

    public get livingCabinetName(): string
    {
        return this._livingCabinetName;
    }

    public get backWallName(): string
    {
        return this._backWallName;
    }

    public get fixedName(): string
    {
        return this._fixedName;
    }

    protected get validSpaceRange(): I_Range2D
    {
        return this._validSpaceRange;
    }

    protected get diningSpaceRange(): I_Range2D
    {
        return this._diningSpaceRange;
    }

    protected get livingSpaceRange(): I_Range2D
    {
        return this._livingSpaceRange;
    }

    protected get splitLivingAndDiningSpaceEdge(): ZEdge
    {
        return this._splitLivingAndDiningSpaceEdge;
    }
        
    protected getFiguresEdgeFromPolys(figureRects: ZRect[], polygons: ZPolygon[]): ZEdge[][]
    {
        let targetPolyEdges: ZEdge[][] = [];
        for(let subPoly of polygons)
        {
            let polyEdges: ZEdge[] = [];
            for(let figureRect of figureRects)
            {
                for(let subPolyEdge of subPoly.edges)
                {
                    for(let figureEdge of figureRect.edges)
                    {
                        if(Math.abs(subPolyEdge.dv.clone().dot(figureEdge.dv)) > 0.9 && TBaseRoomToolUtil.instance.calDistance(subPolyEdge, figureEdge) < 5)
                        {
                            polyEdges.push(subPolyEdge);
                        }
                    }
                }
            }
            if(polyEdges.length)
            {
                targetPolyEdges.push(polyEdges);
            }
        }
        return targetPolyEdges;
    }

    protected hasPolyEdgesBySubPoly(polyEdges: ZEdge[][], subPoly: ZPolygon): boolean
    {
        for(let subPolyEdge of subPoly.edges)
        {
            for(let edges of polyEdges)
            {
                for(let polyEdgeItems of polyEdges)
                {
                    for(let polyEdge of polyEdgeItems)
                    {
                        if(subPolyEdge == polyEdge)
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    protected isDifferentTwoPolygons(poly1: ZPolygon, poly2: ZPolygon): boolean
    {
        if(poly1.edges.length != poly2.edges.length)
        {
            return true;
        }
        for(let edge1 of poly1.edges)
        {
            let isSame: boolean = false;
            for(let edge2 of poly2.edges)
            {
                if(Math.abs(edge1.dv.clone().dot(edge2.dv)) > 0.9 && TBaseRoomToolUtil.instance.calDistance(edge1, edge2) < 5)
                {
                    isSame = true;
                    break;
                }
            }
            if(!isSame)
            {
                return true;
            }
        }
        return false;
    }
}
