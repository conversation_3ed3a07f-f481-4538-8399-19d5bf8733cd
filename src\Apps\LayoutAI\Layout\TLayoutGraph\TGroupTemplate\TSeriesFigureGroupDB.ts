import { ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { getPrefix } from '../../../../../utils/common';
import { FigureZValRangeType } from "../../IRoomInterface";
import { I_FigureElement } from "../../TFigureElements/TFigureElement";
import { TSize, TSizeRange } from "../../TModels/TSizeRange";
import { TGraphBasicConfigs } from "../TGraphBasicConfigs";
import { I_NextToRelation, TNextToRelation } from "../TLayoutRelations/TBasicRelations/TNextToRelation";
import { I_FigureGroup, TFigureGroup } from "./TFigureGroup";
import { TGroupTemplate } from "./TGroupTemplate";
import { TSerialSizeRangeDB } from "./TSeriesSizeRangeDB";

const debug = true;
export class TSeriesFigureGroupDB {
    static instance: TSeriesFigureGroupDB = null;
    _dict: { [key: string]: { [key: string]: TFigureGroupDB } };

    _space_code_dict: { [key: string]: string[] } = {};
    private last_serial_id: string;

    constructor() {
        this._dict = { default: {} };
        this._space_code_dict = {};
        this.last_serial_id = "default";
        
    }

    get space_code_dict()
    {
        return this._space_code_dict;
    }
    static getInstance() {
        if (!TSeriesFigureGroupDB.instance) {
            TSeriesFigureGroupDB.instance = new TSeriesFigureGroupDB();
        }

        return TSeriesFigureGroupDB.instance;
    }

    static getSerialId()
    {
        return TSeriesFigureGroupDB.getInstance().last_serial_id;
    }
    static async LoadSeries(serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();
        serial_id = serial_id === "default" ? instance.last_serial_id : serial_id;

        // console.time("loadSeries");
        if (serial_id === "default") {
            let data = await fetch(getPrefix() +`./static/db/FigureGroupDB/default.json?tm=${Date.now()}`).then(val => val.json());

            if (!instance._dict[serial_id]) {
                instance._dict[serial_id] = {};
            }
            let db_dict = instance._dict[serial_id];


            for (let key in data) {
                let t_data: I_FigureGroupDB = data[key];
                if (db_dict[key]) {
                    let fig_group_db = db_dict[key];
                    fig_group_db.importJson(t_data, false);
                    // fig_group_db.generateBySizeRange();

                }
                else {
                    let fig_group_db = new TFigureGroupDB(t_data.group_code, new TFigureGroup(t_data.seed_figure_group));
                    // console.log(t_data.seed_figure_group,fig_group_db.group_space_category);
                    fig_group_db.importJson(t_data, false);
                    db_dict[key] = fig_group_db;

                    // fig_group_db.generateBySizeRange();

                }
            }

            // console.log(instance._dict,instance._space_code_dict);

            let text="";
            for(let key in instance._space_code_dict)
            {
                text += `"${key}"|`;
            }
            // console.log(text);

        }
        // console.timeEnd("loadSeries");

        this.AddCabinetsTemplates(serial_id);


        // if (debug) console.log(instance._dict,instance._space_code_dict);
    }


    static async SaveSeries(serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();
        serial_id = serial_id === "default" ? instance.last_serial_id : serial_id;
        let db_dict = instance._dict[serial_id];
        let res: { [key: string]: I_FigureGroupDB } = {};
        // console.log(db_dict,serial_id);

        let space_category_dict = TSeriesFigureGroupDB.instance._space_code_dict;
        if(space_category_dict)
        {
            for(let key in space_category_dict)
            {
                space_category_dict[key].forEach((group_code)=>{
                    if(db_dict[group_code])
                    {
                        let data = db_dict[group_code].exportJson();
                        res[data.group_code] = data;
                    }
                })
            }
        }
        if (db_dict) {
            for (let key in db_dict) {
                if(!res[key])
                {
                    let data = db_dict[key].exportJson();
                    res[data.group_code] = data;
                }

            }
        }

        // console.log(res,db_dict);
        let json_str = JSON.stringify(res);

        console.log(json_str);

    }

    static SetGroupDB(template_code: string, db: TFigureGroupDB, serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();
        if (!instance._dict[serial_id]) {
            instance._dict[serial_id] = {};
        }
        instance._dict[serial_id][template_code] = db;
    }
    static GetGroupDB(template_code: string, serial_id: string = "default"): TFigureGroupDB {
        let instance = TSeriesFigureGroupDB.getInstance();

        if (!instance._dict[serial_id]) return null;
        return instance._dict[serial_id][template_code] || null;
    }

    static AddGroupSpaceCategoryCodes(group_space_category:string, group_code:string)
    {
        let instance = TSeriesFigureGroupDB.getInstance();

        if (!instance._space_code_dict[group_space_category]) {
            instance._space_code_dict[group_space_category] = [];
        }
        if (instance._space_code_dict[group_space_category].indexOf(group_code) < 0) {
            instance._space_code_dict[group_space_category].push(group_code);
        }
    }

    /**
     * 添加组合模板, 入库DB
     */
    static AddGroupTemplate(group_template: TGroupTemplate, serial_id: string = "default", options:{add_seed_candidate_list?:boolean}={add_seed_candidate_list:true}) {
        let group_db = TSeriesFigureGroupDB.GetGroupDB(group_template.group_code, serial_id);
        if (!group_db) {
            let db = new TFigureGroupDB(group_template.group_code, group_template.seed_figure_group);
            TSeriesFigureGroupDB.SetGroupDB(db.group_code, db, serial_id);
            group_db = db;
        }

        if(options.add_seed_candidate_list)
        {
            group_db.seed_candidate_list.push(group_template.seed_figure_group.toJsonData());
        }
        return group_db;
    }

    /**
     *  添加橱柜组合模板
     */
    static AddCabinetsTemplates(serial_id : string = "default")
    {
        let configs = TGraphBasicConfigs.MainGroupFigureConfigs["厨房"];

        if(!configs) return;

        let cabinet_modelloc_dict :{[key:string]:string} = {

        }
        for(let group_space_code in configs)
        {
            let g_config = configs[group_space_code];

            if(!g_config) continue;
            for(let main_figure_name of g_config.main_figure_names)
            {
                let length =  g_config.group_length_levels.min||g_config.group_length_levels.values[0]|| 200;
                let zval_range_type = g_config.zval_range_type || FigureZValRangeType.All;

                if(main_figure_name.indexOf("双门")>=0)
                {
                    length = 600;
                }
                let depth =600;
                let height = 710;
                let min_z = 0;
    
                if(zval_range_type === FigureZValRangeType.OnFloor)
                {
                    depth = TGraphBasicConfigs.GlobalDefaultValues.floor_cabinet_depth;
                    min_z = TGraphBasicConfigs.GlobalDefaultValues.floor_cabinet_min_z;
                    height = TGraphBasicConfigs.GlobalDefaultValues.floor_cabinet_height;
                }
                else if(zval_range_type === FigureZValRangeType.OnHalfWall)
                {
                    depth = TGraphBasicConfigs.GlobalDefaultValues.hangon_cabinet_depth;
                    min_z = TGraphBasicConfigs.GlobalDefaultValues.hangon_cabinet_min_z;
                    height = TGraphBasicConfigs.GlobalDefaultValues.hangon_cabinet_height;
                }
                else{
                    depth = TGraphBasicConfigs.GlobalDefaultValues.high_cabinet_depth;
                    min_z = TGraphBasicConfigs.GlobalDefaultValues.high_cabinet_min_z;
                    height = TGraphBasicConfigs.GlobalDefaultValues.high_cabinet_height;
                }

                if(main_figure_name.indexOf("假门")>=0)
                {
                    depth = 100;
                }
                TSeriesFigureGroupDB.AddGroupSpaceCategoryCodes(group_space_code,main_figure_name);

                let model_loc = g_config.main_figure_category || main_figure_name;

                if(main_figure_name.indexOf("吊柜")>=1 && main_figure_name.indexOf("板")<0) 
                {
                    model_loc = "吊柜-"+main_figure_name;
                }
                let group_template = new TGroupTemplate().makeBySeedFigureGroup({
                    group_space_category:group_space_code,
                    main_figure : {
                        category: model_loc,
                        public_category: main_figure_name,
                        sub_category: main_figure_name,
                        
                        priority: 0,
                        min_z: min_z,
                        max_z: min_z + height,
                        params: {
                            length: length,
                            depth: depth,
                            height: height
                        }
                    },
                    sub_figures : []
                });
                
                let group_db = this.AddGroupTemplate(group_template,serial_id,{add_seed_candidate_list:true});

                let size_range_list = TSerialSizeRangeDB.GetSizeRangeList(main_figure_name);

                // if(size_range_list)
                // {
                //     let values:number[] = [];
                //     for(let size_range of size_range_list)
                //     {
                //         values.push(size_range.size_range.max.x);
                //     }
                //     values.sort((a,b)=>a-b);

                //     // 直接覆盖
                //     g_config.group_length_levels.values = values;
                // }
               


                group_db.generateBySizeRange();
            }


        }
    }
    static GetSeedGroupDict(serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();

        if (!instance._dict[serial_id]) return null;

        let res: { [key: string]: I_FigureGroup } = {};

        for (let key in instance._dict[serial_id]) {
            res[key] = instance._dict[serial_id][key].seed_figure_group.toJsonData();
        }

        return res;

    }

    static QureyTemplateByCate(cate: string, target_rect: ZRect, serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();

        if (!instance._dict[serial_id]) return null;

        let db: TFigureGroupDB = null;
        let ans_template: TGroupTemplate = null;
        for (let key in instance._dict[serial_id]) {
            if (key.indexOf(cate) >= 0) {
                db = instance._dict[serial_id][key];

                let template = new TGroupTemplate().makeBySeedFigureGroup(db.seed_figure_group.toJsonData());
                template._target_rect = target_rect;
                let check = template.updateByTargetRect();
                ans_template = template;

                if (check) {
                    return template;
                }

            }
        }

        return ans_template;
    }
    static MakeTemplateByCate(cate: string, serial_id: string = "default") {
        let instance = TSeriesFigureGroupDB.getInstance();

        if (!instance._dict[serial_id]) return null;

        let db: TFigureGroupDB = null;
        for (let key in instance._dict[serial_id]) {
            if (key.indexOf(cate) >= 0) {
                db = instance._dict[serial_id][key];
            }
        }

        if (db) {
            return new TGroupTemplate().makeBySeedFigureGroup(db.seed_figure_group.toJsonData());
        }
        else {
            return null;
        }
    }
}

export interface I_FigureGroupDB {
    /**
     *  种子模板
     */
    seed_figure_group: I_FigureGroup;
    /**
     *  组合模板
     */
    group_code: string;

    /**
     *   系列ID(可选)
     */
    serial_id?: string;

    group_space_category ?: string;
    seed_candidate_list: I_FigureGroup[];  // 种子候选集, 一般优先取这里面的数据
    generated_list: I_FigureGroup[];        // 根据尺寸链生成的  预组合列表, 服务端请求即可
}
/**
 *  预组合数据库
 */
export class TFigureGroupDB {
    /**
     *  组合模板
     */
    group_code: string;

    /**
     *   系列ID(可选)
     */
    serial_id: string;

    seed_candidate_list: I_FigureGroup[];  // 种子候选集, 一般优先取这里面的数据
    generated_list: I_FigureGroup[];        // 根据尺寸链生成的  预组合列表, 服务端请求即可

    seed_figure_group: TFigureGroup;

    group_space_category : string;

    protected _isGenertated  = false;
    constructor(group_code: string, seed_figure_group: TFigureGroup = null) {
        this.group_code = group_code;
        this.serial_id = "";
        this.generated_list = [];
        this.group_space_category = seed_figure_group.group_space_category || "";
        this.seed_figure_group = seed_figure_group;
        if(seed_figure_group)
        {
            this.seed_candidate_list = [];
        }
        else{
            this.seed_candidate_list = [];
        }
        

        
        if(this.group_space_category)
        {
            TSeriesFigureGroupDB.AddGroupSpaceCategoryCodes(TGroupTemplate.stripGroupSpaceCategory(this.group_space_category),this.group_code);
        }
    }


    exportJson(options:{export_seed_candidate:boolean}={export_seed_candidate:false}): I_FigureGroupDB {

        let seed_candidate_list : I_FigureGroup[] = [];
        for(let g of this.seed_candidate_list)
        {
            if((g as any as TFigureGroup).IsFigureGroup)
            {
                seed_candidate_list.push((g as any as TFigureGroup).toJsonData(true));
            }
            else{
                seed_candidate_list.push(g);

            }
        }
        return {
            group_code: this.seed_figure_group.group_code,
            serial_id: this.serial_id,
            group_space_category : this.group_space_category,
            seed_figure_group: this.seed_figure_group.toJsonData(true),
            seed_candidate_list: options.export_seed_candidate? seed_candidate_list:[],
            generated_list: [],
        }
    }

    /**
     * 替换 or 补充
     * @param data  
     * @param is_add_mode 
     */
    importJson(data: I_FigureGroupDB, is_add_mode: boolean = false) {
        if (!is_add_mode) {
            this.seed_candidate_list = [this.seed_figure_group as any];
            this.generated_list = [];
        }

        // if(data.group_space_category && data.group_space_category.length > 0)
        // {
        //     this.group_space_category = data.group_space_category;
        //     TSeriesFigureGroupDB.AddGroupSpaceCategoryCodes(data.group_space_category,data.group_code);
        // }
        if(data.seed_candidate_list.length > 0)
        {
            this.seed_candidate_list = [];
        }

        for (let fig_group of data.seed_candidate_list) {
        
            fig_group.group_space_category = fig_group.group_space_category || data.group_space_category;
            this.seed_candidate_list.push(fig_group);
        }
        for (let fig_group of data.generated_list) {
            fig_group.group_space_category = fig_group.group_space_category || data.group_space_category;
            this.generated_list.push(fig_group);
        }

        
        this.seed_candidate_list.sort((a, b) => {
            return b.size_range.max.x - a.size_range.max.x;
        });
        this.generated_list.sort((a, b) => {
            return b.size_range.max.x - a.size_range.max.x;
        });

    }


    generateBySizeRange() {
        this.generated_list = [];

        let group_data = this.seed_figure_group.toJsonData();

        let len = group_data.sub_figures.length + 1;


        let scope = this;
        let last_fig : I_FigureElement = null;

        let update_res = function (id: number) {
            if (id == len) {
                let t_figure_group = new TFigureGroup(group_data);

                let t_check = true;
                for (let sub_fig of t_figure_group.sub_figures) {
                    if (!(sub_fig.relation as TNextToRelation).checkConditions()) {
                        t_check = false;
                    }
                }
                if (t_check) {
                    scope.generated_list.push(t_figure_group.toJsonData());
                }
                return;
            }
            let fig: I_FigureElement = null;
            let relation: I_NextToRelation = null;
            let last_dist = 600;
            if (id == 0) {
                fig = group_data.main_figure;

            }
            else {
                let ti = id - 1;
                fig = group_data.sub_figures[ti].figure;
                relation = group_data.sub_figures[ti].relation as I_NextToRelation;
                // last_dist = parseFloat(relation.neighbor_info.nor_offset_dist);

        
            }
            // 要清空fig._rect_data
            fig._rect_data = null;




            let list = TSerialSizeRangeDB.GetSizeRangeList(fig.sub_category);
            if(last_fig && last_fig.sub_category === fig.sub_category) // 如果上一个图元跟这个图元是同一个, 尺寸链给同一个即可
            {
                list = [{size_range:new TSizeRange({x:0,y:0,z:0},{x:last_fig.params.length,y:last_fig.params.depth,z:0})}];
            }
            if (list) {
                let params = {};

                for (let key in fig.params) {
                    (params as any)[key] = fig.params[key];
                }
                for (let r_data of list) {
                    if (r_data.size_range.max.x < 100000) {
                        fig.params.length = r_data.size_range.max.x;
                    }
                    if (r_data.size_range.max.y < 100000) {
                        fig.params.depth = r_data.size_range.max.y;
                    }

                    let relation_record: I_NextToRelation = { neighbor_info: {} };
                    if (relation) {
                        for (let key in relation.neighbor_info) {
                            relation_record.neighbor_info[key] = relation.neighbor_info[key];
                        }


                    }
                    last_fig = fig;

                    update_res(id + 1);

                    if (relation) {
                        for (let key in relation.neighbor_info) {
                            relation.neighbor_info[key] = relation_record.neighbor_info[key];
                        }

                    }


                    for (let key in params) {
                        (fig.params as any)[key] = (params as any)[key];
                    }
                }
            }
            else {
                update_res(id + 1);

            }



        }
        update_res(0);
        // this.generated_list.push(this.seed_figure_group.toJsonData());


        this.generated_list.sort((a, b) => {

            if(Math.abs(b.size_range.max.x - a.size_range.max.x) < 1.)
            {
                return  b.size_range.max.y - a.size_range.max.y;
            }
            return b.size_range.max.x - a.size_range.max.x;
        });

        this._isGenertated = true;
    }


    queryWithSize(size: TSize, res_data :{min_dist:number, allow_not_in_size_range?:boolean} = null) {

        if(!this._isGenertated)
        {
            this.generateBySizeRange();
        }
        let size_range = new TSizeRange();
        size_range.expandByPoint({ x: -1, y: -1, z: -1000 } as any);
        size_range.expandByPoint(size as any);

        let target_ele: I_FigureGroup = null;
        let min_dist = 99999;

        /**
         *  优先用生成的布局, 而不是种子布局
         */
        let candidate_group_list = [...this.seed_candidate_list, ...this.generated_list];

        for (let ele of candidate_group_list) {

            if (size_range.containsPoint(ele.size_range.max as Vector3) || (res_data?.allow_not_in_size_range===true)) {

                let dist = new Vector3().copy(size).distanceTo(ele.size_range.max);

                if (!target_ele || dist < min_dist) {
                    min_dist = dist;
                    target_ele = ele;
                    if(res_data)
                    {
                        res_data.min_dist = min_dist;
                    }
                    // console.log(target_ele);
                }
            }
            

        }

        if (target_ele) return target_ele;


        return null;
    }

    queryAllWithSize(size:TSize)
    {
        if(!this._isGenertated)
        {
            this.generateBySizeRange();
        }
        let result_group_list : I_FigureGroup[] = [];
        let size_range = new TSizeRange();
        size_range.expandByPoint({ x: -1, y: -1, z: -1000 } as any);
        size_range.expandByPoint(size as any);

        let target_ele: I_FigureGroup = null;
        let min_dist = 99999;

        /**
         *  优先用生成的布局, 而不是种子布局
         */
        let candidate_group_list = [...this.seed_candidate_list, ...this.generated_list];

        for (let ele of candidate_group_list) {

            if (size_range.containsPoint(ele.size_range.max as Vector3)) {

                result_group_list.push(ele);
            }            
        }
        return result_group_list;

    }


}