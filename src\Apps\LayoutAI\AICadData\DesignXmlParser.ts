import { IRoomEntityRealType, IRoomEntityType } from "../Layout/IRoomInterface";
import { compareNames } from "@layoutai/z_polygon";
import { get_swj_xml_from_url } from "../Utils/xml_utils";
import { I_SwjAreaName, I_SwjCabinetData, I_SwjEntityBase, I_SwjFurnitureData, I_SwjFurnitureGroup, I_SwjLineEdge, I_SwjRoom, I_SwjStructureData, I_SwjWall, I_SwjWindow, I_SwjXmlScheme } from "./SwjLayoutData";

const keyword_map: { [key: string]: string } = {
    "uidN": "uid",
    "nameS": "name",
    "materialIdS": "material_id",
    "thicknessN": "thickness",
    "lengthN": "length",
    "widthN": "width",
    "depthN": "depth",
    "heightN": "height",
    "rotationXN": "rotate_x",
    "rotationYN": "rotate_y",
    "rotationZN": "rotate_z",
    "xN": "pos_x",
    "yN": "pos_y",
    "zN": "pos_z",
    "isMirrorN": "mirror",
    "offsetN": "offset",
    "wallUIDN": "wallUid",
    "doorTypeS": "realType",
    "materialAttributeS": "materialAttribute"
}

const var_keyword_map: { [key: string]: string } = {
    "W": "length",
    "D": "width",
    "H": "height",
    "PX": "pos_x",
    "PY": "pos_y",
    "PZ": "pos_z",
    "RX": "rotate_x",
    "RY": "rotate_y",
    "RZ": "rotate_z"

}
export class DesignXmlParser {
    _xml_doc: XMLDocument;

    _scheme_entity_5: Element;
    _scheme_root: Element;
    _bim: Element;
    _bim_element: Element;
    _bim_extra: Element;
    _furniture_door_window: Element;
    _wholeHouseDesign: Element;


    static _instance: DesignXmlParser;
    constructor() {

    }
    static get instance() {
        if (!DesignXmlParser._instance) {
            DesignXmlParser._instance = new DesignXmlParser();
        }
        return DesignXmlParser._instance;
    }

    async parseXmlUrl(url: string) {
        let xml_str = await get_swj_xml_from_url(url);
        return this.parseXmlStr(xml_str);
    }

    parseXmlStr(xmlstr: string) {
        this._xml_doc = (new DOMParser()).parseFromString(xmlstr, 'application/xml');

        // console.log(this._xml_doc,xmlstr);
        this._scheme_entity_5 = this._xml_doc.getElementsByTagName("swjia_scheme_5_0")[0];
        if (!this._scheme_entity_5) {
            return this.parseXml_version_4_0();
        }
        this._scheme_root = this._scheme_entity_5.getElementsByTagName("SchemeRoot")[0];
        this._bim = this._scheme_root.getElementsByTagName("bim")[0];
        if (!this._bim) {
            console.log(this._scheme_root);
            return null;
        }
        this._bim_element = this._bim.getElementsByTagName("elements")[0];
        this._bim_extra = this._bim.getElementsByTagName("bim_extra")[0];
        this._furniture_door_window = this._scheme_root.getElementsByTagName("FurnitureDoorWindow")[0];
        this._wholeHouseDesign = this._scheme_root.getElementsByTagName("WholeHouseDesign")[0];

        // console.log(this._bim);
        let ans_scheme: I_SwjXmlScheme = {};
        ans_scheme.wall_list = this._getWallEntities();

        ans_scheme.flue_list = this._getStructureEntities("Flue");
        ans_scheme.pillar_list = this._getStructureEntities("Pillar");
        ans_scheme.platform_list = this._getStructureEntities("Platform");
        ans_scheme.pipe_list = this._getStructureEntities("Envelope_Pipe");

        ans_scheme.window_list = this._getWindowsEntities("Window");
        ans_scheme.door_list = this._getWindowsEntities("Door");
        ans_scheme.furniture_list = this._getFurnitureEntities();
        ans_scheme.furniture_group_list = this._getFurnitureGroupEntities();
        ans_scheme.cabinet_list = this._getCabinets();
        ans_scheme.room_list = this._getRoomEntities();
        ans_scheme.area_names = this._getAreaNameEntities();
        return ans_scheme;
    }

    protected parseXml_version_4_0() {
        let wall_info_ele = this._xml_doc.getElementsByTagName("WallInfo")[0];
        console.log(this._xml_doc);
        let inwall_info_ele = this._xml_doc.getElementsByTagName("InWallInfo")[0];
        let room_info_ele = this._xml_doc.getElementsByTagName("RoomInfo")[0];

        if (!wall_info_ele || !inwall_info_ele || !room_info_ele) return null;

        let wall_data_list = wall_info_ele.getElementsByTagName("WallData");
        let ans_scheme: I_SwjXmlScheme = {};

        ans_scheme.wall_list = [];
        for (let wall_data_ele of wall_data_list) {
            let wall_json: I_SwjWall = this._makeBaiscEntity(wall_data_ele);
            wall_json.start_x = parseFloat(wall_data_ele.getAttribute("StartX"));
            wall_json.start_y = parseFloat(wall_data_ele.getAttribute("StartY"));
            wall_json.end_x = parseFloat(wall_data_ele.getAttribute("EndX"));
            wall_json.end_y = parseFloat(wall_data_ele.getAttribute("EndY"));
            wall_json.thickness = parseFloat(wall_data_ele.getAttribute("Thickness"));
            wall_json.uid = wall_data_ele.getAttribute("wallUID");
            ans_scheme.wall_list.push(wall_json);
        }
        ans_scheme.door_list = [];
        ans_scheme.window_list = [];

        let inwall_data_list = inwall_info_ele.getElementsByTagName("InWallData");
        for (let inwall_ele of inwall_data_list) {
            let inwall_json = this._makeBaiscEntity(inwall_ele) as I_SwjWindow;

            inwall_json.height = parseFloat(inwall_ele.getAttribute("Height"));
            inwall_json.length = parseFloat(inwall_ele.getAttribute("Length"));
            inwall_json.width = parseFloat(inwall_ele.getAttribute("Width"));
            inwall_json.thickness = parseFloat(inwall_ele.getAttribute("Thickness") || "0");
            inwall_json.pos_z = parseFloat(inwall_ele.getAttribute("HighFloor"));
            inwall_json.pos_x = parseFloat(inwall_ele.getAttribute("PosX"));
            inwall_json.pos_y = parseFloat(inwall_ele.getAttribute("PosY"));
            inwall_json.mirror = parseFloat(inwall_ele.getAttribute("Mirror"));
            inwall_json.material_id = (inwall_ele.getAttribute("MaterialId"));
            inwall_json.rotate_z = parseFloat(inwall_ele.getAttribute("RotateZ"));
            inwall_json.type = inwall_ele.getAttribute("Type") as IRoomEntityType;

            if (inwall_json.type as string === "DOOR") {
                inwall_json.type = "Door";
            }
            if (inwall_json.type == "Door") {
                ans_scheme.door_list.push(inwall_json);
            }
            else if (inwall_json.type == "Window") {
                ans_scheme.window_list.push(inwall_json);
            }

        }

        ans_scheme.flue_list = [];
        ans_scheme.pillar_list = [];
        ans_scheme.platform_list = [];
        ans_scheme.pipe_list = [];
        ans_scheme.area_names = [];

        ans_scheme.room_list = [];
        let room_data_list = room_info_ele.getElementsByTagName("RoomData");
        for (let room_ele of room_data_list) {
            let data = this._makeBaiscEntity(room_ele);
            data.pos_x = parseFloat(room_ele.getAttribute("PosX"));
            data.pos_y = parseFloat(room_ele.getAttribute("PosY"));
            data.name = room_ele.getAttribute("RoomName");
            ans_scheme.area_names.push(data);

        }
        return ans_scheme;
    }
    protected _getWallEntities() {
        let mWallEntities = this._bim_element.getElementsByTagName("mWall");
        let walls: I_SwjWall[] = [];
        for (let mWall of mWallEntities) {
            let wall_data = this._wallElementToJson(mWall);
            wall_data && walls.push(wall_data);
        }

        return walls;
    }

    protected _getWindowsEntities(win_type: IRoomEntityType) {
        let window_list: I_SwjWindow[] = [];
        if (this._furniture_door_window) {
            let entitys = this._furniture_door_window.getElementsByTagName("Entitys")[0];
            if (entitys) {
                let tagName = "EmbeddingFurnitureDoorEntity";
                if (win_type === "Window") {
                    tagName = "EmbeddingFurnitureWindowEntity";
                }
                let windows = entitys.getElementsByTagName(tagName);

                for (let win_ele of windows) {
                    let win_data = this._makeBaiscEntity(win_ele);
                    if (!win_data.realType && compareNames([win_data.name], ["飘窗"])) {
                        win_data.realType = "BayWindow";
                    }

                    window_list.push(win_data);
                }


            }
        }
        return window_list;
    }

    protected _getFurnitureEntities() {
        let furniture_list: I_SwjFurnitureData[] = [];
        let basic_element = this._scheme_root.getElementsByTagName("Basic")[0];

        if (basic_element) {
            let Furnitures = basic_element.getElementsByTagName("Furnitures")[0];

            if (Furnitures) {
                let furniture_ele_list = Furnitures.getElementsByTagName("CRealFurnitureEntity");
                for (let furniture_ele of furniture_ele_list) {
                    let furniture_ele_data = this._makeBaiscEntity(furniture_ele) as I_SwjFurnitureData;
                    furniture_list.push(furniture_ele_data);
                }
            }

        }
        return furniture_list;
    }
    protected _getFurnitureGroupEntities() {
        let group_list: I_SwjFurnitureGroup[] = [];
        let basic_element = this._scheme_root.getElementsByTagName("Basic")[0];

        if (basic_element) {
            let FurnitureGroups = basic_element.getElementsByTagName("FurnitureGroups")[0];

            if (FurnitureGroups) {
                let group_ele_list = FurnitureGroups.getElementsByTagName("CGroupEntity");
                for (let group_ele of group_ele_list) {
                    let group_data = this._makeBaiscEntity(group_ele) as I_SwjFurnitureGroup;
                    group_list.push(group_data);
                    group_data.furniture_list = [];
                    let furniture_ele_list = group_ele.getElementsByTagName("CRealFurnitureEntity");
                    for (let furniture_ele of furniture_ele_list) {
                        let furniture_ele_data = this._makeBaiscEntity(furniture_ele) as I_SwjFurnitureData;
                        group_data.furniture_list.push(furniture_ele_data);
                    }
                }
            }

        }
        return group_list;
    }
    protected _getRoomEntities() {
        let rooms: I_SwjRoom[] = [];
        let room_ele_list = this._bim_extra.getElementsByTagName("room");
        for (let ele of room_ele_list) {
            let room: I_SwjRoom = this._makeBaiscEntity(ele);

            let curve3_list = ele.getElementsByTagName("curve3");
            room.boundary = [];
            room.beam_list = [];
            room.pillar_list = [];
            room.flue_list = [];
            room.pipe_list = [];
            room.platform_list = [];
            room.door_list = [];
            room.window_list = [];
            room.cabinet_list = [];
            room.furniture_list = [];
            room.furniture_group_list = [];
            for (let curve3 of curve3_list) {
                let start_arr = JSON.parse("[" + curve3.getAttribute("startU") + "]");
                let end_arr = JSON.parse("[" + curve3.getAttribute("endU") + "]");

                room.boundary.push({
                    start: { x: start_arr[0] || 0, y: start_arr[1] || 0, z: start_arr[2] || 0 },
                    end: { x: end_arr[0] || 0, y: end_arr[1] || 0, z: end_arr[2] || 0 }
                })
            }

            rooms.push(room);
        }

        return rooms;
    }

    protected _getAreaNameEntities() {
        let room_areas = this._bim_element.getElementsByTagName("room_area");

        let res_room_areas: I_SwjAreaName[] = [];

        for (let room_area of room_areas) {
            let data = this._makeBaiscEntity(room_area);
            let positionU = room_area.getAttribute("positionU");
            let pos_arr = JSON.parse('[' + positionU + ']');
            data.pos_x = pos_arr[0] || 0;
            data.pos_y = pos_arr[1] || 0;
            data.pos_z = pos_arr[2] || 0;
            res_room_areas.push(data);
        }

        return res_room_areas;
    }
    protected _makeBaiscEntity(ele: Element) {
        let res: I_SwjEntityBase = {};
        for (let key in keyword_map) {
            let val = ele.getAttribute(key);
            if (val) {
                let t_key = keyword_map[key];
                if (key.endsWith("N")) {
                    (res as any)[t_key] = parseFloat(val);
                }
                else {
                    (res as any)[t_key] = (val);
                }
            }
        }
        return res;
    }
    protected _makeCabinetEntity(ele: Element) {
        let res: I_SwjEntityBase = {};

        let keys = ele.getAttributeNames();

        for (let key of keys) {
            let val = ele.getAttribute(key) as any;

            if (key.endsWith("N")) {
                val = parseFloat(val);
            }
            else if (key.endsWith("B")) {
                val = (val == "T");
            }

            let t_key = keyword_map[key];
            if (!t_key) {
                t_key = key;
                if (t_key.endsWith("S") || t_key.endsWith("N") || t_key.endsWith("B")) {
                    t_key = key.substring(0, key.length - 1);
                }
            }
            (res as any)[t_key] = val;
        }
        return res;

    }
    protected _getStructureEntities(structure_type: IRoomEntityRealType) {
        let tagName = structure_type.toLowerCase();
        let structure_elements = this._bim.getElementsByTagName(tagName);

        let structures: I_SwjStructureData[] = [];
        for (let ele of structure_elements) {
            let res: I_SwjStructureData = this._makeBaiscEntity(ele) as I_SwjStructureData;
            let location = ele.getElementsByTagName("location")[0];
            if (location) {
                if (location.getAttribute("locationTypeS") === "point") {
                    let l_res = this._locationTypePointToJson(location);
                    res.pos_x = l_res.origin.x;
                    res.pos_y = l_res.origin.y;
                    res.direct_x = l_res.yAxis.x;
                    res.direct_y = l_res.yAxis.y;
                }
            }
            structures.push(res);
        }
        return structures;


    }
    protected _positionEleToJson(endpoint: Element) {
        const key_map = { "xN": "x", "yN": "y", "zN": "z" } as any;

        let ans: { x: number, y: number, z: number } = { x: 0, y: 0, z: 0 };

        for (let key in key_map) {
            let val = endpoint.getAttribute(key);
            if (val) {
                (ans as any)[key_map[key]] = parseFloat(val);
            }
        }
        return ans;
    }
    protected _locationTypePointToJson(location: Element) {
        let origin = location.getElementsByTagName("origin")[0];
        let xAxis = location.getElementsByTagName("xAxis")[0];
        let yAxis = location.getElementsByTagName("yAxis")[0];

        let res = {
            origin: { x: 0, y: 0, z: 0 },
            xAxis: { x: 1, y: 0, z: 0 },
            yAxis: { x: 0, y: 1, z: 0 }
        }
        origin && (res.origin = this._positionEleToJson(origin));
        xAxis && (res.xAxis = this._positionEleToJson(xAxis));
        yAxis && (res.yAxis = this._positionEleToJson(yAxis));

        return res;
    }
    protected _curveEndpointsToLine(curve: Element) {
        let endpoint0 = curve.getElementsByTagName("endpoint0")[0];
        let endpoint1 = curve.getElementsByTagName("endpoint1")[0];

        let res: I_SwjLineEdge = { start: { x: 0, y: 0, z: 0 }, end: { x: 0, y: 0, z: 0 } };
        if (endpoint0 && endpoint1) {
            res.start = this._positionEleToJson(endpoint0);
            res.end = this._positionEleToJson(endpoint1);
        }
        return res;
    }
    protected _wallElementToJson(mWall: Element) {
        let wall_json: I_SwjWall = this._makeBaiscEntity(mWall);
        wall_json.boundary = [];
        let location = mWall.getElementsByTagName("location")[0] || null;
        if (location) {
            let line = this._curveEndpointsToLine(location);
            wall_json.start_x = line.start.x;
            wall_json.start_y = line.start.y;
            wall_json.end_x = line.end.x;
            wall_json.end_y = line.end.y;
        }
        let profile = mWall.getElementsByTagName("profile")[0] || null;
        if (profile) {
            let curves = profile.getElementsByTagName("curve");
            for (let curve of curves) {
                let line = this._curveEndpointsToLine(curve);
                line && wall_json.boundary.push(line);
            }
        }
        return wall_json;
    }

    protected _getVariableComponet(variable: Element, tagName: "NumberVariable" | "Variable" = "NumberVariable") {
        if (!variable) return null;
        let res: { [key: string]: { value: number, expr: string } } = {};

        let elements = variable.getElementsByTagName(tagName);
        for (let ele of elements) {
            let data = this._makeCabinetEntity(ele);
            res[data.name] = { value: (data as any).value, expr: (data as any).valueExpression };
        }
        return res;
    }

    protected _getMaterialComponent(material: Element) {
        if (!material) return null;

        let materialMapVoId = material.getAttribute("materialMapVoIdS");
        if (!materialMapVoId) {
            return null;
        }

        let res: { [key: string]: { value: number | string } } = {};
        res.id = { value: materialMapVoId };
        let isRotate = material.getAttribute("isRotateN");
        if (isRotate) {
            res.isRotate = { value: parseInt(isRotate) };
        }

        let isReverse = material.getAttribute("isReversePlaneN");
        if (isReverse) {
            res.isReverse = { value: parseInt(isReverse) };
        }

        let isSideRotate = material.getAttribute("isSideRotateN");
        if (isSideRotate) {
            res.isSideRotate = { value: parseInt(isSideRotate) };
        }

        let oriMaterialMapVoId = material.getAttribute("oriMaterialMapVoIdS");
        if (oriMaterialMapVoId) {
            res.oriMaterialMapVoId = { value: oriMaterialMapVoId };
        }

        return res;
    }

    protected _getMaterialIDs(materialIDs: Element) {
        let res: Array<string> = [];
        if (!materialIDs) return res;

        let idsS = materialIDs.getAttribute("idsS");
        if (idsS) {
            res = idsS.split(",");
        }

        return res;
    }

    protected _getCabinetFromEle(ele: Element, visit_depth: number = 0) {
        let cabinet = this._makeCabinetEntity(ele) as I_SwjCabinetData;
        cabinet._xml_entity_type = ele.tagName;

        // console.log("xxxxx", ele.tagName, cabinet.name);

        let variable_data = this._getVariableComponet(ele.getElementsByTagName("VariableComponet")[0]);
        cabinet._variables = variable_data;
        for (let key in variable_data) {
            let t_key = var_keyword_map[key];
            if (t_key) {
                (cabinet as any)[t_key] = variable_data[key].value;
            }
        }

        let material_data = this._getMaterialComponent(ele.getElementsByTagName("CWhMaterialComponent")[0]);
        cabinet.material = material_data;
        cabinet.materialIds = this._getMaterialIDs(ele.getElementsByTagName("MaterialIDs")[0]);

        let children_ele = ele.getElementsByTagName("Children")[0];
        if (children_ele && visit_depth < 5) {
            cabinet._children = [];
            for (let child of children_ele.children) {
                cabinet._children.push(this._getCabinetFromEle(child, visit_depth + 1));
            }
        }

        return cabinet;
    }
    protected _getCabinets() {
        let cabinet_modules = ["CupBoard", "Wardrobe", "SystemCabinet", "BathCabinet"];
        let cabinets: I_SwjCabinetData[] = [];
        for (let cabinet_module of cabinet_modules) {
            let module_entity = this._wholeHouseDesign.getElementsByTagName(cabinet_module)[0];
            if (!module_entity) continue;
            let Entitys = module_entity.getElementsByTagName("Entitys")[0];
            if (!Entitys) continue;
            for (let ele of Entitys.children) {
                let cabient_data = this._getCabinetFromEle(ele);

                cabinets.push(cabient_data);
            }
        }

        return cabinets;
    }

    public parseCupboardPart(ele: Element, visit_depth: number = 0) {
        let cabinet = this._makeCabinetEntity(ele) as I_SwjCabinetData;
        cabinet._xml_entity_type = ele.tagName;

        let variable_data = this._getVariableComponet(ele.getElementsByTagName("Variables")[0], "Variable");

        for (let values_node of ele.children) {
            if (values_node.tagName === "Values") {
                let node = this._makeCabinetEntity(values_node);
                if (!variable_data) {
                    variable_data = {};
                }
                for (let key in node) {
                    variable_data[key] = { value: (node as any)[key], expr: null };
                }

                break;
            }
        }

        cabinet._variables = variable_data;

        for (let key in variable_data) {
            let t_key = var_keyword_map[key];
            if (t_key) {
                let value = parseFloat('' + variable_data[key].value) || 0;

                (cabinet as any)[t_key] = value;
            }
        }
        cabinet._children = [];

        let tagNames = ["Part", "SwingDoor", "SwingDoorLeaf"];
        for (let child of ele.children) {
            if (tagNames.includes(child.tagName)) {
                cabinet._children.push(this.parseCupboardPart(child, visit_depth + 1));

            }
        }

        return cabinet;
    }

    public updateVisibleOfCupboard(cabinet: I_SwjCabinetData) {

        let variables: { [key: string]: string } = {};

        let isNumber = function (val: any) {
            return (typeof val === 'number') && !isNaN(val);
        };

        let visite_node = (node: I_SwjCabinetData) => {

            let record_variables = { ...variables };

            let predefined_code = "";
            for (let key in variables) {
                let value = variables[key];
                if (isNumber(value)) {
                    predefined_code += `let ${key}=${variables[key]}; `;
                }
                else {
                    predefined_code += `let ${key}="${variables[key]}"; `;

                }

            }
            if (node.showCondition) {
                let res = true;
                let showCondition = node.showCondition;
                showCondition = showCondition.replace(new RegExp('{', 'g'), "(");
                showCondition = showCondition.replace(new RegExp('}', 'g'), ")");
                let text = "res = " + showCondition;
                try {

                    eval(predefined_code + " " + text);

                    node.visible = res;

                } catch (error) {
                    console.log(error, node.showCondition, predefined_code + " " + text, res, cabinet);

                }


            }
            else {
                node.visible = true;
            }
            for (let key in node._variables) {
                variables[key] = node._variables[key].value as string;
            }

            if (node._children) {
                for (let child of node._children) {
                    visite_node(child);
                }
            }
            variables = { ...record_variables };
        }
        visite_node(cabinet);
        cabinet.visible = true;



    }
    public parseCabinet(ele: Element) {
        if (ele.tagName === "Part") {
            let cabinetPart = this.parseCupboardPart(ele);
            this.updateVisibleOfCupboard(cabinetPart);
            cabinetPart._is_parts_cabinet = true;
            return cabinetPart;
        }
        else {
            let cabinetData = this._getCabinetFromEle(ele);
            return cabinetData;
        }
    }

}