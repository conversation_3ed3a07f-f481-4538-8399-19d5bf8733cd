import { Box3, Vector3 } from "three";

import { checkIsMobile, ENV } from "@/config";

import { compareNames, formatCurrentTime, ZEdge, ZPolygon, ZRect } from "@layoutai/z_polygon";
import { TAppManagerBase } from "../../../AppManagerBase";
import { EventName } from "../../../EventSystem";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { AI_CadData } from "../../AICadData/AI_CadData";
import { DesignXmlMaker } from "../../AICadData/DesignXmlMaker";
import { I_EzdxfJsonData } from "../../AICadData/EzdxfEntity";
import { I_SwjRemodelingRequestData, I_SwjRoom, I_SwjXmlScheme, SchemeSourceType } from "../../AICadData/SwjLayoutData";
import { CustomerInfo, HouseTypeParam } from "../../Services/Basic/LayoutSchemeService";
import { Logger } from "../../Utils/logger";
import { deflate_to_base64_str, uploadFileToOss } from "../../Utils/xml_utils";
import { DrawingFigureMode, I_Door, I_Layout, I_Room, IRoomEntityRealType, IRoomEntityType } from "../IRoomInterface";
import { SolverMethods } from "../TAppSolvers/TSwjLayoutGraphSolver";
import { TEntityCombiner } from "../TEntityCombiner/TEntityCombiner";
import { TEntitySelector } from "../TEntitySelector/TEntitySelector";
import { TEntityTransformer } from "../TEntityTransformer/TEntityTransformer";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TGroupTemplate } from "../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TPostLayoutCeiling } from "../TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutCeiling";
import { TPostProcessLayout } from "../TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostProcessLayout";
import { TWholeLayoutScheme } from "../TLayoutScheme/TWholeLayoutScheme";
import { TRoom } from "../TRoom";
import { TRoomShape } from "../TRoomShape";
import { LayoutSchemeJsonSaver } from "./loader/LayoutSchemeJsonSaver";
import { LayoutSchemeXmlJsonParser } from "./loader/LayoutSchemeXmlJsonParser";
import { TRoomTemplateSaver } from "./loader/TRoomTemplateSaver";
import { TBaseEntity } from "./TBaseEntity";
import { TBaseGroupEntity } from "./TBaseGroupEntity";
import { TCombinationEntity } from "./TCombinationEntity";
import { TExtDrawingEntity } from "./TExtDrawingElements/TExtDrawingEntity";
import { TFillLightEntity } from "./TFillLightEntity";
import { TFurnitureEntity } from "./TFurnitureEntity";
import { TGroupTemplateEntity } from "./TGroupTemplateEntity";
import { TOutterWallFacesEntity } from "./TOutterWallFacesEntity";
import { TRoomEntity } from "./TRoomEntity";
import { TRoomSpaceMarkEntity } from "./TRoomSpaceMarkEntity";
import { TRulerEntity } from "./TRulerEntity";
import { TStructureEntity } from "./TStructureEntity";
import { TSubSpaceAreaEntity } from "./TSubSpaceAreaEntity";
import { TWall } from "./TWall";
import { TWindowDoorEntity } from "./TWinDoorEntity";
import { LayoutContainerUtils } from "./utils/LayoutContainerUtils";


export const SelectPriority = "SelectPriority";

interface appletRoom {
    roomId: string;
    roomName: string;
    roomImage: string;
    scoreData: any;
    rate: number;
}

/**
 * 针对测试钩子的TypeScript接口定义
 */
export interface ITestHookRoomInfo {
    roomId: number;
    roomName: string;
    area: number;
    boundingBox: {
        min: { x: number; y: number; z: number };
        max: { x: number; y: number; z: number };
        center: { x: number; y: number; z: number };
        width: number;
        height: number;
    };
    posInScreen: { x: number; y: number };
    polygonPoints: { x: number; y: number; z: number }[];
    furnitureCount: number;
    hasSubAreas: boolean;
    furniture: number;
    hasLayoutSchemes: boolean;
}

export interface ITestHookFurnitureInfo {
    entityId: number;
    type: string;
    realType: string;
    isSingleFurniture: boolean;
    drawingOrder: number;
    roomId: number;
    roomName: string;
    category: string;
    subCategory: string;
    modelLoc: string;
    materialName: string;
    modelId: string;
    position: {
        x: number;
        y: number;
        z: number;
    };
    posInScreen: {
        x: number;
        y: number;
    };
    rotation: number;
    dimensions: {
        width: number;
        depth: number;
        height: number;
    };
    materialApplied: boolean;
}

interface ILayoutAITestHooks {
    getRoomInfo: () => ITestHookRoomInfo[];
    getFurnitureInfo: (roomId?: number) => ITestHookFurnitureInfo;
    getSelectedRoomId: () => number | null;
    getRenderingState: () => {
        hasRooms: boolean;
        hasFurniture: boolean;
        hasSelectedRoom: boolean;
    };
}
export interface I_EntitiesCollection {
    _room_entities?: TRoomEntity[];
    _wall_entities?: TWall[];
    _window_entities?: TWindowDoorEntity[];
    _structure_entities?: TStructureEntity[];
    _furniture_entities?: TFurnitureEntity[];
    _combination_entities?: TCombinationEntity[];
    _sub_area_entities?: TSubSpaceAreaEntity[];
    _basegroup_entities?: TBaseGroupEntity[];

    //  扩展的 绘图对象--- 跟3D无关
    _ext_drawing_entities?: TExtDrawingEntity[];
}

/**
 *   所有户型方案数据的容器层
 *    --- AI_CAD_DATA <-> Entities <-> TRoom的内容
 *    --- 逐渐去掉AI_CAD_DATA这个格式
 */
export class TLayoutEntityContainer {
    /**
     *   Cad原始数据
     */
    protected _ezdxf_cad_data: I_EzdxfJsonData;
    /**
     *  外墙面实体
     */
    _outter_wallface_entity: TOutterWallFacesEntity;
    _room_entities: TRoomEntity[];
    _wall_entities: TWall[];
    _window_entities: TWindowDoorEntity[];
    _structure_entities: TStructureEntity[];
    _furniture_entities: TFurnitureEntity[];
    _combination_entities: TCombinationEntity[];
    private _fill_light_entities: TFillLightEntity[];

    // 拆改种用的房间标记 
    _room_space_marks: TRoomSpaceMarkEntity[];
    /**
     *  子分区
     */
    _sub_area_entities: TSubSpaceAreaEntity[];


    //  扩展的 绘图对象--- 跟3D无关
    _ext_drawing_entities: TExtDrawingEntity[];


    _ai_cad_data: AI_CadData;


    _whole_bbox: Box3;

    // 外轮廓线 多边形
    _outter_border_polygons: ZPolygon[];

    /**
     *  当前生成的墙体内容
     */
    _rooms: TRoom[];


    _src_swj_layout_data: I_SwjXmlScheme;

    _current_swj_layout_data: I_SwjXmlScheme;

    _current_xml_is_regenerated: boolean = false;

    _layout_data: I_Layout;

    _material_id_public_category_map: Map<string, string>;

    _manager: TAppManagerBase;


    /**
     *  这是布局方案ID, 默认为空 --- 它可能并不保存在scheme_xml里面
     */
    _layout_scheme_id: string = null;
    _layout_scheme_name: string = null;
    _customer_info: CustomerInfo = null;
    _houseTypeParam: HouseTypeParam = null;
    // 以下是3D方案id
    _scheme_id: string;
    _layout_source: string;
    _is_platform: number; // 是否是平台方案

    _current_room_structure_update_timestamp: number;


    _is_from_3d_scheme: boolean = false;
    _storey_height: number = 2800;

    _ruler_entities: TRulerEntity[];
    _funcRequire: string;

    // city?: string;   //市代码
    // district?:string;   //区代码
    // province?:string;   //省代码
    houseType?: string;
    // towards?: string;  //朝向
    // houseName?: string;  //户型名称
    coverImageUrl: string;
    aidraw_img: string;
    hxId: string;
    /**
     *  当前选中的房间
     */
    public _selected_room: TRoom;

    public _drawing_layer_mode: "FullHouse" | "SingleRoom" | "AIMatching" | "AIViewEdit" = "FullHouse";


    protected _entity_selector: TEntitySelector;

    protected _entity_combiner: TEntityCombiner;

    protected _entity_transformer: TEntityTransformer;

    private _drawing_figure_mode: DrawingFigureMode = DrawingFigureMode.Figure2D;

    static CanvasForSaving: HTMLCanvasElement = null;


    /**
     *  重置用的方案
     */
    public _initial_scheme_data: I_SwjXmlScheme;

    // 临摹底图的Rect
    public copyImageRect: ZRect;

    private _remodeiling_data: I_SwjRemodelingRequestData;

    public _scheme_source: SchemeSourceType = null;

    public _saving_template_configs: {
        needCheckSameLayout: boolean
    } = { needCheckSameLayout: true };


    public _whole_layout_scheme_list: TWholeLayoutScheme[];

    private _curEditHouseSchemeId: string = null;

    /**
     * 
     * @param manager 
     */
    constructor(manager: TAppManagerBase) {
        // 便于调试
        (globalThis as any).TLayoutEntityContainer = this;

        this._manager = manager;
        this.registerGenerators();
        this._ezdxf_cad_data = null;
        this._entity_selector = new TEntitySelector(this);
        this._entity_combiner = new TEntityCombiner(this);
        this._entity_transformer = new TEntityTransformer(this);

        this.init();
        this._scheme_id = "";
        this._layout_source = null;
        this._ai_cad_data = new AI_CadData();
        this._rooms = [];
        this._material_id_public_category_map = new Map();
        this._current_room_structure_update_timestamp = 0;
        this._selected_room = null;
        this._whole_bbox = new Box3();
        this._remodeiling_data = null;
        this._whole_layout_scheme_list = null;
        this.coverImageUrl = null;
        this._funcRequire = '';
        this.houseType = '';

        this._scheme_source = null;

        // 注册测试钩子 - 仅在非生产环境启用
        if (ENV !== 'prod') {
            // 使用setTimeout确保DOM完全加载后再注册钩子
            setTimeout(() => this.registerTestHooks(), 1000);
        }
    }

    public registerGenerators() {
        TWall.RegisterGenerators();
        TRoomEntity.RegisterGenerators();
        TFurnitureEntity.RegisterGenerators();
        TBaseGroupEntity.RegisterGenerators();
        TWindowDoorEntity.RegisterGenerators();
        TStructureEntity.RegisterGenerators();
        TGroupTemplateEntity.RegisterGenerators();
        TCombinationEntity.RegisterGenerators();
        TBaseEntity._FuncGetOrMakeEntityOfCadRect = (rect: ZRect) => {
            let type = TBaseEntity.get_polygon_type(rect);
            if (rect._attached_elements[TGroupTemplate.EntityName]) {
                type = "GroupTemplate";
            }
            if (TBaseEntity.PolyEntityGenerator[type]) {
                return TBaseEntity.PolyEntityGenerator[type](rect);
            }
            return null;
        }
    }

    get curEditHouseSchemeId() {
        return this._curEditHouseSchemeId;
    }

    set curEditHouseSchemeId(id: string) {
        this._curEditHouseSchemeId = id;
    }

    get _scheme_name() {
        return this._layout_scheme_name;
    }

    set _scheme_name(s: string) {
        this._layout_scheme_name = s;
    }

    get room_space_marks() {
        return this._room_space_marks;
    }

    set room_space_marks(marks: TRoomSpaceMarkEntity[]) {
        this._room_space_marks = marks;
    }

    get drawing_figure_mode() {
        return this._drawing_figure_mode;
    }
    set drawing_figure_mode(mode: DrawingFigureMode) {
        this._drawing_figure_mode = mode;
    }
    get remodeiling_data() {
        return this._remodeiling_data;
    }
    set remodeiling_data(data: I_SwjRemodelingRequestData) {
        this._remodeiling_data = data;
    }

    get ezdxf_cad_data() {
        return this._ezdxf_cad_data;
    }
    set ezdxf_cad_data(data: I_EzdxfJsonData) {
        this._ezdxf_cad_data = data;
    }
    get manager() {
        return this._manager;
    }
    get painter() {
        return this._manager.painter;
    }

    get current_swj_layout_data() {
        if (!this._current_swj_layout_data) {
            this._current_swj_layout_data = this.toXmlSchemeData();
        }
        return this._current_swj_layout_data;
    }

    get swj_scheme_json_data() {
        return this.toXmlSchemeData();
    }

    set current_xml_is_regenerated(t: boolean) {
        this._current_xml_is_regenerated = t;
    }

    get current_xml_is_regenerated() {
        return this._current_xml_is_regenerated;
    }

    get scene3D() {
        return this.manager.scene3D;
    }

    get entity_selector() {
        return this._entity_selector;
    }

    get entity_combiner() {
        return this._entity_combiner;
    }

    get entity_transformer() {
        return this._entity_transformer;
    }
    containsEntity(entity: TBaseEntity) {
        if (entity.type === "Wall") {
            return this._wall_entities.includes(entity as TWall);
        }
        else if (entity.type === "Furniture") {
            return this._furniture_entities.includes(entity as TFurnitureEntity);
        }
        else if (entity.type === "Window" || entity.type === "Door") {
            return this._window_entities.includes(entity as any);
        }
        else if (entity.type === "RoomArea") {
            return this._room_entities.includes(entity as any);
        }
        return false;
    }
    // updateScene3D(force: boolean = true) {
    //     if (!this.scene3D) return;
    //     if (force) {
    //         this.scene3D.reset();
    //         this.scene3D.cleanWalls();
    //         this.scene3D.cleanWindows();
    //         this.scene3D.cleanFurnitures();
    //         this.scene3D.cleanRoomEntities();
    //         this.scene3D.updateShadowTexture();
    //         // this.scene3D.createOrbitControls();

    //         this.updateFillLights(true);

    //         for (let entity of this._window_entities)  // 先更新 门窗
    //         {
    //             let mesh = entity.updateMesh3D();
    //             if (mesh) {
    //                 this.scene3D.windows_group.add(mesh);
    //             }
    //         }

    //         for (let wall of this._wall_entities) {

    //             let wall_mesh = wall.updateMesh3D();
    //             if (wall_mesh) {
    //                 this.scene3D.walls_group.add(wall_mesh);
    //             }
    //         }
    //         for (let entity of this._room_entities) {
    //             this.scene3D.rooms_group.add(entity.updateMesh3D());
    //         }


    //         for (let entity of this._furniture_entities) {
    //             if (entity instanceof TBaseGroupEntity) {
    //                 let groupEntity = entity as TBaseGroupEntity;
    //                 if (groupEntity.figure_element.haveMatchedGroupMaterial() && !groupEntity.figure_element.isMaterialMarkAsInvisible()) {
    //                     this.scene3D.furniture_group.add(groupEntity.updateMesh3D());
    //                 } else {
    //                     groupEntity.combination_entitys.forEach((entity) => {
    //                         if (entity.figure_element.isMaterialMarkAsInvisible()) {
    //                             return;
    //                         }
    //                         this.scene3D.furniture_group.add(entity.updateMesh3D());
    //                         if (entity.figure_element.decorationElements) {
    //                             entity.figure_element.decorationElements.forEach((decoration: TFigureElement) => {
    //                                 if (decoration.haveMatchedMaterial() && !decoration.isMaterialMarkAsInvisible()) {
    //                                     this.scene3D.furniture_group.add(decoration.updateMesh3D());
    //                                 }
    //                             })
    //                         }
    //                     });
    //                 }
    //             } else {
    //                 let figure_mesh = entity.updateMesh3D();
    //                 if (figure_mesh && !entity.figure_element.isMaterialMarkAsInvisible()) {
    //                     this.scene3D.furniture_group.add(figure_mesh);
    //                 }
    //                 if (entity.figure_element.rect._attached_elements[TFigureElement.DecorationElements]) {
    //                     entity.figure_element.rect._attached_elements[TFigureElement.DecorationElements].forEach((decoration: TFigureElement) => {
    //                         if (decoration.haveMatchedMaterial() && !decoration.isMaterialMarkAsInvisible()) {
    //                             this.scene3D.furniture_group.add(decoration.updateMesh3D());
    //                         }
    //                     })
    //                 }
    //             }
    //         }

    //         if (this._rooms) {
    //             let lights: TFigureElement[] = [];
    //             let wallDecorations: TFigureElement[] = [];
    //             this._rooms.forEach((room) => {
    //                 // if(!compareNames([room.roomname],["客餐厅","卧室"])) return;

    //                 lights.push(...room._furniture_list.filter(light => compareNames([light.category], ["主灯"])))
    //                 wallDecorations.push(...room._furniture_list.filter(decoration => !decoration.furnitureEntity && compareNames([decoration.category], ["墙饰"])))
    //             });

    //             this.scene3D.addNightLights(lights);
    //             wallDecorations.forEach((decoration) => {
    //                 if (decoration.haveMatchedMaterial() && !decoration.isMaterialMarkAsInvisible()) {
    //                     this.scene3D.furniture_group.add(decoration.updateMesh3D());
    //                 }
    //             })
    //         }


    //         // this.scene3D.setCenter(this.painter.p_center);
    //         /**
    //          *   用自身等于自身的方法, 触发一次更新
    //          */
    //         const temp = this.scene3D.entity_mesh_mode;
    //         this.scene3D.entity_mesh_mode = temp;

    //         if (this.scene3D.active_controls) {
    //             this.scene3D.active_controls.updateMeshesVisible();
    //         }
    //     }
    //     else {
    //         let mesh_groups = [this.scene3D.furniture_group, this.scene3D.walls_group, this.scene3D.windows_group];
    //         for (let group of mesh_groups) {
    //             let to_remove_meshes: Mesh[] = [];

    //             for (let mesh of group.children) {
    //                 let entity = mesh.userData[UserDataKey.EntityOfMesh];
    //                 if (!entity || !this.containsEntity(entity)) {
    //                     to_remove_meshes.push(mesh as any);
    //                 }
    //             }
    //             group.remove(...to_remove_meshes);
    //         }

    //         this.scene3D.updateShadowTexture();
    //         this.scene3D.updateLightingTexture();
    //         // this.scene3D.createOrbitControls();
    //         for (let entity of this._window_entities)  // 先更新 门窗
    //         {
    //             let mesh = entity.updateMesh3D();
    //             if (mesh) {
    //                 this.scene3D.windows_group.add(mesh);
    //             }
    //         }

    //         for (let wall of this._wall_entities) {

    //             let wall_mesh = wall.updateMesh3D();
    //             if (wall_mesh) {
    //                 this.scene3D.walls_group.add(wall_mesh);
    //             }
    //         }
    //         for (let entity of this._room_entities) {
    //             this.scene3D.walls_group.add(entity.updateMesh3D());
    //         }


    //         for (let entity of this._furniture_entities) {
    //             let figure_mesh = entity.updateMesh3D();
    //             if (figure_mesh && !entity.figure_element.isMaterialMarkAsInvisible()) {
    //                 this.scene3D.furniture_group.add(figure_mesh);
    //             }

    //         }




    //         // this.scene3D.setCenter(this.painter.p_center);
    //         /**
    //          *   用自身等于自身的方法, 触发一次更新， 用变量的形式去修改
    //          */
    //         const temp = this.scene3D.entity_mesh_mode;
    //         this.scene3D.entity_mesh_mode = temp;

    //         if (this.scene3D.active_controls) {
    //             this.scene3D.active_controls.updateMeshesVisible();
    //         }
    //     }

    // }
    // async updateDesignMaterialInfos()
    // {
    //     for(let room of this._rooms)
    //     {
    //         await TDesignMaterialUpdater.instance.updateRoomFurnitureDesignMaterials(room,null);
    //     }
    // }
    makeXmlStr() {
        let design_xml_maker = new DesignXmlMaker();
        let doc = design_xml_maker.makeByEntities(this);
        let str = new XMLSerializer().serializeToString(doc);
        str = deflate_to_base64_str(str);
        return str;


    }
    updateSwjLayoutDataXml() {
        if (!this.current_swj_layout_data.xml_str) {
            let design_xml_maker = new DesignXmlMaker();
            let doc = design_xml_maker.makeByEntities(this);

            let str = new XMLSerializer().serializeToString(doc);

            str = deflate_to_base64_str(str);

            this.current_swj_layout_data.xml_str = str;

        }
    }

    /**
     *  是否需要重新生成墙体
     */
    get needs_making_wall_xml() {
        if (this._ai_cad_data) {
            return this._current_room_structure_update_timestamp !== this._ai_cad_data._room_structure_update_timestamp;

        }

        return true;
    }
    set needs_making_wall_xml(t: boolean) {
        if (!t) {
            this._current_room_structure_update_timestamp = this._ai_cad_data._room_structure_update_timestamp;
        }
        else {
            if (this._current_room_structure_update_timestamp == this._ai_cad_data._room_structure_update_timestamp) {
                this._current_room_structure_update_timestamp = this._ai_cad_data._room_structure_update_timestamp - 1;
            }
        }
    }
    init() {
        this._room_entities = [];
        this._wall_entities = [];
        this._window_entities = [];
        this._structure_entities = [];
        this._furniture_entities = [];
        this._fill_light_entities = [];
        this._combination_entities = [];
        this._sub_area_entities = [];
        this._room_space_marks = [];
        this._layout_data = null;
        this._rooms = [];
        this._selected_room = null;
        this._ext_drawing_entities = [];
        this._ruler_entities = [];
        this._scheme_source = null;
        this.hxId = null;
        if (this._outter_wallface_entity) {
            delete this._outter_wallface_entity;
        }
        this._outter_wallface_entity = new TOutterWallFacesEntity(this);
        this._outter_border_polygons = [];

        this._entity_selector.clear();


    }
    // 清除方案信息 用于顶菜新建按钮
    public clearSchemeInfo() {
        this.init();
        this._layout_scheme_id = null;
        this._layout_scheme_name = null;
        this._remodeiling_data = null;
        this._customer_info = null;
        this._funcRequire = null;
        LayoutAI_App.emit(EventName.setMultiSchemeListVisible, false);
    }

    /**
     *  是否有原始Cad数据
     */
    get has_CadData() {
        if (this._ai_cad_data && this._ai_cad_data._wall_rects.length > 0) {
            return true;
        }
        return false;
    }

    generateSchemeId() {
        this._scheme_id = formatCurrentTime({ format_type: 3 });
    }
    fromXmlSchemeData(xmlScheme: I_SwjXmlScheme, post_processing: boolean = true, schemeSource: SchemeSourceType = null) {
        LayoutSchemeXmlJsonParser.Container = this;
        LayoutSchemeXmlJsonParser.fromXmlSchemeData(xmlScheme, post_processing, schemeSource);

    }


    toXmlSchemeData() {
        LayoutSchemeXmlJsonParser.Container = this;
        return LayoutSchemeXmlJsonParser.toXmlSchemeData();
    }


    async exportWireFrameImageJson() {
        let wireFrameImageJson: any = {};
        for (let entity of this._furniture_entities) {
            if (entity._figure_element && entity._figure_element._wireFrameImage) {
                wireFrameImageJson[entity._figure_element._matched_material.nodeId] = entity._figure_element._wireFrameImage?.src;
            }
            if (entity instanceof TBaseGroupEntity) {
                for (let sub_entity of entity.combination_entitys) {
                    wireFrameImageJson[sub_entity._figure_element._matched_material.nodeId] = sub_entity._figure_element._wireFrameImage?.src;
                }
            }
        }
        const dataStr = JSON.stringify(wireFrameImageJson, null, 2);
        const file = new File([dataStr], 'wireFrameImageJson.json', { type: 'application/json' }); // 创建 File 对象
        let url = await uploadFileToOss(file);
        return url;
    }


    /**
     *  保存户型数据
     */
    exportHouseLayoutData() {
        let data: I_SwjXmlScheme = {};

        data.wall_list = [];

        for (let wall_entity of this._wall_entities) {
            data.wall_list.push(wall_entity.exportData());
        }
        data.window_list = [];
        data.door_list = [];

        for (let win of this._window_entities) {
            if (win.type === "Window") {
                data.window_list.push(win.exportData());
            }
            else {
                data.door_list.push(win.exportData());
            }
        }
        return data;
    }

    importHouseLayoutData(data: I_SwjXmlScheme) {

    }

    saveLayoutSchemeImage(width: number = 1200, height: number = 1200, fixed_scale: number = 0, save_room_name: boolean = false, selected_room: TRoom = null) {
        return LayoutSchemeJsonSaver.saveLayoutSchemeImage(width, height, fixed_scale, save_room_name, selected_room);
    }

    /**
     * 保存方案
     * @returns 
     */
    async saveSchemeLayout2Json(): Promise<string> {
        return await this.saveSchemeLayout2JsonAs({
            schemename: this._layout_scheme_name,
            schemeId: this._layout_scheme_id,
            username: this._customer_info?.cntactMan,
            telephone: this._customer_info?.mobile,
            address: this._customer_info?.address,
        });
    }

    /**
     * 方案另存为
     * @param schemeParams  
     * @param enableDownload 
     * @param enableUpload 
     * @returns 
     */
    async saveSchemeLayout2JsonAs(
        schemeParams:
            {
                schemename: string,
                username: string,
                telephone: string,
                address: string,
                schemeId: string,
            },
        enableDownload: boolean = false, enableUpload: boolean = true): Promise<string> {

        let res = await LayoutSchemeJsonSaver.saveSchemeLayout2JsonAs(schemeParams, enableDownload, enableUpload);

        return res;

    }
    updateEntityRelations() {

        this.updateInwallWindows();

        this.updateInRoomWindows();

        for (let wall of this._wall_entities) {
            wall.initWallNeighbors();
            for (let other_wall of this._wall_entities) {
                if (wall === other_wall) continue;

                wall.checkAddWallNeighbor(other_wall);
            }
        }
        for (let entity of this._sub_area_entities) {
            entity.bindRoomEntityFromList(this._room_entities);
        }
        for (let entity of this._room_entities) {
            entity.updateRoomNeighbors();

        }
    }
    updateSubAreasInRooms() {
        this._sub_area_entities.length = 0;
        this._room_entities.forEach((room_entity) => this._sub_area_entities.push(...room_entity._sub_room_areas));
    }
    updateInwallWindows() {
        for (let wall of this._wall_entities) {
            wall.initWindowRects();
        }
        for (let win of this._window_entities) {
            win._wall_rect = null;
            for (let wall of this._wall_entities) {
                win.bindWallRect(wall.rect);
            }

            if (win._wall_rect) {
                let entity = TWall.getOrMakeEntityOfCadRect(win._wall_rect) as TWall;
                entity._win_rects.push(win.rect);
            }
        }
    }
    updateInRoomWindows() {
        for (let entity of this._window_entities) {
            entity.initRoomEntities();
        }
        for (let entity of this._room_entities) {
            entity.computeMainRect();
            entity._num_of_all_rooms = this._room_entities.length;
            entity.initWindowRects();
            entity.initStructureRects();

            for (let win of this._window_entities) {
                entity.addAndBindWinRect(win.rect);
            }

            for (let structure of this._structure_entities) {
                entity.addAndBindStructureRect(structure.rect);
            }
        }

        for (let room_entity of this._room_entities) {
            let win_rects = room_entity._win_rects;

            for (let rect of win_rects) {
                let entity = TWindowDoorEntity.getOrMakeEntityOfCadRect(rect);

                if (entity.realType === "BayWindow") {
                    let target_edge: ZEdge = null;
                    let min_dist = rect.h + 300;
                    for (let edge of room_entity._room_poly.edges) {
                        if (Math.abs(edge.nor.dot(rect.nor)) < 0.9) continue;

                        let pp = edge.projectEdge2d(rect.rect_center);

                        if (pp.x < 0 || pp.x > edge.length) continue;

                        if (Math.abs(pp.y) < Math.abs(min_dist)) {
                            target_edge = edge;
                            min_dist = pp.y;
                        }
                    }

                    if (target_edge) {
                        // rect.nor = target_edge.nor;
                        let pp = target_edge.projectEdge2d(rect.rect_center);
                        rect.nor = target_edge.nor;
                        rect.back_center = target_edge.unprojectEdge2d({ x: pp.x, y: 0 });
                        rect.updateRect();
                    }

                }

                (entity as TWindowDoorEntity).addRoomEntity(room_entity);
            }
        }

    }
    /**
     *  根据邻居调整墙的矩形
     */
    adjustWallsByNeighbors() {
        for (let wall of this._wall_entities) {
            wall.adjustRectByNeighbors();

            // if(wall.rect.max_hh > 300)
            {
                wall.realType = "InnerWall";
            }

        }

    }

    /**
     * 根据墙体计算房间多边形
     * @param clean_old 是否清空原有房间实体列表
     * @param use_overlap 是否使用重叠度匹配策略(true:使用最大重叠度匹配, false:使用最小距离匹配)
     */
    _computeRoomPolysByWall(clean_old: boolean = true, use_overlap: boolean = true) {
        let polys: ZPolygon[] = [];
        let extend_len = 1.;
        for (let wall of this._wall_entities) {
            let rect = wall.rect.clone();
            let r_center = rect.rect_center;
            rect._w += extend_len;
            rect._h += extend_len;
            rect.rect_center = r_center;
            rect.reOrderByOrientation(true);
            polys.push(rect);
        }

        if (polys.length == 0) return;
        let res_polygons = polys[0].union_polygons(polys);
        for (let poly of res_polygons) {
            poly._attached_elements['main_area'] = 99999;

            if (poly.orientation_z_nor.z > 0) {
                continue;
            }
            let main_rect = TRoomShape.computeMaxRectBySplitShape(poly);
            poly._attached_elements['main_rect'] = main_rect;
            poly._attached_elements['main_area'] = main_rect.w / 10000 * main_rect.h / 1000;

        }

        res_polygons.sort((a, b) => b._attached_elements['main_area'] - a._attached_elements['main_area'])
        // this._room_entities.forEach(room=>console.log(room._main_rect.w,room._main_rect.h,room.rect.w,room.rect.h,room.roomname))
        let outter_polygons: ZPolygon[] = [];

        let old_room_entities: TRoomEntity[] = [...this._room_entities];
        if (clean_old) {
            this._room_entities.length = 0;
        }

        let inner_room_polys: ZPolygon[] = [];


        for (let poly of res_polygons) {
            if (poly.orientation_z_nor.z > 0) {
                poly.expandPolygon(-extend_len / 2);
                outter_polygons.push(poly);
                continue;
            }
            else {
                poly.expandPolygon(extend_len / 2);
                inner_room_polys.push(poly);
            }
        }
        old_room_entities.sort((a, b) => b._area - a._area);
        let updated_polys: ZPolygon[] = [];

        old_room_entities.forEach(room_entity => {
            let origin_poly = room_entity._room_poly;
            let target_poly: ZPolygon = null;
            let min_dist = 100000;
            let max_overlap = 0;

            // 合并两种匹配策略的循环
            for (let poly of inner_room_polys) {
                if (updated_polys.includes(poly)) continue;
                let res = poly.comparePolyDistance(origin_poly);
                if (use_overlap) {
                    // 使用最大重叠度策略
                    if (res.overlap_length > max_overlap) {
                        max_overlap = res.overlap_length;
                        target_poly = poly;
                    }
                } else {
                    // 使用最小距离策略
                    if (res.sum < min_dist) {
                        min_dist = res.sum;
                        target_poly = poly;
                    }
                }
            }

            // 如果找到匹配的多边形,更新房间实体
            if (target_poly) {
                updated_polys.push(target_poly);
                room_entity.updateByPoint(target_poly.positions);

                if (clean_old) {
                    this._room_entities.push(room_entity);
                }
            }
        });

        for (let poly of inner_room_polys) {
            if (!updated_polys.includes(poly)) {
                let target_entity = new TRoomEntity(poly);
                target_entity.update();
                target_entity.bindEntity();
                this._room_entities.push(target_entity);
            }

        }


        for (let poly of outter_polygons) {
            for (let edge of poly.edges) {
                for (let wall of this._wall_entities) {
                    let rect = wall.rect;
                    if (edge.islayOn(rect.backEdge, rect.h + 65, 0.5)) {
                        let r_center = wall.rect.rect_center;
                        wall.rect.nor = edge.nor;
                        wall.rect.rect_center = r_center;
                        // wall.realType = "OutterWall";
                    }
                }
            }
        }

        this._outter_border_polygons = outter_polygons;

        this._outter_wallface_entity.initByPolygons(outter_polygons, inner_room_polys);


    }

    _computeOutterBorderPolys() {
        let polys: ZPolygon[] = [];
        let extend_len = 0.2;
        for (let wall of this._wall_entities) {
            let rect = wall.rect.clone();
            let r_center = rect.rect_center;
            rect._w += extend_len;
            rect._h += extend_len;
            rect.rect_center = r_center;
            rect.reOrderByOrientation(true);
            polys.push(rect);
        }

        if (polys.length == 0) return;
        let res_polygons = polys[0].union_polygons(polys);
        let inner_room_polys: ZPolygon[] = [];
        let outter_polygons: ZPolygon[] = [];

        for (let poly of res_polygons) {
            if (poly.orientation_z_nor.z > 0) {
                poly.expandPolygon(-extend_len / 2);
                outter_polygons.push(poly);
                continue;
            }
            else {
                poly.expandPolygon(extend_len / 2);
                inner_room_polys.push(poly);
            }
        }
        this._outter_border_polygons = outter_polygons;

        this._outter_wallface_entity.initByPolygons(outter_polygons, inner_room_polys);

    }



    makeGroupOfAllFunitureEntities() {

    }
    unGroupOfAllFurnitureEntities() {

        let group_template_entities: TGroupTemplateEntity[] = this._furniture_entities.filter((entity) => {
            return !entity.is_single_furniture && entity.type !== 'BaseGroup';
        }) as TGroupTemplateEntity[];

        group_template_entities.forEach(entity => {
            entity.disassembled = true;
            let id = this._furniture_entities.indexOf(entity);
            if (id >= 0) {
                this._furniture_entities.splice(id, 1);
            }
            let figure_elements = entity.figure_elements;
            for (let fig of figure_elements) {
                let furniture_entity = new TFurnitureEntity(fig);
                furniture_entity.bindFigureEntities();
                this._furniture_entities.push(furniture_entity);
            };
        });
    }

    unBaseGroupOfAllFurnitureEntities() {

        let basegroup_entities: TBaseGroupEntity[] = this._furniture_entities.filter((entity) => {
            return entity.type === "BaseGroup";
        }) as TBaseGroupEntity[];

        basegroup_entities.forEach(entity => {

            let id = this._furniture_entities.indexOf(entity);
            if (id >= 0) {
                this._furniture_entities.splice(id, 1);
            }
            for (let t_entity of entity.combination_entitys) {
                t_entity.bindFigureEntities();
                this._furniture_entities.push(t_entity);
            };
        });

    }

    // TGroupTemplateEntity转换为TBaseGroupEntity
    transformGroup() {
        let group_template_entities: TGroupTemplateEntity[] = this._furniture_entities.filter((entity) => {
            return !entity.is_single_furniture && entity.type !== 'BaseGroup';
        }) as TGroupTemplateEntity[];

        group_template_entities.forEach(entity => {
            entity.disassembled = true;
            let id = this._furniture_entities.indexOf(entity);
            if (id >= 0) {
                this._furniture_entities.splice(id, 1);
                let basegroup_entity = entity.toBaseGroupEntity();
                this._furniture_entities.push(basegroup_entity);
            }

        });

    }

    updateRoomsFromEntities(post_processing: boolean = true, force: boolean = false) {
        this._rooms = [];
        for (let entity of this._room_entities) {
            let room_poly = entity._room_poly;
            if (room_poly.orientation_z_nor.z > 0) continue;
            let points: number[][] = [];
            for (let v of room_poly.vertices) {
                points.push([v.pos.x, v.pos.y, v.pos.z]);
            }

            entity._room = entity.makeTRoom(this._furniture_entities, force);

            this._rooms.push(entity._room);
        }
        // this._furniture_entities = [];
        if (post_processing) {
            for (let room of this._rooms) {
                TPostProcessLayout.post_add_patching_boards(room);
            }
        }

        this.findRoomDoorFromHouseStructure();
        for (let room of this._rooms) {
            TPostLayoutCeiling.instance.postAddCeilingFigures(room);
        }
        this._room_entities.forEach((entity) => {
            entity._updateTabletopEntities();
        })
        // if (debug) console.log("从Entities生成的TRooms", this._rooms);
    }

    findAndBindRoomForFurnitures(furnitureEntities: TFurnitureEntity[]): void {
        furnitureEntities.forEach((furnitureEntity) => {
            let targetRoomEntity = this._room_entities.find((roomEntity) => {
                return roomEntity._room_poly.containsPoint(furnitureEntity.position);
            });
            furnitureEntity.roomEntity = targetRoomEntity;
            if(furnitureEntity instanceof TBaseGroupEntity) {
                furnitureEntity.combination_entitys.forEach((entity) => {
                    entity.roomEntity = targetRoomEntity;
                });
            }
            if (targetRoomEntity?._sub_room_areas) {
                let target_area_entity = targetRoomEntity._sub_room_areas.find((area_entity) => {
                    return area_entity.rect.containsPoint(furnitureEntity.position);
                });
                if (target_area_entity) {
                    furnitureEntity.area_entity = target_area_entity;
                }
            }

        });



    }

    findRoomDoorFromHouseStructure() {
        if (!this._src_swj_layout_data) return;

        let roomDoorsMap = new Map<TRoom, I_Door[]>();
        for (let entity of this._room_entities) {
            let room = entity._room;
            let room_poly = entity._room_poly;
            let roomDoors: I_Door[] = [];
            for (let r = 0; r < this._src_swj_layout_data.room_list.length; r++) {
                if (this._src_swj_layout_data.room_list[r].uid != room.room_id) continue;
                let swjRoom = this._src_swj_layout_data.room_list[r];
                // console.log(swjRoom.name + ","+swjRoom.area.toFixed(2));
                swjRoom.door_list.forEach(door => {
                    if (room_poly.distanceToPoint(new Vector3(door.pos_x, door.pos_y)) < 200) {
                        // console.log("  " + door.uid + ":"+ door.type + "," + door.material_id + ",(" + door.pos_x + "," + door.pos_y + ")");
                        roomDoors.push({
                            id: door.uid,
                            pos_x: door.pos_x,
                            pos_y: door.pos_y,
                            pos_z: door.pos_z,
                            length: door.length,
                            width: door.width,
                            height: door.height,
                            rotateZ: door.rotate_z,
                            type: "Door",
                            realType: door.type
                        } as I_Door);
                    }
                });
            }
            roomDoorsMap.set(room, roomDoors);
        }

        let belongtoDoorIds = new Set();

        let loop = true;
        while (loop) {
            let roomsWidthDoors = new Set<TRoom>();
            let minimumDoorCount = 10000;
            for (let doors of roomDoorsMap.values()) {
                if (doors.length < minimumDoorCount) {
                    minimumDoorCount = doors.length;
                }
            }
            for (let [room, doors] of roomDoorsMap.entries()) {
                if (doors && doors.length == minimumDoorCount) {
                    room.doors = doors;
                    roomsWidthDoors.add(room);
                    doors.forEach(item => {
                        belongtoDoorIds.add(item.id);
                    });
                }
            }
            for (let room of roomsWidthDoors) {
                roomDoorsMap.delete(room);
            }
            for (let [room, doors] of roomDoorsMap.entries()) {
                doors = doors.filter(door => !belongtoDoorIds.has(door.id));
                roomDoorsMap.set(room, doors);
            }
            if (roomDoorsMap.size == 0) {
                loop = false;
            }
        }
        for (let [room, doors] of roomDoorsMap.entries()) {
            room.doors = doors;
        }

        // 处理：同时用在多个room的door
        // 步骤1：先统计每个door对应的room有几个，保存至doorId2RoomMap；然后找出同时用在多个room的door，保存至doorsWithRooms
        let doorId2RoomMap: Map<number, TRoom[]> = new Map();
        let doorsWithRooms: Set<I_Door> = new Set();
        for (let entity of this._room_entities) {
            let theRoom: TRoom = entity._room;
            if (theRoom.doors != null) {
                theRoom.doors.forEach((door) => {
                    if (doorId2RoomMap.has(door.id)) {
                        //已经出现过的door,说明这个door跟不止一个room关联
                        doorsWithRooms.add(door);
                        doorId2RoomMap.set(door.id, [theRoom, ...doorId2RoomMap.get(door.id)]);
                    } else {
                        //第一次出现的door
                        doorId2RoomMap.set(door.id, [theRoom]);
                    }
                });
            }
        }

        // 步骤2：处理同时用在2个及2个以上room的door
        doorsWithRooms.forEach((door) => {
            //找到此door对应的所有room
            let rooms: TRoom[] = doorId2RoomMap.get(door.id);
            if (rooms.length <= 1) return;
            //是否处理过的标记
            let handledFlag: boolean = false;
            //优先将door归属于阳台（从其他room中将此door删掉）
            if (rooms.filter((room) => room.roomname == "阳台").length > 0) {
                rooms.forEach((room) => {
                    if (room.roomname != "阳台") {
                        room.doors = room.doors.filter(item => item.id != door.id);
                        handledFlag = true;
                    }
                });
            }
            //然后优先将door归属于卫生间（从其他room中将此door删掉）
            if (!handledFlag && rooms.filter((room) => room.roomname == "卫生间").length > 0) {
                rooms.forEach((room) => {
                    if (room.roomname != "卫生间") {
                        room.doors = room.doors.filter(item => item.id != door.id);
                        handledFlag = true;
                    }
                });
            }
            if (!handledFlag) {
                //如果以上两种情况都没有，随机选择一个room，将此door归属于此room
                for (let i = 0; i < rooms.length - 1; i++) {
                    rooms[i].doors = rooms[i].doors.filter(item => item.id != door.id);
                }
            }
        });

        if (ENV != 'prod' && false) {
            let debugLog = "";
            for (let entity of this._room_entities) {
                let room = entity._room;
                debugLog += room.name + " " + room.area.toFixed(2) + ": ";
                if (room.doors != null) {
                    room.doors.forEach(door => {
                        debugLog += door.id + "," + door.realType + ",(" + door.pos_x.toFixed(0) + "," + door.pos_y.toFixed(0) + ")" + "; ";
                    });
                }
                debugLog = debugLog.substring(0, debugLog.length - 2);
                debugLog += "\n";
            }
            debugLog = debugLog.substring(0, debugLog.length - 1);
            console.log(debugLog);
        }
    }

    /**
     *  getFurnitureEntitiesFromFigureElements: 兼容组合模板
     */
    static getFurnitureEntitiesFromFigureElements(figure_elements: TFigureElement[], options: { needs_make_group_templates?: boolean, make_base_group?: boolean } = { needs_make_group_templates: true, make_base_group: false }) {
        let group_figure_elements: TFigureElement[] = [];
        let funiture_entities: TFurnitureEntity[] = [];
        for (let ele of figure_elements) {
            if (options.needs_make_group_templates && ele._group_uuid && ele._group_uuid.length > 0) {
                group_figure_elements.push(ele);
                continue;
            }
            let entity = new TFurnitureEntity(ele);
            entity.bindFigureEntities();
            funiture_entities.push(entity as TFurnitureEntity);
        }
        if (!options.needs_make_group_templates) return funiture_entities;
        /**
         *  暂时用提取组合模板的方式来处理
         */
        let group_templates = TGroupTemplate.extractGroupTemplatesByGroupUUid(group_figure_elements, true);

        // console.log(group_templates,figure_elements,group_figure_elements);
        for (let group_template of group_templates) {
            if (!group_template.current_s_group) {
                console.warn("存在找不到的组合模板!", group_template);
                continue;
            }
            // 如果只有一个图元
            if (group_template.current_s_group.figure_elements.length == 1) {
                let ele = group_template.current_s_group.figure_elements[0];
                let entity = new TFurnitureEntity(ele);
                entity.bindFigureEntities();
                funiture_entities.push(entity as TFurnitureEntity);
            }
            else {
                if (options.make_base_group) {
                    let entity = TBaseGroupEntity.fromGroupTemplate(group_template);
                    entity.bindFigureEntities();
                    funiture_entities.push(entity as TFurnitureEntity);
                }
                else {
                    let entity = new TGroupTemplateEntity(group_template);
                    entity.bindFigureEntities();
                    funiture_entities.push(entity as TFurnitureEntity);
                }

            }
        }
        return funiture_entities;
    }

    static getFigureElementsFromFurnitureEntities(furniture_entities: TFurnitureEntity[]) {
        let figure_elements: TFigureElement[] = [];

        for (let ele of furniture_entities) {
            figure_elements.push(...ele.figure_elements);
        }
        return figure_elements;
    }

    /**
     * 从rooms获取所有的家具数据
     * @returns  
     */
    fromRoomsToEntities() {
        if (!this._rooms) return;


        this._ai_cad_data._figure_rects = [];
        this._furniture_entities = [];
        for (let room of this._rooms) {
            TPostProcessLayout.post_remove_patching_boards(room);

            let figure_elements = room._furniture_list;

            let funiture_entities = TLayoutEntityContainer.getFurnitureEntitiesFromFigureElements(figure_elements, { needs_make_group_templates: false, make_base_group: false });

            this._furniture_entities.push(...funiture_entities);


        }

        for (let entity of this._furniture_entities) {
            this._ai_cad_data._figure_rects.push(entity.rect);
        }

    }




    /**
     *  更新WholeRoom包围盒
     */
    updateWholeBox(ref_by_room: boolean = true) {
        if (ref_by_room && this._room_entities && this._room_entities.length > 0) {
            this._whole_bbox.makeEmpty();

            for (let room of this._room_entities) {
                this._whole_bbox.union(room._room_poly.computeBBox());
            }
            let wall_thickness = 240;
            this._whole_bbox.max.x += wall_thickness;
            this._whole_bbox.min.x -= wall_thickness;

            this._whole_bbox.max.y += wall_thickness;
            this._whole_bbox.min.y -= wall_thickness;
        }
        else {
            if (this._wall_entities && this._wall_entities.length > 0) {
                this._whole_bbox.makeEmpty();

                for (let wall of this._wall_entities) {
                    this._whole_bbox.union(wall.rect.computeBBox());
                }
            }
            else {
                this._whole_bbox.makeEmpty();
                this._whole_bbox.expandByPoint({ x: -1000, y: -1000, z: 0 } as Vector3);
                this._whole_bbox.expandByPoint({ x: 1000, y: 1000, z: 0 } as Vector3);

            }
        }


    }


    checkIfOutOfView() {
        if (this._whole_bbox) {
            let bbox = new Box3();
            let points = this.painter.getCanvasRectPoints();

            points.forEach(p => bbox.expandByPoint(p as Vector3));

            bbox.min.z = -10000;
            bbox.max.z = 10000;

            return !(bbox.containsBox(this._whole_bbox));
        }
        else {
            return false;
        }
    }
    focusCenter() {
        if (this._wall_entities.length == 0) return;
        let t_points: Vector3[] = [];
        for (let wall_entity of this._wall_entities) {
            t_points.push(...wall_entity.rect.positions);
        }
        let iter = 1;
        let b_center = new Vector3();
        if (t_points.length == 0) return;
        let b_rect: ZRect = null;
        while (iter--) {
            let sum_point: Vector3 = new Vector3();
            for (let p of t_points) {
                sum_point.add(p);
            }
            sum_point.divideScalar(t_points.length);

            b_center = sum_point;
            let t_radius = 0;
            for (let p of t_points) {
                t_radius = Math.max(p.distanceTo(sum_point), t_radius);
            }

            let p_points: Vector3[] = [];
            for (let p of t_points) {
                if (p.distanceTo(sum_point) > t_radius * 0.9) {
                    continue;
                }
                p_points.push(p);
            }
            if (p_points.length > 20) {
                t_points = p_points;
            }
            else {
                break;
            }

        }

        // console.log(b_center);
        this.painter.p_center = b_center;
    }

    focusOnRoom(room: TRoom = null, view_sc: number = 0.8) {
        room = room || this._selected_room;
        if (!room) return;

        let bbox = room.room_shape._poly.computeBBox();

        let center = bbox.getCenter(new Vector3());
        let size = bbox.getSize(new Vector3());

        let ww = Math.max(size.x, size.y);

        let canvasElement = this.painter._canvas;
        let scaleW = canvasElement.width / ww;
        let scaleH = canvasElement.height / ww;
        this.painter.p_center = center;
        this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * view_sc;
    }
    getCandidateRects(targets: IRoomEntityType[] = ["RoomArea", "Wall", "Door", "Window", "StructureEntity", "Furniture"],
        filter_conditions: {
            target_realtypes?: IRoomEntityRealType[],
            ignore_realtypes?: IRoomEntityRealType[]
        } = { ignore_realtypes: ["Decoration"] }, select_priorities: { [key: string]: number } = null, query_matched_rect: boolean = false) {
        let ans_rects: ZRect[] = [];

        let ignore_realtypes = filter_conditions.ignore_realtypes || [];
        let target_realtypes = filter_conditions.target_realtypes || null;
        if (targets.indexOf("Wall") >= 0) {
            for (let wall of this._wall_entities) {
                if (ignore_realtypes.indexOf(wall.realType) >= 0) continue;


                if (select_priorities) {
                    let select_priority = select_priorities[wall.type] || 1;
                    wall.rect.ex_prop[SelectPriority] = '' + select_priority;
                }

                ans_rects.push(wall.rect);
            }
        }
        if (targets.indexOf("Furniture") >= 0) {
            // let logContext = this.constructor.name + ".getCandidateRects() " + "\n";
            for (let entity of this._furniture_entities) {
                if (!entity) continue;
                let rect = entity.rect;
                if (!rect) {
                    continue;
                }
                if (TBaseEntity.is_deleted(rect)) continue;
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) {
                    if (target_realtypes.includes("SoftFurniture") && entity.realType == "Cabinet" && entity.figure_element.haveMatchedMaterial() && !entity.figure_element.haveMatchedCustomCabinet()) {
                    } else {
                        continue;
                    }
                }
                if (target_realtypes && target_realtypes.includes("Cabinet") && entity.realType == "Cabinet" && entity.figure_element.haveMatchedMaterial() && !entity.figure_element.haveMatchedCustomCabinet()) {
                    continue;
                }
                if (this._drawing_layer_mode === "SingleRoom") {
                    if (this._selected_room) {
                        if (!this._selected_room.room_shape._poly.containsPoint(entity.rect.rect_center)) {
                            continue;
                        }
                    }
                }

                // 根据自己的业务，根据不同的mode，显示图元还是显示素材的rect
                // 案例：
                // 1. 在AIMatching模式下，显示素材，应该push matched_rect
                // 2. 在Texture模式下，显示图元，应该push rect
                if (entity.figure_element.haveMatchedMaterial() && (this._drawing_figure_mode !== DrawingFigureMode.Figure2D)) {
                    if (entity.matched_rect) {
                        rect = entity.matched_rect;
                        entity.rect._nor = rect._nor;
                    }
                }
                if (query_matched_rect && entity.figure_element.isMaterialMarkAsInvisible()) {
                    continue;
                }
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;

                    let drawing_order = (entity._drawing_order + 0.5);
                    rect.ex_prop[SelectPriority] = '' + (select_priority + drawing_order);
                }
                // logContext += "  " + rect.ex_prop.label + "\n";
                ans_rects.push(rect);

            }
            // console.info(logContext);
        }

        if (targets.indexOf("Window") >= 0 || targets.indexOf("Door") >= 0) {
            for (let entity of this._window_entities) {
                let rect = entity.rect;
                if (TBaseEntity.is_deleted(rect)) continue;
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                ans_rects.push(rect)
            }
        }

        if (targets.indexOf("StructureEntity") >= 0) {
            for (let entity of this._structure_entities) {
                let rect = entity.rect;
                if (TBaseEntity.is_deleted(rect)) continue;
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                ans_rects.push(rect)
            }
        }

        if (targets.indexOf("RoomArea") >= 0) {
            for (let entity of this._room_entities) {
                let poly = entity._room_poly;
                if (TBaseEntity.is_deleted(poly)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                if (this._drawing_layer_mode === "SingleRoom" && !entity.isSingle) continue;

                ans_rects.push(poly as ZRect);
            }
        }
        if (targets.indexOf("SubArea") >= 0) {
            for (let entity of this._sub_area_entities) {
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                if (this._drawing_layer_mode === "SingleRoom") {
                    if (this._selected_room) {
                        if (!this._selected_room.room_shape._poly.containsPoint(entity.rect.rect_center)) {
                            continue;
                        }
                    }
                }
                ans_rects.push(entity.rect as ZRect);
            }
        }
        return ans_rects;
    }
    getCandidateEntities(targets: IRoomEntityType[] = ["RoomArea", "Wall", "Door", "Window", "StructureEntity", "Furniture"],
        filter_conditions: {
            target_realtypes?: IRoomEntityRealType[],
            ignore_realtypes?: IRoomEntityRealType[]
        } = { ignore_realtypes: ["Decoration"] }, select_priorities: { [key: string]: number } = null, query_matched_rect: boolean = false) {
        let ans_entities: TBaseEntity[] = [];
        let ignore_realtypes = filter_conditions.ignore_realtypes || [];
        let target_realtypes = filter_conditions.target_realtypes || null;
        if (targets.indexOf("Wall") >= 0) {
            for (let wall of this._wall_entities) {
                if (ignore_realtypes.indexOf(wall.realType) >= 0) continue;

                if (select_priorities) {
                    let select_priority = select_priorities[wall.type] || 1;
                    wall.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                ans_entities.push(wall);
            }
        }
        if (targets.indexOf("Furniture") >= 0) {
            for (let entity of this._furniture_entities) {
                if (!entity) continue;
                let rect = entity.rect;
                if (!rect) {
                    continue;
                }
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) {
                    if (target_realtypes.includes("SoftFurniture") && entity.realType == "Cabinet" && entity.figure_element.haveMatchedMaterial() && !entity.figure_element.haveMatchedCustomCabinet()) {
                    } else {
                        continue;
                    }
                }
                if (target_realtypes && target_realtypes.includes("Cabinet") && entity.realType == "Cabinet" && entity.figure_element.haveMatchedMaterial() && !entity.figure_element.haveMatchedCustomCabinet()) {
                    continue;
                }
                if (this._drawing_layer_mode === "SingleRoom") {
                    if (this._selected_room) {
                        if (!this._selected_room.room_shape._poly.containsPoint(entity.rect.rect_center)) {
                            continue;
                        }
                    }
                }

                if ((entity.figure_element.haveMatchedMaterial() && (this._drawing_layer_mode === "AIMatching" || checkIsMobile()))) {
                    if (entity.matched_rect && (this._drawing_figure_mode !== DrawingFigureMode.Figure2D)) {
                        rect = entity.matched_rect;
                        entity.rect._nor = rect._nor;
                        // entity.rect.rect_center = rect.rect_center;
                    }
                }
                if (query_matched_rect && entity.figure_element.isMaterialMarkAsInvisible()) {
                    continue;
                }
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;

                    let drawing_order = (entity._drawing_order + 0.5);
                    rect.ex_prop[SelectPriority] = '' + (select_priority + drawing_order);
                }
                // logContext += "  " + rect.ex_prop.label + "\n";
                ans_entities.push(entity);

            }
        }

        if (targets.indexOf("Window") >= 0 || targets.indexOf("Door") >= 0) {
            for (let entity of this._window_entities) {
                let rect = entity.rect;
                if (TBaseEntity.is_deleted(rect)) continue;
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                ans_entities.push(entity)
            }
        }

        if (targets.indexOf("StructureEntity") >= 0) {
            for (let entity of this._structure_entities) {
                let rect = entity.rect;
                if (ignore_realtypes.indexOf(entity.realType as IRoomEntityRealType) >= 0) continue;
                if (target_realtypes && !target_realtypes.includes(entity.realType)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                ans_entities.push(entity)
            }
        }

        if (targets.indexOf("RoomArea") >= 0) {
            for (let entity of this._room_entities) {
                let poly = entity._room_poly;
                if (TBaseEntity.is_deleted(poly)) continue;
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                if (this._drawing_layer_mode === "SingleRoom" && !entity.isSingle) continue;

                ans_entities.push(entity);
            }
        }
        if (targets.indexOf("SubArea") >= 0) {
            for (let entity of this._sub_area_entities) {
                if (select_priorities) {
                    let select_priority = select_priorities[entity.type] || 1;
                    entity.rect.ex_prop[SelectPriority] = '' + select_priority;
                }
                if (this._drawing_layer_mode === "SingleRoom") {
                    if (this._selected_room) {
                        if (!this._selected_room.room_shape._poly.containsPoint(entity.rect.rect_center)) {
                            continue;
                        }
                    }
                }
                ans_entities.push(entity);
            }
        }

        return ans_entities;
    }

    public async fetchPublicCategoryForAll3DMaterials(xmlScheme: I_SwjXmlScheme) {
        return await LayoutSchemeXmlJsonParser.fetchPublicCategoryForAll3DMaterials(xmlScheme);
    }
    drawCanvas() {

        if (!this.painter) return;
        let entities: TBaseEntity[] = [...this._room_entities, ...this._wall_entities, ...this._window_entities, ...this._structure_entities, ...this._furniture_entities];
        for (let entity of entities) {
            if (!entity) continue;
            entity.drawEntity(this.painter, { is_draw_figure: true });
        }

    }


    public makeWallXml() {
        if (!this.needs_making_wall_xml) return;
        this.needs_making_wall_xml = true;
    }


    public loadRoomEntityFromJson(data: { room_data: I_Room, swj_room_data: I_SwjRoom }) {
        TRoomTemplateSaver.loadRoomEntityFromJson(data);
    }

    public saveTRoomToJson(room: TRoom, furniture_list: TFigureElement[] = null, is_drawing_canvas: boolean = true) {
        return TRoomTemplateSaver.saveTRoomToJson(room, furniture_list, is_drawing_canvas);
    }

    public async applyRoomWithSolvingMethods(room: TRoom, solver_methods: SolverMethods[] = ["BasicTransfer", "SpacePartition", "Turing"], logger: Logger = null) {
        let result_scheme_list = await this.manager.layout_graph_solver.applyRoomWithSolvingMethods(room, this?._src_swj_layout_data?.xml_str || null, solver_methods, logger);

        if (result_scheme_list && result_scheme_list[0]) {
            result_scheme_list.forEach((val) => val.room = room);
            room._layout_scheme_list = result_scheme_list.filter((scheme) => {
                // 准出规则： 1. 每项规则的评分都大于-100
                let recommended = scheme.checkIsValidByLayoutScores();
                if (compareNames([room.roomname], ["卧室", "厨房", "卫生间", "客餐厅", "入户花园"])) {
                    return LayoutAI_App.IsDebug || recommended;
                } else {
                    return true;
                }

            });
            if (room._layout_scheme_list.length == 0) {
                if (result_scheme_list.length > 0) {
                    room._layout_scheme_list.push(result_scheme_list[0]);
                }
            }

            room._layout_scheme_list.forEach((layout_scheme) => {
                let totalScore = 0;
                if (layout_scheme._scheme_name.includes("相似")) {
                    totalScore += 0;
                }
                layout_scheme._layout_scores.forEach((item) => {
                    totalScore += item.score;
                });
                layout_scheme.totalScore = totalScore;
            });

            room._layout_scheme_list = room._layout_scheme_list.sort((a, b) => b.totalScore - a.totalScore);

            result_scheme_list = room._layout_scheme_list;

        }

        return result_scheme_list;

    }


    public async applyRoomEntitiesWithSolvingMethods(room_entities: TRoomEntity[] = null,
        solver_methods: SolverMethods[] = ["BasicTransfer", "SpacePartition"],
        options: { append_furniture_entites?: boolean, needs_make_group_templates?: boolean, force_auto_sub_area?: boolean }
            = { append_furniture_entites: true, needs_make_group_templates: true }, logger: Logger = null) {
        room_entities = room_entities || this._room_entities;

        let apply_room = async (room_entity: TRoomEntity) => {
            await this.manager.layout_graph_solver.queryModelRoomsFromServer([room_entity._room], false, false);
            // 在调用之前开始计时
            console.time('applyRoomWithSolvingMethods ' + room_entity.roomname);
            await this.applyRoomWithSolvingMethods(room_entity._room, solver_methods, logger);
            // 在调用之后结束计时
            console.timeEnd('applyRoomWithSolvingMethods ' + room_entity.roomname);
        }
        let promises = [];

        for (let room_entity of room_entities) {
            if (!room_entity._room) {
                room_entity.makeTRoom(this._furniture_entities, false);
            }

            room_entity._room.roomname = room_entity.roomname;
            promises.push(apply_room(room_entity));



        }

        await Promise.allSettled(promises);

        for (let room_entity of room_entities) {
            let room = room_entity._room;
            let is_auto_sub_area = room_entity.is_auto_sub_area || (options.force_auto_sub_area || false);
            if (room._furniture_list.length == 0 && options.append_furniture_entites) {
                if (room._layout_scheme_list && room._layout_scheme_list.length > 0) {
                    room.addFurnitureElements(room._layout_scheme_list[0].figure_list.figure_elements);
                }
                this.addFunitureEnitiesInRoom(room, false, options.needs_make_group_templates);
            }

            if (is_auto_sub_area) {
                LayoutContainerUtils.postAutoUpdateSubAreas(room_entity, this, options);
            }
            else if (options.append_furniture_entites) {
                LayoutContainerUtils.postProcessInRoom(room_entity._room);
            }

        }

    }
    /**
     *  清空某个房间的素材列表
     */
    cleanInRoomFurnitures(room: TRoom) {
        if (!room._room_entity) return;
        let remain_entities: TFurnitureEntity[] = [];
        let removed_entities: TFurnitureEntity[] = [];
        this._furniture_entities.forEach((entity) => {
            if (!room._room_entity._room_poly.containsPoint(entity.rect.rect_center)) {
                remain_entities.push(entity);
            } else {
                removed_entities.push(entity);
            }
        })

        this._furniture_entities.length = 0;
        this._furniture_entities.push(...remain_entities);

        let room_entity = room._room_entity;
        if (room_entity.tabletop_entities) {
            room_entity.tabletop_entities.length = 0;
        }

        if (room_entity) {
            room_entity.clearFurnitureEntities();
        }
        return removed_entities;
    }

    addFunitureEnitiesInRoom(room: TRoom, inside_only: boolean = false, needs_make_group_templates: boolean = true) {
        if (room._room_entity) {
            room._room_entity.clearFurnitureEntities();
        }
        let figure_elements: TFigureElement[] = [];
        for (let figure of room._furniture_list) {
            let id = this._furniture_entities.findIndex((ele) => ele.figure_element === figure);
            if (id < 0) {
                if (inside_only) {
                    if (!room.room_shape._poly.containsPoint(figure.rect.rect_center)) {
                        continue;
                    }
                }
                figure_elements.push(figure);
            }
        }

        let newEntities: TFurnitureEntity[] = TLayoutEntityContainer.getFurnitureEntitiesFromFigureElements(figure_elements, { needs_make_group_templates: room.roomname !== "厨房" && needs_make_group_templates, make_base_group: true });
        let addEntities: TFurnitureEntity[] = [];
        newEntities.forEach((entity, index) => {
            if (entity instanceof TBaseGroupEntity) {
                const groupEntity: TBaseGroupEntity = entity as TBaseGroupEntity;
                groupEntity.figure_elements.forEach((fe) => {
                    if (fe._room == null) {
                        fe._room = room;
                        fe.onRoomFloorThicknessChanged();
                    }
                });
                if (room._room_entity != null) {
                    groupEntity.roomEntity = room._room_entity;
                }
                groupEntity.combination_entitys.forEach((entity) => {
                    if (entity instanceof TFurnitureEntity) {
                        entity.roomEntity = room._room_entity;
                    }
                });
                addEntities.push(groupEntity);

            } else if (entity instanceof TFurnitureEntity) {
                if (room._room_entity) {
                    entity.roomEntity = room._room_entity;
                }
                if (entity.figure_element._room == null) {
                    entity.figure_element._room = room;
                    entity.figure_element.onRoomFloorThicknessChanged();
                }
                addEntities.push(entity);

            }
        });
        this.addFurnitureEntities(addEntities);
        if (room._room_entity) {
            room._room_entity.updateSpaceLivingInfo();
            if (room._room_entity.is_auto_sub_area) {
                this.updateSubAreasInRooms();
            }
        }

        return addEntities;
        // console.log('this._furniture_entities',this._furniture_entities);

    }

    addFurnitureEntities(furnitrue_list: TFurnitureEntity[]) {
        if (!furnitrue_list) return;
        furnitrue_list.forEach((entity) => {
            if (!this._furniture_entities.includes(entity)) this._furniture_entities.push(entity);
        });
    }
    removeFurnitureEntities(furnitrue_list: TFurnitureEntity[]) {
        if (!furnitrue_list) return;
        furnitrue_list.forEach((entity) => {
            if (this._furniture_entities.includes(entity)) this._furniture_entities.splice(this._furniture_entities.indexOf(entity), 1)
        });
    }

    cleanInSubAreaFurnitures(area_entity: TSubSpaceAreaEntity) {
        if (area_entity && area_entity.furniture_entities) {
            this.removeFurnitureEntities(area_entity.furniture_entities);
            area_entity.furniture_entities.length = 0;
        }
    }
    addInSubAreaFurnitures(area_entity: TSubSpaceAreaEntity, furnitrue_list: TFigureElement[], inside_only: boolean = false) {
        let figure_elements: TFigureElement[] = [];
        for (let figure of furnitrue_list) {
            let id = this._furniture_entities.findIndex((ele) => ele.figure_element === figure);
            if (id < 0) {
                if (inside_only) {
                    if (!area_entity.rect.containsPoint(figure.rect.rect_center)) {
                        continue;
                    }
                }
                figure_elements.push(figure);
            }
        }
        let furniture_entities = TLayoutEntityContainer.getFurnitureEntitiesFromFigureElements(figure_elements, { needs_make_group_templates: area_entity.room_entity.roomname !== "厨房", make_base_group: true });
        furniture_entities.forEach((entity) => entity.figure_element._subarea = area_entity.space_area_type);
        area_entity.clearFurnitureEntities(true);
        area_entity.addFurnitureEntities(furniture_entities);

        this.addFurnitureEntities(furniture_entities);

        if (area_entity?.room_entity) {
            area_entity.room_entity.makeTRoom(area_entity.room_entity.furniture_entities, false);
        }
    }

    _save_model_material_dict() {
        let model_material: { [key: string]: string[] } = {};
        for (let room of this._rooms) {
            for (let ele of room._furniture_list) {
                if (ele._matched_material) {
                    model_material[ele._matched_material.modelId] = [ele.category, ele.sub_category];
                }
            }
        }

        if (localStorage) {
            localStorage.setItem("LayoutAI_ModelMaterialDict", JSON.stringify(model_material));
        }
    }

    _load_model_material_dict() {
        if (localStorage) {
            let item = localStorage.getItem("LayoutAI_ModelMaterialDict");
            if (item) {
                let obj = JSON.parse(item);
                return obj;
            }
        }
        return null;
    }


    /**
     *  过滤一些图元类型
     */
    filterFunitures(cb: (furniture_entity: TFurnitureEntity, index: number) => boolean, replace: boolean = false) {
        let furniture_entities = this._furniture_entities.filter(cb);

        if (replace) {
            this._furniture_entities.length = 0;
            this._furniture_entities.push(...furniture_entities);

            this.updateRoomsFromEntities();
        }
        return furniture_entities;

    }
    /**
     * 测试钩子：获取所有房间的名称和边界信息
     * 用于测试Canvas渲染应用
     * @returns 包含房间信息的数组，每个房间包含名称和边界框信息
     */
    getTestHookAllRoomsInfo(): ITestHookRoomInfo[] {
        if (!this._room_entities || this._room_entities.length === 0) {
            return [];
        }

        return this._room_entities.map(roomEntity => {
            // 获取边界框
            const bbox = roomEntity._room_poly.computeBBox();

            return {
                roomId: roomEntity._room_id || 0,
                roomName: roomEntity.roomname || roomEntity.name || '未命名',
                area: roomEntity._area || 0,
                boundingBox: {
                    min: { x: bbox.min.x, y: bbox.min.y, z: bbox.min.z },
                    max: { x: bbox.max.x, y: bbox.max.y, z: bbox.max.z },
                    center: {
                        x: (bbox.min.x + bbox.max.x) / 2,
                        y: (bbox.min.y + bbox.max.y) / 2,
                        z: (bbox.min.z + bbox.max.z) / 2
                    },
                    width: Math.abs(bbox.max.x - bbox.min.x),
                    height: Math.abs(bbox.max.y - bbox.min.y)
                },
                posInScreen: this.painter.worldToScreen({ x: (bbox.min.x + bbox.max.x) / 2, y: (bbox.min.y + bbox.max.y) / 2 }),
                polygonPoints: roomEntity._room_poly.positions.map(p => ({ x: p.x, y: p.y, z: p.z })),
                // 使用本类中的_furniture_entities查找当前房间的家具数量
                furnitureCount: this._furniture_entities.filter(f =>
                    f.roomEntity === roomEntity).length,
                hasSubAreas: (roomEntity._sub_room_areas?.length || 0) > 0,
                furniture: roomEntity._room._furniture_list?.length || 0,
                hasLayoutSchemes: (roomEntity._room._layout_scheme_list?.length || 0) > 0
            } as ITestHookRoomInfo;
        });
    }

    /**
     * 测试钩子：获取房间内的家具信息
     * @param roomId 可选的房间ID，如果未提供则使用当前选中的房间
     * @returns 包含房间家具信息的数组
     */
    getTestHookAllRoomFurnituresInfo(roomId?: number): ITestHookFurnitureInfo[] {
        // 确定目标房间
        let targetRoomEntity: TRoomEntity = null;
        let targetRoom: TRoom = null;

        if (roomId !== undefined) {
            // 通过ID查找房间
            targetRoomEntity = this._room_entities.find(r => r._room_id === roomId);
            if (targetRoomEntity && targetRoomEntity._room) {
                targetRoom = targetRoomEntity._room;
            }
        } else if (this._selected_room) {
            // 使用当前选中的房间
            targetRoom = this._selected_room;
            targetRoomEntity = targetRoom._room_entity;
        }

        if (!targetRoom) {
            return [];
        }

        // 查找这个房间的所有家具
        const roomFurnitureEntities = targetRoomEntity.furniture_entities;

        // 获取家具信息 - 同时包含视图层和数据层
        const allfurnituresInfo = roomFurnitureEntities.map(furnitureEntity => {
            const figureElement = furnitureEntity.figure_element;
            const rect = figureElement.rect;

            return {
                entityId: furnitureEntity.uidN || 0,
                type: furnitureEntity.type,
                realType: furnitureEntity.realType,
                isSingleFurniture: furnitureEntity.is_single_furniture,
                drawingOrder: furnitureEntity._drawing_order,

                category: figureElement.category || '未分类',
                subCategory: figureElement.sub_category || '',
                modelLoc: figureElement.modelLoc || '',
                materialName: figureElement._matched_material?.name || figureElement.material_name || '',
                modelId: figureElement._matched_material?.modelId || '',
                position: {
                    x: rect.rect_center.x,
                    y: rect.rect_center.y,
                    z: rect.rect_center_3d.z || 0
                },
                posInScreen: this.painter.worldToScreen(rect.rect_center),
                rotation: rect.rotation_z || 0,
                dimensions: {
                    width: rect.w,
                    depth: rect.depth,
                    height: figureElement.params.height || 0
                },
                materialApplied: !!figureElement._matched_material
            } as ITestHookFurnitureInfo;
        });

        return allfurnituresInfo;
    }

    /**
     * 在window对象上注册测试钩子，用于Playwright测试
     * 这些钩子可以被测试框架通过window.layoutAITestHooks访问
     */
    registerTestHooks() {
        // 在window对象上注册测试钩子
        const testHooks = {
            // 获取所有房间信息
            getRoomInfo: () => this.getTestHookAllRoomsInfo(),

            // 获取指定房间或当前选中房间的家具信息
            getFurnitureInfo: (roomId?: number) => this.getTestHookAllRoomFurnituresInfo(roomId),

            // 获取当前选中的房间ID
            getSelectedRoomId: () => this._selected_room ? this._selected_room.room_id : null,

            // 检查渲染状态
            getRenderingState: () => {
                return {
                    hasRooms: this._room_entities.length > 0,
                    hasFurniture: this._furniture_entities.length > 0,
                    hasSelectedRoom: !!this._selected_room
                };
            }
        };

        // 挂载到window对象
        (window as any).layoutAITestHooks = testHooks;

        console.info('LayoutAI测试钩子已注册到window.layoutAITestHooks');

        return testHooks;
    }

    public getFillLightEntities() {
        return this._fill_light_entities;
    }

    public addFillLightEntity(entity: TFillLightEntity) {
        this._fill_light_entities.push(entity);
    }

    public removeFillLightEntity(entity: TFillLightEntity) {
        let index = this._fill_light_entities.indexOf(entity);
        if (index >= 0) {
            this._fill_light_entities.splice(index, 1);
            entity.light.removeFromParent();
            entity.lightMesh.removeFromParent();
        }
    }



    public cleanFillLights() {
        this._fill_light_entities.length = 0;
    }

    getRoomEntityByUUID(uuid: string): TRoomEntity {
        let roomEntity = this._room_entities.find((roomEnt) => roomEnt._room.uuid === uuid);
        if (roomEntity) {
            return roomEntity;
        }
        return null;
    }

    /**
     * 
     * 清除输入框的显示
     */
    cleanDimension() {
        ['.edit_input0', '.edit_input1', '.edit_input2', '.edit_input3'].forEach(className => {
            document.querySelectorAll(className).forEach((input: any) => {
                input.style.display = 'none';
            });
        });
        document.querySelectorAll('[id*=dimension_input]').forEach((element) => {
            let input = element as HTMLInputElement;
            input.style.display = 'none';
        });
    }

}