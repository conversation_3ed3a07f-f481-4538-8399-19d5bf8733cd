import { I_ZRectData, ZRect, compareNames } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";
import { TPainter } from "../../../../LayoutAI/Drawing/TPainter";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { I_SizeRange, TSize, TSizeRange } from "../../TModels/TSizeRange";
import { TGraphBasicConfigs } from "../TGraphBasicConfigs";
import { I_FigureGroupConfigs } from "../TGraphConfigureInterface";
import { TLayoutGraph } from "../TLayoutGraph";
import { I_FigureGroup, TFigureGroup } from "./TFigureGroup";
import { TSeriesFigureGroupDB } from "./TSeriesFigureGroupDB";

/**
 *  组合分区
 */
export interface I_GroupSpace {
    group_space_category?: string;
    target_rect?: I_ZRectData;
    group_code?: string; // 首选的组合编码
}

type GroupCodeSortMethods = "None"|"ByLengthDesc"|"Random";

export interface I_GroupQueryOptions {
    query_same_space_category?:boolean,
    use_rect_center?: boolean, 
    target_point?: Vector3,
    consider_depth?: boolean,
    /**
     * 有效范围
     *   --- 如果min={x:200} max={x:400}, 即目标长度在200-400之间都是合理的
     */
    valid_range ?: I_SizeRange,
    /**
     *  是否在容忍范围内 随机查询
     */
    is_random_query ?: boolean,

    /**
     *  期望主图元长度
     */
    main_figure_length ?: number;

    /**
     *  图元数量的最大差距 --- 图元数量越多, 越丰富
     */
    elements_num_max_diff ?: number,

    default_group_code ?: string
}
/**
 *  组合模板
 *    ->可替换的组合列表
 *    ->从而构成一个整体可变形模型 或者可认为是备选组合列表
 */
export interface I_GroupTemplate extends I_GroupSpace {

    group_space_category?: string;
    target_rect?: I_ZRectData;

    /**
     *  表示具体的组合编码:  转角沙发-FL_单人沙发
     */
    group_code?: string;

    space_code?: string;
    scheme_id?: string;
    seed_figure_group?: I_FigureGroup;
    seed_candiate_range_list?: I_SizeRange[];



    ///  to do : 去掉这几个属性
    category?: string;
    figure_groups?: I_FigureGroup[];
}


export class TGroupTemplate {

    group_code: string;
    group_space_category: string;
    space_code: string;
    scheme_id: string;
    seed_figure_group: TFigureGroup;

    category: string;
    figure_groups: TFigureGroup[];  // 这里的列表对应的是备选尺寸链的子组合

    _s_g_id: number;
    _graph: TLayoutGraph;

    _target_rect: ZRect;

    /**
     *  是否允许重新查询group
     */
    _enable_group_query : boolean = true;

    /**
     *  目标顶点位置
     */
    _target_point: Vector3;

    /**
     *  目标长度, 默认是无定义
     */
    _target_length: number;


    _normal_free: boolean;

    use_rect_center : boolean = false;

    static EntityName = "TGroupTemplate";

    static GroupCodeUiInfo: { [key: string]: { image: string, title: string, default_length: number, default_depth: number, default_height?: number } } = {};

    _group_uuid?: string;

    constructor() {
        this.seed_figure_group = null;
        this.space_code = "";
        this.group_code = "";
        this._group_uuid = generateUUID();
        this.scheme_id = "";


        this.category = "";
        this.figure_groups = [];
        this._graph = null;
        this._target_rect = new ZRect(1, 1);
        this._target_point = null;
        this._normal_free = false;
        this._s_g_id = 0;
    }

    clone() {
        if (!this.seed_figure_group) {
            let group_template = new TGroupTemplate().makeBySeedFigureGroup(this.seed_figure_group.toJsonData());
            group_template._target_rect = this._target_rect.clone();
            group_template.seed_figure_group.applyInRect(this._target_rect.clone());
            return group_template;
        }
        else {
            return this.simple_clone();
        }
    }
    simple_clone() {
        let group_template = TGroupTemplate.getGroupTemplateByGroupCode(this.group_code, this._target_rect.clone());
        return group_template;
    }
    static makeGroupTemplateByGroupSpaceCategory(group_space_category: string, room_name: string, target_rect: ZRect, options:{needs_set_group_code?: boolean,consider_depth?:boolean} = {needs_set_group_code: true,consider_depth:true}) {
        let configs = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
        let config = configs?.[group_space_category];

        if (!config || !target_rect) return null;

        let n_group_template = new TGroupTemplate();
        n_group_template.group_space_category = group_space_category;
        if (options.needs_set_group_code) {
            n_group_template.group_code = config.main_figure_names[0];
        }
        n_group_template._target_rect = target_rect.clone();

        n_group_template.updateByTargetRect({consider_depth:options.consider_depth||false});

        return n_group_template;
    }
    static getGroupTemplateByGroupCode(group_code: string, rect: ZRect) {
        let group_template = new TGroupTemplate();
        group_template.group_code = group_code;

        group_template._target_rect = rect;
        group_template.updateByTargetRect({ query_same_space_category: false });

        if (group_template.current_s_group) {
            group_template.group_space_category = group_template.current_s_group.group_space_category;
        }
        return group_template;
    }
    static getGroupTemplateImageByGroupCode(group_code: string, painter: TPainter, canvas: HTMLCanvasElement = null,
        length: number = 10000, depth: number = 10000) {

        let ts = painter.exportTransformData();
        let main_canvas = painter._canvas;
        canvas = canvas || document.createElement("canvas") as HTMLCanvasElement;

        canvas.width = 200;
        canvas.height = 200;


        painter.bindCanvas(canvas);
        let t_rect = new ZRect(length || 10000, depth || 10000);

        t_rect._nor.copy({ x: 0, y: 1, z: 0 });

        t_rect.updateRect();
        let group_template = TGroupTemplate.getGroupTemplateByGroupCode(group_code, t_rect);

        if (group_template.current_s_group) {
            let current_s_group = group_template.current_s_group;

            let g_rect = current_s_group.group_rect;


            let w_sc = canvas.width / g_rect.w * 0.9;
            let h_sc = canvas.height / g_rect.h * 0.9;

            painter._p_sc = Math.min(w_sc, h_sc);
            painter.p_center = g_rect.rect_center;
            painter.clean();
            painter.enter_drawpoly();
            for (let ele of current_s_group.figure_elements) {
                ele.drawFigure(painter,false,null,12,"#000000");
            }
            painter.leave_drawpoly();
        }
        painter.bindCanvas(main_canvas);
        painter.importTransformData(ts);
        return { group_template: group_template, img_path: canvas.toDataURL() };
    }
    makeBySeedFigureGroup(seed_group: I_FigureGroup) {
        if (!seed_group) return this;
        this.seed_figure_group = (seed_group as any as TFigureGroup).IsFigureGroup === true ? (seed_group as any as TFigureGroup) : new TFigureGroup(seed_group);

        this.category = this.seed_figure_group.main_figure.category;
        this.group_space_category = this.seed_figure_group.group_space_category;
        this.group_code = this.seed_figure_group._updateGroupCode();
        this._normal_free = this.seed_figure_group.normal_free || false;
        this.figure_groups = [this.seed_figure_group];
        this._s_g_id = 0;
        this.seed_figure_group._group_template = this;
        return this;
    }

    importData(group_template: I_GroupTemplate) {
        this.group_space_category = group_template?.group_space_category || "";
        this.group_code = group_template?.space_code || "";

        if (group_template.target_rect) {
            this._target_rect.importRectData(group_template.target_rect);
        }

        if (group_template.seed_figure_group) {
            this.makeBySeedFigureGroup(group_template.seed_figure_group);
        }
        else {
            this.updateByTargetRect();
        }
    }

    updateByTargetRect(options:I_GroupQueryOptions = {}) {
        if (!this._target_rect) return false;
        let target_rect = this._target_rect;
        let size = new TSize(Math.round(target_rect.w + 0.5), Math.round(target_rect.h + 0.5), 5000);

         // 如果不允许query的话
        if(!this._enable_group_query && this.current_s_group)
        {
            this.current_s_group._group_template = this;
            this.current_s_group.applyInRect(target_rect, options);
    
            if (size.length < this.current_s_group.size_range.max.x - 1) {
                return false;
            }
            else if (options.consider_depth && size.depth < this.current_s_group.size_range.max.y - 1) {
                return false;
            }
            else {
                return true;
            }
        }

        if (this._normal_free) {

            options.use_rect_center = true;
        }
        if(this.use_rect_center)
        {
            options.use_rect_center = this.use_rect_center;
        }

        if (options.query_same_space_category === undefined) {
            options.query_same_space_category = true;
        }


        let flag = this.queryBySize(size, options);


        if (!this.current_s_group) return false;


        if (this._target_point && !options.target_point) {
            options.target_point = this._target_point.clone();
        }
        this.current_s_group._group_template = this;
        this.current_s_group.applyInRect(target_rect, options);

        if (size.length < this.current_s_group.size_range.max.x - 1) {
            return false;
        }
        else if (options.consider_depth && size.depth < this.current_s_group.size_range.max.y - 1) {
            return false;
        }
        else {
            return true;
        }

    }
    updateGroupTemplateCode() {

    }

    appendFigureGroup(s_group: TFigureGroup) {
        this.figure_groups.push(s_group);
    }


    get current_s_group() {
        return this.figure_groups[this._s_g_id] || null;
    }

    apply() {
        let s_group = this.current_s_group;
        if (!s_group) return;

        for (let child of s_group.sub_figures) {
            child.figure.initSolverParams();
            child.relation.precompute();
            child.relation.apply();
        }

        s_group._updateGroupRect();
    }

    static stripGroupSpaceCategory(space_category: string) {
        let t_group_space_category: string = space_category;
        // TsAI_app.log(t_group_space_category);

        let ch = t_group_space_category[t_group_space_category.length - 1];
        if (('0123456789').indexOf(ch) >= 0) {
            t_group_space_category = t_group_space_category.substring(0, t_group_space_category.length - 1);
        }

        return t_group_space_category;
    }

    getGroupCodes(options:I_GroupQueryOptions)
    {
        let query_same_space_category = options.query_same_space_category === undefined ? true : options.query_same_space_category;
        query_same_space_category = query_same_space_category && (this.group_space_category && this.group_space_category.length > 0);
        if(compareNames([this.group_code],["衣柜","书柜","电视柜"]))
        {
            query_same_space_category = false;
        }
        let sort_method : GroupCodeSortMethods = "ByLengthDesc";
        if(compareNames([this.group_code],["地柜"])) sort_method = "None";   
        
        let group_codes: string[] = (this.group_code && this.group_code.length > 0) ? [this.group_code] : [];
        if (query_same_space_category) {
            let t_group_space_category: string = TGroupTemplate.stripGroupSpaceCategory(this.group_space_category);
 
            let t_group_code_list = TSeriesFigureGroupDB.getInstance()._space_code_dict[t_group_space_category] || [];
 
            t_group_code_list = [...t_group_code_list].filter((a)=>a!==this.group_code);
            // 排序
            if(sort_method === "ByLengthDesc")
            {
                t_group_code_list.sort((a,b)=>b.length - a.length);
            }
            group_codes.push(...t_group_code_list);
        }

        return group_codes;
    }

    /**
     * querygGroupBySize v.s. queryBySize
     *   --- 前者返回的是I_FigureGroup 或者返回boolean
     * @param size 
     * @param query_same_space_category 
     * @returns 
     */
    queryGroupBySize(size: TSize, options:I_GroupQueryOptions= {}):I_FigureGroup {
        let query_same_space_category = options.query_same_space_category === undefined ? true : options.query_same_space_category;
        options.query_same_space_category = query_same_space_category;

        let size_range = new TSizeRange();
        size_range.expandByPoint({ x: -1, y: -1, z: -1000 } as Vector3);
        // 如果是厨房 则忽略深度
        if (compareNames([this.group_code], ["地柜", "吊柜"])) {
            size.depth += 2000;
        }
        size_range.expandByPoint(size as any as Vector3);
        let group_codes: string[] = this.getGroupCodes(options);
        let result_group: I_FigureGroup = null;
        let t_min_dist = 9999999;
        let t_max_score = 0;
        let result_group_list : I_FigureGroup[] = [];
      
        let max_query_time = 2;

        const groupScore = (group:I_FigureGroup,min_dist:number,elements_weight:number)=>
        {
            let elements_count = ((group?.sub_figures?.length || 0));
            return min_dist - elements_count * elements_weight;

        }
        if(options.is_random_query) max_query_time = 1;

        for (let query_time = 0; query_time < max_query_time; query_time++) {
            let allow_not_in_size_range = query_time != 0;

            for (let group_code of group_codes) {
                let db = TSeriesFigureGroupDB.GetGroupDB(group_code);
                if (db) {
                    let res_data = { min_dist: 9999999, allow_not_in_size_range: allow_not_in_size_range };

                    let group = db.queryWithSize(size, res_data);
                    if (group) {
                        result_group_list.push(group);
                        let min_dist = groupScore(group,res_data.min_dist,query_time==0?200:1);

                        if (!result_group || (min_dist < t_min_dist)) {
                            result_group = group;
                            t_min_dist =min_dist;
                        }
                    }
                }


            }

            if (result_group && query_time == 0) {
                break;
            }
        }

        if(options.is_random_query)
        {
            if(!options.valid_range && result_group) // 如果是随机请求, 初始化一个默认的容忍范围
            {
                let t_size = result_group.size_range.max;
                options.valid_range = {min:{x:t_size.x * 0.8,y:t_size.y * 0.8,z:-100000},max:{x:size.x * 1.0+0.1,y:size.y * 1.0+0.1,z:100000}};
            }
        }
        /**
         *  如果有tolerance_range
         */
        if(options.valid_range && result_group_list.length > 0)
        {
            let t_ValidRange = new TSizeRange();
            t_ValidRange.expandByPoint(options.valid_range.min as Vector3);
            t_ValidRange.expandByPoint(options.valid_range.max as Vector3);
            let elements_num_max_diff = options.elements_num_max_diff || 3;
            let max_figure_element_num = (result_group.sub_figures || []).length;

            result_group_list = result_group_list.filter((group)=>{
                return t_ValidRange.containsPoint(group.size_range.max as Vector3);
            });
            let max_group_code = result_group?.group_code || "";
            result_group_list.forEach((group)=>{
                group.sub_figures = group.sub_figures || [];
                if(group.sub_figures.length > max_figure_element_num)
                {
                    max_group_code = group.group_code;
                    max_figure_element_num = group.sub_figures.length;
                }
            });
            result_group_list = result_group_list.filter((group)=>{
                return max_figure_element_num - group.sub_figures.length <= elements_num_max_diff;
            });
            result_group_list.forEach(group=>group.random_key = Math.random() * 5 + (group.sub_figures.length) * 0.5);
            result_group_list = result_group_list.sort((a,b)=>b.random_key - a.random_key);
            result_group = result_group_list[0] || result_group;

        }
        return result_group;
    }

    queryBySize(size: TSize, options: I_GroupQueryOptions = {}) {

        let result_group = this.queryGroupBySize(size,options);
        if (result_group) {
            let figure_group = new TFigureGroup(result_group);
            this.setCurrentFigureGroup(figure_group);
            return true;
        }

        return false;

    }
    setCurrentFigureGroup(figure_group:TFigureGroup)
    {
        if (this.figure_groups.length == 0) {
            this.seed_figure_group = figure_group;
            this.figure_groups = [this.seed_figure_group];
            this._s_g_id = 0;
        }
        else {
            if (this.figure_groups[1]) {
                delete this.figure_groups[1];

            }
            this.figure_groups[1] = figure_group;
            this._s_g_id = 1;
        }
    }
    updateGroupBySize(size: TSize) {
        if (!this.current_s_group) {
            return false;
        }

        let current_s_group = this.current_s_group;

        let result_group = this.queryGroupBySize(size, {query_same_space_category:false});

        if (result_group) {
            current_s_group.copy(result_group);
        }
        return true;


    }


    /**
     * 获得所有Group
     * @param size  
     * @param options 
     * @returns 
     */
    queryAllGroupsBySize(size : TSize, options :I_GroupQueryOptions = {}) : I_FigureGroup[]
    {
        let query_same_space_category = options.query_same_space_category === undefined ? true : options.query_same_space_category;
        options.query_same_space_category = query_same_space_category;

        let size_range = new TSizeRange();
        size_range.expandByPoint({ x: -1, y: -1, z: -1000 } as any);
        // 如果是厨房 则忽略深度
        if (compareNames([this.group_code], ["地柜", "吊柜"])) {
            size.depth += 2000;
        }
        size_range.expandByPoint(size as any);
        let group_codes: string[] = this.getGroupCodes(options);
        let result_group_list : I_FigureGroup[] = [];
        for (let group_code of group_codes) {
            let db = TSeriesFigureGroupDB.GetGroupDB(group_code);
            if (db) {

                let group = db.queryAllWithSize(size);
            
                result_group_list.push(...group);
            }
        }
        result_group_list.sort((a,b)=>b.size_range.max.x - a.size_range.max.x);
        return result_group_list;
    }

    queryAllGroupsInRect() : I_FigureGroup[]
    {
        if(this._target_rect)
        {
            let target_rect  = this._target_rect;
            let size = new TSize(Math.round(target_rect.w + 0.5), Math.round(target_rect.h + 0.5), 5000);

            return this.queryAllGroupsBySize(size);

        }
        return [];
    }

    /**
     * 组合模板提取函数
     * extractGroupTemplates
     * @params figure_list 
     */
    static extractGroupTemplates(input_figure_list: TFigureElement[], room_name: string, ignore_figurenames: string[] = []) {
        let figure_elements: TFigureElement[] = [];
        for (let figure of input_figure_list) {
            if (compareNames([figure.sub_category], ignore_figurenames)) continue;
            figure_elements.push(figure);
        }

        let group_config_dict = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];

        figure_elements.sort((a, b) => b.rect.w * b.rect.h - a.rect.w * a.rect.h);

        const T_GroupName = "t_group_name";

        let check_visit = (figure_ele: TFigureElement) => {
            return figure_ele._ex_prop[T_GroupName] !== undefined;
        }

        let set_group_name = (figure_ele: TFigureElement, group_name: string) => {
            figure_ele._ex_prop[T_GroupName] = group_name;
        }
        for (let figure of figure_elements) {
            if (figure._ex_prop[T_GroupName]) {
                delete figure._ex_prop[T_GroupName];
            }
        }
        let figure_group_dict: { [key: string]: TFigureGroup[] } = {};

        let add_figure_group = (group_space_category: string, group: TFigureGroup) => {
            if (!figure_group_dict[group_space_category]) figure_group_dict[group_space_category] = [];
            figure_group_dict[group_space_category].push(group);
        }
        if(group_config_dict)
        {
            for (let group_name in group_config_dict) {
                let group_config = group_config_dict[group_name];
                //  先找主元
                let main_figure: TFigureElement = null;
                let size_range: TSizeRange = null;
    
                if (group_config?.main_figure_conditions?.size_range) {
                    let t_size_ranage = group_config?.main_figure_conditions?.size_range;
                    size_range = new TSizeRange(t_size_ranage.min as Vector3, t_size_ranage.max as Vector3);
                }
    
    
                let main_figures = figure_elements.filter((figure_ele) => {
                    if (check_visit(figure_ele)) return false; // 如果已被标记, 则跳过
                    if (size_range) {
                        let size_pos = new Vector3(figure_ele.rect.w, figure_ele.rect.h, figure_ele.max_z || 0); // 直接用max_z
    
                        if (!size_range.containsPoint(size_pos)) return false;
                        if (group_config?.main_figure_conditions?.w_d_ratio) {
                            let w_d_ratio = group_config.main_figure_conditions.w_d_ratio;
    
                            let ratio = figure_ele.rect.w / figure_ele.rect.h;
    
                            if (ratio < w_d_ratio[0] || ratio > w_d_ratio[1]) return false;
                        }
                    }
                    if (compareNames([figure_ele.sub_category, figure_ele.category, figure_ele.public_category], group_config.main_figure_names, false) == 1) {
                        return true;
                    }
                })
    
                main_figures.forEach((main_figure) => {
                    set_group_name(main_figure, group_name);
                    // 再找附属的次元
    
                    let sub_figures: TFigureElement[] = [];
    
                    let group_range = group_config.group_range || { front_dist: 300, side_dist: 300, back_dist: 300 };
    
                    let rect = main_figure.rect.clone();
    
                    let t_pos = rect.unproject({ x: 0, y: ((group_range.front_dist || 0) - (group_range.back_dist || 0)) / 2 });
                    rect._w += (group_range.side_dist || 0) * 2;
                    rect._h += (group_range.front_dist || 0) + (group_range.back_dist || 0);
                    rect.rect_center = t_pos;
                    rect.reOrderByOrientation(true);
    
                    let found_sub_figures = figure_elements.filter((figure_ele) => {
                        if (check_visit(figure_ele)) return false;
                        if (compareNames([figure_ele.sub_category], group_config.sub_figure_names, false) != 1) return false;
                        let res = rect.clone().intersect(figure_ele.rect.clone()); // 看是否有交集
                        if (!res || res.length == 0) return false;
                        return true;
                    });
    
                    for (let figure of found_sub_figures) {
                        set_group_name(figure, group_name);
                        sub_figures.push(figure);
                    }
                    let figure_group = TFigureGroup.fromFigures(main_figure, sub_figures, group_config.sub_figure_neighbor_infos || []);
                    add_figure_group(group_name, figure_group);
                });
            }
        }


        let group_templates: TGroupTemplate[] = [];
        for (let group_name in figure_group_dict) {
            let figure_group_list = figure_group_dict[group_name];

            for (let figure_group of figure_group_list) {
                figure_group.group_space_category = TGroupTemplate.stripGroupSpaceCategory(group_name);
                let group_template = new TGroupTemplate().makeBySeedFigureGroup(figure_group as any);
                group_template._target_rect = figure_group.group_rect.clone();
                group_template.group_space_category = figure_group.group_space_category;
                group_templates.push(group_template);
            }

        }

        // 先保证所有图元都有组合模板
        let unchecked_figures = figure_elements.filter((ele)=>!check_visit(ele));
        const FigureLevelScore = (a:TFigureElement)=>{
            if(a.rect.zval < 200.)
            {
                return a.volume;
            }
            else{
                return a.volume / 100;
            }
        }
        unchecked_figures.sort((a,b)=>{
            return FigureLevelScore(b) - FigureLevelScore(a)
        });
        
        for (let figure of unchecked_figures) {
            if (check_visit(figure)) continue;
            let main_figure = figure;
            let area_rect = main_figure.rect.clone().expandPolygon(100);
            set_group_name(main_figure, main_figure.sub_category+"-组合");

            let sub_figures = unchecked_figures.filter((ele)=>!check_visit(ele) && area_rect.containsPoint(ele.rect.rect_center));

            let figure_group = TFigureGroup.fromFigures(figure,sub_figures);
            
            sub_figures.forEach((ele)=>set_group_name(ele,main_figure.sub_category+"-组合"));
            
            let group_template = new TGroupTemplate().makeBySeedFigureGroup(figure_group as any);
            group_template.group_space_category = figure.sub_category;
            group_template._target_rect = figure_group.group_rect.clone();
            group_templates.push(group_template);
        }

        return group_templates;
    }


    static extractGroupTemplatesByGroupUUid(input_figure_list: TFigureElement[], check_group_db: boolean = false) {
        let id_dict: { [key: string]: TFigureElement[] } = {};

        let ans_group_templates: TGroupTemplate[] = [];
        for (let ele of input_figure_list) {
            if (ele._group_uuid) {
                if (!id_dict[ele._group_uuid]) {
                    id_dict[ele._group_uuid] = [];
                }
                id_dict[ele._group_uuid].push(ele);
            }
        }

        for (let key in id_dict) {
            let g_elements = id_dict[key];

            if (g_elements.length === 0) continue;

            let t_elements = [...g_elements];

            let main_figure: TFigureElement = null;
            let sub_figures: TFigureElement[] = [];
            for (let ele of t_elements) {
                if (ele._group_main_figure === true) {
                    main_figure = ele;
                }
                else {
                    sub_figures.push(ele);
                }
            }
            if (!main_figure) {
                console.log("extractGroupTemplatesByGroupUUid 找不到主元!!");
                continue;
            }
            let figure_group = TFigureGroup.fromFigures(main_figure, sub_figures);

            let group_code = figure_group.group_code;



            // 餐桌需要特殊判断
            if (group_code.indexOf("餐桌") >= 0) {
                if (sub_figures.length == 6) {
                    group_code = "餐桌-FC6_餐椅";

                }
                else {
                    group_code = "餐桌-FC4_餐椅";

                }
            }
            let group_rect = figure_group.group_rect;

            // 检查组合模板库里有没有同款
            let target_group_template: TGroupTemplate = null;

            if (check_group_db) {
                let group_template = TGroupTemplate.getGroupTemplateByGroupCode(group_code, group_rect.clone());
                // console.log(group_template);
                if (group_template && group_template.current_s_group) {
                    let t_group_rect = group_template.current_s_group.group_rect;

                    if (group_rect.is_shape_equal_to(t_group_rect, 10)) {
                        target_group_template = group_template;

                    }
                    if (!target_group_template) {
                        target_group_template = group_template;

                        // console.log(group_rect.w, group_rect.h, t_group_rect.w,t_group_rect.h,group_rect.nor,t_group_rect.nor)   
                        let db = TSeriesFigureGroupDB.GetGroupDB(group_code);
                        figure_group.group_space_category = target_group_template.current_s_group?.group_space_category;
                        db.seed_candidate_list.push(figure_group.toJsonData());

                        target_group_template.figure_groups[0] = figure_group;
                        target_group_template._s_g_id = 0;



                    }
                }

            }

            if (!target_group_template) {
                target_group_template = new TGroupTemplate().makeBySeedFigureGroup(figure_group as any as I_FigureGroup);
                target_group_template._target_rect = group_rect.clone();
                target_group_template.updateByTargetRect();
                // console.log("组合模板-数据库中找不到同款",figure_group.group_code,target_group_template);

            }

            target_group_template._group_uuid = key;


            ans_group_templates.push(target_group_template);
        }
        return ans_group_templates;
    }


    /**
     * 根据房间名, 获得配置
     * @params room_name  
     */
    getFigureGroupConfigs(room_name: string): I_FigureGroupConfigs {
        let config = TGraphBasicConfigs.MainGroupFigureConfigs[room_name];
        if (!config) return null;
        return config[this.group_space_category] || null;
    }


}