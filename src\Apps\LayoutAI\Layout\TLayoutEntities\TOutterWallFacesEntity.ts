import { Mesh, Object3D } from "three";
import { MaterialManager, SceneMaterialMode } from "../../Scene3D/MaterialManager";
import { MeshName } from "../../Scene3D/NodeName";
import { ZPolygon } from "@layoutai/z_polygon";
import { TBaseEntity } from "./TBaseEntity";
import { TLayoutEntityContainer } from "./TLayoutEntityContainter";


/**
 *  外墙面实体
 */
export class TOutterWallFacesEntity extends TBaseEntity {

    _container: TLayoutEntityContainer;

    _inner_polygons: ZPolygon[];
    _outter_polygons: ZPolygon[];
    constructor(container: TLayoutEntityContainer) {
        super();
        this._container = container;
        this._inner_polygons = [];
        this._outter_polygons = [];
    }

    initByPolygons(outter_polygons: ZPolygon[], inner_polygons: ZPolygon[]) {
        this._outter_polygons = outter_polygons;
        this._inner_polygons = inner_polygons;
    }

    updateMesh3D(mode?: SceneMaterialMode): Object3D {

        if (!this._mesh3d) {
            this._mesh3d = new Mesh(null, MaterialManager.wall_material);
            this._mesh3d.name = MeshName.OutterWallFace;
        }
        return null;
    }
}