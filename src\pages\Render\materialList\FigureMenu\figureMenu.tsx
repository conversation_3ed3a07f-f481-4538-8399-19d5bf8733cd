import React, { useEffect, useState, useRef } from 'react';
import { Image, Tooltip } from '@svg/antd';
import useStyles from './style';
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { DragEventListener } from '@svg/antd-cloud-design'
import { useStore } from '@/models';
import { observer } from "mobx-react-lite";
import { getPrefix } from '@/utils/common';
import { useTranslation } from 'react-i18next';

interface Module {
  image: string;
  png: string;
  title: string;
  label: string;
  group_code?:string;
}

interface dataProps {
  label: string;
  figureList: Module[];
}

interface ImageGalleryProps {
  data: any[];
  filterName: string
}
const ImageGallery: React.FC<ImageGalleryProps> = ({ data, filterName }) => {
  const { t } = useTranslation()
  const { styles } = useStyles();
  const store = useStore();
  const [label, setLabel] = useState<string>('');
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const [collapsedSections, setCollapsedSections] = useState<boolean[]>()
  const figureItemRefs = useRef<(HTMLDivElement | null)[]>([]);
  useEffect(() => {
    const dragEventListener = new DragEventListener({
      // 展示缩略图
      isShowThumbnail: true,
      container: document.getElementById('side_pannel') as HTMLElement,
      // 打印
      log: false,
    })
    dragEventListener.bindDrag()
    return () => {
      dragEventListener.unbindDrag()
    }
  }, [])


  // useEffect(() => {
  //   scrollToItem(filterName)
  // },[filterName])

  useEffect(() => {
    setCollapsedSections(data.map(() => false));
  },[data])

  useEffect(() => {
    // 鼠标弹起的时候设置label为空
    const container = scrollContainerRef.current;
    const handleMouseUp = () => {
      if(label)
      {
        LayoutAI_App.RunCommand(LayoutAI_Commands.LeaveSubHandler);
        setLabel('');
      }
    };
    const handleTouchMove = (ev: TouchEvent) => {
      // 在苹果浏览器会默认向上移动
      // ev.preventDefault();
      const containerRect = scrollContainerRef.current.getBoundingClientRect();
      if (ev.touches[0].clientY < (containerRect.bottom - containerRect.height)) {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.style.overflow = 'hidden';
        }
      }
    };

    const handleTouchEnd = (ev: TouchEvent) => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.style.overflow = 'scroll';
      }
    }
    container.addEventListener('mouseup', handleMouseUp);
    container.addEventListener('touchmove', handleTouchMove);
    container.addEventListener('touchend', handleTouchEnd);
    // 在组件卸载时移除事件监听器
    return () => {
      container.removeEventListener('mouseup', handleMouseUp);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };


  }, [label]);
  const MemoizedImage = React.memo(Image);

  let startY = 0;


  return (
    <div className={`${styles.figure}`} ref={scrollContainerRef} id={'scrollContainerRef'}>
      {data.map((item, index) => (
          <React.Fragment key={"figure_menu_key"+index}>
              {item.figureList.map((item: Module, index: number) =>(
                <div 
                  key={index} 
                  className={`item`}
              >
                <div className="image"               
                  onPointerDown={(ev: React.PointerEvent)=>{
                    let label = item.title;
                    if(item.group_code)
                    {
                      label = "GroupTemplate:"+item.group_code;
                    }

                    if(item.title.includes('单开门') || item.title.includes('推拉门') || item.title.includes('一字窗') ||  item.title.includes('飘窗')) //[i18n:ignore]
                    {
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                      return;
                    } 
                    else 
                    {
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.SelectedFurniture, label);
                    }
                  }}

                  onPointerUp={(ev: React.PointerEvent) => {
                    let label = item.title;
                    if(item.group_code)
                    {
                      label = "GroupTemplate:"+item.group_code;
                      LayoutAI_App.DispatchEvent(LayoutAI_Events.mobileAddFurniture, label);
                      return;
                    }
                    LayoutAI_App.DispatchEvent(LayoutAI_Events.mobileAddFurniture, label);
                  }}
                  >
                  <MemoizedImage 
                  src={`https://3vj-fe.3vjia.com/layoutai/figures_imgs/${item.png}`} 
                  preview={false} 
                  alt={item.title}
                  className={filterName === "结构件" ? 'structure-image' : ''}/>
                  {/* {item.group_code ? 
                <Image src={(`${item.image}`)} preview={false} alt={item.title} className='group_image'/> 
                : 
                <Image src={(getPrefix() + `static/figures_imgs/${item.png}`)} preview={false} alt={item.title}/>
              } */}
                </div>
                <Tooltip placement="rightBottom" title={t(item.title)}>
                  <div className="title">{t(item.title)}</div>
                </Tooltip>

                </div>
              ))}
          </React.Fragment>
        ))}  
    </div>
  );
};

export default observer(ImageGallery);
