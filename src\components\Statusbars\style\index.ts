import { createStyles } from '@svg/antd/es/theme/utils';
export default createStyles(({ css }) => {
  return {
    container: css`
      display: flex;
      position: fixed;
      top: calc(var(--vh, 1vh) * 50);
      transform: translateY(-50%);
      right: 12px;
      flex-direction: column;
      z-index: 11;
      padding: 20px 0px;
      gap: 12px;
      background-color: #fff;
      border-radius: 30px;
      /* position: relative; */
    `,
    blackColor: css`
      background-color: rgba(0, 0, 0, 0.40) !important;
      backdrop-filter: blur(50px) !important;
      color: #fff !important;
      .iconButtonText
      {
        color: #fff !important;
      }
      .icon
      {
        color: #fff !important;
      }
    `,
    morebtns_container: css`
      display: flex;
      position: absolute;
      bottom: 0px;
      right: 62px;
      flex-direction: column;
      z-index: 11;
      padding: 20px 0px;
      gap: 12px;
      background-color: #fff;
      border-radius: 30px;
    `,
    center_container :css`
      position : fixed;
      top : 40px;
      left : 40px;
      right : 40px;
      bottom : 40px;
      z-index:101
    `,
    iconButton: css`
      width: 48px;
      height: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      flex-direction: column;
      position: relative;
      /* margin-bottom: 12px; */
      .iconButtonText
      {
        font-size: 12px;
        color: #282828;
        margin-top: 4px;
        width: 24px;
        text-align: center;
        word-break: break-all;
        line-height: 1.2;
      }
      .iconLabel {
        font-size: 13px;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .divider
      {
        margin-top: 12px;
        width: 100%;
        height: 1px;
        background-color: #E0E0E0;
      }
      @media screen and (max-width: 450px) { // 手机宽度
        width: 28px !important;
        height: 28px !important;
        font-size: 16px !important;
      }     
      
      @media screen and (orientation: portrait) {
        width: 48px;
        height: 48px;
      }

      // 横屏样式
      @media screen and (orientation: landscape) {
        width: 48px;
        /* min-height: 40px; */
      }
      
      @keyframes flashEffect {
        0% {
          background-color: rgba(255, 255, 255, 0.7); /* 原始背景色 */
        }
        50% {
          background-color: rgba(200, 200, 255, 0.7); /* 高亮颜色 */
        }
        100% {
          background-color: rgba(255, 255, 255, 0.7); /* 回到原始背景色 */
        }
      }
    `,
    name: css`
      font-size: 12px;
      color: #000;
      margin-top: 4px;
    `,
    checkBoxes:css`
      position: fixed;
      background: #fff;
      z-index: 20;
      right: 64px;
      top: 50vh;
      transform: translateY(-50%);
      font-size:15px;
      padding: 12px;
      line-height: 28px;
      border-radius: 8px;
    `,
    camera: css`
      position: fixed;
      background-color: #ffffff;
      top: 50%;
      right: 60px;
      border-radius: 8px;
      width: 210px;
      font-size: 13px;
      .camera_container {
        padding: 8px;
        .content {
          display: flex;
          align-items: center;
          gap: 8px;
          .slider {
            flex: 1;
          }
          .slider-camera {
            flex: 1;
            padding-bottom: 5px;
            .camera-state {
              display: flex;
              justify-content: space-between;
            }
          }
        }
      }
    `,
    viewCamera: css`
      position: fixed;
      background-color: #ffffff;
      bottom: 100px;
      right: 60px;
      width: 300px;
      border-radius: 10px;
      z-index: 10;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 5px;
      padding: 5px;
      .viewGrid {
        border-radius: 10px;
        overflow: hidden;
      }
    `,
    loading: css`
      background-color: #ffffff;
      position: fixed;
      bottom: 100px;
      right: 60px;
      width: 300px;
      border-radius: 10px;
      z-index: 11;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 5px;
      padding: 5px;
      .loading-item {
        border-radius: 10px;
        position:relative;
        overflow: hidden;
        display: flex; /* 使用flex布局 */
        flex-direction: column; /* 垂直排列 */
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
        gap: 8px;
        height: 106.9px; /* 保持高度 */
        width: 142.5px; /* 保持宽度 */
        span {
          font-size: 12px;
          color: #C0C0C0;
        }
      }
    `,
    ratioContainer: css`
      display: flex;
      gap: 10px;
      position: fixed;
      flex-direction: column;
      top: 40%;
      right: 65px;
      .ratioBtn {
        background-color:rgb(218, 218, 218);
        height: 30px;
        width: 45px;
        border-radius: 5px;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    `,
  }
});
