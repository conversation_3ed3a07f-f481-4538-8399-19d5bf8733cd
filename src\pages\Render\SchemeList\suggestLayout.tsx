import { useEffect, useRef } from 'react';
import { LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { observer } from 'mobx-react-lite';
import { TRoomLayoutScheme } from '@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme';
import useStyles from './style/index';
import { EventName } from '@/Apps/EventSystem';
import { TPainter } from '@/Apps/LayoutAI/Drawing/TPainter';
import { AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import { checkIsMobile } from '@/config';
import { TWholeLayoutScheme } from '@/Apps/LayoutAI/Layout/TLayoutScheme/TWholeLayoutScheme';
import { useTranslation } from 'react-i18next'
import LayoutScorePopUp from './layoutScorePop';
import ReactDOM from 'react-dom';
import { Rate, Tooltip } from '@svg/antd';
import { GradeStarsDict } from '@/Apps/LayoutAI/Layout/IUIInterface';
import { compareNames } from "@layoutai/z_polygon";
import { useStore } from '@/models';
import { TSubAreaLayoutScheme } from '@/Apps/LayoutAI/Layout/TLayoutScheme/TSubAreaLayoutScheme';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import RoomAreaBtns from '@/pages/Mobile/roomAreaBtns/roomAreaBtns';
import { TSeriesFurnisher } from '@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher';
import { roomSubAreaService } from '@/Apps/LayoutAI/Services/Basic/RoomSubAreaService';
import { DrawingFigureMode } from '@/Apps/LayoutAI/Layout/IRoomInterface';

const DrawSchemeOnCanvas = (layout_scheme: TRoomLayoutScheme|TWholeLayoutScheme|TSubAreaLayoutScheme, canvasElement: HTMLCanvasElement, painter: TPainter) => {

  if(!layout_scheme) return;
  layout_scheme.drawOnCanvas(painter,canvasElement,576,576);

  
}
let CurrentSchemeListData : {
  room_scheme_list ?: TRoomLayoutScheme[],
  room_scheme_index ?: number,
  whole_scheme_list ?: TWholeLayoutScheme[],
  whole_scheme_index ?: number,
  subarea_scheme_list ?: TSubAreaLayoutScheme[],
  subarea_scheme_index ?: number;
} = {};
/**
 * @description 按钮组件
 */
const SchemeList: React.FC<{ width: number, showSchemeName:boolean, isLightMobile?:boolean}> = (props: { width: number, showSchemeName:boolean, isLightMobile?:boolean}) => {
  const store = useStore();
  const sideListDivRef = useRef(null);
  const { styles } = useStyles(props.width);
  const showSchemeName = props.showSchemeName || false;
  const { t } = useTranslation()

  const SelectIndex = (selected_index:number)=>{


    let div = sideListDivRef.current as any as HTMLDivElement; 
    if(!div) return;


     let imgs = div.querySelectorAll('img');

     imgs.forEach((ele,index)=>{
        ele.className = (index==selected_index)?styles.active:'';
     })

     let active_divs = div.querySelectorAll('#active_div');
     active_divs.forEach((ele,index)=>{
        ele.className = (index==selected_index)?styles.activeTitle: styles.activeTitleNone;
     })


  }
  
  const updateSchemeList = (schemeList:TRoomLayoutScheme[],selectedIndex:number)=>{
    if (sideListDivRef.current) {
      sideListDivRef.current.scrollTop = 0;
    }
    else{
      return;
    }

    // LayoutAI_App.DispatchEvent(LayoutAI_Events.UpdateSideLayoutSchemeCandidates, {});

    let div = sideListDivRef.current as any as HTMLDivElement; 

    div.innerHTML = "";
    CurrentSchemeListData = {
      room_scheme_list : schemeList,
      room_scheme_index : selectedIndex
    }
    let painter = (LayoutAI_App.instance as AI2DesignManager).painter;
    schemeList.forEach((layout_scheme: TRoomLayoutScheme) => {

      let totalScore = 0;
      layout_scheme._layout_scores.forEach((item: any) => {
        totalScore += item.score;
      });
      layout_scheme.totalScore = totalScore;
    });
    
    let firstElementIsDIY = schemeList[0]?._scheme_name?.includes('DIY');
    let firstElement = firstElementIsDIY ? schemeList.shift() : null;
    
    // 根据 totalScore 对 schemeList 进行排序，由大到小
    // schemeList.sort((a, b) => b.totalScore - a.totalScore);
    // 如果第一个元素是 DIY 布局，将其添加回数组的开头
    if (firstElementIsDIY && firstElement) {
      schemeList.unshift(firstElement);
    }

    
    if(schemeList.length > 0)
    {

      for(let id in schemeList)
      {
        let layout_scheme = schemeList[id];
        let canvas = document.createElement("canvas") as HTMLCanvasElement;
        if(!layout_scheme._drawn_image)
        {
          DrawSchemeOnCanvas(layout_scheme,canvas,painter);
          layout_scheme._drawn_image = new Image();
          layout_scheme._drawn_image.src = canvas.toDataURL()
          layout_scheme._drawn_image.crossOrigin = "anonymous";
        }
        // layout_scheme._drawn_canvas = canvas;

        let t_image = layout_scheme._drawn_image;
        t_image.className = (~~id==selectedIndex)?styles.active:'';

    
        t_image.onclick = ()=>{
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickLayoutScheme, { value: layout_scheme, index: ~~id });
          LayoutAI_App.emit_M(EventName.OnAILayoutSchemeSelected,{ value: layout_scheme, index: ~~id });
          SelectIndex(~~id);

          // 移动端，替换布局也同步使用套系
          if(TSeriesFurnisher.instance.current_rooms?.length == 1 && TSeriesFurnisher.instance.current_rooms[0]?._series_sample_info && (LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D)
          {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: TSeriesFurnisher.instance.current_rooms[0]._series_sample_info, scope: { soft: true, hard: true, cabinet: true, remaining: false } });
          }
        }

        t_image.onpointermove = (ev)=>{
          ev.stopPropagation();
        }
        canvas = null;
        let row_div = document.createElement("div");
        row_div.className = "scheme_div";

        row_div.appendChild(layout_scheme._drawn_image);
        let drawn_image = layout_scheme._drawn_image;
        drawn_image.id = "layout_scheme_cavas_" + id;
        drawn_image.className = (~~id==selectedIndex)?styles.active:'';

        let active_div = document.createElement("div");
        active_div.innerHTML = t('正在使用');
        active_div.className =  (~~id==selectedIndex)?styles.activeTitle: styles.activeTitleNone;
        active_div.id = 'active_div';

        div.appendChild(row_div);
        let text_div = document.createElement("div");
  
        text_div.className = styles._scheme_name+" scheme_name";
        text_div.innerHTML = `[${t(layout_scheme.room.name)}] ${layout_scheme._scheme_name.includes('DIY') ? t(layout_scheme._scheme_name) : t('方案') + (~~id + 1)} ` + `${showSchemeName && LayoutAI_App.IsDebug?layout_scheme._scheme_name||"":""}`
        let label_div = null;
        let difference_div = document.createElement("div");

        if(compareNames([layout_scheme.room.roomname], ["卧室", "厨房", "卫生间", "客餐厅", "入户花园"])) //[i18n:ignore]
        {
     
          let totalGrade = 0;
          let rate = 0;
          layout_scheme._layout_scores.forEach((item: any) => {
              totalGrade += item.grade;
          })
          let recommended = layout_scheme._layout_scores.some(value => {
            if (value.children && value.children.length > 0) {
              return value.children.some(child => child.score <= -100);
            } else {
              return value.score <= -100;
            }
          });
          if(totalGrade)
          {
            rate = Math.round(totalGrade / layout_scheme._layout_scores.length);
          }
          if(GradeStarsDict[rate] < 2 || recommended )
          {
            difference_div.className = styles.difference;
            difference_div.innerHTML = t('差');
          }

          text_div.classList.add(styles.star_name);
          label_div = document.createElement("div");
          const root = (ReactDOM as any).createRoot(label_div);
          root.render(<Rate disabled allowHalf value={GradeStarsDict[rate]} />);


          // label_div.className="iconfont iconinfo_fill";
          let schemeName = `${layout_scheme._scheme_name.includes('DIY') ? t(layout_scheme._scheme_name) : t('方案') + (~~id + 1)} ` + `${showSchemeName && LayoutAI_App.IsDebug?layout_scheme._scheme_name||"":""}`;
          text_div.innerHTML = '';

          
          let label_div_1 = document.createElement("div");
          const tooltipRoot = (ReactDOM as any).createRoot(label_div_1);
          tooltipRoot.render(
            <Tooltip title={schemeName}>
              <span onDoubleClick={()=>{
                if(navigator?.clipboard)
                {
                   let target_name = schemeName;
                   let prefix = "相似"; //[i18n:ignore]
                   let id = target_name.indexOf(prefix);
                   if(id >=0)
                   {
                      target_name = target_name.substring(id+prefix.length);
                   }
                    navigator.clipboard.writeText(schemeName);
                }
              }}>{schemeName}</span>
            </Tooltip>
          );
          label_div.onclick = (e: any)=>{
            // console.log(ev);
            const rect = e.currentTarget.getBoundingClientRect();
            const middlePoint = rect.top + rect.height / 2 - 425;
            const pageHeight = document.documentElement.clientHeight - 680;
            const minTop = -55;  // 弹窗的最小顶部位置
            const maxTop = Math.max(minTop, Math.min(middlePoint, pageHeight));
               LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:layout_scheme, top:maxTop})
          }
          label_div.onmouseenter = (e: any)=>{
            // console.log(ev);
            const rect = e.currentTarget.getBoundingClientRect();
            const middlePoint = rect.top + rect.height / 2 - 425;
            const pageHeight = document.documentElement.clientHeight - 680;
            const minTop = -55;  // 弹窗的最小顶部位置
            const maxTop = Math.max(minTop, Math.min(middlePoint, pageHeight));
               LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:layout_scheme, top:maxTop})
          }
          label_div.onpointerleave = ()=>{
            LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:null, top:Math.max(row_div.offsetTop - div.scrollTop,0)})
  
          }
          text_div.appendChild(label_div_1);
          text_div.appendChild(label_div);
          
        }

        row_div.appendChild(text_div);
        row_div.appendChild(difference_div);
        row_div.appendChild(active_div);
      }

      // SelectIndex(selectedIndex);
      LayoutAI_App.emit(EventName.ShowPopUpLayoutScore, {scheme:null, top:0})

  
    }
    else{
      div.innerHTML=`
      <div class=${styles.letfEmpty}>
          <div class=${styles.letfEmptyItem}>
            <img class='emptyImg' src=${'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div class=${styles.text}>
              ${t('暂无内容')}，${t('请选择其他空间')}
            </div>
          </div>
      </div>
  `
  }

    // LayoutAI_App.instance.update();
  }

  const updateWholeSchemeList = (schemeList:TWholeLayoutScheme[],selectedIndex:number)=>{


    if (sideListDivRef.current) {
      sideListDivRef.current.scrollTop = 0;
    }
    else{
      return;
    }

    CurrentSchemeListData = {
      whole_scheme_list : schemeList,
      whole_scheme_index : selectedIndex
    }
    let div = sideListDivRef.current as any as HTMLDivElement; 
    div.innerHTML = "";

    let painter = (LayoutAI_App.instance as AI2DesignManager).painter;
    if(schemeList.length > 0)
    {
        for(let id in schemeList)
        {
          let layout_scheme = schemeList[id];
    
          
          if(!layout_scheme._drawn_image)
          {
            let canvas = document.createElement("canvas") as HTMLCanvasElement;
    


            DrawSchemeOnCanvas(layout_scheme,canvas,painter);

            layout_scheme._drawn_image = new Image();
            layout_scheme._drawn_image.src = canvas.toDataURL()
            layout_scheme._drawn_image.crossOrigin = "anonymous";

            let t_image = layout_scheme._drawn_image;
    
        
            t_image.onclick = ()=>{
              LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, { value: layout_scheme, index: ~~id });
              LayoutAI_App.emit_M(EventName.OnAILayoutSchemeSelected,{ value: layout_scheme, index: ~~id });

              SelectIndex(~~id);
              // 移动端，替换布局也同步使用套系
              if(TSeriesFurnisher.instance.current_rooms?.length > 1 && TSeriesFurnisher.instance.current_rooms[0]?._series_sample_info && (LayoutAI_App.instance as TAppManagerBase).layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D)
              {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.SeriesSampleSelected, { series: TSeriesFurnisher.instance.current_rooms[0]._series_sample_info, scope: { soft: true, hard: true, cabinet: true, remaining: false } });
              }
            }
            canvas = null;
          }
          let row_div = document.createElement("div");
          row_div.className = "scheme_div";
          row_div.appendChild(layout_scheme._drawn_image);
          let drawn_image = layout_scheme._drawn_image;
          drawn_image.id = "layout_whole_scheme_cavans_" + id;
          drawn_image.className = checkIsMobile() ? 'mobile' : '';
    
          div.appendChild(row_div);
          let text_div = document.createElement("div");
    
          text_div.className = styles._scheme_name+" scheme_name";
          text_div.innerHTML = `${t('全屋') }${layout_scheme._scheme_name.includes('DIY') ? t(layout_scheme._scheme_name) : `${t('方案')}` + (~~id + 1)}` + `${showSchemeName && LayoutAI_App.IsDebug?layout_scheme._scheme_name||"":""}`
    
          row_div.appendChild(text_div);
    
        }

        // SelectIndex(selectedIndex);

    
    }
    else{
      div.innerHTML=`
      <div class=${styles.letfEmpty}>
          <div class=${styles.letfEmptyItem}>
            <img class='emptyImg' src=${'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div class=${styles.text}>
              ${t('暂无全屋')}${t('推荐内容')}
            </div>
          </div>
      </div>
  `
    }

    // LayoutAI_App.instance.update();
  }


  /**
   * 直接应用分区方案
   * @param scheme  
   * @returns 
   */
  const applySubAreaScheme = (scheme :TSubAreaLayoutScheme,index:number)=>{
    roomSubAreaService.applySubAreaScheme(scheme,index);

  }
  const updateSubSchemeList = (schemeList:TSubAreaLayoutScheme[],selectedIndex:number)=>{
    
    if (sideListDivRef.current) {
      sideListDivRef.current.scrollTop = 0;
    }
    else{
      return;
    }

    CurrentSchemeListData = {
      subarea_scheme_list : schemeList,
      subarea_scheme_index : selectedIndex
    }
    let div = sideListDivRef.current as any as HTMLDivElement; 
    div.innerHTML = "";

    let painter = (LayoutAI_App.instance as AI2DesignManager).painter;
    if(schemeList.length > 0)
    {
        for(let id in schemeList)
        {
          let layout_scheme = schemeList[id];
    
          
          if(!layout_scheme._drawn_image)
          {
            let canvas = document.createElement("canvas") as HTMLCanvasElement;
    


            DrawSchemeOnCanvas(layout_scheme,canvas,painter);

            layout_scheme._drawn_image = new Image();
            layout_scheme._drawn_image.src = canvas.toDataURL()
            layout_scheme._drawn_image.crossOrigin = "anonymous";

            let t_image = layout_scheme._drawn_image;
    
        
            t_image.onclick = ()=>{
              // LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, { value: layout_scheme, index: ~~id });
              applySubAreaScheme(layout_scheme,selectedIndex);
              SelectIndex(~~id);

            }
            canvas = null;
          }
          let row_div = document.createElement("div");
          row_div.className = "scheme_div";
          row_div.appendChild(layout_scheme._drawn_image);
          let drawn_image = layout_scheme._drawn_image;
          drawn_image.id = "layout_subarea_scheme_cavans_" + id;
          drawn_image.className = checkIsMobile() ? 'mobile' : '';
    
          div.appendChild(row_div);
          let text_div = document.createElement("div");
    
          text_div.className = styles._scheme_name+" scheme_name";
          text_div.innerHTML = `${t('分区')}${layout_scheme._scheme_name.includes('DIY') ? t(layout_scheme._scheme_name) : `${t('布局')}` + (~~id + 1)}` + `${showSchemeName && LayoutAI_App.IsDebug?layout_scheme._scheme_name||"":""}`
    
          row_div.appendChild(text_div);
    
        }

        // SelectIndex(selectedIndex);

    
    }
    else{
      div.innerHTML=`
      <div class=${styles.letfEmpty}>
          <div class=${styles.letfEmptyItem}>
            <img class='emptyImg' src=${'https://3vj-fe.3vjia.com/layoutai/image/Empty.png'} alt="" />
            <div class=${styles.text}>
              ${t('暂无分区')}${t('推荐内容')}
            </div>
          </div>
      </div>
  `
    }
  }


  useEffect(() => {
    LayoutAI_App.on(EventName.LayoutSchemeList, (data:{schemeList: TRoomLayoutScheme[],index:number}): void => {
      if (!data?.schemeList) {
        updateSchemeList([],0);

      } else {
        updateSchemeList([...data.schemeList],data.index);
      }

    });

    LayoutAI_App.on_M(EventName.WholeLayoutSchemeList, 'schemeList',(data:{schemeList: TWholeLayoutScheme[],index:number}): void => {
      if (!data?.schemeList) {
        updateWholeSchemeList([],0);

      } else {
        updateWholeSchemeList([...data?.schemeList || []],data.index);
      }
    });
    LayoutAI_App.on(EventName.SubAreaLayoutSchemeList, (data:{schemeList: TSubAreaLayoutScheme[],index:number,auto_layout?:boolean}): void => {
      if (!data?.schemeList) {
        updateSubSchemeList([],0);

      } else {
        updateSubSchemeList([...data.schemeList],data.index);
        if(data.auto_layout)
        {
            if(data.schemeList[0])
            {
               applySubAreaScheme(data.schemeList[0],0);
            }
        }
      }
    });
    if(CurrentSchemeListData)
    {
        if(CurrentSchemeListData.room_scheme_list)
        {
          updateSchemeList(CurrentSchemeListData.room_scheme_list,CurrentSchemeListData.room_scheme_index);

        }
        else if(CurrentSchemeListData.whole_scheme_list)
        {
          updateWholeSchemeList(CurrentSchemeListData.whole_scheme_list,CurrentSchemeListData.whole_scheme_index);
        }
    }
  }, []);
 
  if(props.isLightMobile)
  {
      return (
        <>
          <div className={styles.bottom_panel_container}>
            {!store.homeStore.isSingleRoom &&
              <div className={styles.roomListBar}>
                {/* 空间切换组件 */}
                <RoomAreaBtns />
              </div>
            }
            <div className={`${styles.bottom_panel_layout_list}`} id="side_list_div" ref={sideListDivRef}>
            </div>
          </div>
         <LayoutScorePopUp></LayoutScorePopUp>
        </>

      )
  }

  return (
  
    <div className={styles.left_panel_container} style={{marginTop: '16px'}}>
      <div className={`${styles.left_panel_layout_list} ${checkIsMobile() ? styles.mobile: ''}`} id="side_list_div" ref={sideListDivRef}>
      </div>
      <LayoutScorePopUp></LayoutScorePopUp>
    </div>
  );
}
export default observer(SchemeList);
