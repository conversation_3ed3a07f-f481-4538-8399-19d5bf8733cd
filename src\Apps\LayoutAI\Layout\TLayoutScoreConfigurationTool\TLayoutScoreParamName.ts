

export class TLayoutScoreParamName
{
    // 收纳容量
    public static storageGroup: string = "storageGroup";
    // 空间利用率
    public static spaceUsageGroup: string = "spaceUsageGroup";
    // 动线布局
    public static flowGroup: string = "flowGroup";
    // 家具布局
    public static basicGroup: string = "basicGroup";
    // 分区合理性
    public static splitSpaceGroup: string = "splitSpaceGroup";
    // 操作台面
    public static flatPlaneGroup: string = "flatPlaneGroup";
    // 美观度
    public static visualAppealGroup: string = "visualAppealGroup";

    // 组名
        // 规则名
            // 参数配置名

    // ==========================================卫生间==========================================
    // 卫生间
    public static bathRoomParamConfig: string = "bathRoomParamConfig";

    // 收纳容量
        // 收纳容量
        public static bathRoomStorageRatioRule: string = "storageRatioRule";
            // 无柜体得分
            public static withoutBathRoomStorageCabinetScore: string = "withoutBathRoomStorageCabinetScore";
            // 收纳容量（柜体长度）
            public static bathRoomStorageCabinetLen: string = "bathRoomStorageCabinetLen";


    // 空间利用率
        // 家具利用率
        public static furnitureUsageRatioRule: string = "furnitureUsageRatioRule";
        // 功能区丰富度
        public static functionNumRule: string = "functionNumRule";
            // 区分功能区数量的最小面积
            public static minRoomArea: string = "minRoomArea";
            // 大于最小面积的最小功能区数量
            public static normalMinFuncNum: string = "normalMinFuncNum";
            // 最小面积下满足功能区的数量得分
            public static funcScore: string = "funcScore";
            // 正常面积下需要具备的三个基础功能
            public static baseFuncCount: string = "baseFuncCount";
            // 功能丰富度不足扣分数值
            public static bathRoomFunctionAbnormalScore: string = "bathRoomFunctionAbnormalScore";

    // 动线布局
        // 动线流畅度
        public static dryAndWetFlowRule: string = "dryAndWetFlowRule";
            // 干湿区混合的得分
            public static dryAndWetMixScore: string = "dryAndWetMixScore";
            public static dryAndWetSplitScore: string = "dryAndWetSplitScore";
            public static dryAndWetSamePathScore: string = "dryAndWetSamePathScore";
            public static dryAndWetLayonOneWallScore: string = "dryAndWetLayonOneWallScore";
            public static dryAndWetLayonNearWallScore: string = "dryAndWetLayonNearWallScore";
            public static dryAndWetLayonMultiWallScore: string = "dryAndWetLayonMultiWallScore";
            public static washHandNearDoorScore: string = "washHandNearDoorScore";
            public static dryAndWetReverseScore: string = "dryAndWetReverseScore";
        // 干湿规划
        public static DryAndWetLayoutRule: string = "DryAndWetLayoutRule";
            // 无干湿分离得分
            public static withoutDryAndWetSeparationScore = "withoutDryAndWetSeparationScore";
            // 干湿分离得分
            public static dryAndWetSeparationScore = "dryAndWetSeparationScore";
            // 独立空间干湿分离得分
            public static dryAndWetSeparationWithWallScore = "dryAndWetSeparationWithWallScore";
        // 过道宽度
        public static flowMinDistanRule: string = "flowMinDistanRule";


    // 家具布局
        // 马桶区大小
        public static toiletAreaSizeRule: string = "toiletAreaSizeRule";
            // 马桶区长度最小不准允出长度
            public static toiletAreaMinLen: string = "toiletBowlAreaMinLen";
            // 马桶区单侧长度最小不准允出长度
            public static toiletOneSideMinLen: string = "toiletOneSideMinLen";
            // 马桶区长度参数累计得分
            public static toiletAreaSumParam: string = "toiletAreaSumParam";
            // 马桶区单侧参数累计得分
            public static toiletOneSideSumParam: string = "toiletOneSideSumParam";

        // 马桶对门
        public static toiletFaceDoorRule: string = "toiletFaceDoorRule";
            // 开放空间下马桶对门得分
            public static toiletFaceDoorScore: string = "toiletFaceDoorScore";
            public static toiletWithoutFaceDoorScore: string = "toiletWithoutFaceDoorScore";
            // 马桶处于独立空间下得分
            public static toiletIndependentSpaceScore: string = "toiletIndependentSpaceScore";

        // 马桶前侧活动距离
        public static toileFronAreaLenRule: string = "toileFronAreaLenRule";

        //马桶尺寸
        public static toiletBowlSizeRule: string = "toiletBowlSizeRule";

        // 浴缸与其他障碍物检查规则
        public static bathtubAreaLenRule: string = "bathtubAreaLenRule";
            // 浴缸一侧靠墙，其他三侧与其他障碍物的最小距离
            public static bathtubOneSideLayonScore: string = "bathtubOneSideLayonScore";
            // 浴缸三侧靠墙，其他一侧与其他障碍物的最小距离
            public static bathtubThreeSideLayonScore: string = "bathtubThreeSideLayonScore";

        // 浴室柜最小尺寸检查
        public static washHandMinSizeRule: string = "washHandMinSizeRule";
            
        // 家具挡窗
        public static occlusionWindowRule: string = "occlusionWindowRule";

        // 浴室柜前方距离检查
        public static washHandFrontAreRule: string = "washHandFrontAreRule";

        // 淋浴尺寸检查
        public static showerSizeRule: string = "showerSizeRule";
            // 淋浴区尺寸得分
            public static showerSizeScore: string = "showerSizeScore";
            // 不准允出的最小尺寸
            public static abnormalMinSizeLen: string = "abnormalMinSizeLen";

        // 一字形淋浴房检查
        public static IShowerRoomOpenDoorRule: string = "IShowerRoomOpenDoorRule";
            // 障碍物距离
            public static IShowerRoomMinDistance: string = "IShowerRoomMinDistance";
            // 开口方向有障碍物得分
            public static IShowerRoomScore: string = "IShowerRoomScore";

    // ==========================================卫生间==========================================


    // ==========================================厨房==========================================
    // 厨房
    public static kitchenRoomParamConfig: string = "kitchenRoomParamConfig";
        // 收纳容量
            // 收纳容量
            public static kitchenRoomStorageRatioRule: string = "storageRatioRule";
        // 动线布局
            // 厨房过道检查
            public static kitchenFlowLineRule: string = "kitchenFlowLineRule";
                // 最短过道长度
                public static kitchenFlowMinDistance: string = "kitchenFlowMinDistance";
                // 合理过道下最大的过道距离
                public static kitchenFlowMaxDistance: string = "kitchenFlowMaxDistance";
                // 合理过道值下的得分
                public static fineKitchenFlowScore: string = "fineKitchenFlowScore";
                // 其余的过道值下的得分
                public static otherKitchenFlowScore: string = "otherKitchenFlowScore";

            // 三角动线平均长度检查
            public static kitchenWorkingTriangleRule: string = "kitchenWorkingTriangleRule";

            // 动线流畅性
            public static kitchenWorkingTriangleFlowRule: string = "kitchenWorkingTriangleFlowRule";
                // 动线不交叉得分
                public static flowWithoutIntersectScore: string = "flowWithoutIntersectScore";
                // 少量动线不交叉得分
                public static lessFlowWithoutIntersectScore: string = "lessFlowWithoutIntersectScore";
                // 动线不流畅得分
                public static flowIntersectScore: string = "flowIntersectScore";
            
        // 操作台面
            // 操作台面检查
            public static kitchenWorkingPlaneRule: string = "kitchenWorkingPlaneRule";
            // 黄金备菜区长度检查
            public static kitchenCutPlaneRule: string = "kitchenCutPlaneRule";
            // 出餐区长度检查
            public static kitchenPutPlaneRule: string = "kitchenPutPlaneRule";
        // 家具布局
            // 炉灶与烟道的距离
            public static distanceWithFlueRule: string = "distanceWithFlueRule";
            // 炉灶与冰箱的距离
            public static distanceWithCookingRule: string = "distanceWithCookingRule";
            // 炉灶到墙的距离
            public static cookingWithWallRule: string = "cookingWithWallRule";
            // 排烟管道绕梁
            public static kitchenFlueCookingBeamRule: string = "kitchenFlueCookingBeamRule";
                // 不绕梁得分
                public static kitchenFlueCookingWithoutOverBeamScore: string = "kitchenFlueCookingWithoutOverBeamScore";
                // 绕梁得分
                public static kitchenFlueCookingOverBeamScore: string = "kitchenFlueCookingOverBeamScore";
            // 水槽靠窗
            public static rinseOnWidnowRule: string = "rinseOnWidnowRule";
                // 水槽靠窗得分
                public static rinseNearWindowScore: string = "rinseNearWindowScore";
                // 水槽不靠窗得分
                public static rinseNotNearWindowScore: string = "rinseNotNearWindowScore";
            // 水槽与冰箱的距离检查
            public  static fridgeWithRinseDistanceRule: string = "fridgeWithRinseDistanceRule";
            // 冰箱检查
            public static kitchenFridgeRule: string = "kitchenFridgeRule";
                // 具有冰箱的得分
                public static hasFridgeScore: string = "hasFridgeScore";
                // 没有冰箱的得分
                public static withoutFridgeScore: string = "withoutFridgeScore";
            // 布局形状检查
            public static figureShapeRule: string = "figureShapeRule";
                public static IShapeScore: string = "IShapeScore";
                public static IIShapeScore: string = "IIShapeScore";
                public static LShapeScore: string = "LShapeScore";
                public static UShapeScore: string = "UShapeScore";
            // 布局挡门（推拉门）
            public static slidingDoorOcclusionRule: string = "slidingDoorOcclusionRule";
            // 布局挡窗
            public static kitchenWindowOcclusionRule: string = "kitchenWindowOcclusionRule";

    // ==========================================厨房==========================================

    // ==========================================卧室==========================================
    // 卧室
    public static bedRoomParamConfig: string = "bedRoomParamConfig";
    // 收纳容量
        // 收纳容量
        public static bedRoomStorageRatioRule: string = "storageRatioRule";
            // 卧室面积大小
            public static bedRoomArea: string = "bedRoomArea";
            // 大于卧室面积的收纳容量占比得分
            public static moreThanAreaStorageScore: string = "moreThanAreaStorageScore";
            // 小于卧室面积的收纳容量占比得分
            public static lessThanAreaStorageScore: string = "lessThanAreaStorageScore";
    // 空间利用率
        // 家具利用率
        public static bedRoomUseageRule: string = "bedRoomUseageRule";
        // 家具丰富度
        public static bedRoomRichnessRule: string = "bedRoomRichnessRule";
            // 卧室需要具备多少个图元
            public static bedRoomMustHasFigureNum: string = "bedRoomMustHasFigureNum";
            // 有多余的图元的得分
            public static bedRoomMoreFigureScore: string = "bedRoomMoreFigureScore";
            // 没有缺少指定图元的得分
            public static bedRoomNormalFiguteScore: string = "bedRoomNormalFiguteScore";
            // 缺少图元的得分
            public static bedRoomLessFigureScore: string = "bedRoomLessFigureScore";
            // 没有主图元的得分
            public static bedRoomNoMainFigureScore: string = "bedRoomNoMainFigureScore";
    // 动线布局
        // 过道动线

    // 家具布局
        // 床头对门
        public static doorSightFaceDoorRule: string = "doorSightFaceDoorRule";
        // 布局挡门（平开门）
        public static bedRoomDoorOcclusionRule: string = "doorOcclusionRule";
            // 强挡门
            public static strongDoorOcclusionRule: string = "strongDoorOcclusionRule";
            // 弱挡门
            public static weakDoorOcclusionRule: string = "weakDoorOcclusionRule";
        // 布局挡门（推拉门）
        public static bedRoomSlidingDoorOcclusionRule: string = "slidingDoorOcclusionRule";
            // 推拉门预留距离
            public static bedRoomSlidingDoorExtendLen: string = "slidingDoorExtendLen";
            // 挡推拉门范围判断
            public static bedRoomSlidingDoorScore: string = "slidingDoorScore";
        // 床头靠墙
        public static bedBackOnWallRule: string = "bedBackOnWallRule";
        // 床居于空间中心位置
        public static bedCenterToWallRule: string = "bedCenterToWallRule";
            // 位于中心得分
            public static bedInCenterScore: string = "bedInCenterScore";
            // 不位于中心得分
            public static bedNotInCenterScore: string = "bedNotInCenterScore";

        // 床长边不靠墙
        public static bedSideToWallRule: string = "bedSideToWallRule";
        // 床在角落
        public static bedCornerRule: string = "bedCornerRule";
            // 床在角落得分
            public static bedInCornerScore: string = "bedInCornerScore";
            // 床不在角落得分
            public static bedNotInCornerScore: string = "bedNotInCornerScore";

        // 床侧对窗
        public static bedSideToWindowRule: string = "bedSideToWindowRule";
            // 定义床侧到墙的距离
            public static bedSideLayOnWallLen: string = "bedSideLayOnWallLen";
            // 床侧对窗比例得分
            public static bedSideLayonWindonScore: string = "bedSideLayonWindonScore";
        // 布局挡窗
        public static bedRoomWindowOcclusionRule: string = "bedRoomWindowOcclusionRule";
            // 窗户高度延伸距离
            public static bedRoomWindowHightExtendLen: string = "bedRoomWindowHightExtendLen";
            // 挡窗占比得分
            public static bedRoomWindowOcclusionScore: string = "bedRoomWindowOcclusionScore";
        // 衣柜背靠墙
        public static wardrobeToWallRule: string = "wardrobeToWallRule";
            // 衣柜背靠墙得分
            public static wardrobeLayOnWallScore: string = "wardrobeLayOnWallScore";
            // 衣柜不背靠墙得分
            public static wardrobeNoLayOnWallScore: string = "wardrobeNoLayOnWallScore";
        // 床头在窗下
        public static bedBackOnWindowlRule: string = "bedBackOnWindowlRule";
        // 床正对窗
        public static bedFaceWindowlRule: string = "bedFaceWindowlRule";
            // 床背靠墙的容差距离
            public static bedBackLayOnWallLen: string = "bedBackLayOnWallLen";
            public static bedFaceWindowScore: string = "bedFaceWindowScore";


    // ==========================================卧室==========================================

    // ==========================================客餐厅==========================================
    // 客餐厅
    public static livingRoomParamConfig: string = "livingRoomParamConfig";
    // 收纳容量
        // 收纳容量
        public static livingRoomStorageRatioRule: string = "storageRatioRule";
    
    // 空间利用率
        // 餐厅区收纳容量
        public static diningSpaceUsageRatioRule: string = "diningSpaceUsageRatioRule";
    
    // 分区合理性
        // 客厅餐厅面积比(以客厅为主)
        public static livingAndDiningSpaceAreaRatioRule: string = "livingAndDiningSpaceAreaRatioRule";

        // 客厅区域长宽比
        public static livingValidAreaLenAndWidthRatioCheckRule: string = "livingValidAreaLenAndWidthRatioCheckRule";

        // 区域面积利用率
        public static livingAndDiningInWholeRoomAreaRatioCheckRule: string = "livingAndDiningInWholeRoomAreaRatioCheckRule";

        // 客厅餐厅位置关系
        public static livingAndDiningSpacePositionRule: string = "livingAndDiningSpacePositionRule";
            // 餐厅接近厨房得分
            public static diningNearKitchenScore: string = "diningNearKitchenScore";
            // 客厅接近厨房得分
            public static livingNearKitchenScore: string = "livingNearKitchenScore";
            // 客厅接近阳台得分
            public static livingNearBalconyScore: string = "livingNearBalconyScore";
            // 客厅接近入户门得分
            public static livingNearEntranceScore: string = "livingNearEntranceScore";
        
        // 分区完整性
        public static livingRoomSplitSpaceWholenessRule: string = "livingRoomSplitSpaceWholenessRule";
            // 两个分区得分
            public static livingRoomTwoSpaceScore: string = "livingRoomTwoSpaceScore";
            // 少于两个分区得分
            public static livingRoomLessThanTwoSpaceScore: string = "livingRoomLessThanTwoSpaceScore";

        // 区域最小尺寸面积检查
        public static livingRoomSpaceMinAreaRule: string = "livingRoomSpaceMinAreaRule";
            // 客厅最小尺寸面积
            public static livingSpaceMinArea: string = "livingSpaceMinArea";
            // 餐厅最小尺寸面积
            public static diningSpaceMinArea: string = "diningSpaceMinArea";
            // 面积不满足的得分
            public static lessLivingOrDiningSpaceAreaScore: string = "lessLivingOrDiningSpaceAreaScore";

        // 动线布局
            // 主动线流畅度
            public static livingRoomFlowMainFluencyRule: string = "livingRoomFlowMainFluencyRule";
                // 主动线过道最最小距离
                public static livingRoomMainFlowMinDistance: string = "livingRoomMainFlowMinDistance";
                // 动线被遮挡得分
                public static blockLivingRoomMainFlowScore: string = "blockLivingRoomMainFlowScore";
            
            // 次动线检查
            // 沙发到电视柜的动线检查
            public static livingRoomSofaAndTVSubFlowLineRule: string = "livingRoomSofaAndTVSubFlowLineRule";
                // 沙发到电视柜的过道最小距离
                public static sofaAndTVFlowMinDistance: string = "sofaAndTVFlowMinDistance";
                // 动线被遮挡得分
                public static blockSofaAndTVFlowScore: string = "blockSofaAndTVFlowScore";
            
            // 沙发到阳台的动线检查
            public static livingRoomSofaAndBalconySubFlowLineRule: string = "livingRoomSofaAndBalconySubFlowLineRule";
                // 沙发到阳台的过道最小距离
                public static sofaAndBalconyFlowMinDistance: string = "sofaAndBalconyFlowMinDistance";
                // 动线被遮挡得分
                public static blockSofaAndBalconyFlowScore: string = "blockSofaAndBalconyFlowScore";

            // 餐桌到餐边柜的动线检查
            public static diningTableToCabinetSubFlowLineRule: string = "diningTableToCabinetSubFlowLineRule";
                // 餐桌到餐边柜的过道最小距离
                public static diningTableToCabinetFlowMinDistance: string = "diningTableToCabinetFlowMinDistance";
                // 动线比遮挡得分
                public static blockDiningTableToCabinetFlowScore: string = "blockDiningTableToCabinetFlowScore";

            // 玄关柜检查
            public static livingEntranceCabinetRule: string = "livingEntranceCabinetRule";
                // 有玄关柜得分
                public static hasEntranceCabinerScore: string = "hasEntranceCabinerScore";
                // 无玄关柜得分
                public static withoutEntranceCabinetScore: string = "withoutEntranceCabinetScore";

            // 餐厅功能区检查
            public static diningFunctionRule: string = "diningFunctionRule";
                // 有餐桌得分
                public static hasDiningTableScore: string = "hasDiningTableScore";
                // 无餐桌得分
                public static withoutDiningTableScore: string = "withoutDiningTableScore";

            // 餐桌中心点检查
            public static diningTabelCenterRule: string = "diningTabelCenterRule";
                // 餐桌中心点偏离距离
                public static diningTableCenterOffsetCenterLen: string = "diningTableCenterOffsetCenterLen";
                // 餐桌中心点无偏离得分
                public static diningTableCenterWithoutOffsetScore: string = "diningTableCenterWithoutOffsetScore";
                // 餐桌中心点偏离得分
                public static diningTableCenterOffsetScore: string = "diningTableCenterOffsetScore";

            // 客厅功能区检查
            public static livingFunctionRule: string = "livingFunctionRule";
                // 无沙发得分
                public static livingWithoutSofaScore: string = "livingWithoutSofaScore";
                // 有电视柜或者书柜得分
                public static hasTVOrBookCabinerScore: string = "hasTVOrBookCabinerScore";
                // 客厅区多其他家具的新增得分
                public static livingAddBaseScore: string = "livingAddBaseScore";
                // 新增得分上线
                public static livingMaxAddBaseScore: string = "livingMaxAddBaseScore";

            // 沙发中心点检查
            public static sofaCenterRule: string = "sofaCenterRule";
                // 沙发中心点偏离距离
                public static sofaCenterOffsetCenterLen: string = "sofaCenterOffsetCenterLen";
                // 沙发中心点无偏离得分
                public static sofaCenterWithoutOffsetScore: string = "sofaCenterWithoutOffsetScore";
                // 沙发中心点偏离得分
                public static sofaCenterOffsetScore: string = "sofaCenterOffsetScore";

            // 沙发对门
            public static sofaFaceEntranceDoorRule: string = "sofaFaceEntranceDoorRule";
                // 沙发对门扣除分值
                public static sofaFaceEntranceDoorScore: string = "sofaFaceEntranceDoorScore";
            // 沙发后背不靠墙离障碍物检查
            public static sofaBackFlowRule: string = "sofaBackFlowRule";
                // 沙发后背不靠墙离障碍物距离
                public static sofaBackDistance: string = "sofaBackDistance";
                // 沙发后背离障碍物距离过小得分
                public static sofaBackLessDistScore: string = "sofaBackLessDistScore";
            // 客厅与餐厅区过道检查
            public static livingAndDiningFlowRule: string = "livingAndDiningFlowRule";
                // 客厅与餐厅区的过道距离
                public static livingAndDiningFlowDistance: string = "livingAndDiningFlowDistance";
                // 过道距离小于客厅与餐厅区过道距离得分
                public  static livingAndDiningLessFlowDistScore: string = "livingAndDiningLessFlowDistScore";

            // 餐边柜前方距离
            public static diningCabinetFrontDistanceRule: string = "diningCabinetFrontDistanceRule";
                // 餐边柜前方距离
                public static diningCabinetFrontDistance: string = "diningCabinetFrontDistance";
                // 餐边柜前方距离得分
                public static diningCabinetFrontDistScore: string = "diningCabinetFrontDistScore";
            
    // ==========================================客餐厅==========================================

    // ==========================================入户花园==========================================
    public static entranceRoomParamConfig: string = "entranceRoomParamConfig"; 
        // 玄关柜铺满墙面
        public static entranceCabinetFullPaddingRule: string = "entranceCabinetFullPaddingRule";
        // 收纳容量
        public static entranceRoomStorageRatioRule: string = "entranceRoomStorageRatioRule";
        // 主动线检查
        public static entranceRoomFlowMainFluencyRule: string = "entranceRoomFlowMainFluencyRule";
            // 主动线最小距离
            public static entranceRoomMainFlowMinDistance: string = "entranceRoomMainFlowMinDistance";
            // 动线默认距离
            public static entranceRoomDefaultFlowDistance: string = "entranceRoomDefaultFlowDistance";
            // 动线被遮挡得分
            public static blockEntranceRoomMainFlowScore: string = "blockEntranceRoomMainFlowScore";
            // 动线不被遮挡得分
            public static noBlockEntranceRoomMainFlowScore: string = "noBlockEntranceRoomMainFlowScore";
            // 动线大于默认距离得分
            public static moreThanDefaultFlowDistanceScore: string = "moreThanDefaultFlowDistanceScore";
        // 家具布局
            // 图元互相干涉
            public static entranceRoomFiguresOverlayRule: string = "entranceRoomFiguresOverlayRule";
                // 图元互相干涉得分
                public static entranceRoomFiguresOverlayScore: string = "entranceRoomFiguresOverlayScore";
            // 图元挡窗
            public static entranceRoomWindowOcclusionRule: string = "entranceRoomWindowOcclusionRule";
            // 图元挡门
            public static entranceRoomDoorOcclusionRule: string = "entranceRoomDoorOcclusionRule";
        
    // ==========================================入户花园==========================================
}
