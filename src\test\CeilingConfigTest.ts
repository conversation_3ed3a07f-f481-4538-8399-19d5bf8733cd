import { GUI } from "lil-gui";
import { DefaultCeilingType, I_CeilingLayerData, GenCeilingLayerMethod, cloneSectionData, DefaultSection, DefaultSectiontype } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { CeilingConfigManager } from "@/Apps/LayoutAI/Layout/TLayoutGraph/TGraphConfigs/CeilingConfigManager";
import { IFrameMsgServerExFuncs } from "@/pages/SdkFrame/MsgCenter/IFrameMsgServerExFuncs";
import { IFrameMsgClientExFuncs } from "@/pages/SdkFrame/MsgCenter/IFrameMsgClientExFuncs";

export class CeilingConfigTest {
    private static _gui: GUI;
    private static _isInit: boolean = false;
    private static _currentConfigId: string = "default"; // 当前选中的配置ID
    private static _currentConfigName: string = "默认配置"; // 当前配置名称

    // 房间相关
    private static _currentRoomInfo: any = null; // 当前选中的房间信息
    private static _roomList: any[] = []; // 房间列表

    // 高刷选项
    private static _highRefreshMode: boolean = false; // 是否开启高刷模式

    // 吊顶配置参数
    private static _config = {
        ceilingType: DefaultCeilingType.OverhangingEdge,
        // 外层配置
        outerMethod: "Rect" as GenCeilingLayerMethod, // 方法 分为Rect 和 Offset
        outerOffsetValue: 100, // 外层偏移值
        useAdaptiveHeight: false, // 是否使用自适应高度
        zvalToTop: 400, // 离顶400
        zvalToTopFunc: "(maxCeilingHeight)", // 如果设置zvalToTopFunc 则使用函数计算zvalToTop自适应zvalToTop值就不生效了
        aroundToTop: 100, // 吊顶以外房间以内区域离顶高度
        // 内层配置
        hasInnerLayer: true, // 是否有内层配置
        innerMethod: "Offset" as GenCeilingLayerMethod, // 同外层方法
        innerZvalToTop: 1, // 同外层zvalToTop
        offsetValue: 100, // 偏移值
        // 灯槽配置
        direction: -1, // 灯槽方向 1 为正方向 -1 为反方向
        extrudeLength: 50, // 灯槽长度
        extrudeHeight: 20, // 灯槽高度
        // 关联素材ID
        materialId: "", // 关联的3D参数化吊顶ID
    };

    public static get gui(): GUI {
        if (!this._gui) {
            this._gui = new GUI();
        }
        return this._gui;
    }

    public static async init() {
        if (this._isInit) {
            return;
        }

        // 初始化房间列表
        await this.initRoomList();
        
        this.addCeilingConfigCtr();
        this._isInit = true;
    }

    /**
     * 初始化房间列表
     */
    private static async initRoomList() {
        try {
            // 获取房间分区信息
            let sub_area_result = await IFrameMsgServerExFuncs.getCeilingAreasInRoom({});
            this._roomList = [];
            
            sub_area_result.forEach((result) => {
                for (let area of result.sub_areas) {
                    let copy: any = { ...area };
                    // 补充额外信息
                    copy.room_name = result.room_name;
                    copy.room_uid = result.room_uid;
                    copy.room_uuid = result.room_uuid;
                    this._roomList.push(copy);
                }
            });
            
            console.log('房间列表初始化完成:', this._roomList);
            
            // 设置默认房间（优先选择客厅）
            this._currentRoomInfo = this._roomList[0];
            let lvRoom = this._roomList.find((area) => area.name === '客厅');
            if (lvRoom) {
                this._currentRoomInfo = lvRoom;
            }
            
        } catch (error) {
            console.error('初始化房间列表失败:', error);
        }
    }

    public static addCeilingConfigCtr() {
        let ceilingFolder = this.gui.addFolder('吊顶配置录入');
        ceilingFolder.open();

        // 房间选择 - 移动到最上面
        this.addRoomSelection(ceilingFolder);

        // 高刷选项 - 移动到最上面
        ceilingFolder.add({ highRefreshMode: this._highRefreshMode }, 'highRefreshMode')
            .name('高刷模式')
            .onChange((value: boolean) => {
                this._highRefreshMode = value;
                console.log('高刷模式:', value ? '开启' : '关闭');
            });

        // 吊顶ID列表选择 - 显示配置ID
        const ceilingList = this.getCeilingListWithIds();
        const ceilingOptions = Object.entries(ceilingList).reduce((acc, [id, name]) => {
            acc[id] = id; // 显示配置ID
            return acc;
        }, {} as { [key: string]: string });

        // 吊顶ID列表选择器
        ceilingFolder.add({ ceilingList: this._currentConfigId }, 'ceilingList', ceilingOptions)
            .name('吊顶ID列表')
            .onChange((value: string) => {
                this.selectCeilingConfig(value);
            });

        // 配置ID显示 - 不可修改
        ceilingFolder.add({ configId: this._currentConfigId }, 'configId')
            .name('配置ID')
            .disable();

        // 配置名称显示/编辑 - 一直可编辑
        const configNameObj = { configName: this._currentConfigName };
        ceilingFolder.add(configNameObj, 'configName')
            .name('配置名称')
            .onChange((value: string) => {
                this._currentConfigName = value;
            });

        // 关联素材ID显示/编辑 - 一直可编辑
        const materialIdObj = { materialId: this._config.materialId };
        ceilingFolder.add(materialIdObj, 'materialId')
            .name('关联素材ID')
            .onChange((value: string) => {
                this._config.materialId = value;
                this.updateCeilingConfig();
            });

        // 外层配置
        let outerFolder = ceilingFolder.addFolder('外层配置');
        outerFolder.open();
        
        // 外层方法选择 - 改为生成区域
        const methodOptions = {
            "完整吊顶区域": "Rect",
            "向内偏移": "Offset"
        };
        // outerFolder.add(this._config, 'outerMethod', methodOptions)
        //     .name('生成区域')
        //     .onChange((value: string) => {
        //         this._config.outerMethod = value as GenCeilingLayerMethod;
        //         this.updateCeilingConfig();
        //     });
        
        // 外层偏移值 - 只在选择向内偏移时显示
        let outerOffsetController = outerFolder.add(this._config, 'outerOffsetValue', 0, 500, 1)
            .name('偏移值')
            .onChange(() => this.updateCeilingConfig());
        
        // 根据外层方法控制偏移值的显示
        const updateOuterOffsetVisibility = () => {
            if (this._config.outerMethod === "Offset") {
                outerOffsetController.show();
            } else {
                outerOffsetController.hide();
            }
        };
        updateOuterOffsetVisibility();
        
        // 自适应高度勾选项
        outerFolder.add(this._config, 'useAdaptiveHeight')
            .name('自适应高度')
            .onChange(() => {
                updateHeightVisibility();
                this.updateCeilingConfig();
            });
        
        // 离顶高度 - 只在未勾选自适应高度时显示
        let zvalToTopController = outerFolder.add(this._config, 'zvalToTop', 1, 1000, 1)
            .name('离顶高度')
            .onChange(() => this.updateCeilingConfig());
        
        // 根据自适应高度控制显示
        const updateHeightVisibility = () => {
            if (this._config.useAdaptiveHeight) {
                zvalToTopController.hide();
            } else {
                zvalToTopController.show();
            }
        };
        updateHeightVisibility();

        outerFolder.add(this._config, 'aroundToTop', 0, 500, 1)
            .name('吊顶外房间内区域离顶高')
            .onChange(() => this.updateCeilingConfig());

        // 是否启用内层 - 移到外层
        ceilingFolder.add(this._config, 'hasInnerLayer')
            .name('启用内层')
            .onChange(() => {
                updateInnerLayerVisibility();
                this.updateCeilingConfig();
            });

        // 内层配置
        let innerFolder = ceilingFolder.addFolder('内层配置');
        innerFolder.open();
        
        // 内层方法选择
        innerFolder.add(this._config, 'innerMethod', methodOptions)
            .name('生成区域')
            .onChange((value: string) => {
                this._config.innerMethod = value as GenCeilingLayerMethod;
                this.updateCeilingConfig();
            });
        
        innerFolder.add(this._config, 'innerZvalToTop', 1, 1000, 1)
            .name('离顶高度')
            .onChange(() => this.updateCeilingConfig());

        innerFolder.add(this._config, 'offsetValue', 0, 500, 1)
            .name('偏移值')
            .onChange(() => this.updateCeilingConfig());

        // 灯槽配置 - 移到内层配置中
        let lightSlotFolder = innerFolder.addFolder('灯槽配置');
        lightSlotFolder.open();
        
        // 灯槽方向 - 只有-1和1
        const directionOptions = {
            "正向": 1,
            "反向": -1
        };
        lightSlotFolder.add(this._config, 'direction', directionOptions)
            .name('灯槽方向')
            .onChange(() => this.updateCeilingConfig());
        
        lightSlotFolder.add(this._config, 'extrudeLength', 0, 200, 1)
            .name('灯槽长度')
            .onChange(() => this.updateCeilingConfig());
        
        lightSlotFolder.add(this._config, 'extrudeHeight', 0, 100, 1)
            .name('灯槽高度')
            .onChange(() => this.updateCeilingConfig());

        // 根据启用内层状态控制内层配置的显示
        const updateInnerLayerVisibility = () => {
            if (this._config.hasInnerLayer) {
                innerFolder.show();
            } else {
                innerFolder.hide();
            }
        };
        updateInnerLayerVisibility();

        // 操作按钮
        ceilingFolder.add({
            addNewConfig: () => {
                this.addNewConfig();
            }
        }, 'addNewConfig').name('新增配置');

        ceilingFolder.add({
            saveCurrentConfig: () => {
                this.saveCurrentConfig();
            }
        }, 'saveCurrentConfig').name('保存当前配置');

        ceilingFolder.add({
            updateCeilingDisplay: () => {
                this.updateCeilingDisplay();
            }
        }, 'updateCeilingDisplay').name('更新吊顶外显');

        ceilingFolder.add({
            exportAllConfigs: () => {
                this.exportAllConfigs();
            }
        }, 'exportAllConfigs').name('导出配置');

        // 添加空位
        ceilingFolder.add({ spacer: '' }, 'spacer').name('').disable();

        ceilingFolder.add({
            deleteCurrentConfig: () => {
                this.deleteCurrentConfig();
            }
        }, 'deleteCurrentConfig').name('删除当前配置');

        ceilingFolder.add({
            clearAllConfigs: () => {
                this.clearAllConfigs();
            }
        }, 'clearAllConfigs').name('清空所有配置(慎用)');

        ceilingFolder.add({
            closePanel: () => {
                this.closePanel();
            }
        }, 'closePanel').name('关闭面板');

        // 设置下拉选项的背景色
        this.setDropdownStyles();
    }

    /**
     * 添加房间选择功能
     */
    private static addRoomSelection(ceilingFolder: any) {
        // 创建房间下拉列表
        let roomOptions: { [key: string]: any } = {};
        this._roomList.forEach((room, i) => {
            roomOptions[i + "_" + room.room_name] = room;
        });

        // 房间选择器
        ceilingFolder.add({ room: 0 + "_" + (this._currentRoomInfo?.room_name || '') }, 'room', Object.keys(roomOptions))
            .name('房间')
            .onChange((value: string) => {
                this._currentRoomInfo = roomOptions[value];
                console.log('选择房间:', this._currentRoomInfo);
                // 这里可以添加房间切换后的处理逻辑
            });
    }

    private static setDropdownStyles() {
        // 直接设置下拉选项的背景色
        const style = document.createElement('style');
        style.textContent = `
            .lil-gui select {
                background-color: #000000 !important;
                color: #ffffff !important;
                border: 1px solid #333333 !important;
            }
            .lil-gui select option {
                background-color: #000000 !important;
                color: #ffffff !important;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 获取带ID的吊顶列表
     */
    private static getCeilingListWithIds(): { [key: string]: string } {
        const customConfigs = CeilingConfigManager.getCustomConfigs();
        const list: { [key: string]: string } = {};

        // 添加默认配置到列表最前面
        list['default'] = '默认配置';

        // 添加自定义配置
        Object.keys(customConfigs).forEach(key => {
            const config = customConfigs[key];
            list[key] = config.name || key;
        });

        return list;
    }

    /**
     * 选择吊顶配置
     */
    private static selectCeilingConfig(configId: string) {
        this._currentConfigId = configId;
        
        // 获取配置信息
        const configInfo = CeilingConfigManager.getConfigInfo(configId);
        this._currentConfigName = configInfo.name;
        
        // 加载配置数据到UI
        this.loadConfigToUI(configInfo.config);
        
        // 重置状态
        this.refreshUI();
        
        console.log('选择配置:', this._currentConfigName, configId);
    }

    /**
     * 加载配置数据到UI
     */
    private static loadConfigToUI(config: I_CeilingLayerData) {
        // 更新配置名称
        this._currentConfigName = config.name || this._currentConfigName;
        
        // 关联素材ID - 从配置的扩展属性中获取
        this._config.materialId = (config as any).materialId || "";
        
        // 外层配置
        this._config.outerMethod = config.method as GenCeilingLayerMethod;
        this._config.zvalToTop = config.zvalToTop || 400;
        this._config.zvalToTopFunc = config.zvalToTopFunc || "(maxCeilingHeight)";
        this._config.aroundToTop = config.aroundToTop || 100;
        this._config.outerOffsetValue = config.offset_value || 100;
        
        // 自适应高度判断
        this._config.useAdaptiveHeight = !!config.zvalToTopFunc;
        
        // 内层配置
        if (config.children && config.children.length > 0) {
            const innerConfig = config.children[0];
            this._config.hasInnerLayer = true;
            this._config.innerMethod = innerConfig.method as GenCeilingLayerMethod;
            this._config.innerZvalToTop = innerConfig.zvalToTop || 1;
            this._config.offsetValue = innerConfig.offset_value || 100;
            
            // 灯槽配置
            if (innerConfig.lightSlotData) {
                this._config.direction = innerConfig.lightSlotData.direction || -1;
                this._config.extrudeLength = innerConfig.lightSlotData.extrude_length || 50;
                this._config.extrudeHeight = innerConfig.lightSlotData.extrude_height || 20;
            }
        } else {
            this._config.hasInnerLayer = false;
        }
        
        // 更新外显
        this.updateCeilingDisplay();
    }

    private static addNewConfig() {
        // 使用管理器的addNewConfig方法
        const result = CeilingConfigManager.addNewConfig();
        
        if (result.success) {
            this._currentConfigId = result.configId;
            this._currentConfigName = result.configName;
            
            // 加载默认配置到UI，但保持新生成的配置名称
            const defaultConfig = CeilingConfigManager.getDefaultConfig();
            // 确保使用新生成的配置名称，而不是默认配置的名称
            defaultConfig.name = this._currentConfigName;
            this.loadConfigToUI(defaultConfig);
            
            // 刷新UI界面
            this.refreshUI();
            
            console.log('新增配置:', this._currentConfigName, this._currentConfigId);
        } else {
            console.error('新增配置失败:', result.message);
        }
    }

    /**
     * 刷新UI界面
     */
    private static refreshUI() {
        // 重新创建GUI界面
        if (this._gui) {
            this._gui.destroy();
            this._gui = null;
            this._isInit = false;
        }
        
        // 重新初始化
        this.addCeilingConfigCtr();
        this._isInit = true;
    }

    private static deleteCurrentConfig() {
        if (this._currentConfigId && this._currentConfigId !== "default") {
            // 显示确认对话框
            if (confirm(`警告：此操作将删除配置 "${this._currentConfigName}"，此操作不可恢复！\n\n确定要删除这个配置吗？`)) {
                const result = CeilingConfigManager.deleteConfig(this._currentConfigId);
                console.log(result.message);
                
                if (result.success) {
                    this._currentConfigId = "default";
                    this._currentConfigName = "默认配置";
                    this.loadConfigToUI(CeilingConfigManager.getDefaultConfig());
                    this.refreshUI();
                }
            }
        } else {
            console.warn('默认配置不能删除');
        }
    }

    private static saveCurrentConfig() {
        // 如果是默认配置，需要先创建新配置
        if (this._currentConfigId === "default") {
            // 使用当前配置名称，如果没有则生成一个
            if (!this._currentConfigName || this._currentConfigName === "默认配置") {
                this._currentConfigName = `配置_${Date.now()}`;
            }
            // 生成新的ID
            this._currentConfigId = CeilingConfigManager.generateEnglishId(this._currentConfigName);
        }
        
        const config = this.buildCeilingConfig();
        const result = CeilingConfigManager.saveConfigToId(this._currentConfigId, this._currentConfigName, config);
        console.log(result.message);
        
        if (result.success) {
            this.refreshUI();
        }
    }

    private static updateCeilingDisplay() {
        const config = this.buildCeilingConfig();
        console.log('更新吊顶外显:', config);
        if(this._currentRoomInfo){
            this._currentRoomInfo.ceiling_layer_data = config;
            IFrameMsgServerExFuncs.editCeilingAreaInRoom(this._currentRoomInfo);
        }else{
            console.warn('当前没有房间信息');
            }
    }

    private static exportAllConfigs() {
        const allConfigs = CeilingConfigManager.getAllConfigs();
        const configData = {
            exportTime: new Date().toISOString(),
            totalConfigs: Object.keys(allConfigs).length,
            configs: allConfigs
        };

        // 创建格式化的时间戳
        const now = new Date();
        const timestamp = now.getFullYear() + 
            String(now.getMonth() + 1).padStart(2, '0') + 
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') + 
            String(now.getMinutes()).padStart(2, '0')

        // 创建下载链接
        const dataStr = JSON.stringify(configData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `ceiling_configs_${timestamp}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log('导出所有配置:', configData);
    }

    private static clearAllConfigs() {
        // 显示确认对话框
        if (confirm('警告：此操作将清空所有自定义吊顶配置，此操作不可恢复！\n\n确定要清空所有配置吗？')) {
            const result = CeilingConfigManager.clearAllConfigs();
            console.log(result.message);
            
            if (result.success) {
                this._currentConfigId = "default";
                this._currentConfigName = "默认配置";
                this.loadConfigToUI(CeilingConfigManager.getDefaultConfig());
                this.refreshUI();
                alert('所有自定义配置已清空！');
            } else {
                alert('清空配置失败：' + result.message);
            }
        }
    }

    private static updateCeilingConfig() {
        // 只更新外显，不保存到配置
        const config = this.buildCeilingConfig();
        console.log('配置已更新（未保存）:', config);
        
        // 根据高刷模式决定是否自动刷新
        if (this._highRefreshMode) {
            this.updateCeilingDisplay();
        }
    }

    private static buildCeilingConfig(): I_CeilingLayerData {
        // 根据当前参数动态生成配置名称
        const configName = this._currentConfigName || CeilingConfigManager._idCounter +"_吊顶配置";
        
        const baseConfig: I_CeilingLayerData = {
            name: configName,
            method: this._config.outerMethod,
            zvalToTop: this._config.zvalToTop, // 总是设置zvalToTop，即使使用自适应高度
            aroundToTop: this._config.aroundToTop,
        };

        // 根据自适应高度设置zvalToTopFunc
        if (this._config.useAdaptiveHeight) {
            baseConfig.zvalToTopFunc = this._config.zvalToTopFunc;
        }

        // 如果外层方法是Offset，添加offset_value
        if (this._config.outerMethod === "Offset") {
            baseConfig.offset_value = this._config.outerOffsetValue;
        }

        // 如果有内层配置，添加children
        if (this._config.hasInnerLayer) {
            const innerConfig: I_CeilingLayerData = {
                method: this._config.innerMethod,
                zvalToTop: this._config.innerZvalToTop,
                offset_value: this._config.offsetValue,
                strokeStyle: "#aaa", // 默认样式
                lightSlotData: {
                    direction: this._config.direction,
                    extrude_length: this._config.extrudeLength,
                    extrude_height: this._config.extrudeHeight,
                    section_data: cloneSectionData(DefaultSection[DefaultSectiontype.LShapeSection]), // section_data直接固定死 因为为不可选
                }
            };
            baseConfig.children = [innerConfig];
        }

        // 添加关联素材ID作为扩展属性
        (baseConfig as any).materialId = this._config.materialId;

        return baseConfig;
    }

    public static closePanel() {
        if (this._gui) {
            this._gui.destroy();
            this._gui = null;
            this._isInit = false;
        }
    }
} 